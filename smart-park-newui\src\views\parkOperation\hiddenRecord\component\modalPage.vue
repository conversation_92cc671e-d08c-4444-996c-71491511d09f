<template>
  <dialog-common ref="dialog" title="详情" :width="1200" :showButton="false">
    <el-row>
      <el-col :span="14">
        <el-form label-position="top">
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">隐患信息</div>
          </div>
          <el-row :gutter="40" class="card-textBg">
            <el-col :span="12">
              <el-form-item label="隐患类型">
                {{ state.typeListObj[data.type] }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="隐患等级">
                <el-tag :color="state.levelListColorObj[data.level]" class="status-tag">{{ state.levelListObj[data.level ] }}</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="隐患位置">
                {{ data.spaceFullName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="隐患状态">
                <div>
                  <span class="status-circle" :style="{backgroundColor: state.statusColors[data.status ]}"></span>
                  {{ state.statusOptions[data.status ] }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报人">
                {{ data.createUserName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报时间">
                {{ data.createTime }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否现场整改">
                {{ data.whetherRectification == 1 ? '是' : '否' }}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="具体情况(含建议)">
                {{ data.specificCircumstance }}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件"  class="item__content-noBg">
                <img-video :list="pictureVideo(data.reportImgs)"></img-video>
              </el-form-item>
            </el-col>
          </el-row>
         </el-form>
      </el-col>
      <el-col :span="10">
        <div style="margin-left: 40px">
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">整改记录</div>
          </div>
          <div v-for="(item,index) in recordList"  :key="item.id" v-if="recordList && recordList.length">
<!--            处理-->
            <el-card style="margin-bottom: 20px">
              <el-form label-width="80px" label-suffix=":">
                <div class="audit-card" v-show="item.examineResult">
                  <div class="audit-card-info">
                    {{ item.examineUserName }} &nbsp;&nbsp;  <text class="normal-color">  {{ item.examineTime }}</text>
                  </div>
                  <div v-show="item.examineResult == 1">
                    <strong>审核通过!</strong>
                  </div>
                  <div v-show="item.examineResult == 2">
                    <strong>驳回原因:</strong> {{ item.examineReason }}
                  </div>
                </div>
                <el-row>
                  <el-col :span="10">
                    <el-form-item label="整改人">
                      {{ item.handleUserName }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item label="整改时间">
                      {{ item.handleTime }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="10">
                    <el-form-item label="处理时长">
                      {{ item.handleDuration }} 小时
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="处理记录">
                      {{ item.description }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="上传附件">
                      <img-video :list="pictureVideo(item.rectifyImgs)"></img-video>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>
          </div>
          <el-empty description="暂无数据" v-else />
        </div>
      </el-col>
    </el-row>
  </dialog-common>
</template>

<script setup>
import { hiddenDetailAPI, hiddenRecordAPI } from '@/api/parkOperation/hiddenRecord.js'
import {pictureVideo} from "@/utils/util.js";

const dialog = ref()
const data = ref({})
const recordList = ref([])

const state = reactive({
  levelListObj: {
    1: '一般隐患',
    2: '严重隐患',
    3: '重大隐患',
  },
  levelListColorObj: {
    1: '#ffc000',
    2: '#ff0000',
    3: '#c00000'
  },
  typeListObj: {
    1: '消防隐患',
    2: '安全隐患'
  },
  statusColors: {
    1: '#00b0f0',
    2: '#7f83f7',
    3: '#7f83f7',
    4: '#87d121'
  },
  statusOptions: {
    1: '未查验',
    2: '处理中',
    3: '未审核',
    4: '已完成'
  },
})

const cancelClick = () => {
  emit('cancelClick')
}

// 获取详情
const open = (id) => {
  hiddenDetailAPI({ id }).then(res => {
    data.value = res.data
    dialog.value.open()
  })

  hiddenRecordAPI({id}).then(res => {
    recordList.value = res.data.filter(item => item.operate == 1)
  })

}

defineExpose({
  open
})
</script>

<style lang='less' scoped>
.status-tag{
  color: #FFFFFF;
}

.audit-card{
  padding: 10px 20px ;
  background: #f4f9ff;
  border-radius: 10px;
  margin-bottom: 10px;
  &-info{
    margin-bottom: 10px;
  }
}
</style>
