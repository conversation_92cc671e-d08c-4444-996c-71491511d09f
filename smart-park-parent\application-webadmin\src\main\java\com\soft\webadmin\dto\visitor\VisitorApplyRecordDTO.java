package com.soft.webadmin.dto.visitor;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * VisitorApplyRecordDTO对象
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@ApiModel("VisitorApplyRecordDTO对象")
@Data
public class VisitorApplyRecordDTO {

    @ApiModelProperty(value = "访客姓名")
    private String visitorName;

    @ApiModelProperty(value = "访客手机号码")
    private String visitorPhone;

    @ApiModelProperty(value = "访客身份证号")
    private String visitorIdCard;

    @ApiModelProperty(value = "车牌号码")
    private String licenceNumber;

    @ApiModelProperty(value = "来访事由")
    private String visitReason;

    @ApiModelProperty(value = "预计到访时间")
    private Date planVisitTime;

    private String openId;

    private String enterType;
}
