package com.soft.webadmin.controller.check;

import com.soft.common.core.annotation.MyRequestBody;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.object.TokenData;
import com.soft.webadmin.dto.check.CheckRepairLogDTO;
import com.soft.webadmin.dto.check.CheckRepairLogQueryDTO;
import com.soft.webadmin.dto.check.WorkOrderDispatchDTO;
import com.soft.webadmin.service.check.CheckRepairLogService;
import com.soft.webadmin.vo.check.CheckRepairLogVO;
import com.soft.webadmin.vo.check.WorkOrderOfReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 报修记录控制器类
 * 
 * <AUTHOR>
 * @date 2023-12-12
 */
@Api(tags = "我的报修")
@RestController
@RequestMapping("/check/repair")
public class CheckRepairLogController {

    @Autowired
    private CheckRepairLogService checkRepairLogService;

    // @ApiOperation(value = "查询，全部数据")
    // @GetMapping("/getPage")
    // public ResponseResult<MyPageData<CheckRepairLogVO>> list(CheckRepairLogQueryDTO queryDTO) {
    //     return ResponseResult.success(checkRepairLogService.getPage(queryDTO));
    // }

    @ApiOperation(value = "查询，我提交的报修")
    @GetMapping("/getMyPage")
    public ResponseResult<MyPageData<WorkOrderOfReportVO>> myList(CheckRepairLogQueryDTO queryDTO) {
        queryDTO.setReportUserId(TokenData.takeFromRequest().getUserId());
        return ResponseResult.success(checkRepairLogService.listOfOrder(queryDTO));
    }

    @ApiOperation(value = "报单")
    @PostMapping("/save")
    public ResponseResult<Void> save(@MyRequestBody CheckRepairLogDTO repairDTO,
                                     @MyRequestBody WorkOrderDispatchDTO dispatchDTO) {
        return checkRepairLogService.save(repairDTO, dispatchDTO);
    }
}
