<template>
  <div class="page">
    <div class="conent">
      <div class="tai">
        <div class="ju">分项用能分析</div>
        <div class="ju">
          <el-form :inline="true">
            <el-form-item>
              <el-select v-model="formInline.equipmentType" placeholder="Select" @change="change">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div style="display: flex;margin: 40px 40px 20px;justify-content: space-between;">
        <div style="display: flex;width:48%;justify-content: space-between;">
          <div style="display: flex;">
            <div class="line"></div>
            <div>各分项用能统计</div>
          </div>
          <div>
            <el-date-picker :clearable="false" value-format="YYYY-MM-DD HH:mm" v-model="formInline1.time" type="year"
              placeholder="请选择年份" @change="selectEnergyUseStatistics" />
          </div>
        </div>
        <div style="display: flex;width:48%;justify-content: space-between;">
          <div style="display: flex;">
            <div class="line"></div>
            <div>各分项用能占比</div>
          </div>
          <div>
            <el-date-picker :clearable="false" value-format="YYYY-MM-DD HH:mm" v-model="formInline2.time"
              type="daterange" range-separator="到" start-placeholder="开始日期" end-placeholder="结束日期"
              @change="selectRangeStatistics" />
          </div>
        </div>
      </div>
      <div class="flexClass">
        <div ref="leftMounth" style="width: 50%;height: 400px;"></div>
        <div ref="rightYear" style="width: 50%;height: 400px;"></div>
      </div>
      <div style="display: flex;margin: 40px 40px 20px;">
        <div style="display: flex;justify-content: space-between;width: 100%;">
          <div style="display: flex;">
            <div class="line"></div>
            <div>各分项用能分析</div>
          </div>
          <div>
            <el-date-picker :clearable="false" value-format="YYYY-MM-DD HH:mm" v-model="formInline4.time" type="year"
              placeholder="请选择年份" @change="selectDayAndMonth" />
          </div>
        </div>
      </div>
      <!-- 表格 -->
      <div style="display: flex;justify-content: space-between;">
        <el-table :data="state.tableData">
          <el-table-column prop="area" label="区域/月份" width="120" fixed />
          <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :label="item">
            <el-table-column :prop="'current' + index" label="本期" width="80" />
            <el-table-column :prop="'corresponding' + index" label="上年同期" width="120" />
            <el-table-column :prop="'together' + index" label="同比增长率" width="120" />
            <el-table-column :prop="'priorPeriod' + index" label="上期" width="80" />
            <el-table-column :prop="'loop' + index" label="环比增长率" width="120" />
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs';
import { energyUseDtatisticsOfEachItemAPI, energyUseAnalysisByRegionAPI, areaStatisticsAPI } from "@/api/energyManagement/dateAggregateEcharts.js";
const { proxy } = getCurrentInstance()

const echarts = proxy.$echarts

onMounted(() => {
  selectDayAndMonth();
  selectEnergyUseStatistics();
  selectRangeStatistics();
})

onBeforeUnmount(() => {
  let chartInstances = [myChart1, myChart2];
  chartInstances.forEach(chartInstance => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
  });
});

const formInline = reactive({
  equipmentType: '电表'
});

const options = [
  {
    value: '水表',
    label: '水表',
  },
  {
    value: '电表',
    label: '电表',
  },
  {
    value: '气表',
    label: '气表',
  }
]

const leftMounth = ref(null)
let myChart1 = null;

const rightYear = ref(null)
let myChart2 = null;

const state = reactive({
  tableData: [],
  tableHeader: [],
})

const formInline1 = reactive({
  time: dayjs().format('YYYY-MM-DD HH:mm')
});

const formInline2 = reactive({
  time: [
    dayjs().startOf('month').format('YYYY-MM-DD HH:mm'),
    dayjs().format('YYYY-MM-DD HH:mm')
  ]
});

const formInline4 = reactive({
  time: dayjs().format('YYYY-MM-DD HH:mm')
});

// 各分项用能统计
const selectEnergyUseStatistics = () => {
  let query = {
    equipmentType: formInline.equipmentType,
    time: formInline1.time
  }

  energyUseDtatisticsOfEachItemAPI(query).then(res => {
    var list = []
    var y = []
    res.data.forEach(e => {
      y = e.data
      list.push({
        name: e.name,
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: e.dataX

      })
    })

    if (!myChart1 && leftMounth.value) {
      myChart1 = echarts.init(leftMounth.value);
    }

    myChart1.clear()

    let option1 = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // Use axis to trigger tooltip
          type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
        }
      },
      legend: {},
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value'
      },
      yAxis: {
        type: 'category',
        data: y
      },
      series: list
    };

    myChart1.setOption(option1);
  })
}

// 各分项用能占比
const selectRangeStatistics = () => {
  if (dayjs(formInline2.time[0]).format('YYYY') != dayjs(formInline2.time[1]).format('YYYY')) {
    return ElMessage.error("查询禁止跨年");
  }

  let query = {
    equipmentType: formInline.equipmentType,
    startTime: formInline2.time[0],
    endTime: formInline2.time[1],
    type: '用途'
  }

  areaStatisticsAPI(query).then(res => {
    var list = []
    var tuglie = []
    for (var i = 0; i < res.data.x.length; i++) {
      list.push({ name: res.data.x[i], value: res.data.y[i] })
      tuglie.push(res.data.x[i])
    }

    if (!myChart2 && rightYear.value) {
      myChart2 = echarts.init(rightYear.value);
    }

    myChart2.clear()

    let option2 = {
      legend: {
        show: true,
        orient: 'horizontal', //图例水平对齐排列
        textStyle: {
          //图例文字的样式
          color: '#000',
          fontSize: 12
        },
        data: tuglie, //图例组件
        formatter: (name) => {
          let num = '';
          list.forEach((item) => {
            //格式化图例文本，支持字符串模板和回调函数两种形式。
            if (item.name === name) {
              num = String(item.value).replace(/(\d)(?=(?:\d{6})+$)/g, '$1.');
              return;
            }
          });
          return name + '：' + num;
        }
      },
      series: [
        {
          name: 'Access From',
          type: 'pie',
          radius: '50%',
          data: list,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };

    myChart2.setOption(option2);
  })
}

// 各分项用能分析
const selectDayAndMonth = () => {
  let query = {
    equipmentType: formInline.equipmentType,
    time:formInline4.time,
    type: '用途'
  }

  energyUseAnalysisByRegionAPI(query).then(res => {
    state.tableHeader = res.data.tableName
    state.tableData = res.data.tableVale
  })
}

const change = () => {
  selectEnergyUseStatistics()
  selectRangeStatistics()
  selectDayAndMonth();
}
</script>

<style lang='less' scoped>
.page {
  overflow: scroll;
  height: 100%;
  display: flex;
  flex-direction: column;

  .conent {
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
  }
}


.conent {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.flexClass {
  display: flex;
  justify-content: space-around;
}

.tai {
  width: 100%;
  height: 70px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: space-between;
  padding-left: 32px;
}

.ju {
  display: flex;
  align-items: center;

  .el-form-item {
    margin-bottom: 0;
  }
}

.title {
  color: #989fc7;
}

.line {
  width: 4px;
  height: 20px;
  background-color: #5470c6;
  margin-right: 10px;
}
</style>
