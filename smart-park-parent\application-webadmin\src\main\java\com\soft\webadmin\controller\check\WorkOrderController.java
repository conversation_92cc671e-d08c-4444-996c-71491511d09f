package com.soft.webadmin.controller.check;

import com.soft.admin.upms.vo.SysUserVo;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.object.TokenData;
import com.soft.webadmin.dto.check.*;
import com.soft.webadmin.service.check.WorkOrderService;
import com.soft.webadmin.vo.check.WorkOrderAppVO;
import com.soft.webadmin.vo.check.WorkOrderHandleVO;
import com.soft.webadmin.vo.check.WorkOrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工单控制器类
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
@Api(tags = "工单管理")
@RestController
@RequestMapping("/check/order")
public class WorkOrderController {

    @Autowired
    private WorkOrderService workOrderService;

    @ApiOperation(value = "PC端，工单调度列表")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<WorkOrderVO>> list(WorkOrderQueryDTO queryDTO) {
        return ResponseResult.success(workOrderService.list(queryDTO));
    }

    @ApiOperation(value = "PC端，我的工单列表")
    @GetMapping("/getMyPage")
    public ResponseResult<MyPageData<WorkOrderVO>> myList(WorkOrderQueryDTO queryDTO) {
        queryDTO.setWorkUserId(TokenData.takeFromRequest().getUserId());
        return ResponseResult.success(workOrderService.list(queryDTO));
    }

    @ApiOperation(value = "PC端，报事报修列表")
    @GetMapping("/getReportPage")
    public ResponseResult<MyPageData<WorkOrderVO>> reportList(WorkOrderQueryDTO queryDTO) {
        // 只查询上报的工单
        queryDTO.setIsReport(true);
        return ResponseResult.success(workOrderService.list(queryDTO));
    }

    @ApiOperation(value = "移动端，工单管理列表")
    @GetMapping("/getAppPage")
    public ResponseResult<MyPageData<WorkOrderAppVO>> appList(WorkOrderQueryDTO queryDTO) {
        return ResponseResult.success(workOrderService.appList(queryDTO));
    }

    @ApiOperation(value = "移动端，工单处理列表")
    @GetMapping("/getAppMyPage")
    public ResponseResult<MyPageData<WorkOrderAppVO>> appMyList(WorkOrderQueryDTO queryDTO) {
        queryDTO.setWorkUserId(TokenData.takeFromRequest().getUserId());
        return ResponseResult.success(workOrderService.appList(queryDTO));
    }

    @ApiOperation(value = "工单详情")
    @GetMapping("/detail")
    public ResponseResult<WorkOrderVO> detail(@RequestParam Long id) {
        WorkOrderVO workOrderVO = workOrderService.detail(id);
        if (workOrderVO == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success(workOrderVO);
    }

    @ApiOperation(value = "派单")
    @PostMapping("/dispatch")
    public ResponseResult<Void> dispatch(@Validated @RequestBody WorkOrderDispatchDTO dispatchDTO) {
        return workOrderService.dispatch(dispatchDTO);
    }

    @ApiOperation(value = "关闭")
    @PostMapping("/close")
    public ResponseResult<Void> close(@Validated @RequestBody WorkOrderCloseDTO closeDTO) {
        return workOrderService.close(closeDTO);
    }

    @ApiOperation(value = "处理")
    @PostMapping("/handle")
    public ResponseResult<Void> handle(@Validated @RequestBody WorkOrderHandleDTO handleDTO) {
        return workOrderService.handle(handleDTO);
    }

    @ApiOperation(value = "处理-保存")
    @PostMapping("/handleSave")
    public ResponseResult<Void> handleSave(@Validated @RequestBody WorkOrderHandleDTO handleDTO) {
        return workOrderService.handleSave(handleDTO, GlobalDeletedFlag.DELETED);
    }

    @ApiOperation(value = "处理-获取保存的处理信息")
    @GetMapping("/handleView")
    public ResponseResult<WorkOrderHandleVO> handView(@RequestParam Long id) {
        return ResponseResult.success(workOrderService.handView(id));
    }

    @ApiOperation(value = "退回")
    @PostMapping("/back")
    public ResponseResult<Void> back(@Validated @RequestBody WorkOrderReturnDTO returnDTO) {
        return workOrderService.back(returnDTO);
    }

    @ApiOperation(value = "评价")
    @PostMapping("/evaluate")
    public ResponseResult<Void> evaluate(@Validated @RequestBody WorkOrderEvaluateDTO evaluateDTO) {
        return workOrderService.evaluate(evaluateDTO);
    }

    @ApiOperation(value = "查询工单执行人")
    @GetMapping("/getWorkUserList")
    public ResponseResult<List<SysUserVo>> getWorkUserList() {
        return ResponseResult.success(workOrderService.getWorkUserList());
    }

}
