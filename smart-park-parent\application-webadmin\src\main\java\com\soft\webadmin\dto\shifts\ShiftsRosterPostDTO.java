package com.soft.webadmin.dto.shifts;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * ShiftsRosterPostDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsRosterPostDTO对象")
@Data
public class ShiftsRosterPostDTO {

    @ApiModelProperty(value = "花名册ID")
    @NotNull(message = "数据验证失败，花名册ID不能为空！", groups = {UpdateGroup.class})
    private Long rosterId;

    @ApiModelProperty(value = "部门岗位Id")
    @NotNull(message = "数据验证失败，部门岗位Id不能为空！", groups = {UpdateGroup.class})
    private Long deptPostId;

    @ApiModelProperty(value = "岗位Id")
    private Long postId;

}
