import { divide, multiply } from 'mathjs'

const needToChangeAttrs = ['top', 'left', 'width', 'height']
export function changeComponentsSizeWithScale(canvasStyle ,componentData ,scale , oriScale) {
  componentData.forEach(component => {
    Object.keys(component.style).forEach(key => {
      if (needToChangeAttrs.includes(key)) {
        if (component.style[key] === '') return

        component.style[key] = parseFloat(changeStyleWithScale(getOriginStyle(component.style[key], oriScale), scale).toFixed(2))
      }
    })
  })
  // 背景图片
  if(/\s+/g.test(canvasStyle.backgroundSize)){
    canvasStyle.backgroundSize = canvasStyle.backgroundSize.split(" ").map(i => {
      return changeStyleWithScale(getOriginStyle(i.split('px')[0], oriScale), scale).toFixed(2) + 'px'
    }).join(" ")
  }

  return scale
}

export function getOriginStyle(value, scale) {
  return divide(value, divide(parseFloat(scale), 100))
}


export function changeStyleWithScale(value, scale) {
  return multiply(value, divide(parseFloat(scale), 100))
}
