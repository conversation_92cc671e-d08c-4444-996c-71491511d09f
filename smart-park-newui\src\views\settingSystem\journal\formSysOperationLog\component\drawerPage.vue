<template>
  <el-drawer :modelValue="drawer" direction="rtl" :before-close="cancelClick">
    <template #header>
      <h4>操作日期详情</h4>
    </template>
    <template #default>
      <el-form label-width="100px" :model="form" label-suffix=":">
        <el-form-item label="操作模块" prop="serviceName">
          {{ tableData.serviceName }}
        </el-form-item>
        <el-form-item label="操作人账号" prop="operatorLogin">
          {{ tableData.operatorLogin }}
        </el-form-item>
        <el-form-item label="操作人名称" prop="operatorName">
          {{ tableData.operatorName }}
        </el-form-item>
        <el-form-item label="请求地址" prop="requestUrl">
          {{ tableData.requestUrl }}
          <el-tag style="margin-left: 20px" class="ml-2">{{
            tableData.requestMethod
          }}</el-tag>
        </el-form-item>
        <el-form-item label="主机地址" prop="requestIp">
          {{ tableData.requestIp }}
        </el-form-item>
        <el-form-item label="操作方法" prop="apiMethod">
          {{ tableData.apiMethod }}
        </el-form-item>
        <el-form-item label="Trace Id" prop="requestIp">
          {{ tableData.traceId }}
        </el-form-item>
        <el-form-item label="Session ID" prop="sessionId">
          {{ tableData.sessionId }}
        </el-form-item>
        <el-form-item label="请求参数" prop="requestArguments">
          <JsonViewer :value="tableData.requestArguments" boxed sort></JsonViewer>
        </el-form-item>
        <el-form-item label="返回结果" prop="responseResult">
          <JsonViewer :value="tableData.responseResult" boxed sort></JsonViewer>
        </el-form-item>
        <el-form-item label="登录状态" prop="successName">
          <el-tag class="ml-2" type="success" v-if="tableData.successName == '成功'">{{ tableData.successName }}</el-tag>
          <el-tag class="ml-2" type="danger" v-if="tableData.successName == '失败'">{{ tableData.successName }}</el-tag>
        </el-form-item>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
import { JsonViewer } from "vue3-json-viewer";
import { dictionListItemAPI, dictionAddItemAPI, dictionDelItemAPI } from '@/api/settingSystem/dictionary.js'

import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  drawer: {
    type: Boolean,
    default: false
  },
  tableData: {
    type: Object,
    default: {}
  }
})

let { drawer, tableData } = toRefs(props)

const emit = defineEmits(['cancelClick'])


const form = reactive({})

const state = reactive({
})

const cancelClick = () => {
  emit('cancelClick')
}

defineExpose({
})
</script>

<style lang='less' scoped></style>
