package com.soft.webadmin.listener;
import java.util.Date;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.mqtt.MQTTTopicMessageListener;
import com.soft.sub.model.equipment.EquipmentWarning;
import com.soft.webadmin.dao.contingency.EarlyWarningMapper;
import com.soft.webadmin.dao.contingency.EmergencyMapper;
import com.soft.webadmin.dao.contingency.EventMapper;
import com.soft.webadmin.model.contingency.EarlyWarning;
import com.soft.webadmin.model.contingency.Emergency;
import com.soft.webadmin.model.contingency.Event;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Slf4j
@Component
public class EquipmentEarlyWarningListener implements MQTTTopicMessageListener {


    private static final String LISTENER_TOPIC_NAME = "$queue/equipment/early-warning";


    @Resource
    private EarlyWarningMapper earlyWarningMapper;

    @Resource
    private EventMapper eventMapper;

    @Resource
    private EmergencyMapper emergencyMapper;

    @Override
    public String getTopic() {
        return LISTENER_TOPIC_NAME;
    }

    @Override
    public Integer getQos() {
        return null;
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        String payload = new String(message.getPayload());
        if (StrUtil.isNotBlank(payload)) {
            EquipmentWarning equipmentWarning = JSON.parseObject(payload, EquipmentWarning.class);
            Long id = equipmentWarning.getId();
            Long equipmentId = equipmentWarning.getEquipmentId();
            String equipmentNo = equipmentWarning.getEquipmentNo();
            String equipmentName = equipmentWarning.getEquipmentName();
            String equipmentModel = equipmentWarning.getEquipmentModel();
            Long equipmentSpaceId = equipmentWarning.getEquipmentSpaceId();
            String equipmentSpacePath = equipmentWarning.getEquipmentSpacePath();
            String equipmentSpaceFullName = equipmentWarning.getEquipmentSpaceFullName();
            String subType = equipmentWarning.getSubType();
            String warningType = equipmentWarning.getWarningType();
            String content = equipmentWarning.getContent();
            String level = equipmentWarning.getLevel();
            Integer status = equipmentWarning.getStatus();
            String result = equipmentWarning.getResult();
            String remark = equipmentWarning.getRemark();
            Long handleUserId = equipmentWarning.getHandleUserId();
            Date handleTime = equipmentWarning.getHandleTime();
            String attachFile = equipmentWarning.getAttachFile();
            String eventId = equipmentWarning.getEventId();
            Date eventStartTime = equipmentWarning.getEventStartTime();
            Date eventEndTime = equipmentWarning.getEventEndTime();
            Long createUserId = equipmentWarning.getCreateUserId();
            Date createTime = equipmentWarning.getCreateTime();
            Long updateUserId = equipmentWarning.getUpdateUserId();
            Date updateTime = equipmentWarning.getUpdateTime();

            EarlyWarning earlyWarning = new EarlyWarning();
            earlyWarning.setNo("YJ" + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSSS"));

            Event event = eventMapper.selectOne(Wrappers.lambdaQuery(Event.class)
                    .eq(Event::getName, "设备告警"));
            if (event != null) {
                earlyWarning.setEventId(event.getId());
                Emergency emergency = emergencyMapper.selectOne(Wrappers.lambdaQuery(Emergency.class)
                        .eq(Emergency::getEventId, event.getId()));
                if (emergency != null) {
                    earlyWarning.setEmergencyId(emergency.getId());
                }
            }
            earlyWarning.setType(1);
            earlyWarning.setBusinessId(id);
            earlyWarning.setBusinessTable("sp_equipment_warning");
            earlyWarning.setSpaceId(equipmentSpaceId);
            earlyWarning.setSpacePath(equipmentSpacePath);
            earlyWarning.setSpaceFullName(equipmentSpaceFullName);
            earlyWarning.setDescription(content);
            earlyWarning.setStatus(0);
            earlyWarning.setDeletedFlag(GlobalDeletedFlag.NORMAL);
            earlyWarningMapper.insert(earlyWarning);
        }
    }
}
