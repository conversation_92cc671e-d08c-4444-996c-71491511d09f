<template>
  <div>
    <el-form label-position="right" label-width="110px" label-suffix=":">
      <el-timeline class="timeline-box-card">
        <el-timeline-item
            v-for="item in props.data"
            :timestamp="item.createTime"
            placement="top"
            type="primary"
            hollow>
          <el-card class="life-circle">
            <div class="top">
              <h4>{{ state.operateOptions[item.operate] }}</h4>
              <div class="right" v-show="item.createUserName">操作人：{{ item.createUserName }}</div>
            </div>
            <div v-if="item.operate === 2">
              <!--派单-->
              <div class="item">执行人：
                <span class="item-inline-content">{{ JSON.parse(item.content).workUserName }}</span>
              </div>
              <div class="item">备注：
                <div class="item-content">{{ JSON.parse(item.content).remark }}</div>
              </div>
            </div>
            <div v-if="item.operate === 4">
              <!--退回-->
              <div class="item">退回原因：
                <div class="item-content">{{ JSON.parse(item.content).reason }}</div>
              </div>
              <div class="item" v-if="JSON.parse(item.content).img">
                <img-video :list="pictureVideo(JSON.parse(item.content).img)"></img-video>
              </div>
            </div>
            <div v-if="item.operate === 5 || item.operate === 9 || item.operate === 13">
              <!--关闭 挂单、驳回-->
              <div class="item">{{ state.operateOptions[item.operate] }}原因：
                <div class="item-content">{{ JSON.parse(item.content).reason }}</div>
              </div>
            </div>
            <div v-if="item.operate === 11">
              <!--完工-->
              <div class="item" v-if="JSON.parse(item.content).hasSparePartReturn">有备件退回</div>
              <div class="item">描述：
                <div class="item-content">{{ JSON.parse(item.content).describe }}</div>
              </div>
              <div class="item" v-if="JSON.parse(item.content).img">
                <img-video :list="pictureVideo(JSON.parse(item.content).img)"></img-video>
              </div>
            </div>
            <div v-if="item.operate === 12">
              <!--评价-->
              <div class="item"><el-rate v-model="JSON.parse(item.content).score" disabled show-score score-template="{value}星"/></div>
              <div class="item"><div class="item-content">{{ JSON.parse(item.content).content }}</div></div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-form>
  </div>
</template>

<script setup>
import {pictureVideo} from '@/utils/util'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const state = reactive({
  operateOptions: {
    1: '创建工单',
    2: '派单',
    3: '响应',
    4: '退回',
    5: '关闭工单',
    6: '报价',
    7: '审计同意',
    8: '审计编辑并同意',
    9: '挂单',
    10: '取消挂单',
    11: '完成工单',
    12: '评价工单',
    13: '驳回',
  },
})
</script>

<style scoped lang="less">

</style>
