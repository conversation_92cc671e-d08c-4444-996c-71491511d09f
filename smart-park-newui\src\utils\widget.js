export class DropdownWidget {
  /**
   * 下拉组件（Select、Cascade、TreeSelect、Tree等）
   * @param {function () : Promise} loadDropdownData 下拉数据获取函数
   * @param {Boolean} isTree 是否是树数据
   * @param {String} idKey 键字段字段名
   * @param {String} parentIdKey 父字段字段名
   */
  constructor (loadDropdownData, isTree = false, idKey = 'id', parentIdKey = 'parentId') {
    this.loading = false;
    this.dirty = true;
    this.dropdownList = [];
    this.isTree = isTree;
    this.idKey = idKey;
    this.parentIdKey = parentIdKey;
    this.loadDropdownData = loadDropdownData;
    this.setDropdownList = this.setDropdownList.bind(this);
    this.onVisibleChange = this.onVisibleChange.bind(this);
  }
  /**
   * 重新获取下拉数据
   */
  reloadDropdownData () {
    return new Promise((resolve, reject) => {
      if (!this.loading) {
        if (typeof this.loadDropdownData === 'function') {
          this.loading = true;
          this.loadDropdownData().then(dataList => {
            this.setDropdownList(dataList);
            this.loading = false;
            this.dirty = false;
            resolve(this.dropdownList);
          }).catch(e => {
            this.setDropdownList([]);
            this.loading = false;
            reject(this.dropdownList);
          });
        } else {
          reject(new Error('获取下拉数据失败'));
        }
      } else {
        resolve(this.dropdownList);
      }
    });
  }
  /**
   * 下拉框显示或隐藏时调用
   * @param {Boolean} isShow 正在显示或者隐藏
   */
  onVisibleChange (isShow) {
    return new Promise((resolve, reject) => {
      if (isShow && this.dirty && !this.loading) {
        this.reloadDropdownData().then(res => {
          resolve(res);
        }).catch(e => {
          reject(e);
        });
      } else {
        resolve(this.dropdownList);
      }
    });
  }
  /**
   * 设置下拉数据
   * @param {Array} dataList 要显示的下拉数据
   */
  setDropdownList (dataList) {
    if (Array.isArray(dataList)) {
      this.dropdownList = this.isTree ? treeDataTranslate(dataList, this.idKey, this.parentIdKey) : dataList;
    }
  }
}

/**
 * 列表数据转换树形数据
 * @param {Array} data 要转换的列表
 * @param {String} id 主键字段字段名
 * @param {String} pid 父字段字段名
 * @returns {Array} 转换后的树数据
 */
export function treeDataTranslate (data, id = 'id', pid = 'parentId') {
  var res = []
  var temp = {}
  for (var i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i]
  }
  for (var k = 0; k < data.length; k++) {
    if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
      if (!temp[data[k][pid]]['children']) {
        temp[data[k][pid]]['children'] = []
      }
      if (!temp[data[k][pid]]['_level']) {
        temp[data[k][pid]]['_level'] = 1
      }
      data[k]['_level'] = temp[data[k][pid]]._level + 1
      data[k]['_parent'] = data[k][pid]
      temp[data[k][pid]]['children'].push(data[k])
    } else {
      res.push(data[k])
    }
  }

  return res
}
