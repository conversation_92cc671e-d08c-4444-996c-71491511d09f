import { request } from "@/utils/request.js";

/**
 * 获取消费记录（分页）
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export const getChargeRecordPageAPI = (data) => {
  return request('get', '/subSystem/canteen/charge/record/page', data, 'F')
}

/**
 * 获取充值记录（分页）
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export const getRechargeRecordPageAPI = (data) => {
  return request('get', '/subSystem/canteen/recharge/record/page', data, 'F')
}
