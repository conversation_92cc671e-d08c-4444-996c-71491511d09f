<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.sparePart.SparePartQuantityChangeMapper">
    <resultMap type="com.soft.webadmin.model.sparePart.SparePartQuantityChange" id="SparePartQuantityChangeResult">
        <result property="id" column="id" />
        <result property="inoutId" column="inout_id" />
        <result property="storehouseId" column="storehouse_id" />
        <result property="sparePartId" column="spare_part_id" />
        <result property="changeQuantity" column="change_quantity" />
        <result property="inventoryQuantity" column="inventory_quantity" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectSparePartQuantityChangeVo">
        t.id, t.inout_id, t.storehouse_id, t.spare_part_id, t.change_quantity, t.inventory_quantity, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="getList" resultType="com.soft.webadmin.vo.sparePart.SparePartQuantityChangeVO" parameterType="com.soft.webadmin.dto.sparePart.SparePartQuantityChangeQueryDTO">
        select <include refid="selectSparePartQuantityChangeVo"/>,
        i.operate_type, i.inout_no, i.type, i.examine_state,
        (select storehouse_name from sp_spare_part_storehouse where id = t.storehouse_id) storehouse_name,
        (select show_name from common_sys_user where user_id = t.create_user_id) create_user_name
        from sp_spare_part_quantity_change t
        left join sp_spare_part_inout i on t.inout_id = i.id
        <where>
            <if test="sparePartId != null">
                and t.spare_part_id = #{sparePartId}
            </if>
            <if test="operateType != null">
                and i.operate_type = #{operateType}
            </if>
            <if test="type != null">
                and i.type = #{type}
            </if>
            <if test="examineState != null">
                and i.examine_state = #{examineState}
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="getListByInoutId" resultType="com.soft.webadmin.vo.sparePart.SparePartInoutRecordVO">
        select <include refid="selectSparePartQuantityChangeVo"/>,
        (select storehouse_name from sp_spare_part_storehouse where id = t.storehouse_id) storehouse_name
        from sp_spare_part_quantity_change t where inout_id = #{inoutId}
    </select>
    
</mapper>