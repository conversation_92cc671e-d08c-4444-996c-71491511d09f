package com.soft.webadmin.job;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.soft.common.monitor.hk.HikPassengerFlow;
import com.soft.common.monitor.hk.HikUtils;
import com.soft.webadmin.dao.face.PassengerFlowStatisticsMapper;
import com.soft.webadmin.model.face.PassengerFlowStatistics;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;


/**
 * 客流定时任务
 */
@Slf4j
@Component
public class PassengerFlowJob {

    @Resource
    private PassengerFlowStatisticsMapper passengerFlowStatisticsMapper;

    /**
     * 请求日客流量
     */
    private static final String REPORT_DAY_PASSENGER_FLOW_HANDLE = "reportDayPassengerFlowJob";


    @Resource
    private HikUtils hikUtils;


    /**
     * 同步客流量：每十分钟执行一次，每次同步半小时的客流
     */
//    @XxlJob(REPORT_DAY_PASSENGER_FLOW_HANDLE)
    public void reportDayPassengerFlowHandle() throws InterruptedException {
        LocalDateTime start;
        LocalDateTime end;
        // 任务参数
        String jobParam = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(jobParam)) {
            JSONObject jobParamJson = JSON.parseObject(jobParam);
            Date startTime = jobParamJson.getDate("startTime");
            start = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            Date endTime = jobParamJson.getDate("endTime");
            end = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } else {
            // 查询表中最后一条数据
            PassengerFlowStatistics passengerFlowStatistics = passengerFlowStatisticsMapper.selectOne(Wrappers.lambdaQuery(PassengerFlowStatistics.class)
                    .eq(PassengerFlowStatistics::getReportType, 1)
                    .orderByDesc(PassengerFlowStatistics::getEndTime)
                    .last("limit 1"));
            if (passengerFlowStatistics != null) {
                start = passengerFlowStatistics.getEndTime();
                end = start.plusMinutes(30);
            } else {
                start = LocalDateTime.now().with(LocalTime.MIN);
                end = start.plusMinutes(30);
            }
        }

        // 如果 当前时间 小于 end 时间，则结束当前任务
        if (end.isAfter(LocalDateTime.now())) {
            log.warn("passenger flow statistics day report, but end time is after now time, so return current job!");
            return;
        }

        // 查询当前时间段是否已统计
        PassengerFlowStatistics passengerFlowStatistics = passengerFlowStatisticsMapper.selectOne(Wrappers.lambdaQuery(PassengerFlowStatistics.class)
                .eq(PassengerFlowStatistics::getReportType, 1)
                .le(PassengerFlowStatistics::getStartTime, start)
                .ge(PassengerFlowStatistics::getEndTime, end));
        if (passengerFlowStatistics != null) {
            log.warn("passenger flow statistics day report, but current time range (start: {}; end: {}) has record, so return current job!", start, end);
            return;
        }

        // 查询客流量
        HikPassengerFlow hikPassengerFlow = hikUtils.getPassengerFlowStatistics("192.168.10.241", (short) 8000, "admin", "wtzx123456", start, end, (byte) 1);
        PassengerFlowStatistics passengerFlowRecord = new PassengerFlowStatistics();
        passengerFlowRecord.setEnterCount(hikPassengerFlow.getDwEnterNum());
        passengerFlowRecord.setLeaveCount(hikPassengerFlow.getDwLeaveNum());
        passengerFlowRecord.setPassCount(hikPassengerFlow.getDwPeoplePassing());
        passengerFlowRecord.setReportType(1);
        passengerFlowRecord.setStartTime(start);
        passengerFlowRecord.setEndTime(end);
        passengerFlowRecord.setCreateTime(LocalDateTime.now());
        passengerFlowStatisticsMapper.insert(passengerFlowRecord);
    }
}
