<template>
  <div style="height: 100%;overflow: hidden;">
    <!-- 列表 -->
    <transition name="el-zoom-in-center">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" v-show="pageIndex == 0">
        <el-tab-pane label="待处理" name="first">
          <warnPending @showPage="showPage" ref="tAPI"></warnPending>
        </el-tab-pane>
        <el-tab-pane label="处理记录" name="second">
          <processingRecord @showPage="showPage" ref="process"></processingRecord>
        </el-tab-pane>
      </el-tabs>
    </transition>
    <!-- 详情 -->
    <transition name="el-zoom-in-center">
      <detailPage v-show="pageIndex == 1" @showPage="showPage" ref="datail"></detailPage>
    </transition>
  </div>
</template>

<script setup>
import warnPending from './component/warnPending.vue'
import processingRecord from './component/processingRecord.vue'
import detailPage from './component/detailPage.vue';

import { events } from '@/utils/bus.js'

const tAPI = ref()
const process = ref()
const datail = ref()
const pageIndex = ref(0)
const activeName = ref('first')

const handleClick = (tab, event) => {
  nextTick(() => {
    switch (activeName.value) {
      case 'first':
        tAPI.value.getList()
        break;
      case 'second':
        process.value.getList()
        break;
      default:
        break;
    }
    events.emit('tabClick')
  })
};

const showPage = (index, id) => {
  pageIndex.value = index

  if (index == 0) { //刷新
    switch (activeName.value) {
      case 'first':
        tAPI.value.getList()
        break;
      case 'second':
        process.value.getList()
        break;
      default:
        break;
    }
  }else if(index == 1){
    datail.value.loadPage(id)
  }
}
</script>

<style lang='less' scoped>
.el-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);

    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
