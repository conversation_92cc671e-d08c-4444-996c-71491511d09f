package com.soft.webadmin.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.sub.dao.equipment.BlacklistWhitelistMapper;
import com.soft.sub.model.equipment.SpBlacklistWhitelist;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @ClassName CheckPlanHandleJob
 * @description: 黑白名单处理
 * @date 2024年2月19日
 */
@Service
@Slf4j
public class BlacklistWhitelistJob {

    @Autowired
    private BlacklistWhitelistMapper spBlacklistWhitelistMapper;

    /**
     * 处理黑白名单状态
     * 每天执行一次
     */
    @XxlJob("WhitelistStatus")
    public void WhitelistStatus() {
        try {
            List<SpBlacklistWhitelist> spBlacklistWhitelists = spBlacklistWhitelistMapper.selectList(
                    new LambdaQueryWrapper<SpBlacklistWhitelist>()
                            .ne(SpBlacklistWhitelist::getStatus, "2")
            );
            Date date = new Date();
            spBlacklistWhitelists.forEach(e -> {
                if (date.after(e.getEndTime())) {
                    e.setStatus("2");
                    spBlacklistWhitelistMapper.updateById(e);
                    //TODO 发送给厂家
                } else if (date.after(e.getStartTime())) {
                    e.setStatus("1");
                    spBlacklistWhitelistMapper.updateById(e);
                    //TODO 发送给厂家
                }
            });
        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }
}
