<template>
  <div style="height: 100%;">
    <page-common v-model="state.tableHeight" v-if="state.show">
      <template #query>
        <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
          <el-form-item prop="deptName">
            <el-input v-model="formInline.deptName" placeholder="组织名称" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
            <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #operate>
        <el-button type="primary" :icon="Plus" @click="addHandle">新建组织</el-button>
      </template>
      <template #table>
        <el-table :height="state.tableHeight" show-overflow-tooltip :data="state.tableDate" row-key="deptId">
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
            :align="item.align" :formatter="item.formatter" />
          <el-table-column align="center" label="操作" width="230">
            <template #default="scope">
              <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
              <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
              <el-button link icon="Refresh" type="primary" @click="openPost(scope.row)">岗位</el-button>
            </template>
          </el-table-column>
        </el-table>
        <modal-page ref="modal" :title="state.title" @submit="getList"></modal-page>
      </template>
    </page-common>
    <drawer-page ref="drawer" v-else :deptId="state.deptId" @backOn="backOn" ></drawer-page>
  </div>
</template>

<script setup>
import drawerPage from './pagingTable.vue'
import modalPage from '@/views/settingSystem/basic/depart/component/modalPage.vue'
import { treeDataTranslate } from '@/utils';
import {
  Plus, Search, Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { departListAPI, departAddAPI, departEidtAPI, departDelAPI } from '@/api/settingSystem/depart.js'
import { nextTick } from 'vue';

let formInlineRef = ref()
let drawer = ref()
const formInline = reactive({})
let dialog = ref()
const form = reactive({
  deptId: ''
})

let modal = ref()

let emits = defineEmits('changeShow')

const state = reactive({
  disabledPostSetting: true,
  postSettingList: [],
  postShowNameAddButton: false,
  deptId: '',
  show: true,
  title: '',
  rules: {
    deptName: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
    showOrder: [{ required: true, message: '请输入显示顺序', trigger: 'blur' }]
  },
  tableHeight: 100,
  tableDate: [],
  tableDatePost: [],
  tableDatePostSetting: [],
  tableHeader: [
    {
      prop: 'deptName',
      label: '组织名称'
    },
    {
      prop: 'deptCode',
      label: '组织编号'
    },
    {
      prop: 'deptDesc',
      label: '组织描述'
    }
  ],
  tableHeader1: [
    {
      prop: 'postLevel',
      label: '岗位层级'
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
  menuType: [],
  menuTagType: []
})

onMounted(() => {
  getList()
})

const backOn = () => {
  state.show = true
  emits('changeShow')
  getList();
}

//dialog1
// 树形查询
const getList = () => {
  state.show = true
  let query = {
    orderParam: [{ fieldName: 'showOrder', asc: true }],
    sysDeptDtoFilter: formInline
  }
  departListAPI(query).then(res => {
    // console.log(treeDataTranslate(res.data.dataList, 'deptId', 'parentId'));
    var a = JSON.parse(JSON.stringify(res.data.dataList));
    state.tableDate = treeDataTranslate(a, 'deptId', 'parentId')
  })
}

// 岗位
const openPost = (row) => {
  state.deptId = row.deptId
  state.show = false
  emits('changeShow')
  nextTick(() => {
    console.log(drawer);
    drawer.value.getPostList()
  })


}

// 删除部门
const deleteHandle = (info) => {
  ElMessageBox.confirm("是否删除当前部门?", "提醒", {
    type: "warning",
  }).then(() => {
    if (info.children) {
      return ElMessage.error('请删除子级')
    }
    departDelAPI({ deptId: info.deptId }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  });
}

//部门重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

//部门查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 部门dialog保存
const submit = () => {
  let data = {
    sysDeptDto: form
  }

  if (data.sysDeptDto.deptId) {
    subHandle(departEidtAPI, '编辑成功')
  } else {
    subHandle(departAddAPI, '添加成功')
  }

  function subHandle(req, title) {
    req(data).then(res => {
      if (res.success) {
        ElMessage.success(title)
        dialog.value.close()
        getList()
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  }
}

// 新建部门
const addHandle = () => {
  // form.menuId = ''
  // state.title = '添加部门'
  // dialog.value.open()

  modal.value.form.deptId = ''
  state.title = '新建组织'
  modal.value.open()
}

//编辑部门
const editHandle = (info) => {
  if (!Number(info.parentId)) {
    info.parentId = ''
  }
  state.title = '编辑组织'
  modal.value.open()
  nextTick(() => {
    Object.assign(modal.value.form, { ...info })
  })
  // state.title = '编辑部门'
  // dialog.value.open()
  // nextTick(() => {
  //   Object.assign(form, info)
  // })
}


</script>

