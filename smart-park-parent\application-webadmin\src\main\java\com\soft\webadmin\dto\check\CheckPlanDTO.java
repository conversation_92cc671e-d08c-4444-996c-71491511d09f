package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import lombok.Data;

import java.util.List;

/**
 * CheckPlanDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckPlanDTO对象")
@Data
public class CheckPlanDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "计划类型（PATROL_INSPECTION巡检，MAINTENANCE维保）")
    @NotBlank(message = "计划类型不能为空！")
    private String planType;

    @ApiModelProperty(value = "计划名称")
    @NotBlank(message = "计划名称不能为空！")
    private String planName;

    @ApiModelProperty(value = "巡检方式（1人工巡检，2智能巡检）")
    private Integer planMode = 1;

    @ApiModelProperty(value = "工作班组id")
    private Long workGroupId;

    @ApiModelProperty(value = "执行频率（MONTH月，WEEK周，CUSTOM自定义）")
    private String scheduleType = "MONTH";

    @ApiModelProperty(value = "排班规则/首保日期")
    @NotBlank(message = "排班规则/首保日期不能为空！")
    private String scheduleRule;

    @ApiModelProperty(value = "维保周期，单位：月")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "开始时间")
    @NotBlank(message = "开始时间不能为空！")
    private String startTime;

    @ApiModelProperty(value = "提前几小时派单")
    private Integer advanceTime;

    @ApiModelProperty(value = "处理时限，单位：小时")
    private Long handleLimitDuration;

    @ApiModelProperty(value = "状态（0停用，1启用）", hidden = true)
    private Integer state = 1;

    @ApiModelProperty(value = "巡检设备")
    private String equipmentIds;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "检查点位")
    @Valid
    private List<CheckPlanPointDTO> checkPlanPointList;

}
