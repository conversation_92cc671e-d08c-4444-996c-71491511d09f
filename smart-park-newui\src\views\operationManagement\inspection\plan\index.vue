<template>
  <div style="height: 100%;overflow: hidden;">
    <transition name="el-zoom-in-center">
      <listTable v-show="0 === pageIndex" @showPage="showPage" ref="table"></listTable>
    </transition>
    <transition name="el-zoom-in-center">
      <modalPage v-if="1 === pageIndex" @showPage="showPage" :title="state.title" :info="state.row"></modalPage>
    </transition>
    <transition name="el-zoom-in-center">
      <detail v-if="2 === pageIndex" @showPage="showPage" :title="state.title" :info="state.row"></detail>
    </transition>
  </div>
</template>

<script setup>
import listTable from './component/listTable.vue';
import detail from './component/detail.vue';
import modalPage from './component/modalPage.vue';

const pageIndex = ref(0)
const table = ref()

const state = reactive({
  title: '',
  row: {}
})

const showPage = (index, title, row) => {
  pageIndex.value = index;
  if (index === 0) {
    table.value.getList();
  } else {
    state.title = title;
    state.row = JSON.parse(JSON.stringify(row || {}));
  }
}

</script>

<style scoped lang="less">

</style>
