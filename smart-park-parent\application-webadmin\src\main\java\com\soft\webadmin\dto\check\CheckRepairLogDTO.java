package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * CheckRepairLogDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckRepairLogDTO对象")
@Data
public class CheckRepairLogDTO {

    // @ApiModelProperty(value = "主键id")
    // private Long id;

    @ApiModelProperty(value = "设备id")
    private Long equipmentId;

    @ApiModelProperty(value = "位置（空间点位）")
    @NotNull(message = "位置不能为空！")
    private Long spaceId;

    @ApiModelProperty(value = "优先级（1普通，2紧急，3特级）")
    private Integer priority = 1;

    @ApiModelProperty(value = "问题描述")
    @NotBlank(message = "问题描述不能为空！")
    private String content;

    @ApiModelProperty(value = "上报科室id")
    private Long reportDeptId;

    @ApiModelProperty(value = "上报人id")
    @NotNull(message = "上报人不能为空！")
    private Long reportUserId;

    @ApiModelProperty(value = "报修人电话")
    private String reportUserPhone;

    @ApiModelProperty(value = "故障图片")
    private String img;

}
