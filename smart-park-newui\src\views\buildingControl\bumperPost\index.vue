<style lang="less" scoped>
/deep/ .el-card__body {
  height: 100%;
  .flag {
    text-align: center;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      // border: solid red 1px;
      width: 100px;
      // height: 300px;
      margin-left: 50px;
    }
  }
}
</style>

<template>
  <el-card style="height: 100%">
    <div class="flag">
      <img src="@/assets/img/bumperPost.gif" />
      <img src="@/assets/img/bumperPost.gif" />
      <img src="@/assets/img/bumperPost.gif" />
      <img src="@/assets/img/bumperPost.gif" />
      <img src="@/assets/img/bumperPost.gif" />
      <img src="@/assets/img/bumperPost.gif" />
      <img src="@/assets/img/bumperPost.gif" />
      <img src="@/assets/img/bumperPost.gif" />
    </div>
  </el-card>
</template>

<script>
export default {
  data() {
    return {
      isTop: true,
      // topUrl: new URL("@/assets/img/flag/top.png"),
      // bottomUrl: new URL("@/assets/img/flag/bottom.png"),
    };
  },
  methods: {
    async refresh() {
      let { data: res } = await this.axios.post("/flag/system/status");
      if (res.resultObject.search("FAIL") != -1)
        return this.message.error("刷新失败");
      this.isTop = res.resultObject.search("TOP") != -1;
    },
  },
};
</script>
