<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="100px" :model="form" :rules="state.rules" label-suffix=":">
      <el-form-item label="组态名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入组态名称" />
      </el-form-item>
      <el-form-item label="关联菜单" prop="menuId">
        <el-tree-select v-model="form.menuId" :data="state.tableData" check-strictly :render-after-expand="false"
                        node-key="menuId" :props="treeProps" placeholder="请选择关联菜单" clearable />
      </el-form-item>
      <el-form-item label="组态备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" :autosize="{ minRows: 5 }"  placeholder="请输入组态备注"/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { ElMessage } from 'element-plus'

import {topoAddAPI, topoEditAPI} from '@/api/settingSystem/topoCenter.js'
import { menuListAPI } from '@/api/settingSystem/menu.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})

let { title } = toRefs(props)

const emit = defineEmits(['submit'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({})

const open = () => {
  dialog.value.open()
}

const state = reactive({
  tableData: [],
  rules: {
    name: [{ required: true, message: '请输入组态名称', trigger: 'blur' },],
  }
})

onMounted(() => {
  getMenu()
})

const treeProps = computed(() => {
  return {
    label: 'menuName'
  }
})

// 获取全部菜单
const getMenu = () => {
  menuListAPI().then(res => {
    state.tableData = res.data
  })
}

// 提交组态
const submit = () => {
  if (form.id) {
    subHandle(topoEditAPI, '编辑成功')
  } else {
    subHandle(topoAddAPI, '添加成功')
  }

  function subHandle(req, title) {
    req(form).then(res => {
      if (res.success) {
        ElMessage.success(title)
        dialog.value.close()
        emit('submit')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  }
}

defineExpose({
  form,
  open
})
</script>

<style lang='less' scoped>
</style>
