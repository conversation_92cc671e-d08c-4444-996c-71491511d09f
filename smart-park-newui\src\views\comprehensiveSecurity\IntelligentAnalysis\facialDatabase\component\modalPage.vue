<template>
  <dialog-common
    @onClose="onClose"
    ref="dialog"
    :title="title"
    @submit="submit"
    :formRef="ruleFormRef"
    :width="900"
    class="dialogTextarea"
  >
    <el-form
      ref="ruleFormRef"
      :model="form"
      :rules="state.rules"
      label-width="130px"
      label-suffix=":"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="姓名" prop="username">
            <el-input
              v-model="form.username"
              clearable
              placeholder="请输入姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-select v-model="form.sex" placeholder="性别">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" clearable placeholder="手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司名称" prop="companyName">
            <el-input
              v-model="form.companyName"
              clearable
              placeholder="请输入公司名称"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="certificateNum">
            <el-input
              v-model="form.certificateNum"
              placeholder="请输入身份证号"
              maxlength="18"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              :rows="5"
              type="textarea"
              :maxlength="500"
              show-word-limit
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="人脸照片" prop="fileList">
            <el-upload
              drag
              v-model:file-list="form.fileList"
              :action="state.action"
              :headers="{ Authorization: state.Authorization }"
              :on-success="fileSuccess"
              :on-preview="hanldePreview"
              :limit="1"
              :on-exceed="() => ElMessage.warning('最多可支持上传一份文件')"
              :multiple="false"
              accept="image/*"
              style="margin-right: 20px; width: 85%"
            >
              <el-icon class="el-icon--upload">
                <upload-filled />
              </el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  支持格式：JPG、JPEG、PNG，不能超过5MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { faceSaveAPI } from "@/api/comprehensiveSecurity/IntelligentAnalysis.js";
import { ElMessage } from "element-plus";

// 正则校验
const validateRegular = (value, callback, Reg, tip) => {
  if (value && !Reg.test(value)) {
    callback(new Error(tip));
  } else {
    callback();
  }
};

const emit = defineEmits(["submit"]);
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  options: {
    type: Object,
    default: () => {},
  },
});

const { title } = toRefs(props);
let ruleFormRef = ref();
let dialog = ref();

const form = reactive({
  fileList: [],
});

const state = reactive({
  action: import.meta.env.VITE_BASE_URL + "/core/file/upload",
  Authorization: localStorage.getItem("Authorization"),
  rules: {
    username: [{ required: true, message: "请输入姓名", trigger: "blur" }],
    fileList: [{ required: true, message: "请上传人脸照片", trigger: "blur" }],
    phone: [
      {
        validator: (rule, value, callback) =>
          validateRegular(
            value,
            callback,
            /^1[3456789]\d{9}$/,
            "请输入正确的手机号"
          ),
        trigger: "blur",
      },
    ],
    certificateNum: [
      { required: true, message: "请输入身份证号", trigger: "blur" },
      {
        validator: (rule, value, callback) =>
          validateRegular(
            value,
            callback,
            /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
            "请输入正确的身份证"
          ),
        trigger: "blur",
      },
    ],
  },
});

// 上传图片
const fileSuccess = (response, file) => {
  file.filePath = response.data.filePath;
  file.id = response.data.id;

  ruleFormRef.value.validateField("fileList");
};

// 预览
const hanldePreview = (res) => {
  window.open(import.meta.env.VITE_BASE_URL + res.filePath);
};

const open = () => {
  dialog.value.open();
  // console.log(form);
};

const onClose = () => {
  
};

// 提交
const submit = () => {
  let subForm = JSON.parse(JSON.stringify(form));
  subForm.faceImage = subForm.fileList[0].filePath;

  // console.log(subForm);
  faceSaveAPI(subForm).then((res) => {
    if (res.success) {
      ElMessage.success("名单已提交");
      dialog.value.close();
      emit("submit");
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

defineExpose({
  form,
  open,
});
</script>

<style lang="less" scoped></style>
