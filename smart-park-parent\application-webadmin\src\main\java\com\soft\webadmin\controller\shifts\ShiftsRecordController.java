package com.soft.webadmin.controller.shifts;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.shifts.ShiftsAttendanceSaveDTO;
import com.soft.webadmin.dto.shifts.ShiftsLiabilityAreaDTO;
import com.soft.webadmin.dto.shifts.ShiftsRecordQueryDTO;
import com.soft.webadmin.service.shifts.ShiftsRecordService;
import com.soft.webadmin.vo.shifts.ShiftsAttendanceRecordVO;
import com.soft.webadmin.vo.shifts.ShiftsRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 排班记录控制器类
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Api(tags = "排班or调班记录接口")
@RestController
@RequestMapping("/shifts/record")
public class ShiftsRecordController {

    @Autowired
    private ShiftsRecordService shiftsRecordService;

    @ApiOperation(value = "调班记录")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<ShiftsRecordVO>> list(@Validated ShiftsRecordQueryDTO queryDTO) {
        return ResponseResult.success(shiftsRecordService.getPage(queryDTO));
    }

    @ApiOperation(value = "责任区域")
    @GetMapping("/liabilityArea")
    public ResponseResult<List<String>> liabilityArea(@Validated ShiftsLiabilityAreaDTO areaDTO) {
        return ResponseResult.success(shiftsRecordService.liabilityArea(areaDTO));
    }

    @ApiOperation(value = "排班")
    @GetMapping("/attendance")
    public ResponseResult<List<ShiftsAttendanceRecordVO>> attendance(@Validated ShiftsLiabilityAreaDTO areaDTO) {
        return ResponseResult.success(shiftsRecordService.attendance(areaDTO));
    }

    @ApiOperation(value = "添加人员")
    @GetMapping("/attendanceAdd")
    public ResponseResult<Void> attendanceAdd(@Validated ShiftsAttendanceSaveDTO saveDTO) {
        return shiftsRecordService.attendanceAdd(saveDTO);
    }

    @ApiOperation(value = "删除人员")
    @GetMapping("/attendanceDel")
    public ResponseResult<Void> attendanceDel(@Validated ShiftsAttendanceSaveDTO saveDTO) {
        return shiftsRecordService.attendanceDel(saveDTO);
    }
    @ApiOperation(value = "导入模板")
    @GetMapping("/excelTemplate")
    public ResponseResult<Void> excelTemplate(String businessType,String cycle) {
        return shiftsRecordService.excelTemplate(businessType,cycle);
    }

    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public ResponseResult<Void> importExcel(@RequestParam MultipartFile file,
                                            @RequestParam String businessType) {
        return shiftsRecordService.importExcel(businessType,file);
    }

}
