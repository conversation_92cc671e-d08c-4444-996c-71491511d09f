<template>
  <div :class="state.equipClass">
    <el-input
      v-if="state.blur"
      v-model="curComponent.propValue"
      autosize
      type="textarea"
      @blur="inputFocus(false)"
      @keyup.delete.stop
      ref="inputELe"
    />
    <span v-else @dblclick="inputFocus(true)" class="preText">{{ type === 'exhibits' ?  state.text  + ' ' + (element.unit ||  '')  :  element.propValue + (element.unit ||  '') }}</span>
  </div>
</template>

<script setup>
import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent } = storeToRefs(webtopo)

const props = defineProps({
  element:{
    type : Object,
    default: () => {
      return {}
    }
  },
  type:{
    type:String,
    default: ""
  }
})

let {element, type} = toRefs(props)

let inputELe = ref()

const state = reactive({
  blur: false,
  text:'',
  equipClass: {}
})

const inputFocus = (bool) => {
  if(type.value != 'edit') return true
  state.blur = bool
  nextTick(() => {
    if(bool)
    inputELe.value.focus()
  })
}

// 设备样式设定
const classEquip = (info) => {
  if (type.value !== 'exhibits') return false
  state.equipClass = {}
  state.text = element.value.propValue
  // 隐藏 值回显
  let { classInfo } = element.value.params;
  (classInfo.classList || []).forEach(item => {
    ; if ((classInfo.checkList || []).includes(item.classType) && item.attribute) {
      const equipmentAttribute = info.equipmentAttributeList.find(i => i.attributeKey === item.attribute) || {};
      const value = equipmentAttribute.attributeValue;

      if (item.classType === 'topoTextShow') {
        if(equipmentAttribute?.attributeValueEnum){
          if(typeof equipmentAttribute.attributeValueEnum === 'string'){
            equipmentAttribute.attributeValueEnum = JSON.parse(equipmentAttribute.attributeValueEnum || '{}')
          }
          state.text = equipmentAttribute.attributeValueEnum[value]
        }else{
          state.text = value
        }
      } else {
        state.equipClass[item.classType] = item.list.some(i => value >= i.min && value <= i.max)
      }
    }
  })
}

// 设备信息
watch(() => element.value.params.equipInfo, (newVal) => {
  classEquip(newVal)
}, { immediate: true })
</script>

<style lang='less' scoped>
.preText{
  display: inline-block;
  width: 100%;
  word-wrap : break-word
}

.toposhowHide {
  display: none;
}

.el-textarea{
  :deep(.el-textarea__inner){
    min-height: 18px!important;
    height: 100%!important;
  }
}
</style>
