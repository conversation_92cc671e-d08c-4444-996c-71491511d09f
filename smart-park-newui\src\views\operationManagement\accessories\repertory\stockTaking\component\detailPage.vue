<template>
  <div style="height: 100%;">
    <el-card class="box-card card-textBg">
      <template #header>
        <el-row justify="space-between" align="middle">
          <strong>详情</strong>
          <el-button type="primary" icon="Back" @click="showPage">返回</el-button>
        </el-row>
      </template>
      <el-form ref="ruleFormRef" label-position="top">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">基本信息</div>
        </div>
        <div class="detail-area">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item label="盘点单号">
                {{ data.stocktakingNo }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="盘点单名称">
                {{ data.stocktakingName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="盘点开始时间">
                {{ data.beginTime }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="盘点结束时间">
                {{ data.endTime }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="盘点仓库">
                {{ data.storehouseName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="备件类别">
                {{ data.classifyNames }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="负责人">
                {{ data.headUserName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="盘点规则">
                {{ state.ruleOption[data.rule] }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="盘点结果">
                <el-tag :type="state.resultTypeOptions[data.result]">{{ state.resultOptions[data.result] }}</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注">
                {{ data.remark }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="operator">
          <el-radio-group v-model="state.radio">
            <el-radio-button value="inventory">
              盘点明细
            </el-radio-button>
            <el-radio-button value="putIn">
              入库记录
            </el-radio-button>
            <el-radio-button value="outIn">
              出库记录
            </el-radio-button>
          </el-radio-group>
        </div>

        <div style="margin: 20px 0;text-align: right">
          <el-button type="primary" icon="BottomRight" @click="handlePutIn">入库</el-button>
          <el-button type="primary" icon="TopRight" @click="handleOutIn">出库</el-button>
        </div>

        <!--        盘点明细-->
        <div v-show="state.radio == 'inventory'">
          <el-table :data="data.detailList">
            <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                             :label="item.label"
                             :align="item.align" :formatter="item.formatter"/>
            <el-table-column align="center" label="操作" width="150">
              <template #default="{row}">
                <el-button link type="primary" icon="Tickets" @click="handlePicture(row)" v-if="row.imgs">查看图片
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!--        入库记录-->
        <div v-show="state.radio == 'putIn'">
          <el-table :data="data.putInList" >
            <el-table-column v-for="(item, index) in state.tableHeaderP" :key="index" :prop="item.prop"
                             :label="item.label"
                             :align="item.align" :formatter="item.formatter"/>
          </el-table>
        </div>

        <!--        出库记录-->
        <div v-show="state.radio == 'outIn'">
          <el-table :data="data.outList" >
            <el-table-column v-for="(item, index) in state.tableHeaderO" :key="index" :prop="item.prop"
                             :label="item.label"
                             :align="item.align" :formatter="item.formatter"/>
          </el-table>
        </div>
      </el-form>

    </el-card>

    <el-image-Viewer :url-list="state.srcList" hide-on-click-modal v-if="state.imageVisible"
                     @close="state.imageVisible = false">
    </el-image-Viewer>
  </div>
</template>

<script setup>
import {ElInput, ElMessage, ElMessageBox, ElTag} from "element-plus";

import {stockDeleteAPI, stockDetailAPI, stockIntoAPI, stockOutAPI} from '@/api/operationManagement/stockTaking.js'
import {calcPageNo} from "@/utils/util.js";

const emit = defineEmits(['showPage'])

const ruleFormRef = ref();
const dialog = ref();
const data = ref({})

const state = reactive({
  srcList: [],
  imageVisible: false,
  ruleOption: {
    1: '不需要拍照',
    2: '需要拍照'
  },
  resultOptions: {
    0: '草稿',
    1: '无盈亏',
    2: '有盈亏'
  },
  resultTypeOptions: {
    0: 'info',
    1: 'warning',
    2: 'primary',
  },
  resultOption: {
    1: '无盈亏',
    2: '盘盈',
    3: '盘亏'
  },
  resultTypeOption: {
    1: 'primary',
    2: 'success',
    3: 'danger'
  },
  radio: 'inventory',
  tableHeight: 'calc(100vh - 540px)',
  tableHeader: [
    {
      prop: 'sparePartVO.sparePartNo',
      label: '备件编号'
    },
    {
      prop: 'sparePartVO.sparePartName',
      label: '备件名称'
    },
    {
      prop: 'sparePartVO.classifyName',
      label: '备件分类'
    },
    {
      prop: 'sparePartVO.model',
      label: '规格型号'
    },
    {
      prop: 'sparePartVO.unit',
      label: '单位'
    }, {
      prop: 'sparePartVO.unitPrice',
      label: '单价（元）'
    },
    {
      prop: 'storehouseName',
      label: '所在仓库'
    },
    {
      prop: 'inventoryQuantity',
      label: '库存数量'
    },
    {
      prop: 'stocktakingQuantity',
      label: '盘点数量'
    },
    {
      prop: 'resultQuantity',
      label: '盈亏数量',
      formatter: (row, column, cellValue) => {
        if (row.resultQuantity > 0) {
          return h('span', {style: {color: '#87cf63'}}, "+" + row.resultQuantity)
        } else if (row.resultQuantity == 0) {
          return h('span', {style: {color: '#4ea5ff'}}, row.resultQuantity)
        } else if (row.resultQuantity < 0) {
          return h('span', {style: {color: '#f78686'}}, row.resultQuantity)
        }
      }
    },
    {
      prop: 'result',
      label: '盘点结果',
      formatter: (row, column, cellValue) => {
        return cellValue && h(ElTag, {type: state.resultTypeOption[cellValue]}, {default: () => state.resultOption[cellValue]})
      }
    },
    {
      prop: 'remark',
      label: '备注'
    },
  ],
  tableHeaderP: [
    {
      prop: 'inoutNo',
      label: '入库单号'
    },
    {
      prop: 'sparePartNames',
      label: '入库备件'
    },
    {
      prop: 'createUserName',
      label: '创建人'
    },
    {
      prop: 'createTime',
      label: '创建时间'
    },
  ],
  tableHeaderO: [
    {
      prop: 'inoutNo',
      label: '出库单号'
    },
    {
      prop: 'sparePartNames',
      label: '出库备件'
    },
    {
      prop: 'createUserName',
      label: '创建人'
    },
    {
      prop: 'createTime',
      label: '创建时间'
    },
  ],
});


const loadPage = (id) => {
  state.id = id
  getDetail()
}

// 获取详情
const getDetail = () => {
  stockDetailAPI({id: state.id}).then(res => {
    console.log(res)
    data.value = res.data
  })
}

// 返回
const showPage = () => {
  data.value = {}
  state.radio = 'inventory'
  emit('showPage', 0)
}

// 查看图片
const handlePicture = (row) => {
  if (row.imgs) {
    state.srcList = row.imgs.split(',').map(item => import.meta.env.VITE_BASE_URL+ item)
    state.imageVisible = true
  }
}

// 入库
const handlePutIn = () => {
  ElMessageBox.confirm(
      '确定将盘盈备件入库吗?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    stockIntoAPI({id: state.id}).then(res => {
      if (res.success) {
        getDetail()
        ElMessage.success('入库成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 出库
const handleOutIn = () => {
  ElMessageBox.confirm(
      '确定将盘亏备件出库吗？',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    stockOutAPI({id: state.id}).then(res => {
      if (res.success) {
        getDetail()
        ElMessage.success('出库成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}


defineExpose({
  getDetail,
  loadPage
})
</script>

<style scoped lang="less">

.operator {
  display: flex;
  justify-content: center;
}
</style>
