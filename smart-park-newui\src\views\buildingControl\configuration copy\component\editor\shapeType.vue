<template>
  <DraggableContainer :adsorbParent="true" referenceLineColor="#606266">
    <vue3-draggable-resizable tabindex="0" v-for="(item, index) in componentData" :key="item.id" :x="item.style.left"
      :y="item.style.top" :w="item.style.width" :h="item.style.height"  :minH="4"
      :is-conflict-check="true"
      :active="item == curComponent"
      @drag-start="dragMoveHandle(false)"
      @drag-end="dragMoveHandle(true)"
      @dragging = "(info) => draggingHandle(info,item.style)"
      @resizing="(info) => resizingHandle(info,item.style)"
      @mousedown="handleMouseDownOnShape($event, item, index)"
      @keyup.delete="removeHandle"
      @keydown.ctrl.c="copyHandle"
      @keydown.ctrl.s.stop="saveHandle"
      @keydown.ctrl.v.stop="pasteHandle"
      @keydown.left="moveDirHandle($event,'left')"
      @keydown.right="moveDirHandle($event,'right')"
      @keydown.up="moveDirHandle($event,'up')"
      @keydown.down="moveDirHandle($event,'down')"
                              @mousewheel.stop="() => {}"
    >
      <component :is="item.component" class="component" :element="item" type="edit" :style="getComponentStyle(item.style)" />
    </vue3-draggable-resizable>
  </DraggableContainer>
</template>

<script setup>
import Vue3DraggableResizable, {DraggableContainer} from 'vue3-draggable-resizable'

import {ElMessage} from 'element-plus'

import {getStyle} from '@/utils/webtopo/style.js'

import {webtopoStore} from '@/store/modules/webtopo.js'
import {computed} from 'vue'

const webtopo = webtopoStore()

let { componentData, curComponent, copyCurComponent, isClickComponent } = storeToRefs(webtopo)

const emit = defineEmits(['dragMove','saveHandle','pasteHandle'])

// 组件选中样式
const activeClass = computed(() => {
  return (item) => {
    return {
      active: curComponent?.value == item
    }
  }
})

// 防止画布移动
const dragMoveHandle = (bool) => {
  emit('dragMove', bool)
}

// 设置当前组件
const handleMouseDownOnShape = (e, info, index) => {
  e.stopPropagation()
  isClickComponent.value = true
  curComponent.value = info
  curComponent.value.index = index
}

// 拖拽
const draggingHandle = (info,style) => {
  style.left = info.x
  style.top = info.y
}

// 组件大小改变
const resizingHandle = (info,style) => {
  style.width = info.w
  style.height = info.h
  style.left = info.x
  style.top = info.y
}

// 当前组件样式
const getComponentStyle = (style) => {
  return getStyle(style, ['width', 'height', 'top', 'left'])
}

// 删除
const removeHandle = (e) => {
  if (curComponent.value) {
    componentData.value.splice(curComponent.value.index, 1)
    curComponent.value = null
  }
}

// 复制
const copyHandle = () => {
  if (curComponent.value) {
    copyCurComponent.value = curComponent.value
    ElMessage.success('复制成功')
  }
}

// 粘贴
const pasteHandle = () => {
  emit('pasteHandle')
}

// 保存
const saveHandle = (event) => {
  emit('saveHandle',event)
}

// 移动物体
const moveDirHandle = (event,arrow) => {
  event.preventDefault()
  if (curComponent.value) {
    switch (arrow) {
      case 'left':
        curComponent.value.style.left -= 1
        break;
      case 'right':
        curComponent.value.style.left += 1
        break;
      case 'up':
        curComponent.value.style.top -= 1
        break;
      case 'down':
        curComponent.value.style.top += 1
        break;

      default:
        break;
    }
  }
}
</script>

<style lang='less' scoped>
.component {
  width: 100%;
  height: 100%;
  display: flex;
}

.vdr-container {
  outline: none;
}

.active{
  :deep(.vdr-handle){
    display: block!important;;
  }
}
</style>
