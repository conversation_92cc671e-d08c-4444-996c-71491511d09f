<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :width="900" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="100px" :model="form" :rules="state.rules" label-suffix=":">
      <el-row>
        <el-col :span="12">
          <el-form-item label="资源名称" prop="menuName">
            <el-input v-model="form.menuName" placeholder="请输入资源名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="菜单类型" prop="menuType">
            <el-select v-model="form.menuType" placeholder="请选择菜单类型">
              <el-option v-for="item in menuType" :key="item.itemId" :label="item.name" :value="Number(item.itemId)" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="菜单顺序" prop="showOrder">
            <el-input v-model="form.showOrder" type="number" placeholder="请输入菜单顺序" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级菜单" prop="parentId">
            <el-tree-select v-model="form.parentId" :data="tableData" check-strictly :render-after-expand="false"
              node-key="menuId" :props="treeProps" placeholder="请选择上级菜单" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="组件路径" prop="path">
            <el-input v-model="form.path" placeholder="请输入组件路径" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显示" prop="isHidden">
            <el-switch v-model="form.isHidden" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="权限标识" prop="perms">
            <el-input v-model="form.perms" placeholder="请输入权限标识" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图标" prop="icon">
            <el-input v-model="form.icon" placeholder="请输入图标" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { ElMessage } from 'element-plus'

import { menuAddAPI, menuEidtAPI } from '@/api/settingSystem/menu.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  tableData: {
    type: Array,
    default() {
      return []
    }
  },
  menuType: {
    type: Array,
    default() {
      return []
    }
  }
})

let { tableData, menuType, title } = toRefs(props)

const emit = defineEmits(['submit'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({
  menuId: '',
  isHidden: true
})

const state = reactive({
  rules: {
    menuName: [{ required: true, message: '请输入菜单名称', trigger: 'blur' },],
    menuType: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
    showOrder: [{ required: true, message: '请输入菜单顺序', trigger: 'blur' }]
  },
  tableData: [],
  menuType: []
})

const treeProps = computed(() => {
  return {
    label: 'menuName'
  }
})

const open = () => {
  dialog.value.open()
}

// 提交菜单
const submit = () => {
  let data = {
    sysMenuDto: form
  }

  if (data.sysMenuDto.menuId) {
    subHandle(menuEidtAPI, '编辑成功')
  } else {
    subHandle(menuAddAPI, '添加成功')
  }

  function subHandle(req, title) {
    req(data).then(res => {
      if (res.success) {
        ElMessage.success(title)
        dialog.value.close()
        emit('submit')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  }
}


defineExpose({
  form,
  open
})
</script>

<style lang='less' scoped></style>
