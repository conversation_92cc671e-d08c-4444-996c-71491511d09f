package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * CheckRecordPointItemDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckRecordPointItemDTO对象")
@Data
public class CheckRecordPointItemDTO {

    @ApiModelProperty(value = "检查项id")
    @NotNull(message = "检查项id不能为空！")
    private Long id;

    @ApiModelProperty(value = "检查结果：1正常，2异常")
    private Integer itemResult;

    @ApiModelProperty(value = "检查结果数值")
    private String itemResultVal;

    @ApiModelProperty(value = "备注")
    private String remark;

}
