import { request } from '@/utils/request';

// 分页查询
export const getRepairPageAPI = (data) => {
  return request('get', '/check/order/getReportPage', data, 'F');
};

// 保存
export const submitRepairAPI = (data) => {
  return request('post', '/check/repair/save', data);
};

// 运送报单
export const submitTransportAPI = (data) => {
  return request('post', '/transport/record/createOrder', data);
};

//获取运送场景
export const sencePageAPI = (params) => {
  return request('get', '/transport/scene/list', params, 'F');
}

//获取单位
export const unitPageAPI = (params) => {
  return request('get', '/transport/unit/list', params, 'F');
}

// 获取运送工具
export const transportationPageAPI = (params) => {
  return request('get', '/transport/transportation/list', params, 'F');
}
