<template>
  <div class="toolMenu" @keydown.ctrl.67="copyHandle">
    <el-input v-model="state.search" placeholder="搜索图形" @keyup.enter="searchIcon">
      <template #suffix>
        <el-icon class="el-input__icon" style="cursor: pointer;" @click="searchIcon">
          <search />
        </el-icon>
      </template>
    </el-input>
    <div class="collapse">
      <el-collapse v-model="state.activeName" accordion>
        <template v-for="(item, index) in componetJosn" :key="index">
          <el-collapse-item :title="item.title" :name="item.name">
            <template v-for="(i, iIndex) in item.list" :key="iIndex">
              <el-tooltip placement="top-start" :content="i.label">
                <span class="iconCom" draggable="true" @dragstart="handleDragStart($event, i)">
                  <!-- 阿里图标 -->
                  <i v-if="i.icon" :class="['iconfont', i.icon]"></i>
                  <!-- 本地图片 -->
                  <img v-else-if="i.image" :src="imgTransfer(i.image)">
                </span>
              </el-tooltip>
            </template>
          </el-collapse-item>
        </template>
      </el-collapse>
    </div>
  </div>
</template>

<script setup>
import componetJosn from '../dataJson/index.js'

const state = reactive({
  search: '',
  activeName: ''
})

const imgTransfer = (name) => {
  return new URL(`/src/assets/webtopoImg/${name}`, import.meta.url).href
}

// 鼠标拖拽
const handleDragStart = (e, info) => {
  e.dataTransfer.setData('info', JSON.stringify(info))
}

// 搜索图标
const searchIcon = () => {
  if(!state.search) return false

  componetJosn.some(item => {
    let bool = item.list.some(i => {
      if(i.label.includes(state.search)){
        state.activeName = item.name
        return true
      }
    })
    return bool
  })
}
</script>

<style lang='less' scoped>
.toolMenu {
  height: 100%;
  padding: 10px 5px;

  .collapse {
    margin-top: 8px;
    height: calc(100% - 40px);
    overflow: auto;
  }
}

:deep(.el-collapse-item__content) {
  display: grid;
  grid-gap: 10px 5px;
  grid-template-rows: repeat(auto-fill, 40px);
  grid-template-columns: repeat(auto-fill, 40px);

  .iconCom {
    width: 40px;
    height: 40px;
    display: flex;
    cursor: grab;
    justify-content: center;
    align-items: center;

    &:active {
      cursor: grabbing;
    }

    img {
      width: 35px;
      height: 35px;
    }
  }
}</style>
