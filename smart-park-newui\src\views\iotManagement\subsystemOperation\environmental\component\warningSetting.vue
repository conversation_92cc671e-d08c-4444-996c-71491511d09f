<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form ref="queryFormRef" :model="state.queryForm" inline>
        <el-form-item prop="name">
          <el-input v-model="state.queryForm.name" placeholder="告警名称"/>
        </el-form-item>
        <el-form-item prop="itemName">
          <el-select
              v-model="state.queryForm.itemName"
              clearable
              placeholder="告警项"
          >
            <el-option
                v-for="item in state.warningItemOptions"
                :key="item.name"
                :label="item.name"
                :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="level">
          <el-select
              v-model="state.queryForm.level"
              clearable
              placeholder="告警等级"
          >
            <el-option
                v-for="item in state.levelOptions"
                :key="item"
                :label="item"
                :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onQuery">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #operate>
      <el-button type="primary" icon="Plus" @click="onAdd">新建</el-button>
    </template>

    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter">
          <template #default="scope">
            <div v-if="item.prop === 'equipments'">
              <span>{{ scope.row.relationEquipmentList.map(equipmentRelation => equipmentRelation.equipmentName).join(',') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="onUpdate(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="onDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize"
                     :total="state.pageParam.total"
                     @size-change="sizeChange" @current-change="currentChange"/>


      <warning-setting-dialog ref="warningSettingDialogRef" :equipment-type="equipmentType" :job="job" @onClose="onQuery"/>
    </template>
  </page-common>
</template>

<script setup>

import {deleteWarningSettingAPI, getPageAPI, getWarningItemsAPI} from "@/api/iotManagement/warningSetting.js";
import WarningSettingDialog from "@/views/iotManagement/subsystemOperation/environmental/component/warningSettingDialog.vue";
import {ElMessage, ElMessageBox} from "element-plus";
import { calcPageNo } from '@/utils/util.js'

const queryFormRef = ref()

let props = defineProps(['subType', 'equipmentType', 'job'])
const { subType, equipmentType, job} = toRefs(props)

const warningSettingDialogRef = ref()

const state = reactive({
  tableHeight: 100,
  queryForm: {},
  warningItemOptions: [],
  levelOptions: [
    '普通', '紧急', '特急'
  ],
  tableHeader: [
    {label: '告警名称', prop: 'name'},
    {label: '告警项', prop: 'itemName'},
    {label: '单位', prop: 'unit'},
    {label: '上限值', prop: 'maxValue'},
    {label: '下限值', prop: 'minValue'},
    {label: '告警等级', prop: 'level'},
    {label: '关联设备', prop: 'equipments'}
  ],
  tableData: [],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

const loadWarningItemOptions = () => {
  getWarningItemsAPI({warningSettingType: equipmentType.value.type}).then(res => {
    if (res.success) {
      state.warningItemOptions = res.data
    }
  })
}


// 页数改变
const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum
  onQuery()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize
  onQuery()
}


const onQuery = () => {
  let param = {
    name: state.queryForm.name,
    itemName: state.queryForm.itemName,
    equipmentType: equipmentType.value.name,
    level: state.queryForm.level,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  getPageAPI(param).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList
      state.pageParam.total = res.data.totalCount
    }
  })
}

const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  onQuery()
}

const onAdd = () => {
  warningSettingDialogRef.value.open('编辑')
}

const onUpdate = (val) => {
  warningSettingDialogRef.value.open('编辑', JSON.parse(JSON.stringify(val)))
}

const onDelete = (val) => {
  ElMessageBox.confirm(
      '是否删除当前配置？',
      '提醒',
      {
        type: 'warning',
      }
  ).then(() => {
    state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize)

    deleteWarningSettingAPI({id: val.id}).then(res => {
      if (res.success) {
        ElMessage.success('删除成功！')
        onQuery()
      } else {
        ElMessage.warning("删除失败！")
      }
    })
  })
}

// 初始化方法
const init = () => {
  loadWarningItemOptions()
  onQuery()
}

defineExpose({
  init
})
</script>

<style scoped lang="less">

</style>
