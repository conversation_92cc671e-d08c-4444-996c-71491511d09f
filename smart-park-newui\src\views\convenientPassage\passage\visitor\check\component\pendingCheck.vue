<template>
    <page-common v-model="state.tableHeight">
        <template #query>
            <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
                <el-form-item prop="queryWord">
                    <el-input placeholder="访客姓名/车牌号" v-model="formInline.queryWord" />
                </el-form-item>
                <el-form-item prop="applyDate">
                    <el-date-picker v-model="state.applyDateArr" type="daterange" value-format="YYYY-MM-DD"
                        start-placeholder="申请开始日期" end-placeholder="申请结束日期" :clearable="false" />
                </el-form-item>

                <el-form-item>
                    <el-date-picker v-model="state.date" type="daterange" value-format="YYYY-MM-DD"
                        start-placeholder="预计到访开始时间" end-placeholder="预计到访结束时间" :clearable="false" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
                    <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
            </el-form>
        </template>

        <template #operate>
            <el-button @click="handleBatch" type="primary" icon="Promotion">一键审批</el-button>
        </template>

        <template #table>
            <el-table ref="table" show-overflow-tooltip :data="state.tableData" :height="state.tableHeight">
                <el-table-column type="selection" width="55" />
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                    :label="item.label" :formatter="item.formatter" :width="item.width" />

                <el-table-column label="操作" align="center" width="230">
                    <template #default="scope">
                        <el-button link icon="Select" type="primary" @click="agree(scope.row)">通过</el-button>
                        <el-button link icon="Close" type="danger" @click="reject(scope.row)">驳回</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize"
                :total="state.pagetion.total" @size-change="sizeChange" @current-change="currentChange" />

            <modal ref="modalRef" @submit="getList"></modal>
        </template>
    </page-common>
</template>

<script setup>
import { visitlist, audit ,batchApproveAPI} from '@/api/convenientPassage/check.js'
import modal from './modal.vue'
import { ElMessage, ElMessageBox, ElTooltip } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'

import dayjs from 'dayjs'

const route = useRoute()
const modalRef = ref()
const table = ref()

const formInlineRef = ref();
const formInline = reactive({});

const state = reactive({
    date: [],
    applyDateArr:[],
    tableHeight: 100,
    tableData: [],
    tableHeader: [{
        prop: 'visitorName',
        label: '访客姓名',
        // 预计到访时间大于当前时间，并且差值小于2小时，显示警告图标
        formatter: (row, column, cellValue) => {
            return dayjs(row.planVisitTime).isAfter(dayjs()) && dayjs(row.planVisitTime).diff(dayjs(), 'minute') < 120 ?
                h('div', [h(ElTooltip, {
                    placement: 'top',
                    content: '即将到达访客预计到访时间，请及时审核！'
                }, () => h(Warning, { style: { width: '1.5em', height: '1.5em', color: 'red', verticalAlign: 'middle', marginRight: '5px' } })), cellValue])
                : cellValue
        }
    },
    {
        prop: 'visitorIdCard',
        label: '身份证号'
    },
    {
        prop: 'visitorPhone',
        label: '手机号'
    }, {
        prop: 'licenceNumber',
        label: '车牌号'
    }, {
        prop: 'planVisitTime',
        label: '预计到访时间',
        formatter: (row, column, cellValue) => {
            return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
        }
    }, {
        prop: 'visitReason',
        label: '到访事由'
    },
    {
        prop: 'applyTime',
        label: '申请时间'
    }],
    pagetion: {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
})

onMounted(() => {
    getList();
})

const getList = () => {
    const query = {
        ...route.query,
        ...state.pagetion,
        ...formInline,
        approveType: 1,
        applyDateStart: state.applyDateArr[0],
        applyDateEnd: state.applyDateArr[1],
        planVisitDateStart: state.date[0],
        planVisitDateEnd: state.date[1]
    }

    visitlist(query).then(res => {
        state.tableData = res.data.dataList
        state.pagetion.total = res.data.totalCount;
    })
}

const agree = (row) => {
    ElMessageBox.confirm("确定审核通过吗？审核通过后，申请人可凭借移动端二维码进入园区，二维码有效期为申请人预计到访时间后48小时之内", "提醒", {
        type: "warning",
    }).then(() => {
        // 当前房间有人入住，则无法删除
        audit({ approveStatus: 2, id: row.id }).then(res => {
            if (res.success) {
                getList()
                ElMessage.success('已通过')
            } else {
                ElMessage.error(res.errorMessage)
            }
        })
    });
}

// 批量通过
const handleBatch = () => {
    const ids = table.value.getSelectionRows().map(item => item.id)
    if (!ids.length) {
        return ElMessage.warning("请选择访客！");
    }

    ElMessageBox.confirm("您确定审批通过吗?", "提醒", {
        type: "warning",
    }).then(() => {
        batchApproveAPI({ ids }).then(res => {
            if (res.success) {
                ElMessage.success('批量审批成功');
                getList();
            } else {
                ElMessage.error(res.errorMessage);
            }
        })
    });
}

const reject = (row) => {
    modalRef.value.form.id = row.id
    modalRef.value.open()
}

const onSubmit = () => {
    state.pagetion = {
        pageNum: 1,
        pageSize: 10,
        total: 0
    };
    getList();
}

const onReset = () => {
    formInlineRef.value.resetFields();
    state.date = [];
    state.applyDateArr = [];
    onSubmit();
};

// 分页
const currentChange = (pageNum) => {
    state.pagetion.pageNum = pageNum;
    getList();
};

const sizeChange = (pageSize) => {
    state.pagetion.pageSize = pageSize;
    getList();
};

defineExpose({
    getList
})
</script>
