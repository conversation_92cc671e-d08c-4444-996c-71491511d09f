package com.soft.webadmin.dao.check;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.model.check.CheckPlanPoint;
import com.soft.webadmin.vo.check.CheckPlanPointItemVO;
import com.soft.webadmin.vo.check.CheckPlanPointVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查计划点位Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-12
 */
public interface CheckPlanPointMapper extends BaseMapper<CheckPlanPoint> {

    List<CheckPlanPointVO> queryListByPlanIdList(@Param("planIdList") List<Long> planIdList);

    List<CheckPlanPointItemVO> queryItemByIdList(@Param("pointIdList") List<Long> pointIdList);

}
