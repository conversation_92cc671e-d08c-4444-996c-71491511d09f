<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.sparePart.SparePartStorehouseMapper">
    <resultMap type="com.soft.webadmin.model.sparePart.SparePartStorehouse" id="SparePartStorehouseResult">
        <result property="id" column="id" />
        <result property="businessType" column="business_type" />
        <result property="storehouseName" column="storehouse_name" />
        <result property="storehouseNo" column="storehouse_no" />
        <result property="capacity" column="capacity" />
        <result property="headUserId" column="head_user_id" />
        <result property="spaceId" column="space_id" />
        <result property="spacePath" column="space_path" />
        <result property="spaceFullName" column="space_full_name" />
        <result property="state" column="state" />
        <result property="remark" column="remark" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectSparePartStorehouseVo">
        select id, business_type, storehouse_name, storehouse_no, capacity, head_user_id, space_id, space_path, space_full_name, state, remark, deleted_flag, create_user_id, create_time, update_user_id, update_time from sp_spare_part_storehouse
    </sql>
    
</mapper>