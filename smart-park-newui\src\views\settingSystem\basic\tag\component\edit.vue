<template>
  <dialog-common ref="dialog" :formRef="ruleFormRef" :title="state.title" :width="450" @submit="saveOrUpdate">
    <el-form ref="ruleFormRef" :model="state.tagData" :rules="state.rules" label-suffix=":" label-width="100px">
      <el-input v-model="state.tagData.code" type="hidden"/>
      <el-input v-model="state.tagData.id" type="hidden"/>
      <el-input v-model="state.tagData.type" type="hidden"/>
      <el-form-item label="标签名称" prop="name">
        <el-input v-model="state.tagData.name" placeholder="请输入标签名称"/>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" v-model="state.tagData.remark" :maxlength="500" show-word-limit :autosize="{ minRows: 5}"
                placeholder="请输入备注" />
      </el-form-item>

    </el-form>
  </dialog-common>
</template>

<script setup>
import {addAPI, updateAPI} from '@/api/settingSystem/tag.js'
import {ElMessage} from "element-plus"


// 实例化 defineEmits，父组件
const emits = defineEmits(['closeClick'])

// 表单验证
const ruleFormRef = ref()
// 弹出框对象
const dialog = ref()


const state = reactive({
  // 空间类型
  spaceType: '',
  // 操作类型，CREATE、UPDATE
  operatorType: '',
  title: '新建标签',
  // 表单对象
  tagData: {},


  // 校验规则
  rules: {
    name: [
      {required: true, message: '标签名称不能为空', trigger: 'blur'}
    ]
  }
})

// 打开弹出框
const openDialog = (operatorType, tagType, tagData) => {
  // 打开弹窗，必须放在首位
  dialog.value.open()

  // 空间类型
  state.tagData.tagType = tagType
  // 操作类型，CREATE、UPDATE
  state.operatorType = operatorType

  // 新建标签
  if (operatorType === 'CREATE') {
    state.title = '新建标签'
    state.tagData.name = ''
    state.tagData.remark = ''
    state.tagData.code = ''
  } else { // 编辑
    state.title = '编辑标签'
    state.tagData.name = tagData.name
    state.tagData.id = tagData.id
    state.tagData.remark = tagData.remark
    state.tagData.code = tagData.code
  }
}

// 保存
const saveOrUpdate = () => {
  if (state.operatorType === 'CREATE') {
    let spaceSave = {
      type: state.tagData.tagType,
      name: state.tagData.name,
      remark: state.tagData.remark,
    }


    addAPI(spaceSave).then(res => {
      if (res.success) {
        ElMessage.success("保存成功！")
        // 关闭弹窗
        dialog.value.close();

        // 调用父组件定义的方法
        emits('closeClick')
      } else {
        ElMessage.error("保存失败！" + res.errorMessage)
      }
    }).catch(e => {
      ElMessage.error("发生异常，保存失败！")
    })
  } else if (state.operatorType === 'UPDATE') {
    let spaceUpdate = {
      id: state.tagData.id,
      name: state.tagData.name,
      code: state.tagData.code,
      type: state.tagData.tagType,
      remark: state.tagData.remark
    }

    updateAPI(spaceUpdate).then(res => {
      if (res.success) {
        ElMessage.success("保存成功！")
        // 关闭弹窗
        dialog.value.close();

        // 调用父组件定义的方法
        emits('closeClick')
      } else {
        ElMessage.error("保存失败！" + res.errorMessage)
      }
    }).catch(e => {
      ElMessage.error("发生异常，保存失败！")
    })
  }
}

// 向父组件暴露属性或方法
defineExpose({
  state,
  openDialog
});
</script>

<style lang='less' scoped>

</style>
