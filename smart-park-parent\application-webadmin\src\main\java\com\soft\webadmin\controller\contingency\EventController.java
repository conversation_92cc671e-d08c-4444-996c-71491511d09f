package com.soft.webadmin.controller.contingency;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.contingency.EventDTO;
import com.soft.webadmin.dto.contingency.EventQueryDTO;
import com.soft.webadmin.service.contingency.EventService;
import com.soft.webadmin.vo.contingency.EventVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 应急事件控制器类
 * 
 * <AUTHOR>
 * @date 2024-04-17
 */
@Api(tags = "应急事件")
@RestController
@RequestMapping("/contingency/event")
public class EventController {

    @Autowired
    private EventService eventService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<EventVO>> getPage(EventQueryDTO queryDTO) {
        return ResponseResult.success(eventService.list(queryDTO));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@Validated @RequestBody EventDTO saveDTO) {
        eventService.saveOrUpdate(saveDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        eventService.delete(id);
        return ResponseResult.success();
    }

}
