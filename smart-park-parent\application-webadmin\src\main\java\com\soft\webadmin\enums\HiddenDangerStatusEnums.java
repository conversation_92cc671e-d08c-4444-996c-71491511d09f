package com.soft.webadmin.enums;

/**
 * 隐患状态
 */
public enum HiddenDangerStatusEnums {

    NO_INSPECTION(1, "未查验"),

    HANDING(2, "处理中"),

    NO_EXAMINE(3, "未审核"),

    FINISH(4, "已完成"),
    ;

    private Integer value;

    private String name;

    public Integer getValue() {
        return value;
    }

    HiddenDangerStatusEnums(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
