
import axios from "axios";
import JSONbig from 'json-bigint';
import router from '@/router'
import { ElMessage } from 'element-plus'
import qs from 'qs'

const JSONbigString = new JSONbig({ storeAsString: true });

const api = axios.create({  //返回promise实例
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 20000,
  transformResponse: [
    function (data) {
      if (typeof data === 'string') {
        return JSONbigString.parse(data);
      } else {
        return data;
      }
    }
  ]
})

// 拦截器
api.interceptors.request.use(config => {
  config.headers['Authorization'] = localStorage.getItem('Authorization')
  config.headers['projectId'] = localStorage.getItem('projectId')

  config.paramsSerializer = {
    serialize: function (params) {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    }
  }
  return config
}, err => {
  return Promise.error(err)
})

api.interceptors.response.use(response => {
  if (response.status == 200) {
    return Promise.resolve(response.data)
  }
}, err => {
  if (err.response.status == 401) {
    if (router.currentRoute.value.path != '/login')
      router.push('/login')
    ElMessage.error(err.response.data.errorMessage)
  }
  if(err.response.status == 500) {
    ElMessage.error(err.response.data.errorMessage)
  }
  return Promise.reject(err)
})

export const request = (method, url, data, Validate = 'T') => {
  return api({
    method,
    url,
    params: Validate != 'T' ? data : '',
    data: Validate == 'T' ? data : '',
  })
}



