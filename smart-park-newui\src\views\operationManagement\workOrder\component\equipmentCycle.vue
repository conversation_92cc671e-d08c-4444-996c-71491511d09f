<template>
  <div>
    <el-form label-position="right" label-width="110px" label-suffix=":">
      <el-timeline class="timeline-box-card">
        <el-timeline-item
            v-for="item in props.data"
            :timestamp="item.createTime"
            placement="top"
            type="primary"
            hollow>
          <el-card class="life-circle">
            <div class="top">
              <h4>{{ state.cycleTypeOptions[item.cycleType] }}</h4>
            </div>
            <div v-if="item.cycleType === 0">
              <!--设备报废-->
              <div class="item">报废原因：
                <span class="item-inline-content">
                  {{ JSON.parse(item.busiData).scrapReason }}
                </span>
              </div>
              <div class="item">报废日期：
                <span class="item-inline-content">{{ JSON.parse(item.busiData).scrapDate }}</span>
              </div>
            </div>
            <div v-if="item.cycleType === 2">
              <!--设备故障-->
              <div class="item">故障描述：
                <span class="item-inline-content">
                  {{ JSON.parse(item.busiData).content }}
                </span>
              </div>
              <div class="item">工单编号：
                <el-link type="primary" @click="handleView(item.busiData)">{{ JSON.parse(item.busiData).orderNo }}</el-link>
              </div>
            </div>
            <div v-if="item.cycleType === 3">
              <!--设备修复-->
              <div class="item">过程描述：
                <span class="item-inline-content">
                  {{ JSON.parse(item.busiData).describe }}
                </span>
              </div>
              <div class="item">工单编号：
                <el-link type="primary" @click="handleView(item.busiData)"  >{{ JSON.parse(item.busiData).orderNo }}</el-link>
              </div>
            </div>
            <div v-if="item.cycleType === 4">
              <!--设备保养-->
              <div class="item">保养计划：
                <span class="item-inline-content">{{ JSON.parse(item.busiData).planName }}</span>
              </div>
              <div class="item">工单编号：
                <el-link type="primary" @click="handleMaintainView(item.busiData,item.equipmentId)">{{ JSON.parse(item.busiData).orderNo }}</el-link>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-form>
<!--    维修-->
    <maintain-detail ref="maintain"></maintain-detail>
<!--    维保-->
    <point-item ref="point"/>
  </div>
</template>

<script setup>
import JSONbig from 'json-bigint';

import maintainDetail from './maintainDetail.vue'
import pointItem from '../order/component/modal/pointItem.vue'

import {getWorkOrderDetailAPI} from '@/api/operationManagement/workOrder.js';
import {pictureVideo} from "@/utils/util.js";

const JSONbigString = new JSONbig({storeAsString: true});

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const maintain = ref()
const point = ref()

const state = reactive({
  cycleTypeOptions: {
    0: '报废',
    1: '新增设备',
    2: '设备故障',
    3: '设备修复',
    4: '设备保养',
  },
})

// 查看维修详情
const handleView = (busiData) => {
  busiData = JSONbigString.parse(busiData || "{}")
  maintain.value.open(busiData.orderId)
}

// 查看保养详情
const handleMaintainView = (busiData,equipmentId) => {
  busiData = JSONbigString.parse(busiData || "{}")

  getWorkOrderDetailAPI({id: busiData.orderId}).then((res) => {
    let row = res.data.checkRecordVO.pointVOList.find(item => item.dataId == equipmentId)
    point.value.open();
    nextTick(() => {
      point.value.form.itemList = row.itemVOList;
      point.value.form.pointId = row.id;
      point.value.form.remark = row.remark
      point.value.form.fileList = pictureVideo(row.img)
    });
  });
}
</script>

<style scoped lang="less">

</style>
