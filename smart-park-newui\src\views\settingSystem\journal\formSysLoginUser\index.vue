<template>
    <page-common v-model="state.tableHeight">
        <template #query>
            <el-form :inline="true" ref="formInlineRef" label-suffix=":" :model="formInline">
                <el-form-item  prop="loginName">
                    <el-input v-model="formInline.loginName"  placeholder="登录名称" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
                    <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template #table>
            <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
                <el-table-column type="index" width="60" label="序号">
                </el-table-column>
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                    :label="item.label" :align="item.align" :formatter="item.formatter" :width="item.width" />
                <el-table-column align="center" label="操作" width="80">
                    <template #default="scope">
                        <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">强退</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
                @size-change="sizeChange" @current-change="currentChange" />
        </template>
    </page-common>
</template>

<script setup>
import {
    Delete, Plus, Search, Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from "dayjs";

import { formSysLoginUserListAPI, deleteAPI } from '@/api/settingSystem/formSysLoginUser.js'
import { calcPageNo } from '@/utils/util.js'

let formInlineRef = ref()

const formInline = reactive({})

const state = reactive({
    roleId: '',
    //权限选中id
    defaultChecked: [],
    treeData: [],
    title: '',
    drawer: false,
    tableHeight: 100,
    tableData: [],
    tableHeader: [
        {
            prop: 'loginName',
            label: '登录名称'
        },
        {
            prop: 'showName',
            label: '用户昵称'
        },
        {
            prop: 'loginIp',
            label: '登录 IP'
        },
        {
            prop: 'loginTime',
            label: '登录时间',
            width: 160,
            formatter: (row, column, cellValue) => {
              return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
            }
        }
    ],
    pagetion: {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
})

onMounted(() => {
    getList()
})

//删除事件
const deleteHandle = (info) => {
    ElMessageBox.confirm("是否强退此用户?", "提醒", {
        type: "warning",
    }).then(() => {
        state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize)
        deleteAPI({ sessionId: info.sessionId }).then(res => {
            if (res.success) {
                getList()
                ElMessage.success('强退成功')
            } else {
                ElMessage.error(res.errorMessage)
            }
        })
    });
}

//分页
const getList = () => {
    let query = {
        loginName: formInline.loginName,
        pageParam: state.pagetion
    }
    formSysLoginUserListAPI(query).then(res => {
        state.tableData = res.data.dataList
        state.pagetion.total = res.data.totalCount * 1
    })
}

//查询方法
const onSubmit = () => {
    state.pagetion = {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
    getList()
}

//重置方法
const onReset = () => {
    formInlineRef.value.resetFields()
    onSubmit()
}

//分页方法
const currentChange = (pageNum) => {
    state.pagetion.pageNum = pageNum
    getList()
}

//分页方法
const sizeChange = (pageSize) => {
    state.pagetion.pageSize = pageSize
    getList()
}
</script>

<style lang='less' scoped></style>
