import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './style/index.less'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import store from './store'
import registerCom from './components'
import registerDir from './directives'
import './assets/font/iconfont.css'
// 引入echarts
import echarts from './components/echarts.js'
import { parseArrayByStr } from './utils/util.js'
const app = createApp(App)

// 挂载到vue实例中
app.config.globalProperties.$echarts = echarts;//vue3的挂载方式
app.config.globalProperties.parseArrayByStr = parseArrayByStr;

registerCom(app)
registerDir(app)

app.use(ElementPlus).use(router).use(store).mount('#app')
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
