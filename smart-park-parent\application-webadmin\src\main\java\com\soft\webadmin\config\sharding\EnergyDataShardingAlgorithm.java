package com.soft.webadmin.config.sharding;

import com.google.common.collect.Range;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.util.DateUtils;
import com.soft.webadmin.config.sharding.enums.ShardingTableCacheEnum;
import com.soft.webadmin.config.sharding.enums.ShardingTableTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 能耗数据表分表算法
 * @Date 0013, 2023年6月13日 9:15
 * <AUTHOR>
 **/
@Slf4j
public class EnergyDataShardingAlgorithm implements ComplexKeysShardingAlgorithm {

    /**
     * 表分片符号，"_"
     */
    private final String TABLE_SPLIT_SYMBOL = "_";

    @Override
    public void init() {

    }
    @Override
    public String getType() {
        return null;
    }

    @Override
    public Collection<String> doSharding(Collection tableNames, ComplexKeysShardingValue shardingValue) {
        String logicTableName = shardingValue.getLogicTableName();
        ShardingTableCacheEnum logicTable = ShardingTableCacheEnum.of(logicTableName);
        if (logicTable == null) {
            log.error(">>>>>>>>>> 【ERROR】数据表类型错误，请稍后重试，logicTableNames：{}，logicTableName:{}",
                    ShardingTableCacheEnum.logicTableNames(), logicTableName);
            throw new IllegalArgumentException("数据表类型错误，请稍后重试");
        }

        /// 打印分片信息
        log.info(">>>>>>>>>> 【INFO】精确分片，节点配置表名：{}，数据库缓存表名：{}", tableNames, logicTable.resultTableNamesCache());

        // 根据equipment_type分片
        Collection<String> equipmentTypes = (Collection<String>) shardingValue.getColumnNameAndShardingValuesMap().get("equipment_type");
        if(CollectionUtils.isEmpty(equipmentTypes)) {
            throw new ServiceException("能耗数据表分表，equipment_type必传");
        }
        String equipmentType = ShardingTableTypeEnum.getValueByName(equipmentTypes.stream().findFirst().get());

        if(StringUtils.isBlank(equipmentType)){
            log.error(">>>>>>>>>> 【ERROR】分片类型错误，请联系管理员，logicTableNames：{}，logicTableName:{}",
                    ShardingTableCacheEnum.logicTableNames(), logicTableName);
            throw new IllegalArgumentException("分片类型错误错误，请稍联系管理员");
        }

        String preciseTableName = logicTableName + TABLE_SPLIT_SYMBOL + equipmentType + TABLE_SPLIT_SYMBOL;

        // 根据record_date分片（=，in）
        Collection recordDates = (Collection) shardingValue.getColumnNameAndShardingValuesMap().get("record_date");
        if (CollectionUtils.isNotEmpty(recordDates)) {
            Set<String> resultTableNames = (Set<String>) recordDates.stream()
                    .map(DateUtils::getYear)
                    .distinct()
                    .map(recordYear -> preciseTableName + recordYear)
                    .collect(Collectors.toSet());
            return ShardingAlgorithmTool.getShardingTablesAndCreate(logicTable, resultTableNames);
        }
        // 根据record_date分片（between）
        Range recordDateRange = (Range) shardingValue.getColumnNameAndRangeValuesMap().get("record_date");
        if(Objects.nonNull(recordDateRange)) {
            Integer min = recordDateRange.hasLowerBound() ? DateUtils.getYear(recordDateRange.lowerEndpoint()) : getLowerEndpoint(preciseTableName, logicTable.resultTableNamesCache());
            Integer max = recordDateRange.hasUpperBound() ? DateUtils.getYear(recordDateRange.upperEndpoint()) : getUpperEndpoint(preciseTableName, logicTable.resultTableNamesCache());

            Set<String> resultTableNames = new LinkedHashSet<>();
            while (min < max || Objects.equals(min,max)) {
                String tableName = preciseTableName + min;
                resultTableNames.add(tableName);
                // 检查分表获取的表名是否存在，不存在则自动建表
                if (!tableNames.contains(tableName)){
                    tableNames.add(tableName);
                }
                min ++;
            }
            return ShardingAlgorithmTool.getShardingTablesAndCreate(logicTable, resultTableNames);
        }

        // 根据record_year分片（=，in）
        Collection<Integer> recordYears = (Collection<Integer>) shardingValue.getColumnNameAndShardingValuesMap().get("record_year");
        if (CollectionUtils.isNotEmpty(recordYears)) {
            Set<String> resultTableNames = recordYears.stream()
                    .distinct()
                    .map(recordYear -> preciseTableName + recordYear)
                    .collect(Collectors.toSet());
            return ShardingAlgorithmTool.getShardingTablesAndCreate(logicTable, resultTableNames);
        }
        // 根据record_year分片（between）
        Range<Integer> recordYearRange = (Range<Integer>) shardingValue.getColumnNameAndRangeValuesMap().get("record_year");
        if(Objects.nonNull(recordYearRange)) {
            Integer min = recordYearRange.hasLowerBound() ? recordYearRange.lowerEndpoint() : getLowerEndpoint(preciseTableName, logicTable.resultTableNamesCache());
            Integer max = recordYearRange.hasUpperBound() ? recordYearRange.upperEndpoint() : getUpperEndpoint(preciseTableName, logicTable.resultTableNamesCache());
            Set<String> resultTableNames = new LinkedHashSet<>();
            while (min < max || min == max) {
                String tableName = preciseTableName + min;
                resultTableNames.add(tableName);
                // 检查分表获取的表名是否存在，不存在则自动建表
                if (!tableNames.contains(tableName)){
                    tableNames.add(tableName);
                }
                min ++;
            }
            return ShardingAlgorithmTool.getShardingTablesAndCreate(logicTable, resultTableNames);
        }
        throw new ServiceException("能耗数据表分表，record_year和record_date必传其中一个");
    }

    /**
     * 获取 最小分片值
     * @param tableNames 表名集合
     * @return 最小分片值
     */
    private Integer getLowerEndpoint(String preciseTableName, Collection<String> tableNames) {
        Optional<Integer> optional = tableNames.stream()
                .map(tableName -> tableName.replace(preciseTableName, ""))
                .map(Integer::parseInt)
                .min(Comparator.comparing(Function.identity()));
        if (optional.isPresent()) {
            return optional.get();
        } else {
            log.error(">>>>>>>>>> 【ERROR】获取数据最小分表失败，请稍后重试，tableName：{}", tableNames);
            throw new IllegalArgumentException("获取数据最小分表失败，请稍后重试");
        }
    }

    /**
     * 获取 最大分片值
     * @param tableNames 表名集合
     * @return 最大分片值
     */
    private Integer getUpperEndpoint(String preciseTableName, Collection<String> tableNames) {
        Optional<Integer> optional = tableNames.stream()
                .map(tableName -> tableName.replace(preciseTableName, ""))
                .map(Integer::parseInt)
                .max(Comparator.comparing(Function.identity()));
        if (optional.isPresent()) {
            return optional.get();
        } else {
            log.error(">>>>>>>>>> 【ERROR】获取数据最大分表失败，请稍后重试，tableName：{}", tableNames);
            throw new IllegalArgumentException("获取数据最大分表失败，请稍后重试");
        }
    }
}
