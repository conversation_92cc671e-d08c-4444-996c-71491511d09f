<template>
  <DraggableContainer :adsorbParent="true" referenceLineColor="#606266">
    <vue3-draggable-resizable v-for="(item, index) in commonComponent" :key="item.id" :active="item === curComponent" :h="item.style.height"
                              :is-conflict-check="true"  :minW="16" :minH="16" :w="item.style.width" :x="item.style.left"
                              :y="item.style.top"
                              :style="{zIndex: item.style.zIndex}"
                              tabindex="0"
                              @dragging="(info) => draggingHandle(info,item.style)"
                              @mousedown="handleMouseDownOnShape($event, item, index)"
                              @resizing="(info) => resizingHandle(info,item.style)"
                              @drag-start="dragMoveHandle(false)"
                              @drag-end="dragMoveHandle(true)"
                              @keyup.delete="removeHandle"
                              @keydown.ctrl.c="copyHandle"
                              @keydown.ctrl.s.stop="saveHandle"
                              @keydown.ctrl.v.stop="pasteHandle"
                              @keydown.left="moveDirHandle($event,'left')"
                              @keydown.right="moveDirHandle($event,'right')"
                              @keydown.up="moveDirHandle($event,'up')"
                              @keydown.down="moveDirHandle($event,'down')"
                              @mousewheel.stop="() => {}"
    >
      <component :is="item.component" :element="item" :style="getComponentStyle(item.style)" class="component"
                 type="edit"/>
    </vue3-draggable-resizable>
    <div v-for="(item,index) in svgComponent" class="svg-edit" :style="{zIndex: item.style.zIndex}" :key="item.id">
      <component :is="item.component"
                 :active="item === curComponent"
                 :element="item"
                 class="component vdr-container" type="edit"
                 @mousedown="handleMouseDownOnShape($event, item)"
                 @keyup.delete="removeHandle"
                 @keydown.ctrl.c="copyHandle"
                 @keydown.ctrl.s.stop="saveHandle"
                 @keydown.ctrl.v.stop="pasteHandle"
                 tabindex="0"/>
    </div>
  </DraggableContainer>
</template>

<script setup>
import Vue3DraggableResizable, {DraggableContainer} from 'vue3-draggable-resizable'

import {ElMessage} from 'element-plus'

import {getStyle} from '@/utils/webtopo/style.js'

import {webtopoStore} from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let {componentData, curComponent, copyCurComponent, isClickComponent} = storeToRefs(webtopo)

const emit = defineEmits(['dragMove', 'saveHandle', 'pasteHandle'])

// 普通组件
const commonComponent = computed(() => {
  return componentData.value.filter(item => item.type !== 'svg')
})

// svg组件
const svgComponent = computed(() => {
  return componentData.value.filter(item => item.type === 'svg')
})

// 防止画布移动
const dragMoveHandle = (bool) => {
  emit('dragMove', bool)
}

// 设置当前组件
const handleMouseDownOnShape = (e, info) => {
  e.stopPropagation()
  isClickComponent.value = true
  curComponent.value = info
}

// 拖拽
const draggingHandle = (info, style) => {
  style.left = info.x
  style.top = info.y
}

// 组件大小改变
const resizingHandle = (info, style) => {
  style.width = info.w
  style.height = info.h
  style.left = info.x
  style.top = info.y
}

// 当前组件样式
const getComponentStyle = (style) => {
  return getStyle(style, ['width', 'height', 'top', 'left'])
}

// 删除
const removeHandle = (e) => {
  if (curComponent.value) {
    componentData.value.splice( componentData.value.findIndex(item => item.id === curComponent.value.id) , 1)
    curComponent.value = null
  }
}

// 复制
const copyHandle = () => {
  if (curComponent.value) {
    copyCurComponent.value = JSON.parse(JSON.stringify(curComponent.value))
    ElMessage.success('复制成功')
  }
}

// 粘贴
const pasteHandle = () => {
  emit('pasteHandle')
}

// 保存
const saveHandle = (event) => {
  emit('saveHandle', event)
}

// 移动物体
const moveDirHandle = (event, arrow) => {
  event.preventDefault()
  if (curComponent.value) {
    switch (arrow) {
      case 'left':
        curComponent.value.style.left -= 1
        break;
      case 'right':
        curComponent.value.style.left += 1
        break;
      case 'up':
        curComponent.value.style.top -= 1
        break;
      case 'down':
        curComponent.value.style.top += 1
        break;

      default:
        break;
    }
  }
}
</script>

<style lang='less' scoped>
.component {
  width: 100%;
  height: 100%;
  display: flex;
}

.vdr-container {
  outline: none;
}

.active {
  :deep(.vdr-handle) {
    display: block !important;;
  }
}

.svg-edit{
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
</style>
