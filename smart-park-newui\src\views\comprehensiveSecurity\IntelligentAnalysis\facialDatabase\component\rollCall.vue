<template>
  <page-common v-model="state.tableHeight" :leftBool="false">
    <template #query>
      <el-form
        :inline="true"
        ref="formInlineRef"
        :model="formInline"
        label-suffix=":"
      >
        <el-form-item prop="queryWord">
          <el-input v-model="formInline.queryWord" placeholder="姓名/手机号" />
        </el-form-item>
        <el-form-item prop="sex">
          <el-select v-model="formInline.sex" placeholder="性别">
            <el-option
              v-for="item in state.options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="time">
          <el-date-picker
            v-model="formInline.time"
            type="datetimerange"
            range-separator="-"
            start-placeholder="创建时间开始"
            end-placeholder="创建时间结束"
            value-format="YYYY-MM-DD HH:mm"
            format="YYYY-MM-DD HH:mm"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit"
            >查询</el-button
          >
          <el-button type="primary" icon="Refresh" @click="onReset"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addRoll">{{
        listType == 1 ? "新增黑名单" : "新增白名单"
      }}</el-button>
    </template>
    <template #table>
      <el-table
        :data="state.tableData"
        :height="state.tableHeight"
        show-overflow-tooltip
      >
        <el-table-column
          v-for="(item, index) in state.tableHeader"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :align="item.align"
          :formatter="item.formatter"
          :width="item.width"
        >
          <template #default="{ row }">
            <div v-if="item.prop === 'faceUrl'">
              <el-image
                style="width: 40px"
                v-for="photo in [imgTransfer(row?.faceUrl)]"
                :src="photo"
                :zoom-rate="1.2"
                :z-index="2"
                :preview-teleported="true"
                :preview-src-list="[photo]"
                :initial-index="0"
                fit="fill"
              >
                <template #error>
                  <div class="error-image"></div>
                </template>
              </el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="300">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="editHandle(scope.row)"
              >编辑</el-button
            >

            <el-button
              link
              type="danger"
              icon="Delete"
              @click="deleteMessage(scope.row.id)"
              >移出{{ listType == 1 ? "黑" : "白" }}名单</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum"
        :page-size="state.pagetion.pageSize"
        :total="state.pagetion.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />

      <modal-page
        ref="modal"
        @submit="getList"
        :title="state.title"
        :options="state.options"
      />
    </template>
  </page-common>
</template>

<script setup>
import dayjs from "dayjs";
import modalPage from "./modalPage.vue";
import {
  facePageAPI,
  faceDeteleAPI,
} from "@/api/comprehensiveSecurity/IntelligentAnalysis.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { calcPageNo } from "@/utils/util.js";

const props = defineProps({
  listType: {
    type: Number,
    default: 1,
  },
});

const route = useRoute();
const formInlineRef = ref();
const modal = ref();
const formInline = reactive({});

const dialogVisible = ref(false);
const imgUrl = ref("");

const state = reactive({
  title: "",
  options: [
    {
      label: "男",
      value: 1,
    },
    {
      label: "女",
      value: 2,
    },
    {
      label: "未知",
      value: 3,
    },
  ],
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: "username",
      label: "姓名",
    },
    {
      prop: "sex",
      label: "性别",
      formatter: (row, column, cellValue) => {
        if (cellValue == 1) {
          return "男";
        } else if (cellValue == 2) {
          return "女";
        } else {
          return "未知";
        }
      },
    },
    {
      prop: "phone",
      label: "手机号",
    },
    {
      prop: "companyName",
      label: "公司名称",
    },
    // {
    //   prop: 'cardType',
    //   label: '证件类型'
    // },
    {
      prop: "certificateNum",
      label: "证件号码",
    },
    {
      prop: "remark",
      label: "备注",
    },
    {
      prop: "createTime",
      label: "创建时间",
      formatter: (row, column, cellValue) => {
        return cellValue ? dayjs(cellValue).format("YYYY-MM-DD HH:mm") : "";
      },
    },
    {
      prop: "faceUrl",
      label: "人脸照片",
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
});

onMounted(() => {
  getList();
});


const imgTransfer = (param) => {
    return import.meta.env.VITE_BASE_URL + param;
};

// 修改1
const editHandle = (row) => {
  if (props.listType == 1) {
    state.title = "编辑黑名单";
  } else {
    state.title = "编辑白名单";
  }
  modal.value.open();
  nextTick(() => {
    Object.assign(modal.value.form, row);
    modal.value.form.listType = props.listType;

    if(row?.faceUrl){
      modal.value.form.fileList = [{filePath:row.faceUrl}]
    }
    
  });
};

// 新增0
const addRoll = () => {
  if (props.listType == 1) {
    state.title = "新增黑名单";
  } else {
    state.title = "新增白名单";
  }
  modal.value.form.id = undefined;
  modal.value.form.listType = props.listType;
  // modal.value.form.source = 0
  modal.value.open();
};

const deleteMessage = (id) => {
  ElMessageBox.confirm("是否移出当前名单?", "提醒", {
    type: "warning",
  }).then(() => {
    state.pagetion.pageNum = calcPageNo(
      state.pagetion.total,
      state.pagetion.pageNum,
      state.pagetion.pageSize
    );

    faceDeteleAPI({ id }).then((res) => {
      if (res.success) {
        ElMessage.success("移出成功");
        getList();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};

//分页
const getList = () => {
  let query = {
    ...route.query,
    ...formInline,
    ...state.pagetion,
    listType: props.listType,
  };

  if (formInline.time) {
    query.createDateStart = formInline.time[0];
    query.createDateEnd = formInline.time[1];
  }

  facePageAPI(query).then((res) => {
    state.tableData = res.data.dataList;

    //  state.tableData[0].faceUrl = 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100'
    //  state.tableData[1].faceUrl = 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'

    state.pagetion.total = res.data.totalCount * 1;
  });
};

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

//重置方法
const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};

defineExpose({
  getList,
  state,
});
</script>

<style lang="less" scoped></style>
