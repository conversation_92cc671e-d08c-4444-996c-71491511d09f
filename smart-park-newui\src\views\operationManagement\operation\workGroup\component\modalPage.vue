<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef" :width="600">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":">
      <el-row>
        <el-col>
          <el-form-item label="工作组名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入工作组名称" clearable/>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="负责人" prop="leaderId">
            <el-select v-model="form.leaderId" filterable placeholder="请选择负责人(可直接搜索)" clearable>
              <el-option v-for="user in state.userList" :key="user.userId" :label="user.showName + (user.deptName ? ' - ' + user.deptName : '')" :value="user.userId"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="成员" prop="userIds">
            <rt-personnel placeholder="请选择工作组成员" v-model="form.userIds" :multiple="true" @update:model-value="() => {ruleFormRef.validateField('staffDTOList.' + $index + '.userIdList')} "></rt-personnel>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="标签" prop="tagIds">
            <el-select v-model="form.tagIds" placeholder="请选择工作组标签" multiple clearable>
              <el-option v-for="tag in state.tagList" :key="tag.id" :label="tag.name" :value="tag.id"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="form.remark" placeholder="请输入备注信息"
                      show-word-limit
                      maxlength="300"
                      :autosize="{ minRows: 4, maxRows: 6 }"
                      @input="(value) => {
                          if(value.length >= 300) {
                           ElMessage.warning('最多只能输入300个字符！')
                          }
                        }"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {saveWorkGroupAPI} from '@/api/operationManagement/workGroup.js';
import {listUsersAPI} from '@/api/settingSystem/user.js';
import {ElMessage, ElMessageBox} from 'element-plus';
import {pageListAPI} from "@/api/settingSystem/tag.js";

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
const {title} = toRefs(props);
const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({
  userIds: []
});
const state = reactive({
  userList: [],
  tagList: [],
  rules: {
    name: [{ required: true, message: '工作组名称不能为空', trigger: 'blur' }],
    leaderId: [{ required: true, message: '负责人不能为空', trigger: 'change' }],
    userIds: [{ required: true, message: '成员不能为空', trigger: 'change' }],
  },
});

const open = () => {
  dialog.value.open();
  loadUserList();
  listTags()
};

/** 查询用户 */
const loadUserList = () => {
  const sysUserDtoFilter = {};
  listUsersAPI({sysUserDtoFilter}).then((res) => {
    state.userList = res.data.dataList;
  });
};

// 查询 设备标签列表
const listTags = () => {
  let params = {
    type: 2
  }
  pageListAPI(params).then(res => {
    if (res.success) {
      state.tagList = res.data.dataList
    }
  })
}

/** 提交表单 */
const submit = () => {
  saveWorkGroupAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success(title.value + '成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

defineExpose({
  form,
  open,
});
</script>
