import { request } from '@/utils/request.js';

// 分页查询
export const selectPagePayAPI = (data) => {
  return request('get', '/energy/pay/selectPagePay', data, 'F');
};

// 查询设置
export const selectWarningAPI = (data) => {
  return request('get', '/energy/warning/selectWarning', data, 'F');
};

// 提醒设置
export const setWarningAPI = (data) => {
  return request('post', '/energy/warning/setWarning', data, 'F');
};

// 充值
export const rechargeAPI = (data) => {
  return request('post', '/energy/pay/recharge', data, 'F');
};

// 充值记录
export const selectPayRecordAPI = (data) => {
  return request('get', '/energy/pay/selectPayRecord', data, 'F');
};
