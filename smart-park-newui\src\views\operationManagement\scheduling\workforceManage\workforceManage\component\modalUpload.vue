<template>
  <dialog-common ref="dialog" :title="state.title"  :width="900" @onClose="onClose" :showButton="false">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span style="font-weight: bold;">导入数据</span>
        </div>
      </template>
      <el-steps :active="state.active" align-center>
        <el-step title="上传文件"/>
        <el-step title="导入完成"/>
      </el-steps>
      <div class="conent">
        <div v-show="state.active === 0" v-loading="state.loading" element-loading-text="正在导入...">
          <el-card style="margin: 15px 30px;">
            <h4>填写导入排班的信息</h4>
            <span
                class="introduce">请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除，单次导入的数据不超过1000条。</span>
            <div>
              <el-button type="primary" link @click="downTem">下载模板</el-button>
            </div>
            <el-date-picker
                v-model="state.cycle"
                type="month"
                placeholder="请选择下载模板月份"
                value-format="YYYY-MM"
                :clearable="false"
            />
          </el-card>
          <el-card style="margin: 15px 30px;">
            <h4>上传填好的排班信息表</h4>
            <span class="introduce">文件后缀名必须为xls或xlsx(即Excel格式),文件大小不得大于10M</span>
            <el-upload :limit="1" action="#" accept=".xls,.xlsx" v-model:file-list="state.fileList" :auto-upload="false"
                       ref="upload">
              <el-button type="primary" icon="Upload">上传文件</el-button>
            </el-upload>
          </el-card>
        </div>
        <div v-show="state.active === 1" class="successPage">
          <el-icon size="50" color="#67c23a">
            <SuccessFilled/>
          </el-icon>
          <span class="introduce" style="margin: 10px 0 20px">成功导入数据</span>
        </div>
      </div>
      <div style="text-align: center;" v-show="state.active === 0">
        <el-button type="primary" size="large" icon="Right" @click="stepHandleFirst">下一步</el-button>
      </div>
      <div style="text-align: center;" v-show="state.active === 1">
        <el-button type="primary" size="large" icon="Check" @click="handleSuccess">完成</el-button>
      </div>
    </el-card>
  </dialog-common>
</template>


<script setup>
import {ElMessage} from 'element-plus'
import {exportFile, exportFileMessage, uploadFile} from '@/utils/down.js'

import dayjs from "dayjs";

const emit = defineEmits(['submit'])

// dialog组件
const dialog = ref();

const state = reactive({
  active: 0,
  loading: false,
  fileList: [],
  cycle: dayjs().format('YYYY-MM')
})

// 上传成功
const handleSuccess = () => {
  dialog.value.close()
}

const onClose = () => {
  state.active = 0
  state.fileList = []
  emit('submit')
}

// 下载模板
const downTem = () => {
  exportFile('/shifts/record/excelTemplate', {businessType:'OPERATIONS',cycle: state.cycle}, `${state.cycle}排班模版.xlsx`)
}

const stepHandleFirst = () => {
  // 步骤一
  if (!state.fileList.length) {
    return ElMessage.error('请上传文件')
  }

  state.loading = true

  // 上传文件
  uploadFile('/shifts/record/importExcel?businessType=OPERATIONS',state.fileList[0].raw).then(res => {
    console.log(res)
    if (res.headers['content-type'] == 'application/json;charset=UTF-8') {
      ElMessage.success("导入成功")
      state.active += 1
    } else {
      state.fileList = []
      ElMessage.error("已下载错误文件,请检查错误文件")
      exportFileMessage(res, '排班错误信息.xlsx')
    }
  }).finally(() => {
    state.loading = false
  })
}

const open = () => {
  dialog.value.open();
};

defineExpose({
  open,
});

</script>

<style scoped lang="less">
.box-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  margin: 10px;

  :deep(.el-card__header) {
    background-color: rgb(249 249 249);
  }

  :deep(.el-card__body) {
    flex: 1;
    overflow: hidden;
  }
}

.conent {
  height: calc(100% - 100px);
  overflow: auto;
}

.introduce {
  color: #7b7b7b;
  font-size: 14px;
}

.successPage {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>