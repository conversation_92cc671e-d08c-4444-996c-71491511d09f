import {getEquipmentTypeTypesAPI, getSubSystemTypesAPI} from "@/api/iotManagement/equipManage.js";
export default function  () {

  const subTypeOptions = ref([]) // 子系统数据
  const subTypeDictionaries = reactive({}) // 子系统映射数据
  const deviceTypes  = ref([]) // 子系统数据

  // 获取子系统
  getSubSystemTypesAPI().then(res => {
    if (res.success) {
      subTypeOptions.value = res.data.map(item => {
        subTypeDictionaries[item.name] = item.value
        return item
      });
    }
  })

  // 获取设备子系统下分类
  getEquipmentTypeTypesAPI().then(res => {
    if (res.success) {
      deviceTypes.value = res.data.map(item => {
        return item
      });
    }
  })


  return {
    subTypeOptions,
    subTypeDictionaries,
    deviceTypes
  }
}
