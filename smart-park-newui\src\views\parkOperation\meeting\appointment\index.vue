<template>
  <page-common v-model="state.tableHeight" :queryBool="false" :operate-bool="false">
    <template #table>
      <el-tabs v-model="state.activeName" class="meeting-tabs" @tab-click="handleClick">
        <el-tab-pane :name="tab" v-for="{tab, columns} of state.tabOptions">
          <template #label>
            <span class="meeting-tabs-label" v-if="tab === 'rooms'">
              <el-icon><OfficeBuilding/></el-icon>
              <span>会议预约</span>
            </span>
            <span class="meeting-tabs-label" v-else-if="tab === 'reserved'">
              <el-icon><Orange/></el-icon>
              <span>我的预约</span>
            </span>
          </template>
          <div class="meeting-tabs-table">
            <el-table :data="state.tableData" :height="state.tableHeight - 55" tooltip-effect="light">
              <el-table-column v-for="column of columns" :label="column.label" :prop="column.prop" show-overflow-tooltip>
                <template #default="scope">
                  <div v-if="column.prop === 'roomName'">
                    <el-space>
                      <el-image
                        style="width: 2.0rem; height: 2.0rem"
                        :src="imgTransfer(scope.row.roomImg)"
                        :zoom-rate="1.2"
                        :z-index="2"
                        :preview-teleported="true"
                        :preview-src-list="[imgTransfer(scope.row.roomImg)]"
                        :initial-index="0"
                        fit="fill"
                      >
                        <template #error>
                          <div class="error-image">
                            <el-icon size="40"><icon-picture /></el-icon>
                          </div>
                        </template>
                      </el-image>
                      <span> {{ scope.row.roomName }} </span>
                    </el-space>
                  </div>
                  <div v-else-if="column.prop === 'isEnableApproval'">
                    <el-text v-if="scope.row.isEnableApproval === 1" type="success">是</el-text>
                    <el-text v-else type="info">否</el-text>
                  </div>
                  <div v-else-if="column.prop === 'approvalStatus'">
                    <el-tag type="warning" v-if="scope.row[column.prop] === 0">待审核</el-tag>
                    <el-tag type="success" v-else-if="scope.row[column.prop] === 1">通过</el-tag>
                    <el-tag type="danger" v-else-if="scope.row[column.prop] === 2">未通过</el-tag>
                  </div>
                  <div v-else>
                    <span>
                      {{ scope.row[column.prop] ? scope.row[column.prop] : '/' }}
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作"  align="center" width="200">
                <template #default="scope">
                  <el-button
                    link
                    v-if="tab === 'rooms'"
                    type="primary"
                    icon="Edit"
                    @click.prevent="onAppointment(scope.row)"
                  >
                    开始预定
                  </el-button>

                  <el-button
                    link
                    v-if="tab === 'reserved'"
                    type="primary"
                    icon="Document"
                    @click.prevent="onView(scope.row)"
                  >
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              :page-sizes="[10, 20, 30, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="state.pageParam.pageNum"
              :page-size="state.pageParam.pageSize"
              :total="state.pageParam.total"
              @size-change="sizeChange"
              @current-change="pageChange"
            />
          </div>
        </el-tab-pane>
      </el-tabs>

      <appointment-dialog ref="appointmentDialogRef" @close="queryList" />

      <meeting-check-dialog ref="meetingCheckRef" @onClose="queryList" />
    </template>
  </page-common>
</template>
<script setup>
import {ref} from 'vue'
import {ElTable} from 'element-plus'
import PageCommon from "@/components/basic/pageCommon.vue"
import AppointmentDialog from "@/views/parkOperation/meeting/appointment/component/appointmentDialog.vue";
import {listMeetingRoomAPI} from "@/api/parkOperation/meetingRoom.js";
import {listMeetingAPI} from "@/api/parkOperation/meeting.js";
import { Picture as IconPicture } from '@element-plus/icons-vue'
import MeetingCheckDialog from "@/views/parkOperation/meeting/check/component/meetingCheckDialog.vue";


const appointmentDialogRef = ref()

const meetingCheckRef = ref()

// 定义属性
const state = reactive({
  tableHeight: 100,
  tableData: [],
  // 激活的标签页
  activeName: ref('rooms'),
  tabOptions: [
    {
      tab: "rooms",
      columns: [
        {label: "会议室编号", prop: "roomNo"},
        {label: "会议室名称", prop: "roomName"},
        {label: "会议室位置", prop: "roomSpaceFullName"},
        {label: "容纳人数", prop: "capacity"},
        {label: "管理员", prop: "ownerName"},
        {label: "电话", prop: "ownerPhone"},
        {label: "预约审批", prop: "isEnableApproval"}
      ]
    },
    {
      tab: "reserved",
      columns: [
        {label: "会议室名称", prop: "roomName"},
        {label: "会议室位置", prop: "roomSpaceFullName"},
        {label: "管理员", prop: "ownerName"},
        {label: "电话", prop: "ownerPhone"},
        {label: "会议名称", prop: "meetingName"},
        {label: "会议人数", prop: "meetingUserCount"},
        {label: "申请时间", prop: "createTime"},
        {label: "审核状态", prop: "approvalStatus"},
        {label: "审核时间", prop: "approvalTime"},
        {label: "审核备注", prop: "approvalRemark"}
      ]
    }
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
})

// 标签页点击事件，查询选择列表
const handleClick = (tab, event) => {
  state.activeName = tab.props.name
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  state.pageParam.total = 0
  state.tableData = []
  queryList();
}


// 开始预约
const onAppointment = (val) => {
  appointmentDialogRef.value.open('预定', JSON.parse(JSON.stringify(val)))
}

const onView = (val) => {
  meetingCheckRef.value.open('我的预约', JSON.parse(JSON.stringify(val)))
}

// 变更分页记录数
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  queryList();
}
// 变更分页页数
const pageChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  queryList()
}

// 提交查询表单
const queryList = () => {
  let params = {
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  if (state.activeName === 'rooms') {
    listMeetingRoomAPI(params).then(res => {
      if (res.success) {
        state.tableData = res.data.dataList
        state.pageParam.total = res.data.totalCount
      }
    })
  } else {
    params.queryConfine = 1
    listMeetingAPI(params).then(res => {
      if (res.success) {
        state.tableData = res.data.dataList
        state.tableData.forEach(meeting => {
          if (meeting.meetingUserIds) {
            let meetingUserIds = meeting.meetingUserIds.split(',');
            meeting.meetingUserCount = meetingUserIds.length
            if (!meetingUserIds.includes(meeting.createUserId)) {
              meeting.meetingUserCount = meeting.meetingUserCount + 1
            }
          }
        })
        state.pageParam.total = res.data.totalCount
      }
    })
  }
}


const imgTransfer = (name) => {
  if (name) {
    return import.meta.env.VITE_BASE_URL + name
  }
  return name
}

onMounted(() => {
  queryList();
})
</script>


<style scoped lang="less">
/* 标签页 标题 icon */
.meeting-tabs .meeting-tabs-label .el-icon {
    vertical-align: middle;
}

/* 标签页 标题名称 */
.meeting-tabs .meeting-tabs-label span {
    vertical-align: middle;
    margin-left: 4px;
    font-size: 14px;
    font-weight: 400;
}


//.error-image {
//    display: flex;
//    justify-content: center;
//    align-items: center;
//    width: 100%;
//    height: 100%;
//    background: var(--el-fill-color-light);
//    color: var(--el-text-color-secondary);
//    font-size: 30px;
//  .el-icon{
//    font-size: 30px;
//    svg {
//      width: 2rem;
//      height: 2rem;
//    }
//  }
//}
</style>
