<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.face.FaceRecognitionEventRecordMapper">
    <resultMap type="com.soft.webadmin.model.face.FaceRecognitionEventRecord" id="FaceRecognitionEventRecordResult">
        <result property="id" column="id" />
        <result property="eventType" column="event_type" />
        <result property="happenTime" column="happen_time" />
        <result property="gender" column="gender" />
        <result property="glass" column="glass" />
        <result property="bkgUrl" column="bkg_url" />
        <result property="faceUrl" column="face_url" />
        <result property="faceTime" column="face_time" />
        <result property="matchFaceGroupCode" column="match_face_group_code" />
        <result property="matchFaceGroupName" column="match_face_group_name" />
        <result property="matchFaceIndexCode" column="match_face_index_code" />
        <result property="matchUsername" column="match_username" />
        <result property="matchFaceSex" column="match_face_sex" />
        <result property="matchFaceCertificate" column="match_face_certificate" />
        <result property="matchFaceSimilarity" column="match_face_similarity" />
        <result property="matchFacePicUrl" column="match_face_pic_url" />
        <result property="resourceType" column="resource_type" />
        <result property="resourceIndexCode" column="resource_index_code" />
        <result property="resourceName" column="resource_name" />
        <result property="createTime" column="create_time" />
    </resultMap>


</mapper>