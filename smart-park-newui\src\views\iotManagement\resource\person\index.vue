<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="name">
          <el-input v-model="formInline.name" placeholder="人员姓名" />
        </el-form-item>
        <el-form-item prop="type">
          <el-select v-model="formInline.type" placeholder="人员类型">
            <el-option label="物业人员" value="1" />
            <el-option label="企业人员" value="2" />
            <el-option label="商家人员" value="3" />
            <el-option label="访客" value="4" />
            <el-option label="白名单人员" value="5" />
            <el-option label="其他" value="99" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新建人员</el-button>
      <el-button type="primary" icon="Download" @click="exportExcel">导出</el-button>
      <el-button type="primary" icon="Upload" @click="importExcel">导入</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column label="序号" align="center" type="index" width="55" />
        <el-table-column prop="name" label="人员姓名" />
        <el-table-column prop="sex" label="性别" />
        <el-table-column prop="phone" label="手机号码" />
        <el-table-column prop="" label="人员类型">
          <template #default="scope">
            {{ scope.row.type === 1 ? "物业人员"
              : scope.row.type === 2 ? "企业人员"
              : scope.row.type === 3 ? "商家人员"
              : scope.row.type === 4 ? "访客"
              : scope.row.type === 5 ? "白名单人员" :  "其他" }}
          </template>
        </el-table-column>
        <el-table-column prop="orgName" label="所属组织" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" align="center" width="230">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog v-model="state.dialogVisible" title="导入人员" width="30%">
        <el-upload ref="uploadRef" :http-request="onUploadFile" align="center">
          <template #trigger>
            <el-button size="small" type="primary">上传数据</el-button>
          </template>
          <template #tip>
            <div class="el-upload__tip" align="center">
              可以先导出人员,再上传一个xlsx文件
            </div>
          </template>
        </el-upload>
      </el-dialog>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
<!-- 新建、编辑用户Dialog -->
      <modal-page ref="modal" :title="state.title" @submit="getList"></modal-page>
    </template>
  </page-common>
</template>

<script setup>
import {
  getPageAPI,
  deleteUserAPI, getUserInfoAPI,
} from '@/api/iotManagement/person.js';
import {Delete, Plus, Search, Refresh, Promotion} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

import { calcPageNo } from '@/utils/util.js';
import modalPage from './component/modalPage.vue';
import {exportFile, uploadFile, exportFileMessage} from "@/utils/down.js";
import axios from "axios";

const modal = ref();
const formInlineRef = ref();
const formInline = reactive({
  name: '',
  type: '',
});

const state = reactive({
  srcList:[],
  imageVisible:false,
  deptList: [],
  tableData: [],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  title: '',
});

const getList = () => {
  let query = {
    type: formInline.type,
    name: formInline.name,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  };
  getPageAPI(query).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};


const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

/** 创建用户 */
const addHandle = () => {
  state.title = '新建人员';
  modal.value.form.id = undefined;
  modal.value.open();
};

/** 编辑用户 */
const editHandle = (row) => {
  state.title = '编辑人员';
  modal.value.open();
  getUserInfoAPI({ id: row.id }).then((res) => {
    const data = res.data;
    nextTick(() => {
      Object.assign(modal.value.form, { ...data });
      modal.value.echoData(data)
    });
  });
};

/** 删除用户 */
const deleteHandle = (row) => {
  ElMessageBox.confirm('是否删除当前人员?', '提醒', {
    type: 'warning',
  }).then(() => {
    let fd = new FormData();
    fd.append('personId', row.id);
    state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize);
    deleteUserAPI(fd).then((res) => {
      ElMessage.success('删除成功');
      getList();
    });
  });
};


/**
 * 导出
 * @returns {Promise<void>}
 */
const exportExcel = async () => {
  await exportFile('/admin/upms/person/export?type=' + formInline.type, null, '人员信息.xlsx')
}

// 上传文件
const onUploadFile = async (fileData) => {
  const formData = new FormData()
  formData.append('file', fileData.file)
  let headers = {
    Authorization: localStorage.getItem('Authorization')
  }
  axios.post(import.meta.env.VITE_BASE_URL + '/admin/upms/person/import', formData, { responseType: "arraybuffer", headers }).then(res => {
    if (res.headers['content-type'] == 'application/json;charset=UTF-8') {
      ElMessage.success("导入成功")
      uploadRef.value.clearFiles()
      getList()
    } else {
      ElMessage.error("已下载错误文件,请检查错误文件")
      exportFileMessage(res, '人员错误信息.xlsx')
    }
  })

}
const uploadRef = ref()

const importExcel = () => {
  state.dialogVisible = true;
}


onMounted(() => {
  getList();
})
</script>

<style lang="less" scoped>
.el-dropdown-link {
  margin-left: 10px;
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  line-height: 22px;

}
</style>
