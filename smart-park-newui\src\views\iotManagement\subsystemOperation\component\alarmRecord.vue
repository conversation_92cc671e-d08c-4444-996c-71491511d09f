<template>
  <page-common v-model="state.tableHeight" :operate-bool="false">
    <template #query>
      <el-form :inline="true" ref="queryFormRef" :model="state.queryForm" label-suffix=":">
        <el-form-item prop="queryName">
          <el-input v-model="state.queryForm.queryName" placeholder="设备名称/编号"/>
        </el-form-item>
        <el-form-item prop="spacePath">
          <el-cascader v-model="state.queryForm.spacePath" :options="state.spaceOptions" :props="optionsProps" clearable
                       placeholder="设备位置" />
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="state.queryForm.status" placeholder="状态" clearable>
            <el-option v-for="(value, key) in state.statusOptions" :key="key" :label="value" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onQuery">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter"/>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize"
                     :total="state.pageParam.total"
                     @size-change="sizeChange" @current-change="currentChange"/>
    </template>
  </page-common>
</template>


<script setup>

import PageCommon from "@/components/basic/pageCommon.vue";
import {Refresh, Search} from "@element-plus/icons-vue";
import {ElTag} from "element-plus";
import {pageList} from "@/api/iotManagement/device.js";
import {treeAPI} from "@/api/iotManagement/space.js";
import dayjs from 'dayjs'

const queryFormRef = ref()

const props = defineProps({
  equipmentType: {
    type: Object,
    default: {
      id: '',
      name: ''
    }
  }
})

const { equipmentType } = toRefs(props)


// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'path',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};


const state = reactive({
  tableHeight: 100,
  queryForm: {},
  spaceOptions: [],
  tableData: [],
  tableHeader: [
    {
      prop: 'equipmentNo',
      label: '设备编号'
    },
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'equipmentSpaceFullName',
      label: '位置'
    },
    {
      prop: 'warningTypeName',
      label: '告警类型'
    },
    {
      prop: 'content',
      label: '描述'
    },
    {
      prop: 'createTime',
      label: '告警时间',
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    },
    {
      prop: 'statusName',
      label: '状态',
      formatter: (row, column, cellValue) => {
        if (cellValue === '已处理') {
          return h(ElTag, {type: "success"}, {default: () => "已处理"})
        } else if (cellValue === '未处理') {
          return h(ElTag, {type: "danger"}, {default: () => "未处理"})
        }
      }
    }
  ],
  statusOptions: {
    0: '未处理',
    1: '已处理'
  },
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})


//查询方法
const onQuery = () => {
  let param = {
    queryName: state.queryForm.queryName,
    status: state.queryForm.status,
    spacePath: state.queryForm.spacePath,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
    equipmentType: equipmentType.value.name
  }
  pageList(param).then(res => {
    if (res.success) {
      res.data.dataList.forEach(e => {
        if (e.status === 0) {
          e.statusName = '未处理'
        } else if (e.status === 1) {
          e.statusName = '已处理'
        }
        if (e.warningType === 'WARN') {
          e.warningTypeName = '告警'
        } else if (e.warningType === 'ERROR') {
          e.warningTypeName = '故障'
        }
      })
      state.tableData = res.data.dataList
      state.pageParam.total = res.data.totalCount
    }
  })
}

//重置方法
const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  onQuery()
}

//分页方法
const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum
  onQuery()
}

//分页方法
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize
  onQuery()
}

// 设备位置
const getSpaceTree = () => {
  treeAPI({ deep: 4 }).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
};

const init = () => {
  onQuery()
  getSpaceTree()
}

defineExpose({
  init
})
</script>

<style scoped lang="less">

</style>
