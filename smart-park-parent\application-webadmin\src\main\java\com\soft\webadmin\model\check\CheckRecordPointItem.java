package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.check.CheckRecordPointItemVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 检查记录检查项对象 sp_check_record_point_item
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@Data
@TableName(value = "sp_check_record_point_item")
public class CheckRecordPointItem {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 检查点id */
    private Long pointId;

    /** 检查项 */
    private String itemName;

    /** 检查内容 */
    private String itemContent;

    /** 检查项类型：1选项，2数值，3选项&数值 */
    private Integer itemType;

    /** 检查结果：1正常，2异常 */
    private Integer itemResult;

    /** 检查结果数值 */
    private String itemResultVal;

    /** 备注 */
    private String remark;


    @Mapper
    public interface CheckRecordPointItemModelMapper extends BaseModelMapper<CheckRecordPointItemVO, CheckRecordPointItem> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        CheckRecordPointItem toModel(CheckRecordPointItemVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        CheckRecordPointItemVO fromModel(CheckRecordPointItem entity);
    }

    public static final CheckRecordPointItemModelMapper INSTANCE = Mappers.getMapper(CheckRecordPointItemModelMapper.class);
}
