import { defineStore } from 'pinia'

export const webtopoStore = defineStore({
  id: 'webtopo',
  state: () => {
    return {
      topoid:'',
      canvasStyle:{
        width: 1200,
        height: 740,
        scale: 100,
        opacity: 1,
        backgroundColor:'',
        backgroundImage:''
      },
      componentData:[],
      curComponent: null,
      copyCurComponent: null,
      isClickComponent:false
    }
  },
  persist:{
    enabled:true,
  },
  actions: {
  }
})
