package com.soft.webadmin.dto.check;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class WorkGroupSaveOrUpdateDTO {


    @ApiModelProperty(value = "${column.columnComment}")
    private Long id;

    @ApiModelProperty(value = "班组名称")
    @NotBlank(message = "工作组名称不能为空！")
    private String name;

    @ApiModelProperty(value = "负责人id")
    @NotNull(message = "负责人不能为空！")
    private Long leaderId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "成员用户列表")
    private List<Long> userIds;

    @ApiModelProperty(value = "标签列表")
    private List<Long> tagIds;
}
