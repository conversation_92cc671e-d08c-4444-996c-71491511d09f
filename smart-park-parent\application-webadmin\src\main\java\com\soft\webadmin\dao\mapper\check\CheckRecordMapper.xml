<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.CheckRecordMapper">
    <resultMap type="com.soft.webadmin.model.check.CheckRecord" id="CheckRecordResult">
        <result property="id" column="id" />
        <result property="planId" column="plan_id" />
        <result property="planName" column="plan_name" />
        <result property="workGroupId" column="work_group_id" />
        <result property="checkUserId" column="check_user_id" />
        <result property="startTime" column="start_time" />
        <result property="finishTime" column="finish_time" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectCheckRecordVo">
        t.id, t.plan_id, t.plan_name, t.work_group_id, t.check_user_id, t.start_time, t.finish_time, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="queryRecordTree" resultType="com.soft.webadmin.vo.check.CheckRecordVO">
        select <include refid="selectCheckRecordVo" />, t.create_time create_date,
        (select show_name from common_sys_user where user_id = t.check_user_id) check_user_name
        from sp_check_record t
        <where>
            and t.deleted_flag = 1
            <if test="beginDate != null and beginDate != ''">
                and date_format(t.start_time, '%Y-%m-%d') &gt;= date_format(#{beginDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and date_format(t.start_time, '%Y-%m-%d') &lt;= date_format(#{endDate}, '%Y-%m-%d')
            </if>
            <if test="planIdList != null and planIdList.size() > 0">
                and t.plan_id in
                <foreach collection="planIdList" item="planId" open="(" separator="," close=")">
                    #{planId}
                </foreach>
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="detail" resultType="com.soft.webadmin.vo.check.CheckRecordVO">
        select <include refid="selectCheckRecordVo" />,
        (select show_name from common_sys_user where user_id = t.check_user_id) check_user_name
        from sp_check_record t where t.id = #{id}
    </select>
    
</mapper>