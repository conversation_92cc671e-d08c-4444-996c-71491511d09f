package com.soft.webadmin.dao.contingency;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.contingency.EarlyWarningQueryDTO;
import com.soft.webadmin.model.contingency.EarlyWarning;
import com.soft.webadmin.vo.contingency.EarlyWarningVO;
import com.soft.webadmin.vo.daping.xswt.SafetyMonitor.EarlyWarningStatisVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预警记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-17
 */
public interface EarlyWarningMapper extends BaseMapper<EarlyWarning> {

    List<EarlyWarningVO> queryList(EarlyWarningQueryDTO queryDTO);

    List<EarlyWarningStatisVO> earlyWarningStatis(@Param("queryDate") String queryDate);

}
