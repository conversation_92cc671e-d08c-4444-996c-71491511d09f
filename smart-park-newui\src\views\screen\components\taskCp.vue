<template>
  <div class="taskbar">
    <div class="menubar">
      <window-bar></window-bar>
      <TransitionGroup name="list">
        <div class="task-icon" v-for="task in windowArray"  :key="task.id">
          <task-icon :task="task"></task-icon>
        </div>
      </TransitionGroup>
    </div>
    <div class="toolbar">
      <img src="/src/assets/window/iphone.png" alt=" " class="iphone"  @click="handleIphone">
      <div class="toolbar-time" v-html="state.time"></div>
      <el-tooltip content="显示桌面" placement="top">
        <div class="show-desktop" @click="handleShow">
          <div class="mainline"></div>
        </div>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'

import { useWindowStore } from '@/store/modules/window.js'

import windowBar from './windowBar/index.vue'
import taskIcon from './taskIcon/index.vue'

let timer

const windowStore = useWindowStore()

let { windowArray } = storeToRefs(windowStore)

const state = reactive({
  time: dayjs().format('HH:mm <br> YYYY/MM/DD')
})

onMounted(() => {
  clockStart()
})

onBeforeUnmount(() => {
  clearInterval(timer)
})

// 手机端
const handleIphone = () => {
  let windowTask = windowArray.value.find(item => item.url === import.meta.env.VITE_BASE_IPHONE_URL)

  if(windowTask){
    windowStore.cancelMin(windowTask.id);
    windowStore.goTop(windowTask.id);
  }else {
    windowStore.push({
      id: new Date().getTime(),
      title: '手机端',
      url: import.meta.env.VITE_BASE_IPHONE_URL,
      icon: 'iphone',
      isSize: false,
    })
  }
}

// 计时器
const clockStart = () => {
  timer = setInterval(() => {
    state.time = dayjs().format('HH:mm <br> YYYY/MM/DD')
  },10000)
}

// 显示桌面
const handleShow = () => {
  windowStore.allcancelMin()
}

</script>

<style scoped lang="less">
.taskbar{
  display: flex;
  width: 100%;
  .menubar{
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .toolbar{
    flex-shrink: 0;
    display: flex;
    align-items: center;
    margin-left: 10px;
    .iphone{
      width: 26px;
      height: 26px;
      cursor: pointer;
    }
    &-time{
      width: 80px;
      color: #FFFFFF;
      text-align: right;
      font-size: 12px;
      margin: 0 10px;
    }
  }
}

.task-icon{
  margin-left: 5px;
}

.show-desktop{
  width: 1px;
  height: 30px;
  padding: 6px 12px;
}

.show-desktop:hover{
  .mainline{
    width: 1px;
    height: 18px;
    background-color: #fff;
  }
}

.list-enter-active{
  animation: list-in 0.2s ease ;
}

.list-leave-active{
  animation: list-in 0.2s ease reverse;
}

@keyframes list-in {
  0% {
    transform: translateY(50px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
