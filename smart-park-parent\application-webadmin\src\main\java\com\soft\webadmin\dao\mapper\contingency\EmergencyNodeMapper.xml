<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.contingency.EmergencyNodeMapper">
    <resultMap type="com.soft.webadmin.model.contingency.EmergencyNode" id="EmergencyNodeResult">
        <result property="id" column="id" />
        <result property="emergencyId" column="emergency_id" />
        <result property="nodeName" column="node_name" />
    </resultMap>

    <sql id="selectEmergencyNodeVo">
        select t.id, t.emergency_id, t.node_name from cm_emergency_node t
    </sql>
    
</mapper>