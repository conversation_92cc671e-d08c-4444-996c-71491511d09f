package com.soft.webadmin.dto.equipment;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * SpEquipmentVO视图对象
 *
 * <AUTHOR>
 * @date 2023-04-06
 */
@ApiModel("EquipmentOmExcelDTO视图对象")
@Data
@ColumnWidth(value = 15)
public class EquipmentOmExcelDTO {

    @ExcelProperty(value = "设备编号")
    @ColumnWidth(value = 25)
    private String equipmentCode;

    @ExcelProperty(value = "*设备名称")
    @ColumnWidth(value = 20)
    private String equipmentName;

    @ExcelProperty(value = "设备类型")
    private String equipmentTypeName;

    @ExcelProperty(value = "设备型号")
    private String equipmentModel;

    @ExcelProperty(value = "*安装位置")
    @ColumnWidth(value = 25)
    private String spaceFullName;

    @ExcelProperty(value = "生产厂家")
    private String factory;

    @ExcelProperty(value = "保修开始日期")
    private String warrantyBeginDate;

    @ExcelProperty(value = "保修结束日期")
    private String warrantyEndDate;

    @ExcelProperty(value = "管理部门")
    @ColumnWidth(value = 25)
    private String deptName;

}