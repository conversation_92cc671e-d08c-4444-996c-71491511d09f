<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.sparePart.SparePartDetailMapper">
    <resultMap type="com.soft.webadmin.model.sparePart.SparePartDetail" id="SparePartDetailResult">
        <result property="id" column="id" />
        <result property="sparePartId" column="spare_part_id" />
        <result property="storehouseId" column="storehouse_id" />
        <result property="inventoryQuantity" column="inventory_quantity" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectSparePartDetailVo">
        t.id, t.spare_part_id, t.storehouse_id, t.inventory_quantity, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="getList" parameterType="com.soft.webadmin.dto.sparePart.SparePartDetailQueryDTO" resultType="com.soft.webadmin.vo.sparePart.SparePartFullDetailVO">
        select <include refid="selectSparePartDetailVo"/>,
        i.spare_part_name, i.spare_part_no, i.classify_id, i.model, i.bar_code, i.unit, i.unit_price, s.storehouse_name, c.classify_name
        from sp_spare_part_detail t
        left join sp_spare_part_info i on t.spare_part_id = i.id
        left join sp_spare_part_storehouse s on t.storehouse_id = s.id
        left join sp_spare_part_classify c on c.id = i.classify_id
        <where>
            and i.deleted_flag = 1
            <if test="nameOrNo != null and nameOrNo != ''">
                and (spare_part_name like concat('%', #{nameOrNo}, '%') or spare_part_no like concat('%', #{nameOrNo}, '%'))
            </if>
            <if test="classifyId != null">
                and c.id_path like concat('%', #{classifyId}, '%')
            </if>
            <if test="classifyIds != null and classifyIds.size > 0">
                and
                <foreach collection="classifyIds" item="classifyId" separator=" or " open="(" close=")">
                    c.id_path like concat('%', #{classifyId}, '%')
                </foreach>
            </if>
            <if test="storehouseId != null">
                and storehouse_id = #{storehouseId}
            </if>
            <if test="storehouseState != null">
                and s.state = #{storehouseState}
            </if>
            <if test="hasInventory != null and hasInventory == 1">
                and t.inventory_quantity > 0
            </if>
        </where>
        order by i.create_time desc, t.create_time desc
    </select>

    <select id="getListByCondition" resultType="com.soft.webadmin.vo.sparePart.SparePartFullDetailVO">
        select <include refid="selectSparePartDetailVo"/>, i.spare_part_no
        from sp_spare_part_detail t, sp_spare_part_info i where t.spare_part_id = i.id
        and t.deleted_flag = 1 and i.deleted_flag = 1
        and i.spare_part_no in
        <foreach collection="sparePartNos" item="sparePartNo" separator="," open="(" close=")">
            #{sparePartNo}
        </foreach>
        and t.storehouse_id = #{storehouseId}
    </select>
    
</mapper>