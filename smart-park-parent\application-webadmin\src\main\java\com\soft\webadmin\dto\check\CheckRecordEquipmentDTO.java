package com.soft.webadmin.dto.check;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * CheckRecordEquipmentDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckRecordEquipmentDTO对象")
@Data
public class CheckRecordEquipmentDTO {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "数据验证失败，主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "检查记录id")
    private Long recordId;

    @ApiModelProperty(value = "设备id")
    private Long equipmentId;

    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编号")
    private String equipmentNo;

    @ApiModelProperty(value = "设备位置")
    private String equipmentSpace;

    @ApiModelProperty(value = "设备状态（1正常，2故障，3离线）")
    private Integer state;

    @ApiModelProperty(value = "上报时间")
    private Date reportTime;

}
