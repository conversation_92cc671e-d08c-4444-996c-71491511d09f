package com.soft.webadmin.controller.face;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.face.FaceRepositoryDTO;
import com.soft.webadmin.dto.face.FaceRepositoryQueryDTO;
import com.soft.webadmin.dto.face.FaceRepositorySaveDTO;
import com.soft.webadmin.service.face.FaceRepositoryService;
import com.soft.webadmin.vo.face.FaceRepositoryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 人脸库控制器类
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Api(tags = "人脸库接口管理")
@RestController
@RequestMapping("/face/repository")
public class FaceRepositoryController {

    @Resource
    private FaceRepositoryService faceRepositoryService;


    @ApiOperation("列表查询")
    @PostMapping("/list")
    public ResponseResult<MyPageData<FaceRepositoryVO>> list(@RequestBody FaceRepositoryQueryDTO faceRepositoryQueryDTO) {
        MyPageData<FaceRepositoryVO> pageData = faceRepositoryService.list(faceRepositoryQueryDTO);
        return ResponseResult.success(pageData);
    }


    @ApiOperation("新增或修改")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@RequestBody FaceRepositorySaveDTO faceRepositorySaveDTO) {
        faceRepositoryService.saveOrUpdate(faceRepositorySaveDTO);
        return ResponseResult.success();
    }


    @ApiOperation("删除")
    @GetMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        faceRepositoryService.delete(id);
        return ResponseResult.success();
    }
}
