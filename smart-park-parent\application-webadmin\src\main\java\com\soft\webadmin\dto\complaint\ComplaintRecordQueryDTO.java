package com.soft.webadmin.dto.complaint;


import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class ComplaintRecordQueryDTO extends MyPageParam {

    @ApiModelProperty("编号")
    private String code;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("类型：1投诉；2建议；")
    private Integer type;

    @ApiModelProperty("处理状态：1待查看；2进行中；3已处理；")
    private Integer handleStatus;

    @ApiModelProperty("上报日期（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date reportDateStart;

    @ApiModelProperty("上报日期（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date reportDateEnd;

    @ApiModelProperty("上报人id")
    private Long reportUserId;

}
