<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form ref="queryFormRef" :model="state.queryForm" :inline="true">
        <el-form-item prop="roomName">
          <el-input v-model="state.queryForm.roomName" placeholder="会议室名称"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="queryList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #operate>
      <el-button type="primary" :icon="Plus" @click="onAdd">新建会议室</el-button>
    </template>

    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" tooltip-effect="light">
        <el-table-column v-for="tableHeader in state.tableHeader" :label="tableHeader.label" :prop="tableHeader.prop"
                         show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="tableHeader.prop === 'roomName'">
              <el-space>
                <el-image
                  style="width: 2.0rem; height: 2.0rem"
                  :src="imgTransfer(row.roomImg)"
                  :zoom-rate="1.2"
                  :z-index="2"
                  :preview-teleported="true"
                  :preview-src-list="[imgTransfer(row.roomImg)]"
                  :initial-index="0"
                  fit="fill"
                >
                  <template #error>
                    <div class="error-image">
                      <el-icon size="40"><icon-picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <span> {{ row.roomName }} </span>
              </el-space>
            </div>
            <div v-else-if="tableHeader.prop === 'isEnableApproval'">
              <el-text v-if="row.isEnableApproval === 1" type="success">是</el-text>
              <el-text v-else type="info">否</el-text>
            </div>
            <div v-else>
              <span>
                {{ row[tableHeader.prop] ? row[tableHeader.prop] : '/' }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Document"
              @click.prevent="onView(scope.row)">
              查看
            </el-button>
            <el-button
              link
              type="primary"
              icon="Edit"
              @click.prevent="onEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.prevent="onDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />

      <meeting-room-dialog ref="roomDialog" @onClose="queryList" />
      <meeting-room-view-dialog ref="roomDetailDialog" @onClose="queryList" />
    </template>
  </page-common>
</template>

<script setup>
import {Plus, Refresh, Search} from "@element-plus/icons-vue";
import MeetingRoomDialog from "@/views/parkOperation/meeting/manager/component/meetingRoomDialog.vue";
import {ElMessage, ElMessageBox} from "element-plus";
import MeetingRoomViewDialog from "@/views/parkOperation/meeting/manager/component/meetingRoomViewDialog.vue";
import {deleteMeetingRoomAPI, listMeetingRoomAPI} from "@/api/parkOperation/meetingRoom.js";
import { Picture as IconPicture } from '@element-plus/icons-vue'
import { calcPageNo } from '@/utils/util.js';

const queryFormRef = ref()

const roomDialog = ref()

const roomDetailDialog = ref()

const state = reactive({
  tableHeight: 100,
  queryForm: {},
  tableData: [],
  tableHeader: [
    {label: '会议室编号', prop: 'roomNo'},
    {label: '会议室名称', prop: 'roomName'},
    {label: '会议室位置', prop: 'roomSpaceFullName'},
    {label: '容纳人数', prop: 'capacity'},
    {label: '管理员', prop: 'ownerName'},
    {label: '电话', prop: 'ownerPhone'},
    {label: '预约审批', prop: 'isEnableApproval'}
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
    pageSizes: [10, 20, 30, 50]
  }
})

const queryList = () => {
  let params = {
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  if (state.queryForm.roomName) {
    params.roomName = state.queryForm.roomName
  }
  listMeetingRoomAPI(params).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList
      state.pageParam.total = res.data.totalCount
    }
  })
}

const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  queryList()
}

// 分页每页查询数量修改触发事件
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize
  queryList();
}
// 分页页数修改触发事件
const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum
  queryList();
}

// 新建会议室
const onAdd = () => {
  roomDialog.value.open('新建会议室')
}

// 编辑会议室
const onEdit = (val) => {
  roomDialog.value.open('编辑会议室', JSON.parse(JSON.stringify(val)))
}

const onView = (val) => {
  roomDetailDialog.value.open('会议室详情', JSON.parse(JSON.stringify(val)))
}

// 删除会议室
const onDelete = (val) => {
  ElMessageBox.confirm(
    '是否删除当前会议室?',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize);

    deleteMeetingRoomAPI({id: val.id}).then(res => {
      if (res.success) {
        ElMessage.success('删除成功!')
      } else {
        ElMessage.error('删除失败, ' + res.errorMessage)
      }
      queryList()
    }).catch(e => ElMessage.error('发生异常，' + e))
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除！',
    })
  })
}


const imgTransfer = (name) => {
  if (name) {
    return import.meta.env.VITE_BASE_URL + name
  }
  return name
}


onMounted(() =>{
  queryList()
})

</script>

<style scoped lang="less">

</style>
