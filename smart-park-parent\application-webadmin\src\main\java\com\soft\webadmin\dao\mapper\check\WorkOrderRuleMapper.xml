<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.WorkOrderRuleMapper">
    <resultMap type="com.soft.webadmin.model.check.WorkOrderRule" id="SpWorkOrderRuleResult">
        <result property="id" column="id" />
        <result property="ruleName" column="rule_name" />
        <result property="equipmentTypeId" column="equipment_type_id" />
        <result property="equipmentTypeIdPath" column="equipment_type_id_path" />
        <result property="priority" column="priority" />
        <result property="predictRepairDuration" column="predict_repair_duration" />
        <result property="handleLimitDuration" column="handle_limit_duration" />
        <result property="dispatchType" column="dispatch_type" />
        <result property="workGroupIds" column="work_group_ids" />
        <result property="laborCost" column="labor_cost" />
        <result property="treatmentPlan" column="treatment_plan" />
        <result property="deleteFlag" column="delete_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectSpWorkOrderRuleVo">
        select id, rule_name, equipment_type_id, equipment_type_id_path, priority, predict_repair_duration,
        handle_limit_duration, dispatch_type, work_group_ids, labor_cost, treatment_plan, create_time, create_user_id,
        update_user_id, update_time, delete_flag
        from sp_work_order_rule
    </sql>
    <insert id="insertBatch">
        insert into sp_work_order_rule
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.ruleName}, #{item.subType}, #{item.priority}, #{item.predictRepairDuration},
             #{item.handleLimitDuration}, #{item.dispatchType}, #{item.workGroupIds}, #{item.laborCost}, #{item.treatmentPlan},
             #{item.createTime}, #{item.createUserId}, #{item.updateUserId}, #{item.updateTime}, #{item.deleteFlag})
        </foreach>
    </insert>

    <select id="queryOfEquipmentTypeId" resultType="com.soft.webadmin.model.check.WorkOrderRule">
        <include refid="selectSpWorkOrderRuleVo"/>
        where (find_in_set(equipment_type_id, #{equipmentTypeIds}) or equipment_type_id = 0)
        and delete_flag = 1
        order by length(equipment_type_id_path) desc limit 1
    </select>

</mapper>