<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.face.FaceRepositoryMapper">
    <resultMap type="com.soft.webadmin.model.face.FaceRepository" id="FaceRepositoryResult">
        <result property="id" column="id" />
        <result property="listType" column="list_type" />
        <result property="username" column="username" />
        <result property="sex" column="sex" />
        <result property="phone" column="phone" />
        <result property="companyName" column="company_name" />
        <result property="certificateType" column="certificate_type" />
        <result property="certificateNum" column="certificate_num" />
        <result property="faceUrl" column="face_url" />
        <result property="faceIndexCode" column="face_index_code" />
        <result property="faceGroupIndexCode" column="face_group_index_code" />
        <result property="faceGroupName" column="face_group_name" />
        <result property="remark" column="remark" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectFaceRepositoryVo">
        select id, list_type, username, sex, phone, company_name, certificate_type, certificate_num, face_url, face_index_code, face_group_index_code, face_group_name, remark, deleted_flag, create_user_id, create_time, update_user_id, update_time from face_repository
    </sql>

</mapper>