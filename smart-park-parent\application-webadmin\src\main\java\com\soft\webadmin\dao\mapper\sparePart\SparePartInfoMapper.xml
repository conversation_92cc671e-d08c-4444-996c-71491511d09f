<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.sparePart.SparePartInfoMapper">
    <resultMap type="com.soft.webadmin.model.sparePart.SparePartInfo" id="SparePartInfoResult">
        <result property="id" column="id" />
        <result property="businessType" column="business_type" />
        <result property="sparePartName" column="spare_part_name" />
        <result property="sparePartNo" column="spare_part_no" />
        <result property="classifyId" column="classify_id" />
        <result property="model" column="model" />
        <result property="barCode" column="bar_code" />
        <result property="unit" column="unit" />
        <result property="unitPrice" column="unit_price" />
        <result property="inventoryQuantity" column="inventory_quantity" />
        <result property="earlyWarningLeast" column="early_warning_least" />
        <result property="earlyWarningMost" column="early_warning_most" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectSparePartInfoVo">
        t.id, t.business_type, t.spare_part_name, t.spare_part_no, t.classify_id, t.model, t.bar_code, t.unit, t.unit_price, t.inventory_quantity, t.early_warning_least, t.early_warning_most, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="getList" parameterType="com.soft.webadmin.dto.sparePart.SparePartInfoQueryDTO" resultType="com.soft.webadmin.vo.sparePart.SparePartInfoVO">
        select <include refid="selectSparePartInfoVo" />, c.classify_name,
        (
        case when ((early_warning_least is not null and inventory_quantity &lt; early_warning_least)
        or
        (early_warning_most is not null and inventory_quantity > early_warning_most))
        then 1 else 0 end
        ) is_warning
        from sp_spare_part_info t
        left join sp_spare_part_classify c on c.id = t.classify_id
        <where>
            and t.deleted_flag = 1
            <if test="nameOrNo != null and nameOrNo != ''">
                and (spare_part_name like concat('%', #{nameOrNo}, '%') or spare_part_no like concat('%', #{nameOrNo}, '%'))
            </if>
            <if test="classifyId != null">
                and c.id_path like concat('%', #{classifyId}, '%')
            </if>
        </where>
        order by is_warning desc, id desc
    </select>

    <select id="getListByIds" resultType="com.soft.webadmin.vo.sparePart.SparePartInfoVO">
        select <include refid="selectSparePartInfoVo" />,
        (select classify_name from sp_spare_part_classify where id = t.classify_id) classify_name
        from sp_spare_part_info t where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="getListByStorehouseIds" resultType="com.soft.webadmin.vo.sparePart.SparePartInfoVO">
        select <include refid="selectSparePartInfoVo" />,
        (select classify_name from sp_spare_part_classify where id = t.classify_id) classify_name
        from sp_spare_part_info t, sp_spare_part_detail d where t.id = d.spare_part_id
        and storehouse_id = #{storehouseId}
        order by t.classify_id
    </select>

    <select id="detail" resultType="com.soft.webadmin.vo.sparePart.SparePartInfoVO">
        select <include refid="selectSparePartInfoVo" />,
        (select classify_name from sp_spare_part_classify where id = t.classify_id) classify_name,
        (select show_name from common_sys_user where user_id = t.update_user_id) update_user_name
        from sp_spare_part_info t where id = #{id}
    </select>
    
</mapper>