<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form inline :model="state.queryForm" ref="queryFormRef">
        <el-form-item prop="spaceIds">
          <el-cascader
            clearable
            placeholder="所在位置"
            v-model="state.queryForm.spaceIds"
            :options="state.spaceOptions"
            :props="{
              checkStrictly: true,
              expandTrigger: 'blur',
              value: 'id',
              label: 'name',
            }"
          ></el-cascader>
        </el-form-item>
        <el-form-item prop="keyWord">
          <el-input v-model="state.queryForm.keyWord" placeholder="搜索关键词"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="queryList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #operate>
      <div class="button">
        <el-button type="primary" icon="Plus" @click="onAdd">新建线路连接</el-button>
        <el-popover v-model:visible="state.showPopover" placement="bottom" :width="180" trigger="click"
                    @hide="hiddenPopover">
          <template #reference>
            <el-button type="primary" icon="Upload">导入</el-button>
          </template>
          <template #default>
            <el-upload
              ref="uploadRef"
              :http-request="onUploadFile"
            >
              <template #trigger>
                <el-button size="small" type="primary">上传数据</el-button>
              </template>
              <template #default>
                <el-button
                  type="primary"
                  size="small"
                  style="margin-left: 10px"
                  @click="downTemplate"
                >下载模板
                </el-button>
              </template>
              <template #tip>
                <div class="el-upload__tip" align="center">
                  只能上传一个xlsx文件
                </div>
              </template>
            </el-upload>
          </template>
        </el-popover>
        <el-button type="primary" icon="Download" @click="onExport">导出</el-button>
      </div>
    </template>

    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" tooltip-effect="light">
        <el-table-column label="信息点" align="center">
          <el-table-column
            :key="index"
            :label="item.label"
            :prop="item.prop"
            show-overflow-tooltip="true"
            v-for="(item, index) in state.tableHeader.one">
            <template #default="scope">
              <span v-if="item.prop === 'portType'">
                {{ scope.row.portType === 1 ? "语音" : scope.row.portType === 2 ? "数据" : scope.row.portType === 3 ? "光纤" : "设备" }}
              </span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="配线架" align="center">
          <el-table-column
            :key="index"
            :label="item.label"
            :prop="item.prop"
            show-overflow-tooltip="true"
            v-for="(item, index) in state.tableHeader.two">
          </el-table-column>
        </el-table-column>
        <el-table-column label="交换机" align="center">
          <el-table-column
            :key="index"
            :label="item.label"
            :prop="item.prop"
            show-overflow-tooltip="true"
            v-for="(item, index) in state.tableHeader.three">
          </el-table-column>
        </el-table-column>
        <el-table-column :label="state.tableHeader.four.label" width="170" :prop="state.tableHeader.four.prop">
        </el-table-column>
        <el-table-column label="操作" align="center" width="212">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Document"
              @click.prevent="onView(scope.row)"
            >
              详情
            </el-button>
            <el-button
              link
              type="primary"
              icon="Edit"
              @click.prevent="onEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.prevent="onDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="state.pageParam.pageNum"
        :page-sizes="state.pageSizes"
        :page-size="state.pageParam.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="state.pageParam.total"
      >
      </el-pagination>

      <!--   子组件   -->
      <link-dialog ref="linkDialogRef" @onClose="queryList" />

      <!--   线路连接详情   -->
      <link-detail ref="linkDetailRef" />
    </template>
  </page-common>
</template>


<script setup>

import PageCommon from "@/components/basic/pageCommon.vue";
import {Refresh, Search} from "@element-plus/icons-vue";
import {treeAPI} from "@/api/iotManagement/space.js";
import {deleteCabLinkAPI, listCabLinkAPI} from "@/api/systemIntegration/cablingSystem/cablingLink.js";
import {exportFile, uploadFile} from "@/utils/down.js";
import {ElMessage, ElMessageBox} from "element-plus";
import LinkDialog from "@/views/systemIntegration/cablingSystem/cablingLink/component/linkDialog.vue";
import LinkDetail from "@/views/systemIntegration/cablingSystem/cablingLink/component/linkDetail.vue";

// 查询表单引用
const queryFormRef = ref()

// 上传组件
const uploadRef = ref()

// 新建或编辑
const linkDialogRef = ref()
// 详情页面
const linkDetailRef = ref()


const state = reactive({
  tableHeight: 100,
  queryForm: {},
  spaceOptions: [],
  tableData: [],
  tableHeader: {
    one: [
      { label: '端口', prop: 'firstPortCode' },
      { label: '类型', prop: 'portType' },
      { label: '设备名称', prop: 'firstEquipmentName' },
      { label: '位置', prop: 'firstSpaceFullName' }
    ],
    two: [
      { label: '端口', prop: 'secondPortCode' },
      { label: '设备名称', prop: 'secondEquipmentName' },
      { label: '位置', prop: 'secondSpaceFullName' }
    ],
    three: [
      { label: '端口', prop: 'thirdPortCode' },
      { label: '设备名称', prop: 'thirdEquipmentName' },
      { label: '位置', prop: 'thirdSpaceFullName' }
    ],
    four: { label: '创建时间', prop: 'createTime' }
  },
  // 显示提示框
  showPopover: false,
  pageSizes: [10, 20, 30, 50],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})


/**
 * 查询空间下拉选项列表
 * @returns {Promise<unknown>}
 */
const querySpaces = () => {
  return new Promise((resolve, reject) => {
    nextTick(() => {
      let query = {
        deep: 4
      }
      treeAPI(query).then(res => {
        state.spaceOptions = res.data
        resolve()
      })
    })
  })
}

// 查询列表数据
const queryList = () => {
  let param = assembleQueryParam();
  listCabLinkAPI(param).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList
      state.pageParam.total = res.data.totalCount
    }
  })
}


// 组装请求参数
const assembleQueryParam = () => {
  let param = {
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  if (state.queryForm.keyWord) {
    param.keyWord = state.queryForm.keyWord
  }
  if (state.queryForm.spaceIds) {
    param.spacePath = state.queryForm.spaceIds.join(',')
  }
  return param;
}

// 重置
const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 12
  queryList()
}

const handleSizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize
  queryList()
}

const handleCurrentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum
  queryList()
}


// 新建
const onAdd = () => {
  linkDialogRef.value.open('新建线路连接')
}

// 编辑
const onEdit = (val) => {
  linkDialogRef.value.open('编辑线路连接', val)
}

// 详情
const onView = (val) => {
  linkDetailRef.value.open('线路连接详情', val)
}

// 删除
const onDelete = (val) => {
  ElMessageBox.confirm(
    '是否删除当前线路连接?',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    let params = {
      id: val.id
    }
    deleteCabLinkAPI(params).then(res => {
      if (res.success) {
        ElMessage.success('删除成功！')
        queryList()
      } else {
        ElMessage.error('删除失败！')
      }
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除！',
    })
  })
}


// 隐藏提示框
const hiddenPopover = () => {
  uploadRef.value.clearFiles()
}


// 上传数据文件
const onUploadFile = async (fileData) => {
  let {data: res} = await uploadFile('/cablingSystem/link/import', fileData.file);
  if (res.success) {
    ElMessage.success('上传成功！');

    // 2s 后关闭气泡框
    setTimeout(() => {
      state.showPopover = false
    }, 2000)
  } else {
    ElMessage.error("上传 " + res.errorMessage)
  }
  queryList()
}

// 下载模板
const downTemplate = async () => {
  let param = {
    template: true
  }
  await exportFile('/cablingSystem/link/export', param, '线路连接模板.xlsx')
}

// 导出数据
const onExport = async () => {
  let param = {
    template: false
  }
  if (state.queryForm.keyWord) {
    param.keyWord = state.queryForm.keyWord
  }
  if (state.queryForm.spaceIds) {
    param.spacePath = state.queryForm.spaceIds.join(',')
  }
  await exportFile('/cablingSystem/link/export', param, '线路连接数据.xlsx')
}



onMounted(() => {
  querySpaces()
  queryList()
})
</script>


<style scoped lang="less">

</style>
