import { request } from "@/utils/request";

// 获取部门
export const departListAPI = (data) => {
  return request('post','/admin/upms/sysDept/list',data)
}

// 部门添加
export const departAddAPI = (data) => {
  return request('post','/admin/upms/sysDept/add',data)
}

// 部门编辑
export const departEidtAPI = (data) => {
  return request('post','/admin/upms/sysDept/update',data)
}

// 部门删除
export const departDelAPI = (data) => {
  return request('post','/admin/upms/sysDept/delete',data)
}


// 获取组织架构
export const getOrganization = (data) => {
  return request('get','/admin/upms/sysDept/getOrganization',data)
}
