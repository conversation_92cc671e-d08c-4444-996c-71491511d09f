<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="queryName">
          <el-input v-model="formInline.queryName" placeholder="设备名称/编号" />
        </el-form-item>
        <el-form-item prop="spacePath">
          <el-cascader v-model="formInline.spacePath" :options="state.spaceOptions" :props="optionsProps" clearable
            placeholder="设备位置" />
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="formInline.status" clearable placeholder="状态">
            <el-option v-for="(val, key) in state.statusOptions" :value="key" :label="val" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :formatter="item.formatter" :width="item.width" />
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button :disabled="scope.row.status === 1" link icon="CircleCheckFilled"
              :type="scope.row.status === 1 ? 'info' : 'primary'" @click="alarmHandle(scope.row, 1)">
              {{ scope.row.orderId ? '已生成工单' : '生成工单' }}
            </el-button>
            <el-button :disabled="scope.row.status === 1" link icon="CircleCloseFilled"
              :type="scope.row.status === 1 ? 'info' : 'primary'" @click="alarmHandle(scope.row, 2)">
              取消告警
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />
    </template>
  </page-common>
</template>

<script setup>
import { ElButton, ElMessage, ElMessageBox, ElTag } from 'element-plus';
import { treeAPI } from '@/api/iotManagement/space.js';
import { alarmHandleAPI, getAlarmCenterPageAPI } from '@/api/iotManagement/alarmCenter.js';

const formInlineRef = ref();
const formInline = reactive({});

// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'path',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};

const state = reactive({
  spaceOptions: [],
  tableData: [],
  tableHeight: 100,
  tableHeader: [
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'equipmentNo',
      label: '设备编号'
    },
    {
      prop: 'equipmentSpaceFullName',
      label: '设备位置'
    },
    {
      prop: 'content',
      label: '告警信息'
    },
    {
      prop: 'createTime',
      label: '上报时间',
      width: 160
    },
    {
      prop: 'duration',
      label: '持续时间',
      formatter: (row, column, cellValue) => {
        let val = timeInterval(cellValue);
        if (val.includes('天')) {
          return h(ElTag, { type: 'danger' }, { default: () => val });
        } else if (val.includes('小时')) {
          return h(ElTag, { type: 'warning' }, { default: () => val });
        } else {
          return h(ElTag, { type: 'success' }, { default: () => val });
        }
      }
    },
    {
      prop: 'status',
      label: '状态',
      formatter: (row, column, cellValue) => {
        if (cellValue === 0) {
          return h(ElTag, { type: 'danger' }, { default: () => state.statusOptions[cellValue] });
        } else if (cellValue === 1) {
          return h(ElTag, { type: 'success' }, { default: () => state.statusOptions[cellValue] });
        }
      }
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  statusOptions: {
    0: '未处理',
    1: '已处理'
  }
});

onMounted(() => {
  getList();
  getSpaceTree();
});

// 空间位置
const getSpaceTree = () => {
  let query = {
    deep: 4
  };
  treeAPI(query).then(res => {
    state.spaceOptions = res.data;
  });
};

// 分钟数换算
const timeInterval = (interval) => {
  let day_rough = interval / (60 * 24);
  let hour_rough = (interval % (60 * 24)) / 60;
  let minute_rough = interval % 60;

  let minute = Math.floor(minute_rough);
  let hour = hour_rough > 0 ? Math.floor(hour_rough) : 0;
  let day = day_rough > 0 ? Math.floor(day_rough) : 0;

  let duration = '';
  if (day > 0) {
    duration += day + '天';
  }
  if (hour > 0) {
    duration += hour + '小时';
  }
  if (minute > 0) {
    duration += minute + '分钟';
  }
  if (!duration) {
    duration = '1分钟内';
  }
  return duration;
};

const getList = () => {
  let query = {
    ...formInline,
    ...state.pagetion
  };
  getAlarmCenterPageAPI(query).then((res) => {
    state.tableData = res.data.dataList;
    state.pagetion.total = res.data.totalCount;
  });
};

const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};

const alarmHandle = (row, operate) => {
  ElMessageBox.confirm('确定要' + (operate === 1 ? '生成工单' : '取消告警') + '吗?', '提醒', {
    type: 'warning',
  }).then(() => {
    alarmHandleAPI({ id: row.id, operate: operate }).then((res) => {
      if (res.success) {
        ElMessage.success('操作成功');
        getList();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};
</script>
