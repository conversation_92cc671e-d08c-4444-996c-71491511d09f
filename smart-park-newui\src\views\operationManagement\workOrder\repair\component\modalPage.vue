<template>
  <dialog-common ref="dialog" :formRef="ruleFormRef" :width="900" class="dialogTextarea" title="报事报修" @onClose="onClose"
                 @submit="submit">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-suffix=":" label-width="120px">

      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="报修设备" prop="equipmentId">
            <el-tag v-if="form.equipmentName" class="tag" style="margin-right: 5px;">{{ form.equipmentName }}</el-tag>
            <el-button class="button-new-tag ml-1" size="small" @click="openSelectPage"> + 选择设备</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位置" prop="spaceId">
            <el-cascader
                v-model="form.spaceId"
                :options="state.spaceOptions"
                :props="optionsProps"
                clearable
                placeholder="请选择位置信息"/>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="内容描述" prop="content">
            <el-input v-model="form.content" :autosize="{ minRows: 5 }" :maxlength="500" placeholder="内容描述" show-word-limit
                      type="textarea"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属部门" prop="reportDeptId">
            <el-cascader
                v-model="form.reportDeptId"
                :clearable="true"
                :loading="state.deptInfo.impl.loading"
                :options="state.deptInfo.impl.dropdownList"
                :props="{
                      value: 'deptId',
                      label: 'deptName',
                      emitPath: false,
                      checkStrictly: true,
                      expandTrigger: 'hover',
                    }"
                placeholder="请选择所属部门"
                @change="onDeptIdValueChange"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报修人" prop="reportUserId">
            <el-select v-model="form.reportUserId" clearable filterable placeholder="请选择报修人"
                       @change="reportUserChangeHandle">
              <el-option v-for="item in state.userList" :label="item.showName" :value="item.userId"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="reportUserPhone">
            <el-input v-model.number="form.reportUserPhone" clearable maxlength="11" placeholder="请输入联系电话"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" clearable placeholder="请选择优先级">
              <el-option v-for="(val, key) in state.priorityOptions" :label="val" :value="Number(key)"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="附件" prop="imgList">
            <el-upload
                v-model:file-list="form.imgList"
                :class="{'disUpload': form.imgList.length == 3 }"
                :http-request="httpRequest"
                :limit="3"
                :on-preview="handleImgPreview"
                :on-success="(response) => { fileSuccess(response, form.imgList) }"
                list-type="picture-card"
                accept="image/*">
              <el-icon>
                <Plus/>
              </el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  支持格式：jpg、png ，单个文件不能超过5MB，最多支持3张
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">派单信息</div>
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="执行人" prop="workUserId">
            <el-select v-model="form.workUserId" clearable filterable placeholder="请选择执行人"
                       @change="changeWorkUser">
              <el-option v-for="item in state.workUserOptions" :label="item.showName + (item.deptName ? ' - ' + item.deptName : '')"
                         :value="item.userId"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" :autosize="{ minRows: 5 }" :maxlength="500" placeholder="请输入备注" show-word-limit
                      type="textarea"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <select-operate ref="modal" :multiple="false" @rowClick="equipSelect"/>

  </dialog-common>
  <el-dialog id="imgDialog" v-model="state.dialogVisible">
    <img :src="state.dialogImageUrl" alt="Preview Image" style="width: 100%;" w-full/>
  </el-dialog>
</template>

<script setup>
import {ElMessage} from 'element-plus';

import {submitRepairAPI} from '@/api/operationManagement/repair.js';
import {getWorkUserListAPI} from '@/api/operationManagement/workOrder.js';
import {treeAPI} from '@/api/iotManagement/space.js';
import {getDeptListAPI, listUsersAPI} from '@/api/settingSystem/user.js';

import {annexUpload} from '@/api/file.js';
import {DropdownWidget} from '@/utils/widget.js';

const emit = defineEmits(['submit'])

let ruleFormRef = ref();
let dialog = ref();
const modal = ref();

const form = reactive({
  type: 1,
  priority: 1,
  reportDeptId: null,
  reportUserId: null,
  imgList: [],
});

const state = reactive({
  priorityOptions: {
    1: '普通',
    2: '紧急',
    3: '特急'
  },
  deptInfo: {
    impl: new DropdownWidget(loadDeptDropdownList, true, 'deptId'),
  },
  spaceOptions: [],
  userList: [],
  workUserOptions: [], // 派单人
  dialogVisible: false,
  dialogImageUrl: '',
  rules: {
    type: [{required: true, message: '请选择报修类别', trigger: 'change'},],
    spaceId: [{required: true, message: '请选择位置', trigger: 'change'}],
    content: [{required: true, message: `请输入内容描述`, trigger: 'blur'}],
    reportDeptId: [{required: true, message: '请选择所属部门', trigger: 'change'}],
    reportUserId: [{required: true, message: '请选择报修人', trigger: 'change'}],
    reportUserPhone: [{required: true, message: '请输入联系电话', trigger: 'blur'}],
    workUserId: [{required: true, message: '请选择执行人', trigger: 'blur'}]
  }
});

onMounted(() => {
  getWorkUser()
  getSpaceTree();
  state.deptInfo.impl.onVisibleChange(true);
})

// 查询部门
function loadDeptDropdownList() {
  return new Promise((resolve, reject) => {
    getDeptListAPI({}).then((res) => {
      resolve(res.data.dataList);
    }).catch((e) => {
      reject(e);
    });
  });
}

// 部门选中值改变
const onDeptIdValueChange = (value) => {
  form.reportUserId = null;
  getUserList(value);
}

// 查询系统用户
const getUserList = (deptId) => {
  if (deptId) {
    listUsersAPI({deptId: deptId}).then((res) => {
      state.userList = res.data.dataList;
    });
  } else {
    state.userList = [];
  }
};

// 选择人员
const reportUserChangeHandle = (val) => {
  let index = state.userList.findIndex(e => e.userId === val);
  form.reportUserPhone = state.userList[index]?.phone;
};

// 打开选择设备窗口
const openSelectPage = () => {
  modal.value.open();
};

// 设备选择
const equipSelect = (row) => {
  form.equipmentId = row.equipmentId;
  form.equipmentName = row.equipmentName;
  if (row.spaceId) {
    form.spaceId = row.spaceId;
    ruleFormRef.value.validateField('spaceId');
  }
};

// 覆盖Http
const httpRequest = (option) => {
  const formData = new FormData()
  formData.append('file', option.file)
  return annexUpload(formData)
}

// 查看图片
const handleImgPreview = (uploadFile) => {
  state.dialogImageUrl = uploadFile.url
  state.dialogVisible = true
}

// 上传图片
const fileSuccess = (response, fileList) => {
  fileList[fileList.length - 1].filePath = response.data.filePath
}

// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'id',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};

// 查询位置信息
const getSpaceTree = () => {
  treeAPI({deep: 4}).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
};

// 执行人选择
const changeWorkUser = (val) => {
  form.workUserName = state.workUserOptions.find(item => item.userId == val)?.showName
}

const open = () => {
  dialog.value.open()
}

const onClose = () => {
  form.equipmentId = ''
  form.equipmentName = '';
  form.workUserName = ''
}

// 获取执行人
const getWorkUser = () => {
  getWorkUserListAPI().then((res) => {
    state.workUserOptions = res.data;
  });
}

// 提交
const submit = () => {
  form.img = form.imgList.map(img => img.filePath).join(',')

  let dispatchDTO = {
    remark: '',
    workUserId: '',
    workUserName: ''
  }

  Object.keys(dispatchDTO).forEach(key => {
    dispatchDTO[key] = form[key]
  })

  submitRepairAPI({dispatchDTO, repairDTO: form}).then((res) => {
    if (res.success) {
      ElMessage.success('报单已提交');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

defineExpose({
  form,
  open
})
</script>
