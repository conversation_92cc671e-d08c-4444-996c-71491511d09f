package com.soft.webadmin.controller.check;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.check.WorkOrderRuleDTO;
import com.soft.webadmin.dto.check.WorkOrderRuleQueryDTO;
import com.soft.webadmin.service.check.WorkOrderRuleService;
import com.soft.webadmin.vo.check.WorkOrderRuleVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 工单规则控制器类
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@RestController
@RequestMapping("/check/work/order/rule")
public class WorkOrderRuleController {

    @Resource
    private WorkOrderRuleService workOrderRuleService;

    @GetMapping("/list")
    public ResponseResult<MyPageData<WorkOrderRuleVO>> list(WorkOrderRuleQueryDTO workOrderRuleQueryDTO) {
        MyPageData<WorkOrderRuleVO> pageData = workOrderRuleService.list(workOrderRuleQueryDTO);
        return ResponseResult.success(pageData);
    }


    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@RequestBody @Validated WorkOrderRuleDTO workOrderRuleDTO) {
        return workOrderRuleService.saveOrUpdate(workOrderRuleDTO);
    }


    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        return workOrderRuleService.delete(id);
    }
}
