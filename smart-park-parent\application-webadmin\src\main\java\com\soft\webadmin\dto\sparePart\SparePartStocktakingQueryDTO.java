package com.soft.webadmin.dto.sparePart;

import com.soft.common.core.object.MyPageParam;
import com.soft.webadmin.enums.BusinessTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SparePartStocktakingDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartStocktakingDTO对象")
@Data
public class SparePartStocktakingQueryDTO extends MyPageParam {

    // @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房", required = true)
    // private BusinessTypeEnums businessType = BusinessTypeEnums.OPERATIONS;

    @ApiModelProperty(value = "盘点单号/盘点单名称")
    private String noOrName;

    @ApiModelProperty(value = "盘点结果：0草稿、1无盈亏、2有盈亏")
    private Integer result;

    @ApiModelProperty(value = "盘点仓库")
    private Long storehouseId;

}
