package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * WorkQuoteRelationDTO对象
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@ApiModel("WorkQuoteRelationDTO对象")
@Data
public class WorkSparePartReturnDTO {

    @ApiModelProperty(value = "报价id")
    @NotNull(message = "报价id不能为空！")
    private Long quoteId;

    @ApiModelProperty(value = "报价关联备件id")
    @NotNull(message = "报价关联备件id不能为空！")
    private Long relationId;

    @ApiModelProperty(value = "退回数量")
    @NotNull(message = "退回数量不能为空！")
    @Min(value = 1, message = "退回数量不能小于1！")
    private Integer returnQuantity;

}
