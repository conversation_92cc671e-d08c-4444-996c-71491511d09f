<template>
  <div class="toolMain">
    <div class="drawMain" @mousedown="mouseDownHandle" @mouseup="mouseUpHandle">
      <Editor></Editor>
    </div>
    <page-tags></page-tags>
  </div>
</template>

<script setup>
import Editor from './editor/index.vue'
import pageTags from './pageTags/index.vue'

import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent, isClickComponent } = storeToRefs(webtopo)

// 点击画布
const mouseDownHandle = (e) => {
  isClickComponent.value = false
}

const mouseUpHandle = (e) => {
  if (!isClickComponent.value) {
    curComponent.value = null
  }
}

</script>

<style lang='less' scoped>
.toolMain {
  height: 100%;
  overflow: hidden;

  .drawMain {
    height: calc(100% - 41px);
    position: relative;
    background-image: linear-gradient(90deg, rgba(60, 10, 30, .04) 3%, transparent 0), linear-gradient(1turn, rgba(60, 10, 30, .04) 3%, transparent 0);
    background-size: 20px 20px;
    background-position: 50%;
    background-repeat: repeat;
  }
}
</style>
