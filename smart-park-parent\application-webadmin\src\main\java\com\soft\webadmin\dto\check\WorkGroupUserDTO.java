package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * SpWorkGroupUserDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@ApiModel("WorkGroupUserDTO对象")
@Data
public class WorkGroupUserDTO {

    @ApiModelProperty(value = "${column.columnComment}")
    @NotNull(message = "数据验证失败，${column.columnComment}不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "班组id")
    private Long workGroupId;

    @ApiModelProperty(value = "成员id")
    private Long userId;

}
