  <template>
  <el-drawer :modelValue="drawer" :before-close="cancelClick" size="750">
    <template #header>
      <h4>维保计划信息</h4>
    </template>
    <template #default>
      <el-form ref="ruleFormRef" :model="form" label-width="100px" label-suffix=":">
        <div>
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">基本信息</div>
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="计划名称" prop="templateName">
                {{ form.planName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作班组" prop="workGroupId"> {{ form.workGroupName }} </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="首保时间" prop="scheduleRule">
                {{ form.scheduleRule }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="维保周期" prop="frequency"> {{ form.frequency }} 月 </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="排班时间" prop="scheduleTime">
                {{ form.scheduleTime }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="派单时间" prop="advanceTime"> 提前 {{ form.advanceTime }} 天 </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="有效期" prop="beginDate"> {{ form.beginDate }} 至 {{ form.endDate }} </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="state">
                <el-tag :type="form.state == 1 ? 'success' : 'danger'">{{ form.state == 1 ? '启用' : '停用' }}</el-tag>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注" prop="remark">
                {{ form.remark }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div>
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">维保点</div>
          </div>
          <el-table :data="form.checkPlanPointList">
            <el-table-column label="序号" type="index" width="60" />
            <el-table-column v-for="column in state.columnList" :label="column.label" :prop="column.prop" :key="column.prop" />
          </el-table>
        </div>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
const props = defineProps({
  drawer: {
    type: Boolean,
    default: false,
  },
});
let { drawer } = toRefs(props);
const emit = defineEmits(['cancelClick']);
const form = ref({});
const state = reactive({
  columnList: [
    { prop: 'dataName', label: '设备' },
    { prop: 'spaceFullName', label: '位置' },
    { prop: 'templateName', label: '检查模板' },
  ],
});

const cancelClick = () => {
  emit('cancelClick');
};

defineExpose({
  form,
});
</script>

<style lang="less" scoped>
</style>
