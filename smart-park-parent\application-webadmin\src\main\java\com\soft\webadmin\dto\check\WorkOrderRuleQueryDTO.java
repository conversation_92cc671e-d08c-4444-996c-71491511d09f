package com.soft.webadmin.dto.check;


import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrderRuleQueryDTO extends MyPageParam {

    @ApiModelProperty("工单规则名称")
    private String ruleName;

    @ApiModelProperty("设备类型")
    private String equipmentTypeId;

}
