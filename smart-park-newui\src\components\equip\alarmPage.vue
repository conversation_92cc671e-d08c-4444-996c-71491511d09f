<template>
  <el-dialog v-model="dialogVisible" title="报警记录" :width="900" :close-on-click-modal="false" class="dialogCommon"
    :before-close="close" :append-to-body="true">
    <el-table :height="state.tableHeight" :data="warnRecord" row-key="menuId">
      <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :width="item.width" :label="item.label"
        :align="item.align" :formatter="item.formatter" />
    </el-table>
  </el-dialog>
</template>

<script setup>
import { ElTag } from 'element-plus'

const props = defineProps({
  warnRecord: { // 记录
    type: Array,
    default: []
  },
})

let { warnRecord } = toRefs(props)

let dialogVisible = ref(false)

const state = reactive({
  tableHeight: 500,
  tableHeader: [
    {
      prop: 'level',
      label: '严重程度',
      formatter: (row, column, cellValue) => {
        let obj =
        {
          '一般': '#c9c900',
          '重要': '#f74f4f',
          '紧急': '#c50000'
        }
        return h('span', { style: { color: obj[cellValue] } }, cellValue)
      }
    },
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'equipmentNo',
      label: '设备编号'
    },
    {
      prop: 'createTime',
      label: '报警时间',
      width: 110  
    },
    {
      prop: 'equipmentSpaceFullName',
      label: '安装位置'
    },
    {
      prop: 'warningType',
      label: '告警类型',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { type: cellValue == 'WARN' ? 'danger' : 'warning' }, { default: () => cellValue == 'WARN' ? '告警' : '故障' })
      }
    },
    {
      prop: 'content',
      label: '内容描述'
    }
  ]
})

const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>

<style scoped>

</style>