<template>
  <el-drawer v-model="drawerRef" :title="state.title" size="35%" :before-close="close">
    <template #default>
      <el-form :model="state.tableData" label-position="left" label-suffix=":">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">群组信息</div>
        </div>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="组织名称">
              {{ state.tableData.name }}
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="组织类型">
              <el-space>
                <el-tag>{{ state.tableData.type }}</el-tag>
              </el-space>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="详细地址">
              {{ state.tableData.address }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="22">
            <el-form-item label="关联区域">
              <el-space wrap>
                <div v-for="spaceName in state.tableData.spaceNames">
                  <el-button text>{{ spaceName }}</el-button>
                </div>
              </el-space>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">通行权限</div>
        </div>
        <el-row :gutter="20">
          <el-col :span="22">
            <el-form-item label="通行权限">
              <el-space wrap>
                <div v-for="equipment in state.tableData.equipments">
                  <el-button text>{{ equipment.equipmentName }}</el-button>
                </div>
              </el-space>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">其他信息</div>
        </div>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-form-item label="备注">
              {{ state.tableData.remark }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
// 实例化 drawer抽屉对象
const drawerRef = ref(false)

const state = reactive({
  title: '',
  // 详情数据对象
  tableData: ''
})


/**
 * 打开抽屉
 * @param title
 * @param val
 */
const open = (title, val) => {
  state.title = title
  drawerRef.value = true
  state.tableData = val
}

/**
 * 关闭抽屉
 */
const close = () => {
  state.title = ''
  state.tableData = {}
  drawerRef.value = false
}

defineExpose({
  open
})
</script>

<style scoped lang="less">

.el-drawer__header {
  background-color: #F5F7FA;
  margin-right: 0;
}

.divFlex {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.divRight {
  margin-left: 8px;
}
.divLeft {
  display: inline-block;

  width: 5px;
  height: 18px;
  background-color: #3f9eff;
}
</style>
