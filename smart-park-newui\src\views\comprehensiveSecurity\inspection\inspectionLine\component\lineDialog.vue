<template>
  <el-dialog v-model="showDialog" :title="state.title" width="40%" :before-close="close" align-center class="dialogCommon">
    <template #default>
      <div class="content">
        <el-form :model="state.dataForm" ref="dataFormRef" :rules="state.rules" label-width="85" label-suffix=":">
          <el-form-item label="编号" prop="code">
            <el-input v-model="state.dataForm.code" placeholder="巡更路线编号" />
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="state.dataForm.name" placeholder="巡更路线名称" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="state.dataForm.remark" placeholder="备注" />
          </el-form-item>
          <el-form-item prop="pointIds">
            <el-transfer ref="transferRef" id="transfer"
                         v-model="state.dataForm.pointIds"
                         :data="state.pointsData"
                         :titles="['待选巡更点','已选巡更点']"
                         target-order="push">
              <template #default="{ option }">
                <span>{{ option.label }}</span>
              </template>
            </el-transfer>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import Sortable from 'sortablejs'
import {listInspectionPointAPI} from "@/api/comprehensiveSecurity/inspectionPoint.js"
import {saveOrUpdateInspectionLineAPI} from "@/api/comprehensiveSecurity/inspectionLine.js";
import {ElMessage} from "element-plus";


// 父组件中的事件
const emits = defineEmits(['onClose'])
// 父组件中的属性
const props = defineProps([])
let {} = toRefs(props)


// 是否显示 dialog 组件
const showDialog = ref(false)

// 穿梭框对象
let transferRef = ref()


// 表单对象
const dataFormRef = ref()

const state = reactive({
  title: '',
  dataForm: {},
  // 巡更点列表
  pointsData: [],
  rules: {
    code: [
      { required: true, message: '编号不能为空', trigger: 'blur'}
    ],
    name: [
      { required: true, message: '名称不能为空', trigger: 'blur'}
    ],
    pointIds: [
      { required: true, message: '巡更点不能为空', trigger: 'blur'}
    ]
  }
})


const open = (title, val) => {
  showDialog.value = true
  nextTick(() => {
    state.title = title
    if (val) {
      let data = JSON.parse(JSON.stringify(val))
      state.dataForm.id = data.id
      state.dataForm.code = data.code
      state.dataForm.name = data.name
      state.dataForm.remark = data.remark
      if (data.pointIds) {
        state.dataForm.pointIds = data.pointIds.split(',')
      } else {
        state.dataForm.pointIds = []
      }
    }
    queryPoints()
  })
}


const close = () => {
  dataFormRef.value.resetFields()
  dataFormRef.value.clearValidate()
  state.pointsData = []
  showDialog.value = false
  emits('onClose')
}


// 新增或修改
const onSubmit = () => {
  dataFormRef.value.validate((valid, fields) => {
    if (valid) {
      saveOrUpdateInspectionLineAPI(state.dataForm).then(res => {
        if (res.success) {
          ElMessage.success('保存成功！')
          close()
        } else {
          ElMessage.error('保存失败，' + res.errorMessage)
        }
      })
    }
  })
}

// 查询巡更点列表
const queryPoints = () => {
  listInspectionPointAPI({}).then(res => {
    let points = res.data.dataList;
    for (let point of points) {
      state.pointsData.push({
        key: point.id,
        label: point.name
      });
    }
    transferSort()
  })
}

const transferSort = () => {
    const transfer = transferRef.value.$el
    const rightPanel = transfer.getElementsByClassName("el-transfer-panel")[1].getElementsByClassName("el-transfer-panel__body")[0];
    const rightEl = rightPanel.getElementsByClassName("el-transfer-panel__list")[0]
    Sortable.create(rightEl, {
      onEnd: (evt) => {
        const { oldIndex, newIndex } = evt;
        const temp = state.dataForm.pointIds[oldIndex]
        if (!temp || temp === 'undefined') {
          return
        }
        // 解决右边最后一项从右边拖左边，有undefined的问题
        // value.value[oldIndex] = value.value[newIndex]
        // value.value[newIndex] = temp
        state.dataForm.pointIds.splice(oldIndex, 1)
        state.dataForm.pointIds.splice(newIndex,0, temp)
      }
    })
    rightPanel.ondragover = (ev) => {
      ev.preventDefault()
    }
}


// 向父组件暴露方法或属性
defineExpose({
  open
})
</script>

<style scoped lang="less">

.content {
  .el-input {
    width: 85%;
  }
  .el-select,.el-cascader{
    width: 100%;
  }
  .el-input-number{
    width: 85%;
    .el-input {
      width: 100%;
    }
  }
  .el-textarea{
    width: 85%;
  }
}
</style>
