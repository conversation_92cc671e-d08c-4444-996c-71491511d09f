<template>
  <dialog-common ref="dialog" title="预警设置" @submit="submit" :formRef="ruleFormRef" :width="500">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":">
      <el-row>
        <el-col>
          <el-form-item label="设备编号" prop="equipmentNo">
            <el-input v-model="form.equipmentNo" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="设备名称" prop="equipmentName">
            <el-input v-model="form.equipmentName" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="日用量上限" prop="dayUpperLimit">
            <el-input-number v-model="form.dayUpperLimit" :min="1" placeholder="请输入日用量上限" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="月用量上限" prop="monthUpperLimit">
            <el-input-number v-model="form.monthUpperLimit" :min="1" placeholder="请输入月用量上限" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { addWarningSettingAPI } from '@/api/iotManagement/warningSetting.js';
import { ElMessage, ElMessageBox } from 'element-plus';

const emit = defineEmits(['submit']);
const dialog = ref();
const ruleFormRef = ref();
const form = reactive({
  level: '重要',
  subType: 'ENERGY',
});
const state = reactive({
  rules: {},
});

const open = () => {
  dialog.value.open();
};

/** 保存 */
const submit = () => {
  form.name = form.equipmentName + '能耗预警设置';
  const ruleJson = {
    dayUpperLimit: form.dayUpperLimit,
    monthUpperLimit: form.monthUpperLimit,
  };
  form.ruleJson = JSON.stringify(ruleJson);
  form.job = 'energyWarningHandler';
  addWarningSettingAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success('设置成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

defineExpose({
  form,
  open,
});
</script>
