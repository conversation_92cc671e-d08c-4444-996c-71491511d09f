package com.soft.webadmin.dao.check;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.model.check.WorkOrderRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单规则Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public interface WorkOrderRuleMapper extends BaseMapper<WorkOrderRule> {


    /**
     * 批量新增
     *
     * @param insertWorkOrderRules
     */
    void insertBatch(List<WorkOrderRule> insertWorkOrderRules);

    /**
     * 根据设备类型查询工单规则
     * @param equipmentTypeIds
     * @return
     */
    WorkOrderRule queryOfEquipmentTypeId(@Param("equipmentTypeIds") String equipmentTypeIds);
}
