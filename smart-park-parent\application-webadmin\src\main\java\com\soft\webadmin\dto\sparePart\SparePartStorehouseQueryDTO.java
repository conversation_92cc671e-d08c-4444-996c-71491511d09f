package com.soft.webadmin.dto.sparePart;

import com.soft.common.core.object.MyPageParam;
import com.soft.webadmin.enums.BusinessTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SparePartWarehouseDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@ApiModel("SparePartStorehouseDTO对象")
@Data
public class SparePartStorehouseQueryDTO extends MyPageParam {

    // @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房", required = true)
    // private BusinessTypeEnums businessType = BusinessTypeEnums.OPERATIONS;

    @ApiModelProperty(value = "名称或编号")
    private String nameOrNo;

    @ApiModelProperty(value = "负责人id")
    private Long headUserId;

    @ApiModelProperty(value = "状态（0锁定，1启用）")
    private Integer state;

}
