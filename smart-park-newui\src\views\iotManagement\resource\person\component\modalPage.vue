<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef" :width="900" @onClose="onClose">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="120px" label-suffix=":">
      <el-row>
        <el-col :span="12">
          <el-form-item label="人员姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入人员姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-select v-model="form.sex" placeholder="请选择性别">
              <el-option v-for="(val, key) in state.sexOptions" :value="val" :label="val" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="人员类型" prop="type">
            <el-select v-model="form.type" placeholder="人员类型">
              <el-option v-for="item in state.typeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入手机号码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="出生日期" prop="birthday">
            <el-date-picker v-model="form.birthday" type="date" :clearable="false" :value-format="'YYYY-MM-DD'"
              placeholder="出生日期" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属组织" prop="orgName">
            <el-input v-model="form.orgName" placeholder="请输入组织名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="证件类型" prop="idCardType">
            <el-select v-model="form.idCardType" placeholder="证件类型">
              <el-option v-for="item in state.idCardTypeOptions" :key="item.value" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号" prop="idCard">
            <el-input v-model="form.idCard" placeholder="请输入证件号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-radio-group v-model="state.ridao">
        <el-radio-button value="门禁权限" label="门禁权限" />
        <el-radio-button value="梯控权限" label="梯控权限" />
      </el-radio-group>

      <div class="scroll">
        <el-tree

          ref="treeRef"
          :data="state.entranceData"
          :props="defaultProps"
          node-key="id"
          highlight-current
          show-checkbox
          v-show="state.ridao == '门禁权限'"
          default-expand-all />

        <div v-show="state.ridao == '梯控权限'">
          <template v-for="(item, index) in state.tableData" :key="index">
            <el-checkbox
              v-model="item.checkAll"
              :indeterminate="item.isIndeterminate"
              @change="(val) => handleCheckAllChange(val, item)">
              {{item.equipmentName }}
            </el-checkbox>
            <el-checkbox-group style="margin-bottom: 10px;"
              v-model="item.checkedCities"
              @change="(val) => handleCheckedCitiesChange(val, item)">
              <el-checkbox v-for="i in item.hikEcsFloorVOList" :key="i.id" :value="i.id">{{
                i.name
              }}</el-checkbox>
            </el-checkbox-group>
          </template>
        </div>
      </div>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {
  addUserAPI,
  updateUserAPI,
  controlInfoAPI,
  entranceTreeAPI
} from '@/api/iotManagement/person.js';

import { ElMessage } from 'element-plus';
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});

const ruleFormRef = ref();
const form = reactive({
  deptPostIdList: [],
  roleIdList: [],
  dataPermIdList: []
});

const { title } = toRefs(props);
const emit = defineEmits(['submit']);

const defaultProps = {
  children: 'children',
  label: 'name',
}

const dialog = ref();

const treeRef = ref()

const state = reactive({
  sexOptions:{
    1: '男',
    2: '女'
  },
  ridao: '门禁权限',
  typeOptions: [ // 下拉列表
    { value: 1, label: '物业人员' },
    { value: 2, label: '企业人员' },
    { value: 3, label: '商家人员' },
    { value: 4, label: '访客' },
    { value: 5, label: '白名单人员' },
    { value: 99, label: '其他' }
  ],
  idCardTypeOptions: [ // 下拉列表
    { value: 1, label: '身份证' },
    { value: 2, label: '护照' },
    { value: 3, label: '户口簿' },
    { value: 4, label: '驾驶证' },
    { value: 5, label: '学生证' },
    { value: 99, label: '其他' }
  ],
  roleList: [],
  dataPermList: [],
  deptPostList: [],
  rules: {
    name: [{ required: true, message: '人员姓名不能为空', trigger: 'blur' }],
    sex: [{ required: true, message: '请选择性别', trigger: 'blur' }],
    type: [{ required: true, message: '请选择人员类型', trigger: 'change' }],
    phone: [
      { required: true, message: '手机号码不能为空', trigger: 'blur' },
      { pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/, message: '手机号码格式不正确！', trigger: 'blur' }
    ]
  },
  entranceData: [], // 门禁数据
  tableData: [], // 梯控设备
  tableDataCopy: []
});

onMounted(() => {
  getControl()
})


// 获取门禁和梯控系统
const getControl = () => {

  // 递归
  function deepTree(array) {
    array.forEach(item => {
      let equipmentList = (item.equipment || '').split('$,$').filter(i => i).map(item => {
        return {
          id: item.split('$#$')[0],
          name: `${item.split('$#$')[1]}（门禁）`,
          type: 'ENTRANCE_GUARD'
        }
      })
      item.children = [...equipmentList, ...(item.children || [])]
      if (item.children) {
        deepTree(item.children)
      }
    })
  }

  // 门禁
  entranceTreeAPI().then(res => {
    deepTree(res.data)
    state.entranceData = JSON.parse(JSON.stringify(res.data))
  })

  // 梯控
  controlInfoAPI().then(res => {
    state.tableData = JSON.parse(JSON.stringify(res.data))
    state.tableDataCopy = JSON.parse(JSON.stringify(res.data))
  })
}

// 数据回显
const echoData = (info) => {
  treeRef.value.setCheckedKeys((info.personEntranceLinks || []).map(item => item.equipmentId))

  state.tableData.forEach(item => {
    if (info.hikEcsFloorUserMap[item.equipmentName]) {
      // 选中
      item.checkedCities = info.hikEcsFloorUserMap[item.equipmentName].map(i => i.floorId)

      // 全选
      item.checkAll = item.checkedCities.length === item.hikEcsFloorVOList.length

      // 半选
      item.isIndeterminate = item.checkedCities.length > 0 && item.checkedCities.length < item.hikEcsFloorVOList.length
    }
  })
}

const open = () => {
  dialog.value.open();
};

// 全选
const handleCheckAllChange = (val, item) => {
  item.checkedCities = val ? item.hikEcsFloorVOList.map(i => i.id) : []
  item.isIndeterminate = false
}

// 单选
const handleCheckedCitiesChange = (value, item) => {
  const checkedCount = value.length
  item.checkAll = checkedCount === item.hikEcsFloorVOList.length
  item.isIndeterminate = checkedCount > 0 && checkedCount < item.hikEcsFloorVOList.length
}

/** 保存用户 */
const submit = () => {
  // 门禁
  let entranceIds = treeRef.value.getCheckedNodes().filter(item => item.type == "ENTRANCE_GUARD").map(item => item.id)

  // 梯控
  let floorIds = []
  state.tableData.forEach(item => {
    if (item.checkedCities instanceof Array) {
      floorIds = floorIds.concat(item.checkedCities);
    }
  });

  let data = {
    ...form,
    doorAuth: entranceIds.length ? 1 : -1,
    parkAuth: floorIds.length ? 1 : -1,
    entranceIds,
    floorIds
  };

  if (form.id) {
    subHandle(updateUserAPI, '编辑成功');
  } else {
    subHandle(addUserAPI, '添加成功');
  }

  function subHandle(req, title) {
    req(data).then((res) => {
      if (res.success) {
        ElMessage.success(title);
        dialog.value.close();
        emit('submit');
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  }
};

// 关闭弹框
const onClose = () => {
  state.ridao = '门禁权限'
  treeRef.value.setCheckedKeys([])
  state.tableData = JSON.parse(JSON.stringify(state.tableDataCopy))
  state.tableData.forEach(item => {
    item.checkAll = false
  })
}

defineExpose({
  form,
  open,
  echoData
});
</script>

<style lang="less" scoped>
.scroll {
  padding: 10px;
  max-height: 50vh;
  overflow: auto;
}
</style>
