<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.CheckPlanMapper">
    <resultMap type="com.soft.webadmin.model.check.CheckPlan" id="CheckPlanResult">
        <result property="id" column="id" />
        <result property="planType" column="plan_type" />
        <result property="planName" column="plan_name" />
        <result property="planMode" column="plan_mode" />
        <result property="workGroupId" column="work_group_id" />
        <result property="scheduleType" column="schedule_type" />
        <result property="scheduleRule" column="schedule_rule" />
        <result property="maintenanceCycle" column="maintenance_cycle" />
        <result property="maintenanceLast" column="maintenance_last" />
        <result property="startTime" column="start_time" />
        <result property="advanceTime" column="advance_time" />
        <result property="handleLimitDuration" column="handle_limit_duration" />
        <result property="state" column="state" />
        <result property="equipmentIds" column="equipment_ids" />
        <result property="remark" column="remark" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectCheckPlanVo">
        select id, plan_type, plan_name, plan_mode, work_group_id, schedule_type, schedule_rule, maintenance_cycle, maintenance_last, start_time, advance_time, time_limit, state, equipment_ids, remark, deleted_flag, create_user_id, create_time, update_user_id, update_time from sp_check_plan
    </sql>
    
</mapper>