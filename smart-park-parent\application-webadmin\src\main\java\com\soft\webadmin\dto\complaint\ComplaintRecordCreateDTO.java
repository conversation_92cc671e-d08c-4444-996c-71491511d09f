package com.soft.webadmin.dto.complaint;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ComplaintRecordCreateDTO {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "类型：1投诉；2建议；")
    private Integer type;

    @ApiModelProperty(value = "问题描述")
    private String content;

    @ApiModelProperty(value = "联系方式")
    private String reportUserPhone;

    @ApiModelProperty(value = "附件id列表")
    private String annexIds;
}
