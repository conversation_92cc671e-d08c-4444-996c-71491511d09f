<template>
  <div class="page">
    <div class="tree">
      <el-input v-model="filterText" placeholder="请输入搜索关键词" />
      <el-tree ref="treeRef" node-key="id" :data="state.data" :props="{ label: 'name' }" :filter-node-method="filterNode" :default-expanded-keys="state.expandedKeys"
               highlight-current accordion style="margin-top: 10px;" @node-click="handleNodeClick">
        <template #default="{ node, data }">
          <el-icon v-show="data.type == 2" style="margin-right: 5px;" color="#409EFF">
            <VideoCamera />
          </el-icon>
          {{ data.name }}
        </template>
      </el-tree>
    </div>
    <div class="video">
      <el-radio-group v-model="state.radio" class="radio-group">
        <el-radio-button :value="1">
          <img src="@/assets/img/oneIcon.png" alt="" srcset="">
        </el-radio-button>
        <el-radio-button :value="4">
          <img src="@/assets/img/fourIcon.png" alt="" srcset="">
        </el-radio-button>
      </el-radio-group>

      <!-- 一路 -->
      <div class="one-way" v-show="state.radio === 1">
        <div style="width: 100%;height: 100%;" ref="videoMain" v-if="showVideo">
        </div>
        <el-empty description="视频播放失败" v-else>
          <el-button type="primary" @click="reconnection">点击重连</el-button>
        </el-empty>
      </div>

      <!-- 四路 -->
      <div class="grid" v-show="state.radio === 4">
        <div class="four-ways" :class="{ 'active': index == state.currentIndex }" v-for="(item, index) in urlList"
             @click="handleClick(index)">
          <div style="width: 100%;height: 100%;" :ref="el => videoMainList[index] = el" v-if="showVideoList[index]">
          </div>
          <el-empty description="视频播放失败" v-else>
            <el-button type="primary" @click="reconnectionFour(index)">点击重连</el-button>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import DPlayer from 'dplayer';

import { perviewURLs, equipTree } from '@/api/iotManagement/realtime.js'

// 一路
let url
let dp;
let videoMain = ref()
const showVideo = ref(true)

// 四路
let urlList = new Array(4);
let dpList = [];
let flvPlayerList = [];
let videoMainList = ref([])
let showVideoList = ref(new Array(4).fill(true))


const filterText = ref('')
const treeRef = ref()

const state = reactive({
  radio: 1,
  data: [],
  expandedKeys: [],
  currentIndex: 0
})

onMounted(() => {
  getMonitor()
})

onBeforeUnmount(() => {
  videoDestroy()
  showVideoList.value.forEach((item,index) => {
    videoDestroyFour(index)
  });
})

// 获取监控树形
const getMonitor = () => {
  equipTree({
    subSystemEnums: 'MONITOR'
  }).then(res => {
    state.data = res.data
    state.expandedKeys = state.data.map(build => build.id)
  })
}

// 点击视频
const handleClick = (index) => {
  state.currentIndex = index
}

// 选择节点
const handleNodeClick = (node) => {
  if (node.type == 2) { // type 1空间，2设备
    perviewURLs({ equipmentId: node.id, mainStream : state.radio === 1 }).then(res => {
      handleChoose(res.data)
      // handleChoose('https://vjs.zencdn.net/v/oceans.mp4')
    })
  }
}

// 视频选择
const handleChoose = (vidoeurl) => {
  if (state.radio == 1) {  // 一路
    url = vidoeurl
    if (dp) {
      handleChange()
    } else {
      if(showVideo.value){
        initVideo()
      }else{
        reconnection()
      }
    }
  } else if (state.radio == 4) { //四路
    urlList[state.currentIndex] = vidoeurl
    if (dpList[state.currentIndex]) {
      handleChangeFour(state.currentIndex)
    } else { // 初次加载
      if(showVideoList.value[state.currentIndex]){
        initVideoFour(state.currentIndex)
      }else{
        reconnectionFour(state.currentIndex)
      }
    }
  }
}

// 初始化摄像头
const initVideo = () => {
  dp = new DPlayer({
    container: videoMain.value,
    live: true,
    autoplay: true,
    preventClickToggle: true,
    screenshot: true,
    hotkey: true,
    mutex: false,
    preload: 'auto',
    volume: 0,
    video: {
      url,
      type: 'customFlv',
    },
  });

  dp.on('play', function () {
    let flvPlayer = dp.video
    if (flvPlayer.buffered.length) {
      let end = flvPlayer.buffered.end(0);//获取当前buffered值
      let diff = end - flvPlayer.currentTime;//获取buffered与currentTime的差值
      if (diff >= 2.5) {//如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定
        dp.seek(flvPlayer.buffered.end(0));
      }
    }
  });

  dp.on('error', function () {
    if(dp){
      ElMessage({
        message: '播放失败',
        type: 'error',
      })
      videoDestroy()
      showVideo.value = false
    }
  })
}

// 四路初始化
const initVideoFour = (index) => {
  dpList[index] = new DPlayer({
    container: videoMainList.value[index],
    live: true,
    autoplay: true,
    preventClickToggle: true,
    screenshot: true,
    hotkey: true,
    mutex: false,
    preload: 'auto',
    volume: 0,
    video: {
      url: urlList[index],
      type: 'customFlv',
    },
  });

  dpList[index].on('play', function () {
    let flvPlayer = dpList[index].video
    if (flvPlayer.buffered.length) {
      let end = flvPlayer.buffered.end(0);//获取当前buffered值
      let diff = end - flvPlayer.currentTime;//获取buffered与currentTime的差值
      if (diff >= 2.5) {//如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定
        dpList[index].seek(flvPlayer.buffered.end(0));
      }
    }
  });

  dpList[index].on('error', function () {
    if(dpList[index]){
      ElMessage({
        message: '播放失败',
        type: 'error',
      })
      videoDestroyFour(index)
      showVideoList.value[index] = false
    }
  })
}

// 视频销毁
const videoDestroy = () => {
  if (dp) {
    dp.destroy()
    dp = null
  }
}

// 视频销毁四路
const videoDestroyFour = (index) => {
  if (dpList[index]) {
    dpList[index].destroy()
    dpList[index] = null
  }
}

// 重新连接
const reconnection = () => {
  showVideo.value = true
  nextTick(() => {
    initVideo()
  })
}

// 重新连接四路
const reconnectionFour = (index) => {
  showVideoList.value[index] = true
  nextTick(() => {
    initVideoFour(index)
  })
}

// 切换摄像头
const handleChange = () => {

  nextTick(() => {
    dp.switchVideo({url})
  })
}

// 切换摄像头四路
const handleChangeFour = (index) => {
  nextTick(() => {
    dpList[index].switchVideo({url: urlList[index]})
  })
}

// 过滤方法
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value)
}

watch(filterText, (val) => {
  treeRef.value.filter(val)
})

defineExpose({
  getMonitor
})
</script>

<style lang='less' scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;

  .tree {
    width: 220px;
    height: 100%;
    margin-right: 20px;
    flex-shrink: 0;
    background: #FFFFFF;
    border-radius: 10px;
    padding: 10px;
    overflow: auto;
  }

  .video {
    flex: 1;
    background: #FFFFFF;
    border-radius: 10px;
    overflow: hidden;
    padding: 18px;

    .radio-group {
      margin-bottom: 10px;

      :deep(.el-radio-button__inner) {
        padding: 6px;
      }

      img {
        width: 26px;
      }
    }

    .one-way {
      height: calc(100% - 65px);
      cursor: pointer;
      border-radius: 5px;
      background-image: url('@/assets/img/videobg.png');
      background-size: 100% 100%;
    }

    .grid {
      height: calc(100% - 65px);
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      grid-gap: 10px;

      .four-ways {
        cursor: pointer;
        border-radius: 5px;
        background-image: url('@/assets/img/videobg.png');
        background-size: 100% 100%;
        overflow: hidden;
      }

      .active {
        border: 3px solid #16BCC2;
      }
    }
  }
}
</style>
