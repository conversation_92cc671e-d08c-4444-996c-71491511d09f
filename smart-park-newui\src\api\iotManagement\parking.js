import { request } from "@/utils/request";

// 查询车场信息方法
export const info = (query) => {
  return request('get', '/core/parking/info', query, 'F')
}

// 车辆通行记录分页查询方法
export const pageList = (query) => {
  return request('get', '/core/record/pageList', query, 'F')
}

// 黑白名单分页查询
export const rollList = (query) => {
  return request('get', '/equipment/whitelist/list', query, 'F')
}

// 通行权限
export const accessAuthority = (query) => {
  return request('get', '/equipment/whitelist/accessAuthority', query, 'F')
}

// 新增&编辑
export const saveAPI = (data) => {
  return request('post', '/equipment/whitelist/save',data)
}

// 删除
export const deleteAPI = (data) => {
  return request('post', '/equipment/whitelist/delete', data, 'F')
}

// 同步
export const syncData = (query) => {
  return request('get', '/equipment/whitelist/sync', query, 'F')
}
