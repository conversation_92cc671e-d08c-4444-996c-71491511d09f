package com.soft.webadmin.controller.face;


import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.service.face.FaceEventService;
import org.springframework.http.RequestEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@NoAuthInterface
@RestController
@RequestMapping("/face/event")
public class FaceEventController {

    @Resource
    private FaceEventService faceEventService;

    /**
     * 重点目标识别事件（黑名单）
     *
     * @param requestEntity
     */
    @RequestMapping("/black/recognition/eventRcv")
    public ResponseResult<Void> receiveBlackRecognition(RequestEntity<String> requestEntity) {
        faceEventService.receiveBlackRecognition(requestEntity);
        return ResponseResult.success();
    }
}
