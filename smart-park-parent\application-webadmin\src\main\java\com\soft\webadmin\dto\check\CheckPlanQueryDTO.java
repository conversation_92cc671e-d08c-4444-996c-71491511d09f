package com.soft.webadmin.dto.check;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CheckPlanDTO对象
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@ApiModel("CheckPlanQueryDTO对象")
@Data
public class CheckPlanQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "检查计划类型（PATROL_INSPECTION巡检，MAINTENANCE维保）")
    private String planType;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "状态（0停用，1启用）")
    private Integer state;

    @ApiModelProperty(value = "巡检方式（1人工巡检，2智能巡检）")
    private Integer planMode;

}
