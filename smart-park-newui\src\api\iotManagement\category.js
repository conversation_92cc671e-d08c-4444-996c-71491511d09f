import { request } from "@/utils/request.js";

// 获取设备分类列表
export const equipemtListAPI = (data) => {
  return request('get','/equipment/type/list/tree', data, 'F')
}

// 设备分类添加
export const equipmentAddAPI = (data) => {
  return request('post','/equipment/type/add',data)
}

// 设备分类编辑
export const equipmentEidtAPI = (data) => {
  return request('post','/equipment/type/update',data)
}

// 设备分类删除
export const equipmentDelAPI = (data) => {
  return request('post','/equipment/type/delete',data)
}
