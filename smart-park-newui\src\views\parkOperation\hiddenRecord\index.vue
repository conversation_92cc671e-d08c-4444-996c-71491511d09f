<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="type">
          <el-select v-model="formInline.type" placeholder="隐患类型">
            <el-option v-for="(value, key) in state.typeList" :key="key" :label="value" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item prop="level">
          <el-select v-model="formInline.level" placeholder="隐患等级">
            <el-option v-for="(value, key) in state.levelListObj" :key="key" :label="value" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="formInline.status" placeholder="隐患状态">
            <el-option v-for="(value, key) in state.statusOptions" :label="value" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item  prop="createUserName">
          <el-input v-model="formInline.createUserName" placeholder="上报人"/>
        </el-form-item>
        <el-form-item prop="date">
          <el-date-picker range-separator="至" v-model="formInline.date" type="daterange" value-format="YYYY-MM-DD"
                          start-placeholder="开始日期" end-placeholder="结束日期"
                          :clearable="false" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" />
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button link type="primary" icon="Document" @click="viewHandle(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <modal-page ref="model"></modal-page>
    </template>
  </page-common>
</template>

<script setup>
import { ElTag } from 'element-plus'

import modalPage from './component/modalPage.vue'

import { hiddenListAPI } from '@/api/parkOperation/hiddenRecord.js'

let formInlineRef = ref({})
let model = ref()

const formInline = reactive({
  date:[]
})

const state = reactive({
  levelListObj: {
    1: '一般隐患',
    2: '严重隐患',
    3: '重大隐患',
  },
  levelListColorObj: {
    1: '#ffc000',
    2: '#ff0000',
    3: '#c00000'
  },
  typeList: {
    1: '消防隐患',
    2: '安全隐患'
  },
  statusColors: {
    1: '#00b0f0',
    2: '#7f83f7',
    3: '#7f83f7',
    4: '#87d121'
  },
  statusOptions: {
    1: '未查验',
    2: '处理中',
    3: '未审核',
    4: '已完成'
  },
  drawer: false,
  tableHeight: 100,
  tableData: [
    {}
  ],
  tableHeader: [
    {
      prop: 'type',
      label: '隐患类型',
      formatter: (row, column, cellValue) => {
        return state.typeList[cellValue]
      }
    },
    {
      prop: 'level',
      label: '隐患等级',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { color: state.levelListColorObj[cellValue], class: 'status-tag'   }, { default: () => state.levelListObj[cellValue] })
      }
    },
    {
      prop: 'spaceFullName',
      label: '隐患位置'
    },
    {
      prop: 'status',
      label: '隐患状态',
      formatter: (row, column, cellValue) => {
        return h("div", [
          h("span", {
            class: "status-circle",
            style: "background-color: " + state.statusColors[cellValue],
          }),
          state.statusOptions[cellValue],
        ]);
      }
    },
    {
      prop: 'createUserName',
      label: '上报人'
    },
    {
      prop: 'createTime',
      label: '上报时间'
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

// 获取
const getList = () => {
  let query = {
    ...formInline,
    ...state.pagetion
  }

  query.beginDate = query.date[0]
  query.endDate = query.date[1]

  hiddenListAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 当前详情
const viewHandle = ({id}) => {
  model.value.open(id)
}
</script>

<style lang='less' scoped>
:deep(.status-tag){
  color: #fff;
}
</style>
