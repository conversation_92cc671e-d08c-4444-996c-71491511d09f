<template>
  <dialog-common ref="dialog" title="报价" @submit="submit" @onClose="onClose" :width="1100">
    <el-button type="primary" icon="Plus" link @click="handleChoose"
      style="float: right;margin-bottom: 10px;">选择备件</el-button>
    <el-table :data="state.tableData">
      <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
        :align="item.align" :formatter="item.formatter" :width="item.width" />
      <el-table-column align="center" label="操作">
        <template #default="{ $index }">
          <el-button link type="danger" icon="Delete" @click="handleDelete($index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="cost">
      <el-form :model="form" ref="formRef">
        <el-row>
          <el-col :span="5">
            <el-form-item label="备件费用合计">
              <el-input :model-value="sparePartCost" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="人工费用">
              <el-input :model-value="props.laborCost" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="其他费用" prop="otherCost">
              <el-input v-model="form.otherCost" type="number" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="费用合计">
              <el-input :model-value="totalCost" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="quoteRemark">
          <el-input v-model="form.quoteRemark" maxlength="200" placeholder="请输入备注" show-word-limit type="textarea"
            style="width: 97%;" />
        </el-form-item>
      </el-form>
    </div>
  </dialog-common>

  <chooseAccessories ref="choose" @submit="handleReceive" :hasInventory="1"></chooseAccessories>
</template>

<script setup>
import { ElInputNumber, ElMessage } from 'element-plus';

import chooseAccessories from '@/views/operationManagement/workOrder/component/chooseAccessories.vue'

import { quoteWorkAPI } from '@/api/operationManagement/workOrder.js'
const props = defineProps({
  laborCost: {
    type: Number,
    default: 0
  },
  orderId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['submit']);

const dialog = ref()
const choose = ref()
const formRef = ref()

const form = reactive({
  otherCost: 0
})

const state = reactive({
  tableData: [],
  tableHeader: [
    {
      prop: 'sparePartName',
      label: '备件名称'
    },
    {
      prop: 'model',
      label: '规格型号'
    },
    {
      prop: 'unit',
      label: '单位'
    },
    {
      prop: 'inventoryQuantity',
      label: '库存数量'
    },
    {
      prop: 'receiveQuantity',
      label: '领用数量',
      width: 200,
      formatter: (row, column, cellValue) => {
        return h(ElInputNumber, { modelValue: row.receiveQuantity, 'onUpdate:modelValue': (value) => row.receiveQuantity = value, max: row.inventoryQuantity, min: 1, stepStrictly: true });
      }
    },
    {
      prop: 'unitPrice',
      label: '单价（元）'
    },
    {
      prop: 'cost',
      label: '合计（元）',
      formatter: (row) => {
        row.cost = row.unitPrice * row.receiveQuantity
        return row.unitPrice * row.receiveQuantity
      }
    },
  ]
});

const open = () => {
  dialog.value.open();
}

// 配品总价
const sparePartCost = computed(() => {
  return state.tableData.reduce((pre, item) => {
    return pre + item.unitPrice * item.receiveQuantity
  }, 0)
})

// 费用总价
const totalCost = computed(() => {
  return sparePartCost.value + props.laborCost + Number(form.otherCost)
})

// 接收配件信息
const handleReceive = (list) => {
  state.tableData = [...state.tableData, ...list.filter(item => {
    item.receiveQuantity = 1
    return state.tableData.findIndex(i => i.id == item.id) == -1
  })]
}

// 删除
const handleDelete = (index) => {
  state.tableData.splice(index, 1)
}

// 选择配件
const handleChoose = () => {
  choose.value.open()
}

// 关闭配件
const onClose = () => {
  formRef.value.clearValidate()
  formRef.value.resetFields()
  state.tableData = []
}

// 提交报价
const submit = () => {

  if (!state.tableData.length) {
    return ElMessage.error('请添加配件!')
  }

  let subForm = {
    ...form,
    laborCost: props.laborCost,
    orderId: props.orderId,
    sparePartCost: sparePartCost.value,
    totalCost: totalCost.value,
    sparePartList: state.tableData.map(item => {
      return {
        cost: item.cost,
        receiveQuantity: item.receiveQuantity,
        sparePartId: item.sparePartId,
        storehouseId: item.storehouseId,
        unitPrice: item.unitPrice
      }
    })
  }

  quoteWorkAPI(subForm).then(res => {
    if (res.success) {
      ElMessage.success('报价申请成功')
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

defineExpose({
  open
});
</script>

<style lang="less" scoped>
.cost {
  margin-top: 20px;
}
</style>
