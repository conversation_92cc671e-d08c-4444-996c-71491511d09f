<template>
  <el-dialog v-model="dialogRef" :title="state.title" :width="700" @close="close" class="dialogCommon">
    <template #default>
      <el-form ref="dataFormRef" :model="state.dataForm" :rules="state.rules" label-suffix=":" label-width="120"
               class="content">
        <el-form-item label="编号" prop="roomNo">
          <el-input v-model="state.dataForm.roomNo" placeholder="请输入会议室编号"/>
        </el-form-item>
        <el-form-item label="名称" prop="roomName">
          <el-input v-model="state.dataForm.roomName" placeholder="请输入会议室名称"/>
        </el-form-item>
        <el-form-item label="位置" prop="roomSpaceId">
          <el-cascader
            clearable
            v-model="state.dataForm.roomSpaceId"
            :options="state.spaceOptions"
            :props="state.spaceOptionsProps"
            placeholder="请选择会议室位置"/>
        </el-form-item>
        <el-form-item label="容纳人数" prop="capacity">
          <el-input-number v-model="state.dataForm.capacity" :min="1" :controls="false" placeholder="请输入容纳人数"/>
        </el-form-item>
        <el-form-item label="会议室设备" prop="devices">
          <el-select v-model="state.dataForm.devices" multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="4"
                     placeholder="请输入会议室设备">
            <el-option
              v-for="device in state.deviceOptions"
              :key="device.name"
              :label="device.name"
              :value="device.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="管理员" prop="ownerId">
          <el-select v-model="state.dataForm.ownerId" clearable placeholder="请选择会议室管理员">
            <el-option
              v-for="item in state.userOptions"
              :key="item.userId"
              :label="item.showName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预约审批" prop="isEnableApproval">
          <el-switch v-model="state.dataForm.isEnableApproval" :active-value="1" :inactive-value="0" inline-prompt active-text="开启"
                     inactive-text="关闭"/>
        </el-form-item>
        <el-form-item label="会议室图片" prop="roomImg">
          <el-upload class="avatar-uploader" :action="state.action" :headers="state.headers" :show-file-list="false"
                     :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
            <img v-if="state.dataForm.roomImg" :src="imgTransfer(state.dataForm.roomImg)" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon">
              <Plus />
            </el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <el-button @click="close">关闭</el-button>
      <el-button type="primary" @click="onSave">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>

import {getPageAPI} from "@/api/settingSystem/user.js";
import {treeAPI} from "@/api/iotManagement/space.js";
import {dictionListItemAPI} from "@/api/settingSystem/dictionary.js";
import {Plus} from "@element-plus/icons-vue";
import {ElMessage} from "element-plus";
import {saveMeetingRoomAPI} from "@/api/parkOperation/meetingRoom.js";

const emits = defineEmits(['onClose'])


const dialogRef = ref(false)

const dataFormRef = ref()

const state = reactive({
  title: '',
  dataForm: {
    isEnableApproval: 0
  },
  userOptions: [],
  userMap: new Map(),
  deviceOptions: [],
  spaceOptions: [],
  spaceMap: new Map(),
  // 级联选择配置
  spaceOptionsProps: {
    label: 'name',
    value: 'id',
    checkStrictly: true,
    emitPath: false,
    expandTrigger: 'hover',
  },
  rules: {
    roomNo: [
      {required: true, message: '编号不能为空', trigger: 'blur'}
    ],
    roomName: [
      {required: true, message: '名称不能为空', trigger: 'blur'}
    ],
    roomSpaceId: [
      {required: true, message: '位置不能为空', trigger: 'blur'}
    ],
    capacity: [
      {required: true, message: '容纳人数不能为空', trigger: 'blur'}
    ],
    devices: [
      {required: true, message: '会议设备不能为空', trigger: 'blur'}
    ],
    ownerId: [
      {required: true, message: '管理员不能为空', trigger: 'blur'}
    ],
    isEnableApproval: [
      {required: true, message: '预约审批不能为空', trigger: 'blur'}
    ],
    roomImg: [
      {required: true, message: '会议室图片不能为空', trigger: 'blur'}
    ]
  },
  // 上传路径
  action: import.meta.env.VITE_BASE_URL + '/core/file/upload',
  headers: {
    Authorization: localStorage.getItem('Authorization')
  },
})



const onSave = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      let params = {
        roomNo: state.dataForm.roomNo,
        roomName: state.dataForm.roomName,
        roomImg: state.dataForm.roomImg,
        roomSpaceId: state.dataForm.roomSpaceId,
        capacity: state.dataForm.capacity,
        devices: state.dataForm.devices.join(','),
        ownerId: state.dataForm.ownerId,
        isEnableApproval: state.dataForm.isEnableApproval
      }
      if (state.dataForm.id) {
        params.id = state.dataForm.id
      }
      if (state.dataForm.ownerId) {
        params.ownerPhone = state.userMap.get(state.dataForm.ownerId).phone
      }
      if (state.dataForm.roomSpaceId) {
        let space = state.spaceMap.get(state.dataForm.roomSpaceId);
        params.roomSpacePath = space.path
        params.roomSpaceFullName = space.fullName
      }
      saveMeetingRoomAPI(params).then(res => {
        if (res.success) {
          ElMessage.success('保存成功！')
          close()
        } else {
          ElMessage.error('保存失败，' + res.errorMessage)
        }
      }).catch(e => ElMessage.error('发生异常, ' + e))
    }
  })
}

const open = (title, val) => {
  dialogRef.value = true
  nextTick(() => {
    state.title = title
    queryUsers()
    queryDevicesOfDict()
    spaceTree()
    if (val) {
      state.dataForm = val
      state.dataForm.devices = val.devices.split(',')
    }
  })
}

const close = () => {
  dialogRef.value = false
  dataFormRef.value.resetFields()
  state.dataForm = {}
  emits('onClose')
}


// 查询指派人员列表
const queryUsers = () => {
  let data = {
    sysUserDtoFilter: {
      userStatus: 0
    }
  }
  getPageAPI(data).then(res => {
    state.userOptions = res.data.dataList
    state.userOptions.forEach(user => {
      state.userMap.set(user.userId, user)
    })
  })
}

const queryDevicesOfDict = () => {
  /** 查询设备分组 */
  dictionListItemAPI({dictCode: '007'}).then((res) => {
    state.deviceOptions = res.data.cachedResultList;
  });
}

// 位置级联选择查询
const spaceTree = () => {
  let query = {
    deep: 4
  }
  treeAPI(query).then(res => {
    if (res.success) {
      state.spaceOptions = res.data
      state.spaceOptions.forEach(space => {
        transferToMap(space)
      })
    }
  })
}

const transferToMap = (space) => {
  state.spaceMap.set(space.id, space)
  if (space.children) {
    space.children.forEach(cSpace => transferToMap(cSpace))
  }
}

// 上次前校验
const beforeAvatarUpload = (rawFile) => {
  if (rawFile.type !== 'image/jpeg') {
    ElMessage.error('图片格式错误!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图片大小不能超过 2MB！')
    return false
  }
  return true
}

// 上传后返回信息
const handleAvatarSuccess = (response, uploadFile) => {
  // state.dataForm.roomImg = URL.createObjectURL(uploadFile.raw)
  state.dataForm.roomImg = response.data.filePath
}

const imgTransfer = (name) => {
  if (name) {
    return import.meta.env.VITE_BASE_URL + name
  }
  return name
}


defineExpose({
  open
})
</script>

<style scoped lang="less">
.content {
  margin-top: 10px;

  .el-input {
    width: 85%;
  }

  .el-select, .el-cascader {
    width: 85%;
  }

  .el-input-number {
    width: 85%;

    .el-input {
      width: 100%;
    }

    :deep(.el-input__inner) {
      text-align: left;
    }
  }

  .el-textarea {
    width: 85%;
  }

  :deep(.el-cascader) {
    width: 85%;
    flex-grow: 0;
  }
}




.avatar-uploader .avatar {
  width: 58px;
  height: 58px;
  display: block;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 58px;
  height: 58px;
  text-align: center;
}
</style>
