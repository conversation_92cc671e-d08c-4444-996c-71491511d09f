<template>
  <el-dialog v-model="state.dialogVisible" title="事件设置" width="450" class="dialogCommon">
    <div class="conent">
      <!-- 属性控制 -->
      <el-form label-suffix=":" v-if="state.eventInfo.eventType === 'attributeControl'">
        <el-form-item label="设备属性">
          <el-select placeholder="请选择" clearable  v-model="state.eventInfo.attribute">
            <el-option v-for="item in attributeOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 页面跳转 -->
      <el-form label-suffix=":" v-else-if="state.eventInfo.eventType === 'pageJump'">
        <el-form-item label="路由页面">
          <el-input placeholder="请输入" v-model="state.eventInfo.pageRouter"/>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="close">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
const props = defineProps({
  attributeOptions:{
    type: Array,
    default:[]
  }
})

const state = reactive({
  eventInfo: {},
  dialogVisible: false,
})


// 打开
const open = () => {
  state.dialogVisible = true
}

// 关闭
const close = () => {
  state.dialogVisible = false
}

defineExpose({
  open,
  state
})
</script>

<style lang='less' scoped></style>
