package com.soft.webadmin.dto.shifts;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * ShiftsCertificateDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsCertificateDTO对象")
@Data
public class ShiftsCertificateDTO {

    @ApiModelProperty(value = "花名册ID")
    private Long rosterId;

    @ApiModelProperty(value = "证书名称")
    private String certificateName;

    @ApiModelProperty(value = "证书有效期开始时间")
    private Date certificateStartTime;

    @ApiModelProperty(value = "证书有效期结束时间")
    private Date certificateEndTime;

    @ApiModelProperty(value = "证书照片")
    private String img;

}
