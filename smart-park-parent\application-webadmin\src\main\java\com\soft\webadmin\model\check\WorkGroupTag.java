package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.check.WorkGroupTagVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 工作班组标签对象 sp_work_group_tag
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Data
@TableName(value = "sp_work_group_tag")
public class WorkGroupTag {

    /**
     * 班组 id
     */
    private Long workGroupId;

    /**
     * 用户标签 id
     */
    private Long tagId;


    @Mapper
    public interface WorkGroupTagModelMapper extends BaseModelMapper<WorkGroupTagVO, WorkGroupTag> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        WorkGroupTag toModel(WorkGroupTagVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        WorkGroupTagVO fromModel(WorkGroupTag entity);
    }

    public static final WorkGroupTagModelMapper INSTANCE = Mappers.getMapper(WorkGroupTagModelMapper.class);
}
