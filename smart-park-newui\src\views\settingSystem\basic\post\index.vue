<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="postName">
          <el-input v-model="formInline.postName" placeholder="岗位名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" :icon="Plus" @click="addHandle">新建岗位</el-button>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableData" show-overflow-tooltip row-key="menuId">
        <el-table-column label="序号" align="center" type="index" width="55"
          :index="(state.pageParam.pageNum - 1) * state.pageParam.pageSize + 1" />
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter">
          <template #default="{ row }">
            <div v-if="item.prop === 'sysDepts'">
              <span v-if="row.sysDepts">{{ row.sysDepts.map(sysDept => sysDept.deptName).join(',') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="160">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize" :total="state.pageParam.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <modal-page ref="modal" :title="state.title" @submit="getList"></modal-page>
    </template>
  </page-common>
</template>

<script setup>
import modalPage from './component/modal.vue'
import { getPageAPI, deletePostAPI } from "@/api/settingSystem/post.js";
import { Plus, Refresh, Search } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, dayjs, ElTag } from "element-plus";
import { nextTick, reactive, ref } from "vue";
import { calcPageNo } from "@/utils/util.js";

let modal = ref()
const formInlineRef = ref();
const formInline = reactive({
  postName: "",
});
const state = reactive({
  title: "",
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'postName',
      label: '岗位名称'
    },
    {
      prop: 'sysDepts',
      label: '所属部门'
    },
    {
      prop: 'postLevel',
      label: '岗位层级'
    },
    {
      prop: 'leaderPost',
      label: '是否领导',
      formatter: (row, column, cellValue) => {
        if (cellValue) {
          return h(ElTag, { type: "danger" }, { default: () => "是" })
        } else {
          return h(ElTag, { type: "info" }, { default: () => "否" })
        }
      }
    },
    {
      prop: 'postUserNum',
      label: '岗位人数'
    }
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  }
});

onMounted(() => {
  getList();
});

/**
 * 查询列表数据
 */
const getList = () => {
  let query = {
    sysPostDtoFilter: formInline,
    pageParam: state.pageParam,
  };
  getPageAPI(query).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

/**
 * 提交查询
 */
const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

/**
 * 重置查询
 */
const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

/**
 * 分页查询（页码）
 * @param pageNum
 */
const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

/**
 * 分页查询（条数）
 * @param pageSize
 */
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

/** 打开创建岗弹窗 */
const addHandle = () => {
  state.title = '新建岗位'
  modal.value.form.postId = ''
  modal.value.open()
};

/** 打开编辑岗位弹窗 */
const editHandle = (row) => {
  state.title = '编辑岗位'
  modal.value.open()
  nextTick(() => {
    if (row.sysDepts) {
      modal.value.form.deptIds = row.sysDepts.map(sysDept => sysDept.deptId)
    }
    Object.assign(modal.value.form, row)
  })
};

/** 删除岗位 */
const deleteHandle = (row) => {
  ElMessageBox.confirm("是否删除当前岗位?", "提醒", {
    type: "warning",
  }).then(() => {
    state.pageParam.pageNum = calcPageNo(
      state.pageParam.total,
      state.pageParam.pageNum,
      state.pageParam.pageSize
    );
    deletePostAPI({ postId: row.postId }).then((res) => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    });
  });
};

</script>
