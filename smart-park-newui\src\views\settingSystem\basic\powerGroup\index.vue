<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form inline :model="state.queryForm" ref="queryFormRef">
        <el-form-item prop="name">
          <el-input v-model="state.queryForm.name" placeholder="权限组名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onQuery">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>


    <template #operate>
      <el-button type="primary" :icon="Plus" @click="onAdd">新建权限组</el-button>
    </template>


    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column :key="index" :prop="item.prop" :label="item.label"
          v-for="(item, index) in state.tableHeader">
        </el-table-column>

        <el-table-column label="操作" width="160" align="center">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click.prevent="onEdit(scope.row)">
              编辑
            </el-button>
            <el-button link type="danger" icon="Delete" @click.prevent="onDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="state.pageParam.pageNum" :page-sizes="state.pageParam.pageSizes"
        :page-size="state.pageParam.pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="state.pageParam.total">
      </el-pagination>

      <power-group-page ref="powerGroupRef" :id="state.id" :title="state.title" @onClose="onQuery" />

    </template>
  </page-common>
</template>

<script setup>

import { Plus, Refresh, Search } from "@element-plus/icons-vue";
import { deletePowerGroupAPI, listPowerGroupAPI } from "@/api/settingSystem/powerGroup.js";
import PowerGroupPage from "./component/powerGroupPage.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { calcPageNo } from '@/utils/util.js';
const powerGroupRef = ref()

let queryFormRef = ref()

const state = reactive({
  tableHeight: 100,
  queryForm: {},
  tableData: [],
  tableHeader: [
    {
      label: '权限组名称',
      prop: 'name'
    },
    {
      label: '描述',
      prop: 'desc'
    }
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
    pageSizes: [10, 20, 30, 50]
  },
  // 传给子组件属性
  title: ''
})


// 新建权限组
const onAdd = () => {
  state.title = '新增权限组'
  powerGroupRef.value.open();
}


// 编辑权限组
const onEdit = (row) => {
  state.title = '编辑权限组'
  powerGroupRef.value.open(row.id);
}


const onDelete = (row) => {
  ElMessageBox.confirm('是否删除当前权限组?',
    '提醒',
    {
      type: 'warning',
    }
  ).then(() => {
    state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize);

    const params = {
      id: row.id,
    };
    deletePowerGroupAPI(params).then((res) => {
      if (res.success) {
        ElMessage.success('删除成功');
        onQuery();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
}


// 查询
const onQuery = () => {
  let param = {
    name: state.queryForm.name,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  listPowerGroupAPI(param).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList
      state.pageParam.total = res.data.totalCount
    }
  })
}

// 重置
const onReset = () => {
  queryFormRef.value.resetFields()
  queryFormRef.value.clearValidate()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  onQuery()
}


//分页器选择展示条数
const handleSizeChange = (val) => {
  state.pageParam.pageSize = val
  onQuery()
}

//分页器点击页面数字
const handleCurrentChange = (val) => {
  state.pageParam.pageNum = val;
  onQuery()
}


onMounted(() => {
  onQuery()
})
</script>


<style scoped></style>
