package com.soft.webadmin.controller.sparePart;

import cn.hutool.core.lang.tree.Tree;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.sparePart.SparePartClassifyDTO;
import com.soft.webadmin.dto.sparePart.SparePartClassifyQueryDTO;
import com.soft.webadmin.service.sparePart.SparePartClassifyService;
import com.soft.webadmin.vo.sparePart.SparePartClassifyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 备品备件分类控制器类
 * 
 * <AUTHOR>
 * @date 2024-03-05
 */
@Api(tags = "物资分类")
@RestController
@RequestMapping("/sparePart/classify")
public class SparePartClassifyController {

    @Autowired
    private SparePartClassifyService sparePartClassifyService;

    @ApiOperation(value = "树形查询")
    @GetMapping("/tree")
    public ResponseResult<List<Tree<String>>> tree(SparePartClassifyQueryDTO queryDTO) {
        return ResponseResult.success(sparePartClassifyService.tree(queryDTO));
    }

    @ApiOperation(value = "数据查询")
    @GetMapping("/getList")
    public ResponseResult<List<SparePartClassifyVO>> getList(SparePartClassifyQueryDTO queryDTO) {
        return ResponseResult.success(sparePartClassifyService.getList(queryDTO));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@Validated @RequestBody SparePartClassifyDTO saveDTO) {
        return sparePartClassifyService.saveOrUpdate(saveDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        return sparePartClassifyService.delete(id);
    }

}
