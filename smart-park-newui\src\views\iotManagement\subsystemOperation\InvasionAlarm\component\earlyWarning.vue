<template>
  <page-common v-model="state.tableHeight" :operateBool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="queryName">
          <el-input v-model="formInline.queryName" placeholder="设备名称/编号" />
        </el-form-item>
        <el-form-item prop="spacePath">
          <el-cascader v-model="formInline.spacePath" :options="state.spaceOptions" :props="optionsProps" clearable
            placeholder="设备位置" />
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="formInline.status" placeholder="状态">
            <el-option v-for="(value, key) in state.statusOptions" :key="key" :label="value" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" :width="item.width" />
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />
    </template>
  </page-common>
</template>

<script setup>
import { ElTag } from 'element-plus'

import { treeAPI } from '@/api/iotManagement/space.js';
import { pageListAPI } from '@/api/iotManagement/equipmentwaning.js'
import dayjs from 'dayjs'
// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'path',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};

const formInlineRef = ref()
const formInline = reactive({})

const state = reactive({
  drawer: false,
  spaceOptions: [],
  statusOptions: {
    0: '未处理',
    1: '已处理'
  },
  statusTypeOptions: {
    0: 'danger',
    1: 'success'
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'equipmentNo',
      label: '设备编号'
    },
    {
      prop: 'equipmentSpaceFullName',
      label: '设备位置'
    },
    {
      prop: 'content',
      label: '告警信息'
    },
    {
      prop: 'createTime',
      label: '告警时间',
      width: 160,
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    },
    {
      prop: 'status',
      label: '状态',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { type: state.statusTypeOptions[cellValue] }, { default: () => state.statusOptions[cellValue] })
      }
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getSpaceTree()
  getList()
})

// 设备位置
const getSpaceTree = () => {
  treeAPI({ deep: 4 }).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
};

// 获取设备
const getList = () => {
  let query = {
    ...formInline,
    subType: 'INTRUSION_ALARM',
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
  }
  pageListAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}
</script>

<style lang='less' scoped></style>
