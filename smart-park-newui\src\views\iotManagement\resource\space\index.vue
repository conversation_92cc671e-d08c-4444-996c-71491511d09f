<template>
  <page-common v-model="state.tableHeight" :queryBool="false" :operate-bool="false">
    <template #table>
      <el-tabs v-model="state.activeName" class="space-tabs" @tab-click="handleClick">
        <el-tab-pane :name="tab" v-for="{ tab, columns } of state.spaceTabs">
          <template #label>
            <span class="space-tabs-label" v-if="tab === 'AREA'">
              <el-icon>
                <Location />
              </el-icon>
              <span>区域</span>
            </span>
            <span class="space-tabs-label" v-else-if="tab === 'BUILDING'">
              <el-icon>
                <OfficeBuilding />
              </el-icon>
              <span>楼栋</span>
            </span>
            <span class="space-tabs-label" v-else-if="tab === 'FLOOR'">
              <el-icon>
                <Collection />
              </el-icon>
              <span>楼层</span>
            </span>
            <span class="space-tabs-label" v-else-if="tab === 'POINT'">
              <el-icon>
                <SwitchFilled />
              </el-icon>
              <span>点位</span>
            </span>
          </template>

          <div class="space-tabs-search">
            <el-form :inline="true" :model="state.queryForm" class="space-tabs-form-inline">
              <template v-if="tab === 'AREA'">
                <el-form-item>
                  <el-input v-model="state.queryForm.name" placeholder="区域名称" />
                </el-form-item>
              </template>
              <template v-else-if="tab === 'BUILDING'">
                <el-form-item>
                  <el-cascader v-model="state.queryForm.selectId" :options="spaceOptions" :props="optionsProps"
                    clearable placeholder="请选择所在区域" />
                </el-form-item>
                <el-form-item>
                  <el-input v-model="state.queryForm.name" placeholder="楼栋名称" />
                </el-form-item>
              </template>
              <template v-else-if="tab === 'FLOOR'">
                <el-form-item>
                  <el-cascader v-model="state.queryForm.selectId" :options="spaceOptions" :props="optionsProps"
                    clearable placeholder="请选择所在楼栋" />
                </el-form-item>
                <el-form-item>
                  <el-input v-model="state.queryForm.name" placeholder="楼层名称" />
                </el-form-item>
              </template>
              <template v-else-if="tab === 'POINT'">
                <el-form-item>
                  <el-cascader v-model="state.queryForm.selectId" :options="spaceOptions" :props="optionsProps"
                    clearable placeholder="请选择所在位置" />
                </el-form-item>
                <el-form-item>
                  <el-input v-model="state.queryForm.name" placeholder="点位名称" />
                </el-form-item>
              </template>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="queryList" class="search-query">
                    <el-icon>
                      <Search />
                    </el-icon>
                    <span>查询</span>
                  </el-button>
                  <el-button type="primary" @click="resetQuery" class="search-reset">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    <span>重置</span>
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </div>
          <div class="space-tabs-btn">
            <el-button type="primary" @click="editBtn">
              <el-icon>
                <Plus />
              </el-icon>
              <span>新建{{
                tab === "AREA"
                  ? "区域"
                  : tab === "BUILDING"
                    ? "楼栋"
                    : tab === "FLOOR"
                      ? "楼层"
                      : tab === "POINT"
                        ? "点位"
                        : "空间"
              }}</span>
            </el-button>
            <el-button type="primary" icon="Download" @click="exportExcel">下载空间二维码</el-button>
          </div>
          <div class="space-tabs-table">
            <el-table :data="tableData" :height="state.tableHeight - 152" show-overflow-tooltip>
              <el-table-column v-for="column of columns" :label="column.label" :prop="column.prop"
                :width="column.width">
                <template #default="scope">
                  <div v-if="column.prop === 'qrcode'">
                    <img src="../../../../assets/qrcode.svg" @click="qrCode(scope.row)" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="160">
                <template #default="scope">
                  <el-button link type="primary" icon="Edit" @click.prevent="editBtn(scope.row)">
                    编辑
                  </el-button>
                  <el-button link type="danger" icon="Delete" @click.prevent="deleteRow(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
              :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize"
              :total="state.pageParam.total" @size-change="sizeChange" @current-change="pageChange" />
          </div>
        </el-tab-pane>
      </el-tabs>

      <el-dialog v-model="dialogVisible" title="预览" width="300px">
        <div>
          <el-col>
            <el-card>
              <img :src="equipmentQrCodeRef.qrcodeUrl" alt="二维码" />
            </el-card>
          </el-col>
        </div>
        <template #footer>
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button @click="onDownQrCode" type="primary">下载</el-button>
        </template>
      </el-dialog>

      <!-- 子组件，编辑和新建空间 -->
      <edit ref="edit" @closeClick="queryList"></edit>
    </template>
  </page-common>
</template>
<script setup>
import {
  treeAPI,
  pageListAPI,
  deleteAPI,
  qrCodeAPI,
} from "@/api/iotManagement/space.js";
import { ref } from "vue";
import { Search } from "@element-plus/icons-vue";
import { ElTable, ElMessage, ElMessageBox } from "element-plus";
import Edit from "./component/edit.vue";
import PageCommon from "@/components/basic/pageCommon.vue";

import { exportFile } from "@/utils/down.js";
import { calcPageNo } from '@/utils/util.js'

//定义子组件实例，名称要和子组件中的 ref 属性值一致
const edit = ref(null);

// 定义属性
const state = reactive({
  tableHeight: 100,
  // 激活的标签页
  activeName: ref("AREA"),

  spaceTabs: [
    {
      tab: "AREA",
      columns: [
        { label: "区域编码", prop: "code" },
        { label: "区域名称", prop: "name" },
        { label: "二维码", prop: "qrcode" },
        { label: "创建人", prop: "createUsername" },
        { label: "创建时间", prop: "createTime", width: 160 },
      ],
    },
    {
      tab: "BUILDING",
      columns: [
        { label: "楼栋编码", prop: "code" },
        { label: "楼栋名称", prop: "name" },
        { label: "区域名称", prop: "areaName" },
        { label: "二维码", prop: "qrcode" },
        { label: "创建人", prop: "createUsername" },
        { label: "创建时间", prop: "createTime", width: 160 },
      ],
    },
    {
      tab: "FLOOR",
      columns: [
        { label: "楼层编码", prop: "code" },
        { label: "楼层名称", prop: "name" },
        { label: "楼栋名称", prop: "buildingName" },
        { label: "二维码", prop: "qrcode" },
        { label: "创建人", prop: "createUsername" },
        { label: "创建时间", prop: "createTime", width: 160 },
      ],
    },
    {
      tab: "POINT",
      columns: [
        { label: "点位编码", prop: "code" },
        { label: "点位名称", prop: "name" },
        { label: "楼层名称", prop: "floorName" },
        { label: "楼栋名称", prop: "buildingName" },
        { label: "区域名称", prop: "areaName" },
        { label: "二维码", prop: "qrcode" },
        { label: "创建人", prop: "createUsername" },
        { label: "创建时间", prop: "createTime", width: 160 },
      ],
    },
  ],

  // 查询表单
  queryForm: {
    name: "",
    selectId: [],
  },

  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
});

// 区域列表
const spaceOptions = ref([]);
// 级联选择配置
const optionsProps = {
  checkStrictly: true,
  label: "name",
  value: "id",
};

// 表格列表数据
const tableData = ref([]);

// 导出
const exportExcel = async () => {
  let spaceQuery = {
    type: state.activeName,
    name: state.queryForm.name,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };

  if (state.queryForm.selectId != null && state.queryForm.selectId.length > 0) {
    spaceQuery.path = state.queryForm.selectId.join("/");
  }

  await exportFile("/core/space/qrcode/export", spaceQuery, "空间二维码.xlsx");
};

// 标签页点击事件，查询选择列表
const handleClick = (tab, event) => {
  let activeName = tab.props.name;
  state.activeName = activeName;
  state.queryForm.selectId = [];
  state.queryForm.name = "";
  state.pageParam.pageNum = 1;
  state.pageParam.pageSize = 10;

  let deep;
  if (activeName === "AREA") {
    deep = 1;
  } else if (activeName === "BUILDING") {
    deep = 1;
  } else if (activeName === "FLOOR") {
    deep = 2;
  } else if (activeName === "POINT") {
    deep = 3;
  }
  let query = {
    deep: deep,
  };
  treeAPI(query).then((res) => {
    if (res.success) {
      spaceOptions.value = res.data;
    }
  });
  queryList();
};

// 变更分页记录数
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  queryList();
};
// 变更分页页数
const pageChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  queryList();
};

// 重置查询条件
const resetQuery = () => {
  state.queryForm.selectId = [];
  state.queryForm.name = "";
  queryList();
};

const equipmentQrCodeRef = ref({
  qrcodeUrl: "",
  equipmentName: "",
});

// 获取二维码
const dialogVisible = ref(false);
const qrCode = (row) => {
  qrCodeAPI(row.id).then((res) => {
    equipmentQrCodeRef.value.qrcodeUrl = `data:image/png;base64,${res.data}`;
    equipmentQrCodeRef.value.equipmentName = row.fullName;
    dialogVisible.value = true;
  });
};

// 下载二维码
const onDownQrCode = async () => {
  // 如果浏览器支持msSaveOrOpenBlob方法（也就是使用IE浏览器的时候），那么调用该方法去下载图片
  const imgUrl = equipmentQrCodeRef.value.qrcodeUrl;
  if (window.navigator.msSaveOrOpenBlob) {
    let bstr = atob(imgUrl.split(",")[1]);
    let n = bstr.length;
    let u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    let blob = new Blob([u8arr]);
    window.navigator.msSaveOrOpenBlob(
      blob,
      equipmentQrCodeRef.value.equipmentName + "." + "png"
    );
  } else {
    // 这里就按照chrome等新版浏览器来处理
    let a = document.createElement("a");
    a.href = imgUrl;
    a.setAttribute("download", equipmentQrCodeRef.value.equipmentName);
    a.click();
  }
};

// 提交查询表单
const queryList = () => {
  let spaceQuery = {
    type: state.activeName,
    name: state.queryForm.name,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };

  if (state.queryForm.selectId != null && state.queryForm.selectId.length > 0) {
    spaceQuery.path = state.queryForm.selectId.join("/");
  }

  pageListAPI(spaceQuery).then((res) => {
    if (res.success) {
      tableData.value = res.data.dataList;
      state.pageParam.total = res.data.totalCount;
      for (let data of tableData.value) {
        let fullName = data.fullName;
        let split = fullName.split("/");
        switch (state.activeName) {
          case "BUILDING":
            data.areaName = split[0];
            break;
          case "FLOOR":
            data.areaName = split[0];
            data.buildingName = split[1];
            break;
          case "POINT":
            let length = split.length;
            if (length === 2) {
              data.areaName = split[0];
            } else if (length === 3) {
              data.areaName = split[0];
              data.buildingName = split[1];
            } else if (length === 4) {
              data.areaName = split[0];
              data.buildingName = split[1];
              data.floorName = split[2];
            }
        }
      }
    }
  });
};

// 删除
const deleteRow = (row) => {
  ElMessageBox.confirm("是否删除当前空间?", "提醒", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize)
      let params = {
        spaceId: row.id,
      };
      deleteAPI(params)
        .then((res) => {
          if (res.success) {
            ElMessage.success("删除成功！");
          } else {
            ElMessage.error("删除失败！" + res.errorMessage);
          }
          queryList();
        })
        .catch((e) => {
          ElMessage.error("删除失败！");
        });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消删除！",
      });
    });
};

// 编辑
const editBtn = (val) => {
  edit.value.openDialog(
    val.id == null ? "CREATE" : "UPDATE",
    state.activeName,
    JSON.parse(JSON.stringify(val))
  );
};

onMounted(() => {
  queryList();
});
</script>

<style>
/* 标签页 标题 icon */
.space-tabs .space-tabs-label .el-icon {
  vertical-align: middle;
}

/* 标签页 标题名称 */
.space-tabs .space-tabs-label span {
  vertical-align: middle;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
}

/* 标签页内容 */
.space-tabs>.el-tabs__content {
  margin-left: 2px;
  /*color: #6b778c;*/
  /*font-weight: 600;*/
}

/* 查询按钮 */
.space-tabs .space-tabs-search .search-query {
  border-radius: 5px;
}

/* 重置按钮 */
.space-tabs .space-tabs-search .search-reset {
  border-radius: 5px;
  margin-left: 8px;
}

/* 表格 */
.space-tabs .space-tabs-table {
  margin-top: 15px;
}
</style>
