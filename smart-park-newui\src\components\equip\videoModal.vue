<template>
  <el-dialog v-model="centerDialogVisible" :title="title" :width="900" @close="close" class="videoModal dialogCommon"
             :append-to-body="true" :close-on-press-escape="false">
    <div id="mui-player" style="width: 100%;height: 100%;" v-if="showVideo">
    </div>
    <div class="control" v-if="showVideo && state.videoInfo.equipmentType == '球机'">
      <div v-for="(value, key) in controlObj" :key="key" class="control-icon" @mousedown="controlHandle(key, 0)"
           @mouseup="controlHandle(key, 1)">
        <el-icon :size="16">
          <component :is="value">
          </component>
        </el-icon>
      </div>
    </div>
    <el-empty description="视频播放失败" v-if="!showVideo">
      <el-button type="primary" @click="reconnection">点击重连</el-button>
    </el-empty>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import DPlayer from 'dplayer'

import { equipContorlAPI } from '@/api/settingSystem/topoConnect.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})

const { title } = props

const centerDialogVisible = ref(false)
const showVideo = ref(true)

const state = reactive({
  videoUrl: '',
  videoInfo: {}
})

const controlObj = {
  "LEFT_UP": "TopLeft",
  "UP": "Top",
  "RIGHT_UP": "TopRight",
  "LEFT": "Back",
  "CENTER": "Aim",
  "RIGHT": "Right",
  "LEFT_DOWN": "BottomLeft",
  "DOWN": "Bottom",
  "RIGHT_DOWN": "BottomRight"
}
let dp;

// 打开
const open = (url, info) => {
  centerDialogVisible.value = true
  showVideo.value = true
  state.videoUrl = url
  state.videoInfo = info
  nextTick(() => {
    videoPlay()
  })
}

// 视频播放
const videoPlay = () => {
  dp = new DPlayer({
    container: document.getElementById('mui-player'),
    live: true,
    autoplay: true,
    preventClickToggle: true,
    screenshot: true,
    hotkey: true,
    preload: 'auto',
    muted: true,
    video: {
      url: state.videoUrl,
      type: 'customFlv',
    },
  });
  dp.on('play', function () {
    let flvPlayer = dp.video
    if (flvPlayer.buffered.length) {
      let end = flvPlayer.buffered.end(0);//获取当前buffered值
      let diff = end - flvPlayer.currentTime;//获取buffered与currentTime的差值
      if (diff >= 2.5) {//如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定
        dp.seek(flvPlayer.buffered.end(0));
      }
    }
  });

  dp.on('error', function () {
    if(dp){
      ElMessage({
        message: '播放失败',
        type: 'error',
      })
      videoDestroy()
      showVideo.value = false
    }
  })
}

// 方向控制
const controlHandle = (controlDir, controlValue) => {
  if (controlDir == 'CENTER') return false
  if (controlValue == 1 && state.videoInfo.factory == "DA_HUA_MONITOR") return false // 大华不用调停止接口
  let attributes = [
    {
      key: controlValue,
      value: controlDir
    }
  ]
  equipContorlAPI({ equipmentId: state.videoInfo.equipmentId, attributes })
}

// 视频销毁
const videoDestroy = () => {
  if (dp) {
    dp.destroy()
    dp = null
  }
}

// 重新连接
const reconnection = () => {
  showVideo.value = true
  nextTick(() => {
    videoPlay()
  })
}

// 关闭
const close = () => {
  centerDialogVisible.value = false
  videoDestroy()
}

defineExpose({
  open,
  close
})
</script>

<style lang="less">
.videoModal {
  .el-dialog__body {
    height: 60vh;
    position: relative;
  }
}

#mui-player {
  .dplayer-live-badge {
    display: none;
  }
}
</style>

<style lang="less" scoped>
.control {
  width: 100px;
  height: 100px;
  position: absolute;
  right: 50px;
  bottom: 100px;
  display: grid;
  grid-gap: 5px 5px;
  grid-template-rows: repeat(auto-fill, 30px);
  grid-template-columns: repeat(auto-fill, 30px);

  &-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(45deg, #7e7e7e, rgb(255, 255, 255) 100%);
    cursor: pointer;
    border-radius: 5px;
  }
}
</style>
