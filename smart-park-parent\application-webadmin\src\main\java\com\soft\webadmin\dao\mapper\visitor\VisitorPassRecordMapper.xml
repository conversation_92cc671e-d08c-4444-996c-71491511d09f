<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.visitor.VisitorPassRecordMapper">
    <resultMap type="com.soft.webadmin.model.visitor.VisitorPassRecord" id="VisitorPassRecordResult">
        <result property="id" column="id" />
        <result property="applyId" column="apply_id" />
        <result property="visitorName" column="visitor_name" />
        <result property="visitorPhone" column="visitor_phone" />
        <result property="visitorIdCard" column="visitor_id_card" />
        <result property="licenceNumber" column="licence_number" />
        <result property="planVisitTime" column="plan_visit_time" />
        <result property="realVisitTime" column="real_visit_time" />
        <result property="visitReason" column="visit_reason" />
        <result property="visitStatus" column="visit_status" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="selectVisitorPassRecordVo">
        select id, apply_id, visitor_name, visitor_phone, visitor_id_card, licence_number, plan_visit_time, real_visit_time, visit_reason, visit_status, create_user_id, create_time from visitor_pass_record
    </sql>

</mapper>