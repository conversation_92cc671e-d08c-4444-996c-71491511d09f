<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.shifts.ShiftsRecordMapper">
    <resultMap type="com.soft.webadmin.model.shifts.ShiftsRecord" id="ShiftsRecordResult">
        <result property="id" column="id" />
        <result property="shiftsSettingId" column="shifts_setting_id" />
        <result property="shiftsName" column="shifts_name" />
        <result property="liabilityArea" column="liability_area" />
        <result property="rosterId" column="roster_id" />
        <result property="attendanceDate" column="attendance_date" />
        <result property="startTime" column="start_time" />
        <result property="endTime" column="end_time" />
        <result property="deptId" column="dept_id" />
        <result property="week" column="week" />
        <result property="businessType" column="business_type" />
        <result property="allocateStatus" column="allocate_status" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectShiftsRecordVo">
        select id, shifts_setting_id,liability_area, shifts_name, roster_id, attendance_date, start_time, end_time, dept_id, week, business_type, allocate_status, deleted_flag, create_user_id, create_time, update_user_id, update_time from sp_shifts_record
    </sql>

    <select id="queryList" resultType="com.soft.webadmin.vo.shifts.ShiftsRecordVO">
        select re.attendance_date,re.start_time,re.end_time,ro.show_name ,re.allocate_status,us.show_name as operate_name ,re.update_time,re.shifts_name
        FROM sp_shifts_record re
        left join sp_shifts_roster ro on re.roster_id = ro.roster_id
        left join common_sys_user us on re.create_user_id = us.user_id
        <where>
            re.deleted_flag = 1
            and re.business_type = #{businessType}
            and re.allocate_status in (1,-1)
            <if test="showName != null and showName != ''">
                and ro.show_name like concat('%', #{showName}, '%')
            </if>
            <if test="allocateDate != null  and allocateDate != ''">
                and re.attendance_date = #{allocateDate}
            </if>
            <if test="allocateStatus != null">
                and re.allocate_status = #{allocateStatus}
            </if>
        </where>
        order by re.update_time desc
    </select>


    <insert id="batchInsert" parameterType="list">
        insert into sp_shifts_record (
         id,
         shifts_setting_id,
         liability_area,
         shifts_name,
         roster_id,
         attendance_date,
         start_time,
         end_time,
         dept_id,
         week,
         cycle,
         business_type,
         allocate_status,
         deleted_flag,
         create_user_id,
         create_time,
         update_user_id,
         update_time
        ) values
        <foreach collection="list" item="item" separator=",">
         (  #{item.id},
            #{item.shiftsSettingId},
            #{item.liabilityArea},
            #{item.shiftsName},
            #{item.rosterId},
            #{item.attendanceDate},
            #{item.startTime},
            #{item.endTime},
            #{item.deptId},
            #{item.week},
            #{item.cycle},
            #{item.businessType},
            #{item.allocateStatus},
            #{item.deletedFlag},
            #{item.createUserId},
            #{item.createTime},
            #{item.updateUserId},
            #{item.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        shifts_setting_id = VALUES(shifts_setting_id),
        liability_area = VALUES(liability_area),
        shifts_name = VALUES(shifts_name),
        attendance_date = VALUES(attendance_date),
        start_time = VALUES(start_time),
        end_time = VALUES(end_time),
        allocate_status = VALUES(allocate_status),
        week = VALUES(week),
        update_user_id = VALUES(update_user_id),
        update_time = VALUES(update_time),
        deleted_flag = VALUES(deleted_flag)
    </insert>
</mapper>