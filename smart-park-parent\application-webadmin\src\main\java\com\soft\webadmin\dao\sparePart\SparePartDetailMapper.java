package com.soft.webadmin.dao.sparePart;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.sparePart.SparePartDetailQueryDTO;
import com.soft.webadmin.model.sparePart.SparePartDetail;
import com.soft.webadmin.vo.sparePart.SparePartFullDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备品备件明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface SparePartDetailMapper extends BaseMapper<SparePartDetail> {

    List<SparePartFullDetailVO> getList(SparePartDetailQueryDTO queryDTO);

    List<SparePartFullDetailVO> getListByCondition(@Param("sparePartNos") List<String> sparePartNos,
                                                   @Param("storehouseId") Long storehouseId);

}
