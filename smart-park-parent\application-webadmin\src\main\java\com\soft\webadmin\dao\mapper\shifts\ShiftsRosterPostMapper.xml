<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.shifts.ShiftsRosterPostMapper">
    <resultMap type="com.soft.webadmin.model.shifts.ShiftsRosterPost" id="ShiftsRosterPostResult">
        <result property="rosterId" column="roster_id" />
        <result property="deptPostId" column="dept_post_id" />
        <result property="postId" column="post_id" />
    </resultMap>

    <sql id="selectShiftsRosterPostVo">
        select roster_id, dept_post_id, post_id from sp_shifts_roster_post
    </sql>
    
</mapper>