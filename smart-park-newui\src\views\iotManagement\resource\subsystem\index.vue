<template>
  <page-common v-model="state.tableHeight" :queryBool="false">
    <template #table>
      <el-tabs ref="subSystemTab" v-model="state.activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane :label="item.value" :name="item.name" v-for="item in state.tableHeader" :key="item.name">
          <div style="margin:20px">
            <div class="flexStyle">
              <div class="diamond"></div>
              <div style="margin-left: 20px;">系统描述</div>
            </div>
            <div class="flexStyle" style="justify-content: space-between;">
              <div style="margin:20px">{{ item.describe }}</div>
              <img src="@/assets/img/system.svg" class="systemSvg">
            </div>
          </div>
          <div style="margin:20px">
            <div class="flexStyle">
              <div class="diamond"></div>
              <div style="margin-left: 20px;">系统配置</div>
            </div>
            <div class="flexStyle"
              style="margin-top: 20px;padding-left: 20px;display: flex;flex-flow: row wrap;max-height: 462px;overflow-y:auto;width: 97%;padding-bottom: 80px;">
              <el-card class="box-card" v-for="(i, index)  in state.manufacturersList" :key="i.id">
                <template #header>
                  <div style="display:flex">
                    <img src="@/assets/img/data.png" style="width:100px;height: 100px;">
                    <div>
                      <div style="font-size: 20px;font-weight:600">{{ i.factoryName }}</div>
                      <div style="font-size: 14px;">{{ i.remark }}</div>
                    </div>
                    <!-- <div v-for="o in 4" :key="o" class="text item">{{ 'List item ' + o }}</div> -->
                  </div>
                </template>
                <div style="display:flex;justify-content: space-between;">
                  <div style="display:flex;cursor : pointer;" @click="editHandle(i)">
                    <div>
                      <el-icon>
                        <EditPen />
                      </el-icon>
                    </div>
                    <div style="margin-left:5px">编辑</div>
                  </div>
                  <div style="display:flex;cursor : pointer;"
                    v-if="state.activeName != 'LIGHTING' && state.activeName != 'BA'" @click="particularsHandle(i)">
                    <div>
                      <el-icon style="margin-top: 3px;">
                        <InfoFilled />
                      </el-icon>
                    </div>
                    <div style="margin-left:2px">详情</div>
                  </div>
                  <div style="display:flex;cursor : pointer;" @click="synchronization(i)">
                    <div>
                      <el-icon style="margin-top: 3px;">
                        <Switch />
                      </el-icon>
                    </div>
                    <div style="margin-left:3px">同步</div>
                  </div>
                  <el-popover v-if="item.name === 'MONITOR'" v-model:visible="showPopover[index]" :width="180"
                    trigger="click" @hide="hiddenPopover(index)">
                    <template #reference>
                      <div style="display: flex;cursor : pointer;" @click="importHandle(i.id, index)">
                        <div>
                          <el-icon style="margin-top: 3px">
                            <Upload />
                          </el-icon>
                        </div>
                        <div style="margin-left: 3px">导入</div>
                      </div>
                    </template>
                    <template #default>
                      <el-upload :ref="el => uploadRef[index] = el" :http-request="onUploadFile" :data-index='index'>
                        <template #trigger>
                          <el-button type="primary" size="small">上传数据</el-button>
                        </template>
                        <template #default>
                          <el-button type="primary" size="small" style="margin-left: 10px"
                            @click="downTemplate">下载模板</el-button>
                        </template>
                        <template #tip>
                          <div class="el-upload__tip" align="left">只能上传一个xlsx文件</div>
                        </template>
                      </el-upload>
                    </template>
                  </el-popover>
                  <div style="display:flex;cursor : pointer;" @click="showSchedulePage(i)">
                    <div>
                      <el-icon style="margin-top: 3px;">
                        <Timer />
                      </el-icon>
                    </div>
                    <div style="margin-left:3px">定时</div>
                  </div>
                  <div style="display:flex;cursor : pointer;" @click="deleteData(i)">
                    <div>
                      <el-icon style="margin-top: 3px;">
                        <DeleteFilled />
                      </el-icon>
                    </div>
                    <div style="margin-left:3px">删除</div>
                  </div>
                </div>
              </el-card>
              <el-card class="add_box-card" @click="add">
                <el-icon :size="50">
                  <Plus />
                </el-icon>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <dialog-common ref="dialog" :title="state.title" :formRef="ruleFormRef" :width="700" @submit="submit">
        <el-form ref="ruleFormRef" label-width="100px" :model="form" label-suffix=":" style="margin-right: 20px;"
          :rules="state.rules">
          <el-form-item label="接口厂家" prop="factory">
            <el-select v-model="form.factory" class="m-2" placeholder="选择接口厂家" size="large"
              :disabled="state.title == '详情'" @change="change">
              <el-option v-for="item in state.options" :key="item.factoryCode" :label="item.factoryName"
                :value="item.factoryCode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="key" :prop="key" v-for="(value, key) in state.parameterList" :key='key'>
            <el-input v-model="form[key]" placeholder="输入连接参数" :disabled="state.title == '详情'" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" maxlength="100" :show-word-limit="true" placeholder="输入备注"
              :rows="5" :disabled="state.title == '详情'" />
          </el-form-item>
        </el-form>
      </dialog-common>
      <dialog-common ref="dialog1" :title="state.title1" :formRef="ruleFormRef1" :width="700" @submit="submit1">
        <div style="display: flex;">
          <el-icon color="#ff9900" size="30">
            <QuestionFilled />
          </el-icon>
          <div style="font-size: 20px;">&nbsp;&nbsp;确认同步{{ state.deviceName }}数据吗？</div>
        </div>
        <el-form ref="ruleFormRef1" label-width="100px" :model="form1" label-suffix=":" style="margin-top: 20px;">
          <el-form-item label="" prop="dataTypes">
            <el-checkbox-group v-model="form1.dataTypes">
              <el-checkbox :label="item.value" v-for="item in state.buttonList" :key="item.name"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </dialog-common>
      <schedule-page ref="schedulePage"></schedule-page>
    </template>
  </page-common>
</template>

<script setup>
import {
  Delete, Plus, Search, Refresh
} from '@element-plus/icons-vue'
import { subsystemListAPI, FactoriesListAPI, saveOrUpdate, deleteAPI, syncData, getIntegrationList } from '@/api/iotManagement/subsystem.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { exportFile, uploadFile2 } from "@/utils/down.js";
import SchedulePage from "./component/schedulePage.vue";

const schedulePage = ref()
const subSystemTab = ref()
const activeName = ref('ENTRANCE_GUARD')
const state = reactive({
  parameterList: {},
  options: [],
  buttonList: [],
  id1: '',
  deviceName: '',
  id: '',
  title: '',
  title1: '',
  currentIndex: -1, // 当前点击位置
  tableHeader: [],
  activeName: '',
  manufacturersList: [],
  rules: {
    factory: [{ required: true, message: '请选择接口厂家', trigger: 'blur' },],
    params: [{ required: true, message: '请输入链接参数', trigger: 'blur' },]
  },
  rules1: {
    factory: [{ required: true, message: '请选择接口厂家', trigger: 'blur' },]
  }
})

let dialog = ref()
let dialog1 = ref()
let ruleFormRef = ref()
let ruleFormRef1 = ref()
let form = reactive({})
let form1 = reactive({ dataTypes: [] })


// 气泡框组件
const showPopover = ref([]);

// 上传组件
const uploadRef = ref([]);

onMounted(() => {
  getIntegrationList().then(res => {
    state.tableHeader = res.data
    state.activeName = res.data[0].name
    state.buttonList = res.data[0].dataTypeList
    getList(state.activeName);
  })
})

const handleClick = (tab, event) => {
  state.buttonList = state.tableHeader
    .filter(subSystem => subSystem.name == tab.props.name)[0]
    .dataTypeList;
  getList(tab.props.name);
}

const submit1 = () => {
  var list = [];
  if (form1.dataTypes.length == 0) {
    return ElMessage.error('请选择同步设置')
  }
  state.buttonList.forEach(e => {
    form1.dataTypes.forEach(i => {
      if (i == e.value) {
        list.push(e.name)
      }
    })
  })
  let query = {
    dataTypes: list,
    subSystemConfigId: state.id1
  }
  syncData(query).then(res => {
    if (res.success) {
      ElMessage.success('同步成功')
      dialog1.value.close()
    } else {
      ElMessage.error('同步失败，' + res.errorMessage)
    }
  }).catch(e => {
    ElMessage.error('同步失败，发生异常信息！')
  })
}

const getList = (value) => {
  let query = {
    subType: value
  }
  subsystemListAPI(query).then(res => {
    form.type = ''
    selectTree(value, res.data, 1)
  })
}

const change = (val) => {
  state.options.forEach(e => {
    if (e.factoryCode == val) {
      state.parameterList = JSON.parse(e.factoryTemplate)
    }
  })
}

const selectTree = (value, data, i) => {
  let query = {
    subType: value
  }
  FactoriesListAPI(query).then(res => {
    state.options = res.data
    if (data.length > 0) {
      data.forEach(e => {
        state.options.forEach(i => {
          if (i.factoryCode == e.factory) {
            e.factoryName = i.factoryName
          }
        })
      })
    }
    if (i == 1) {
      data.forEach(p => {
        var json = JSON.parse(p.configJson)
        for (var item in json) {
          p[item] = json[item]
        }
      })
      state.manufacturersList = data
    }
  })
}

const add = () => {
  // form = { factory: '', remark: '' }
  for (var i in form) {
    form[i] = ''
  }
  state.parameterList = {}
  state.id = ''
  state.title = '添加'
  dialog.value.open()
}

const editHandle = (item) => {
  change(item.factory);
  state.id = item.id
  state.title = '编辑'
  selectTree(item.type, [], 0);
  item.params = item.configJson
  nextTick(() => {
    Object.assign(form, { ...item })
  })
  dialog.value.open()
}

const particularsHandle = (item) => {
  change(item.factory);
  state.id = item.id
  state.title = '详情'
  selectTree(item.type, [], 0);
  item.params = item.configJson
  nextTick(() => {
    Object.assign(form, { ...item })
  })
  dialog.value.open()
}

const synchronization = (item) => {
  form1.dataTypes.checked3 = false
  state.id1 = item.id
  state.title1 = '操作确认'
  dialog1.value.open()
}

const submit = () => {
  if (state.title == '详情') {
    dialog.value.close()
    return
  }
  form.type = state.activeName
  form.params = {}
  for (var item in state.parameterList) {
    form.params[item] = form[item]
  }
  let subSystemConfigSave = form
  if (subSystemConfigSave.id) {
    subHandle(saveOrUpdate, '编辑成功')
  } else {
    subHandle(saveOrUpdate, '添加成功')
  }

  function subHandle(req, title) {
    req(subSystemConfigSave).then(res => {
      if (res.success) {
        ElMessage.success(title)
        dialog.value.close()
        getList(state.activeName)
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  }
}

const deleteData = (item) => {
  ElMessageBox.confirm(
    '是否删除当前厂家?',
    '提醒',
    {
      type: 'error',
      center: true,
    }
  ).then(() => {
    let query = {
      id: item.id
    }
    deleteAPI(query).then(res => {
      if (res.success) {
        getList(item.type)
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

const showSchedulePage = (item) => {
  // Object.assign(schedulePage.value.dataTypes, state.buttonList)
  schedulePage.value.form.id = item.id
  schedulePage.value.open()
}

const importHandle = (id, index) => {
  state.id = id;
  state.currentIndex = index;
}

// 下载模板文件
const downTemplate = async () => {
  await exportFile('/equipment/importExcel', null, '监控设备数据模板.xlsx');
};

// 上传文件
const onUploadFile = async (fileData) => {
  let params = [
    { key: 'configId', value: state.id }
  ];
  let { data: res } = await uploadFile2('/equipment/importExcel', fileData.file, params);
  if (res.success) {
    ElMessage.success('上传成功！');

    // 1s 后关闭气泡框
    setTimeout(() => {
      showPopover.value[state.currentIndex] = false;
    }, 1000)
  } else {
    ElMessage.error('上传 ' + res.errorMessage);
  }
};

// 隐藏气泡框事件
const hiddenPopover = (index) => {
  uploadRef.value[index].clearFiles();
};
</script>

<style lang='less' scoped>
.diamond {
  width: 9px;
  height: 15px;
  background-color: #3f9eff;
}

.flexStyle {
  display: flex;
}

.systemSvg {
  width: 10%;
  height: 10%;
}

.box-card {
  width: 360px;
  margin-left: 20px;
  margin-top: 20px;
}

.add_box-card {
  margin-top: 20px;
  width: 200px;
  height: 200px;
  line-height: 200px;
  text-align: center;
  margin-left: 20px;
}
</style>
