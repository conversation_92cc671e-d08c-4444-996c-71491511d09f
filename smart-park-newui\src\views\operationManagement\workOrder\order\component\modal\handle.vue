<template>
  <dialog-common ref="dialog" :formRef="ruleFormRef" :width="state.dialogWidth" title="工单处理" @onClose="onclose"
                 @submit="submit">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-suffix=":" label-width="110px">
      <el-form-item label="过程描述" prop="describe">
        <el-input v-model="form.describe" :autosize="{ minRows: 5 }" :maxlength="500" placeholder="请输入过程描述"
                  show-word-limit
                  type="textarea"/>
      </el-form-item>
      <el-form-item label="上传附件">
        <el-upload
            v-model:file-list="state.fileList"
            :class="{'disUpload': state.fileList.length == 3 }"
            :http-request="httpRequest"
            :limit="3"
            :on-preview="handleImgPreview"
            :on-success="(response) => { fileSuccess(response, state.fileList) }"
            list-type="picture-card"
        >
          <el-icon>
            <Plus/>
          </el-icon>
          <template #tip>
            <div class="el-upload__tip">
              支持格式：jpg、png ，单个文件不能超过5MB，最多支持3张
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="是否使用备件" prop="use">
        <el-radio-group v-model="form.use">
          <el-radio :value="1">是</el-radio>
          <el-radio :value="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-row justify="end" v-show="form.use === 1">
        <el-button icon="Plus" link style="margin-bottom: 10px;" type="primary" @click="handleChoose">选择备件
        </el-button>
      </el-row>
      <el-form-item label-width="0" v-show="form.use === 1">
        <el-table :data="form.sparePartList">
          <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :align="item.align" :formatter="item.formatter"
                           :label="item.label" :prop="item.prop" :width="item.width"/>
          <el-table-column align="center" label="操作">
            <template #default="{ $index }">
              <el-button icon="Delete" link type="danger" @click="handleDelete($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="备注" prop="remarks"  v-show="form.use === 1">
        <el-input v-model="form.remarks" :autosize="{ minRows: 5 }" :maxlength="500" placeholder="请输入备注"
                  show-word-limit
                  type="textarea"/>
      </el-form-item>
    </el-form>
    <template #footerBtn>
      <el-button type="primary" @click="handleSave">
        暂存
      </el-button>
    </template>
  </dialog-common>

  <word-accessories ref="choose" :hasInventory="1" @submit="handleReceive"></word-accessories>

  <el-dialog id="imgDialog" v-model="state.dialogVisible">
    <img :src="state.dialogImageUrl" alt="Preview Image" style="width: 100%;" w-full/>
  </el-dialog>
</template>

<script setup>
import {workDraftSaveAPI, workHandleAPI} from '@/api/operationManagement/workOrder.js';
import {ElInput, ElInputNumber, ElMessage} from 'element-plus';
import {annexUpload} from '@/api/file.js';

import wordAccessories from '@/views/operationManagement/workOrder/component/wordAccessories.vue'

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const choose = ref()
const form = reactive({
  sparePartList: []
});

const state = reactive({
  dialogWidth: 1000,
  rules: {
    describe: [{required: true, message: '请输入过程描述', trigger: 'blur'},],
  },
  fileList: [],
  dialogVisible: false,
  dialogImageUrl: '',
  tableHeader: [
    {
      prop: 'sparePartName',
      label: '备件名称',
    },
    {
      prop: 'classifyName',
      label: '备件分类'
    },
    {
      prop: 'receiveQuantity',
      label: '领用数量',
      width: 200,
      formatter: (row, column, cellValue) => {
        return h(ElInputNumber, {
          modelValue: row.receiveQuantity,
          'onUpdate:modelValue': (value) => row.receiveQuantity = value,
          max: row.inventoryQuantity,
          min: 1,
          stepStrictly: true
        });
      }
    },
  ]
});


// 覆盖Http
const httpRequest = (option) => {
  const formData = new FormData()
  formData.append('file', option.file)
  return annexUpload(formData)
}

// 查看图片
const handleImgPreview = (uploadFile) => {
  state.dialogImageUrl = uploadFile.url
  state.dialogVisible = true
}

// 上传图片
const fileSuccess = (response, fileList) => {
  fileList[fileList.length - 1].filePath = response.data.filePath
}

// 选择配件
const handleChoose = () => {
  choose.value.open()
}

// 接收配件信息
const handleReceive = (list) => {
  form.sparePartList = [...form.sparePartList, ...list.filter(item => {
    item.receiveQuantity = 1
    return form.sparePartList.findIndex(i => i.id == item.id) == -1
  })]
}

// 删除
const handleDelete = (index) => {
  state.tableData.splice(index, 1)
}

// 提交表单
const submit = () => {
  form.img = (state.fileList || []).map(img => img.filePath).join(',');
  form.sparePartList = form.sparePartList.map(item => {
    return {
      ...item,
      sparePartId: item.sparePartId ? item.sparePartId : item.id
    }
  })
  workHandleAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success('操作成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

// 保存临时草稿
const handleSave = () => {
  form.img = (state.fileList || []).map(img => img.filePath).join(',');
  form.sparePartList = form.sparePartList.map(item => {
    return {
      ...item,
      sparePartId: item.sparePartId ? item.sparePartId : item.id
    }
  })

  workDraftSaveAPI(form).then(res => {
    if (res.success) {
      ElMessage.success('保存成功');
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = () => {
  dialog.value.open();
};

const onclose = () => {
  delete form.id
  state.fileList = []
  form.sparePartList = []
}

defineExpose({
  state,
  form,
  open,
});
</script>
<style lang="less" scoped>
.title {
  font-size: 14px;
  font-weight: bold;
}
</style>
