// 设备管理相关接口
import { request } from "@/utils/request";

// 分页查询
export const getEquipListAPI = (query) => {
  return request('get', '/om/equipment/getPage', query, 'F');
};

// 设备保存
export const equipSaveAPI = (data) => {
  return request('post', '/om/equipment/saveOrUpdate', data);
}

// 设备删除
export const equipDeleteAPI = (params) => {
  return request('post', '/om/equipment/delete', params, 'F');
}

// 查看设备详情
export const viewEquipAPI = (data) => {
  return request('get', '/om/equipment/detail', data, 'F')
}


// 设备二维码接口
export const qrCodeAPI = (data) => {
  return request('get', '/om/equipment/qrcode/' + data)
}


// 报废
export const equipScrapAPI = (data) => {
  return request('post', '/om/equipment/scrap', data);
}

// 导入
export const equipUploadAPI = (data) => {
  return request('post', '/om/equipment/importExcel', data);
}

