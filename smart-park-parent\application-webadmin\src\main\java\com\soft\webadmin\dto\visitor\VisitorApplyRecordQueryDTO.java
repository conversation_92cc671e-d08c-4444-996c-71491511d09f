package com.soft.webadmin.dto.visitor;


import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class VisitorApplyRecordQueryDTO extends MyPageParam {

    @ApiModelProperty("访客姓名/车牌号")
    private String queryWord;

    @ApiModelProperty("申请日期（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date applyDateStart;

    @ApiModelProperty("申请日期（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date applyDateEnd;

    @ApiModelProperty("预计到访日期(开始)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planVisitDateStart;

    @ApiModelProperty("预计到访日期(结尾)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planVisitDateEnd;

    @ApiModelProperty("审批类型：1待审核；2已审核")
    private Integer approveType;

    @ApiModelProperty("审核状态：1待审核；2已通过；3未通过；4已过期")
    private Integer approveStatus;

//    @ApiModelProperty("申请人id")
//    private Long applyUserId;
    private String enterType;
    private String openId;

}
