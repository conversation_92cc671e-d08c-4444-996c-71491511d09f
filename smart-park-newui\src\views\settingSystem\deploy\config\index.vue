<template>
  <page-common v-model="state.tableHeight" :queryBool="false">
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column label="序号" align="center" type="index" width="55" />
        <el-table-column prop="configKey" label="键名称" />
        <el-table-column prop="keyValue" label="值" />
        <el-table-column prop="explainInit" label="说明" />
        <el-table-column prop="isEdit" label="是否可编辑" >
          <template #default="scope">
            <el-text v-if="scope.row.isEdit==0">否</el-text>
            <el-text v-if="scope.row.isEdit==1">是</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" />
        <el-table-column align="center" label="操作"  width="80">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)" :disabled="!(scope.row.isEdit == 1)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pageParam.pageNum"
          :page-size="state.pageParam.pageSize"
          :total="state.pageParam.total"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
      <modal-page ref="modal"  @submit="getList"></modal-page>
    </template>
  </page-common>
</template>

<script setup>
import { getPageAPI } from '@/api/settingSystem/config.js';
import { Delete, Plus, Search, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref } from 'vue';
import { calcPageNo } from '@/utils/util.js';
import modalPage from './component/modalPage.vue'

const modal = ref();
const formInlineRef = ref();
const formInline = reactive({
  name: '',
});
const state = reactive({
  tableData: [],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  title: '',
});

onMounted(() => {
  getList();
});

const getList = () => {
  let params = {
    name: formInline.name,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };
  getPageAPI(params).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

// 编辑字典
const editHandle = (info) => {
  modal.value.open()
  nextTick(() => {
    Object.assign(modal.value.form, { ...info })
  })
}


</script>

<style lang="less" scoped></style>
