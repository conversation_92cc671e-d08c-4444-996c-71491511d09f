<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.contingency.EventMapper">
    <resultMap type="com.soft.webadmin.model.contingency.Event" id="EventResult">
        <result property="id" column="id" />
        <result property="name" column="name" />
        <result property="level" column="level" />
        <result property="remark" column="remark" />
        <result property="img" column="img" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectEventVo">
        t.id, t.name, t.level, t.remark, t.img, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="queryList" parameterType="com.soft.webadmin.dto.contingency.EventQueryDTO" resultType="com.soft.webadmin.vo.contingency.EventVO">
        select <include refid="selectEventVo" />,
        (select id from cm_emergency where event_id = t.id and deleted_flag = 1) emergency_id
        from cm_event t
        <where>
            and t.deleted_flag = 1
            <if test="name != null and name != ''">
                and t.name like concat('%', #{name}, '%')
            </if>
            <if test="level != null">
                and t.level = #{level}
            </if>
        </where>
        order by t.create_time desc
    </select>
    
</mapper>