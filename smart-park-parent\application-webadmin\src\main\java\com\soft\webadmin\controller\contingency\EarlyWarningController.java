package com.soft.webadmin.controller.contingency;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.contingency.EarlyWarningDTO;
import com.soft.webadmin.dto.contingency.EarlyWarningQueryDTO;
import com.soft.webadmin.dto.contingency.EarlyWarningSummarizeDTO;
import com.soft.webadmin.service.contingency.EarlyWarningService;
import com.soft.webadmin.vo.contingency.EarlyWarningEquipmentPreviewVO;
import com.soft.webadmin.vo.contingency.EarlyWarningVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 预警记录控制器类
 * 
 * <AUTHOR>
 * @date 2024-04-17
 */
@Api(tags = "预警中心")
@RestController
@RequestMapping("/contingency/warning")
public class EarlyWarningController {

    @Autowired
    private EarlyWarningService earlyWarningService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<EarlyWarningVO>> getPage(EarlyWarningQueryDTO queryDTO) {
        return ResponseResult.success(earlyWarningService.list(queryDTO));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public ResponseResult<EarlyWarningVO> detail(@RequestParam Long id) {
        return ResponseResult.success(earlyWarningService.detail(id));
    }

    @ApiOperation(value = "事件上报")
    @PostMapping("/save")
    public ResponseResult<Void> save(@Validated @RequestBody EarlyWarningDTO saveDTO) {
        earlyWarningService.save(saveDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "事件处理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "预警记录id"),
            @ApiImplicitParam(name = "status", value = "状态：1误报，2已解决")
    })
    @PostMapping("/handle")
    public ResponseResult<Void> handle(@RequestParam Long id, @RequestParam Integer status) {
        earlyWarningService.handle(id, status);
        return ResponseResult.success();
    }

    @ApiOperation(value = "事件总结")
    @PostMapping("/summarize")
    public ResponseResult<Void> summarize(@Validated @RequestBody EarlyWarningSummarizeDTO summarizeDTO) {
        earlyWarningService.summarize(summarizeDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "导出word")
    @GetMapping("/exportWord")
    public void getPage(Long id, HttpServletResponse response) {
        earlyWarningService.exportWord(id, response);
    }



    @ApiOperation("获取预警关联的监控设备的视频流")
    @GetMapping("/previewFlvUrls")
    public ResponseResult<List<EarlyWarningEquipmentPreviewVO>> previewFlvUrls(@RequestParam Long warningId) {
        List<EarlyWarningEquipmentPreviewVO> earlyWarningEquipmentPreviewVOS = earlyWarningService.previewFlvUrls(warningId);
        return ResponseResult.success(earlyWarningEquipmentPreviewVOS);
    }
}
