package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * WorkQuoteRelationDTO对象
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@ApiModel("WorkOrderSpacePartDTO对象")
@Data
public class WorkOrderSpacePartDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "工单id")
    private Long orderId;

    @ApiModelProperty(value = "备件id")
    private Long sparePartId;

    @ApiModelProperty(value = "备件名称")
    private String sparePartName;

    @ApiModelProperty(value = "备件分类")
    private String sparePartClassifyName;

    @ApiModelProperty(value = "领用数量")
    private Integer receiveQuantity;

}
