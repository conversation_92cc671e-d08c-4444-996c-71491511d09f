<template>
  <dialog-common ref="dialog" title="充值记录" :width="500" :showButton="false">
    <el-table :data="state.tableData" style="width: 100%">
      <el-table-column prop="createTime" label="充值时间" width="180" />
      <el-table-column prop="price" label="充值金额" width="180" />
      <el-table-column prop="createUserName" label="充值人" />
    </el-table>
  </dialog-common>
</template>

<script setup>
import { selectPayRecordAPI } from '@/api/energyManagement/pay.js';
import { ElMessage, ElMessageBox } from 'element-plus';

const emit = defineEmits(['submit']);
const dialog = ref();
const ruleFormRef = ref();
const form = reactive({});
const state = reactive({
  tableData: []
});

const open = () => {
  selectPayRecordAPI({ equipmentId: form.equipmentId }).then(res => {
    state.tableData = res.data
  })
  dialog.value.open();

};


defineExpose({
  form,
  open,
});
</script>
