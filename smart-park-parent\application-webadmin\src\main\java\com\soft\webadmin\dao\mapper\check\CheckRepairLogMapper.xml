<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.CheckRepairLogMapper">
    <resultMap type="com.soft.webadmin.model.check.CheckRepairLog" id="CheckRepairLogResult">
        <result property="id" column="id" />
        <result property="equipmentId" column="equipment_id" />
        <result property="spaceId" column="space_id" />
        <result property="spacePath" column="space_path" />
        <result property="spaceFullName" column="space_full_name" />
        <result property="priority" column="priority" />
        <result property="content" column="content" />
        <result property="reportDeptId" column="report_dept_id" />
        <result property="reportUserId" column="report_user_id" />
        <result property="reportUserPhone" column="report_user_phone" />
        <result property="img" column="img" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectCheckRepairLogVo">
        t.id, t.equipment_id, t.space_id, t.space_path, t.space_full_name, t.priority, t.content, t.report_dept_id, t.report_user_id, t.report_user_phone, t.img, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="queryList" resultType="com.soft.webadmin.vo.check.CheckRepairLogVO">
        select <include refid="selectCheckRepairLogVo" />,
        o.state order_state, o.id order_id,
        (select show_name from common_sys_user where user_id = t.create_user_id) create_user_name
        from sp_check_repair_log t left join sp_work_order o on t.id = o.business_id
        <where>
            and t.deleted_flag = 1 and o.deleted_flag = 1
            <if test="priority != null">
                and t.priority = #{priority}
            </if>
            <if test="state != null">
                and o.state = #{state}
            </if>
            <if test="beginDate != null and beginDate != ''">
                and date_format(t.create_time, '%Y-%m-%d') &gt;= date_format(#{beginDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and date_format(t.create_time, '%Y-%m-%d') &lt;= date_format(#{endDate}, '%Y-%m-%d')
            </if>
            <if test="createUserId != null">
                and t.create_user_id = #{createUserId}
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="queryListOfOrder" resultType="com.soft.webadmin.vo.check.WorkOrderOfReportVO">
        select t.id record_id, t.content, t.create_time, o.id order_id, o.order_no, o.state
        from sp_check_repair_log t
        left join sp_work_order o on t.id = o.business_id
        <where>
            and t.deleted_flag = 1 and o.deleted_flag = 1
            <if test="priority != null">
                and t.priority = #{priority}
            </if>
            <if test="state != null">
                and o.state = #{state}
            </if>
            <if test="beginDate != null and beginDate != ''">
                and date_format(t.create_time, '%Y-%m-%d') &gt;= date_format(#{beginDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and date_format(t.create_time, '%Y-%m-%d') &lt;= date_format(#{endDate}, '%Y-%m-%d')
            </if>
            <if test="reportUserId != null">
                and t.report_user_id = #{reportUserId}
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="queryById" resultType="com.soft.webadmin.vo.check.CheckRepairLogVO">
        select <include refid="selectCheckRepairLogVo" />,
        (select equipment_name from sp_equipment_om where equipment_id = t.equipment_id) equipment_name,
        (select show_name from common_sys_user where user_id = t.report_user_id) report_user_name,
        (select dept_name from common_sys_dept where dept_id = t.report_dept_id) report_dept_name
        from sp_check_repair_log t where t.id = #{id}
    </select>
    
</mapper>