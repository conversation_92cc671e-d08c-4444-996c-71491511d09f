package com.soft.webadmin.dto.shifts;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * ShiftsAttendanceDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsAttendanceDTO对象")
@Data
public class ShiftsAttendanceQueryDTO  extends MyPageParam {

    @ApiModelProperty(value = "姓名")
    private String showName;

    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    @ApiModelProperty(value = "上班打卡结果")
    private String workResult;

    @ApiModelProperty(value = "出勤开始时间")
    private String startTime;

    @ApiModelProperty(value = "出勤结束时间")
    private String endTime;

    @ApiModelProperty(value = "出勤周期(统计时必填 YYYY-MM)")
    private String cycle;

    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private String businessType;
}
