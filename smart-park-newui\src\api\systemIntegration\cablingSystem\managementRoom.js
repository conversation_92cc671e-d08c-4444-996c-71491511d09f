import { request } from "@/utils/request";



export const listManagementRoomAPI = (data) => {
  return request('get', '/cablingSystem/management-room/list', data, 'F');
}


export const saveOrUpdateManagementRoomAPI = (data) => {
  return request('post', '/cablingSystem/management-room/saveOrUpdate', data);
}


export const deleteManagementRoomAPI = (data) => {
  return request('post', '/cablingSystem/management-room/delete', data, 'F');
}
