<template>
  <div>
    <el-form label-suffix=":">
      <el-form-item label="设备">
        <el-input :value="equipNameShow" @click="equipClick" placeholder="请选择设备" readonly
          class="inputCursor" />
      </el-form-item>
    </el-form>

    <span class="commonTitle">样式</span>
    <el-divider style="margin: 14px 0;" />
    <el-checkbox-group v-model="curComponent.params.classInfo.checkList">
      <el-checkbox v-for="item in styleList" :value="item.class">{{ item.label }}
        <el-icon size="18" @click.stop.prevent="addClassHandle(item.class)" v-show="item.label != '提示'">
          <Setting />
        </el-icon>
      </el-checkbox>
    </el-checkbox-group>
    <el-input v-show="curComponent.type != 'text'" v-model="state.itemInfo.conent"  placeholder="请输入"  :rows="3" type="textarea" style="margin-bottom: 10px;"/>

    <div v-if="curComponent.params.eventInfo">
      <span class="commonTitle">事件</span>
      <el-divider style="margin: 14px 0;" />
      <el-form label-suffix=":">
        <el-form-item label="类型">
          <el-select v-model="curComponent.params.eventInfo.eventType" :style="stlyeSetting" @change="clearHandle" clearable>
            <el-option v-for="item in state.typeOption" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <Setting @click="addEventList" class="settingIcon" v-show="['attributeControl', 'pageJump'].includes(curComponent.params.eventInfo.eventType)"/>
        </el-form-item>
      </el-form>
    </div>

    <class-modal ref="classRef" :classType="state.classType" :attributeOptions="state.attributeOptions"></class-modal>
    <event-modal ref="eventRef" :attributeOptions="state.attributeOptions"></event-modal>
    <select-iot ref="equipRef" :multiple="false" @rowClick="equipSelect" />
  </div>
</template>

<script setup>
import { computed, watch } from 'vue';
import classModal from './classModal.vue';
import eventModal from './eventModal.vue';

import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent } = storeToRefs(webtopo)

let classRef = ref()
let eventRef = ref()
let equipRef = ref()

const state = reactive({
  classType: '',
  attributeOptions: [],
  itemInfo:{}, // 提示对象
  typeOption: [
    {
      value: 'videoPre',
      label: '视频预览'
    },
    {
      value: 'equipControl',
      label: '设备控制'
    },
    {
      value: 'attributeControl',
      label: '属性控制'
    },
    {
      value: 'pageJump',
      label: '页面跳转'
    },
  ],
  subTypeOptions:{
    'BA': '楼控',
    'ENTRANCE_GUARD': '门禁',
    'FIRE_FIGHTING': '消防',
    'INFORMATION': '信息发布',
    'LADDER_CONTROL': '梯控',
    'LIGHTING': '照明',
    'MONITOR': '监控',
    'PARKING': '停车'
  }
})

const styleList = computed(() => {
  let textList = [
    {
      label: '隐藏',
      class: 'toposhowHide'
    },
    {
      label: '值显示',
      class: 'topoTextShow'
    }
  ]

  let evnetList = [
    {
      label: '隐藏',
      class: 'toposhowHide'
    },
    {
      label: '闪烁',
      class: 'topoTwinkle'
    },
    {
      label: '切换',
      class: 'topoCheckout'
    },
    {
      label: '提示',
      class: 'topoPrompt'
    }
  ]

  return curComponent.value.type == 'text' ? textList : evnetList
})

const stlyeSetting = computed(() => {
  return { width: `${['attributeControl', 'pageJump'].includes(curComponent.value.params.eventInfo.eventType) ? 'calc(100% - 25px)' : '100%'}` }
})

const equipNameShow = computed(() => {
  if(Object.keys(curComponent.value.params.equipInfo).length)
  return state.subTypeOptions[curComponent.value.params.equipInfo.subType] + '-' + curComponent.value.params.equipInfo.equipmentName
})

// 设备触发选择
const equipClick = () => {
  equipRef.value.open();
}

// 设备选择
const equipSelect = (row) => {
  (row.equipmentAttributeList || []).forEach(item => delete item.attributeValue)
  curComponent.value.params.equipInfo = { ...row }
  state.attributeOptions = (row.equipmentAttributeList || []).map(item => item.attributeKey)
  if(curComponent.value.params.eventInfo.attribute){   //清空源有属性
    curComponent.value.params.eventInfo.attribute = null
  }
}

// 当设备属性
const getEquipAtt = (info) => {
  let { equipInfo , classInfo } = info?.params instanceof Object && info.params

  if (equipInfo instanceof Object && Object.keys(equipInfo).length) {
    state.attributeOptions = (equipInfo.equipmentAttributeList || []).map(key => key.attributeKey)
  }

  // 创建class数组
  if (!classInfo.classList) {
    classInfo.classList = []
  }

  if(info.type == 'text')
  return true

  // 查找当前class对象提示
  let prompt = classInfo.classList.find(item => item.classType == 'topoPrompt')

  if(!prompt){
    classInfo.classList.push({
      classType: 'topoPrompt'
    })
    prompt = classInfo.classList.find(item => item.classType == 'topoPrompt')
  }
  state.itemInfo = prompt
}

// 添加class对象
const addClassHandle = (info) => {
  state.classType = info

  let { classInfo } = curComponent.value.params

  // 查找当前class对象
  let current = classInfo.classList.find(item => item.classType == info)

  // 如果不存在  重新赋值  关联(当前组件)
  if (!current) {
    if (info == 'topoTextShow') {
      classInfo.classList.push({
        classType: info
      })
    } else {
      classInfo.classList.push({
        classType: info,
        list: [
          {
            min: null,
            max: null
          }
        ]
      })
    }
    current = classInfo.classList.find(item => item.classType == info)
  }

  classRef.value.state.itemInfo = current

  classRef.value.open()
}

// 添加事件
const addEventList = () => {
  let { eventInfo } = curComponent.value.params
  eventRef.value.state.eventInfo = eventInfo
  eventRef.value.open()
}

// 清空之前值
const clearHandle = (value) => {
  curComponent.value.params.eventInfo = {
    eventType: value
  }
}

watch(curComponent, (newVal) => {
  if(newVal)
  getEquipAtt(newVal)
}, { immediate: true })

</script>

<style lang='less' scoped>
.inputCursor {
  :deep(.el-input__inner) {
    cursor: pointer;
  }
}

.commonTitle {
  font-size: 14px;
  color: #606266;
}

.el-checkbox {
  display: block;
  margin-right: 10px;
}

.el-icon {
  position: absolute;
  right: 0;
  cursor: pointer;
}

.settingIcon{
  width: 18px;
  height: 18px;
  margin-left: 7px;
  color: #606266;
  cursor: pointer;
}
</style>
