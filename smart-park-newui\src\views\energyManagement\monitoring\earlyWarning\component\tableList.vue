<template>
    <page-common v-model="state.tableHeight">
        <template #query>
            <el-form :inline="true" ref="formInlineRef" :model="formInline">
                <el-form-item prop="queryName">
                    <el-input v-model="formInline.queryName" placeholder="设备名称或编号" />
                </el-form-item>
                <el-form-item prop="status">
                    <el-select v-model="formInline.status" clearable placeholder="处理状态">
                        <el-option v-for="item in state.statusOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="equipmentType">
                    <el-select v-model="formInline.equipmentType" placeholder="设备类型">
                        <el-option v-for="item in state.options" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="queryTime">
                    <el-date-picker v-model="formInline.queryTime" type="datetimerange" range-separator="到"
                        format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" start-placeholder="预警开始时间"
                        end-placeholder="预警结束时间" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
                    <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template #table>
            <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                    :label="item.label" :align="item.align" :formatter="item.formatter" :width="item.width">
                    <template #default="scope">
                        <div v-if="item.prop === 'status'">
                            <span v-if="scope.row.status === 1" class="status-circle"
                                style="background-color: greenyellow;"></span>
                            <span v-else class="status-circle" style="background-color: red;"></span>
                            <span>{{ scope.row.status === 1 ? '已处理' : '未处理' }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="230">
                    <template #default="scope">
                        <el-button link type="primary" icon="Edit" :disabled="scope.row.status === 1"
                            @click.prevent="onHandle(scope.row.id)">
                            处理
                        </el-button>
                        <el-button link type="primary" icon="Tickets" @click="onDetail(scope.row.equipmentId)">
                            详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize"
                :total="state.pagetion.total" @size-change="sizeChange" @current-change="currentChange" />
        </template>
    </page-common>
</template>

<script setup>
import { pageListAPI, handleAPI } from "@/api/iotManagement/equipmentwaning.js"

const emit = defineEmits(['showPage'])

const formInlineRef = ref()
const formInline = reactive({
    queryTime: []
})

const state = reactive({
    tableHeight: 100,
    statusOptions: [
        { label: '未处理', value: 0 },
        { label: '已处理', value: 1 }
    ],
    options: [
        {
            value: '水表',
            label: '水表',
        },
        {
            value: '电表',
            label: '电表',
        },
        {
            value: '气表',
            label: '气表',
        },
    ],
    tableData: [],
    pagetion: {
        pageNum: 1,
        pageSize: 10,
        total: 0
    },
    tableHeader: [
        { label: '设备编号', prop: 'equipmentNo',width: 210 },
        { label: '设备名称', prop: 'equipmentName' },
        { label: '设备类型', prop: 'energyName' },
        { label: '设备分类', prop: 'equipmentType' },
        { label: '安装位置', prop: 'equipmentSpaceFullName' },
        { label: '预警时间', prop: 'createTime',width: 150},
        { label: '预警内容', prop: 'content',width: 210 },
        { label: '处理状态', prop: 'status' },
        { label: '处理人', prop: 'handleUsername' },
        { label: '处理时间', prop: 'handleTime',width: 150 },
    ]
})

onMounted(() => {
    getList()
})

/** 查看详情 */
const onDetail = (equipmentId) => {
    emit('showPage', 1, equipmentId);
};


const getList = () => {
    let query = {
        ...formInline,
        ...state.pagetion,
        startTime: formInline.queryTime[0],
        endTime: formInline.queryTime[1],
        subType: 'ENERGY'
    }

    pageListAPI(query).then(res => {
        if (res.success) {
            state.tableData = res.data.dataList
            state.pagetion.total = res.data.totalCount
        }
    })
}

// 处理告警
const onHandle = (id) => {
    ElMessageBox.confirm(
        '确认预警已处理吗?',
        '提醒',
        {
            type: "warning"
        }
    ).then(() => {
        handleAPI({
            id,
            result: '已解决'
        }).then(res => {
            if (res.success) {
                ElMessage.success("保存成功！");
                queryPageList()
            } else {
                ElMessage.error("保存失败，" + res.errorMessage);
            }
        })
    })
}

// 查询
const onSubmit = () => {
    state.pagetion = {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
    getList()
}

// 重置
const onReset = () => {
    formInlineRef.value.resetFields()
    onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
    state.pagetion.pageNum = pageNum
    getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
    state.pagetion.pageSize = pageSize
    getList()
}

defineExpose({
    getList
})
</script>