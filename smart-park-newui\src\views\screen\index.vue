<template>
  <div id="window">
    <desktop-cp class="desktop"></desktop-cp>
    <task-cp class="task"></task-cp>
    <web-window
      class="web-window"
      v-for="window in windowArray"
      :key="window.id"
      :window="window"
      v-dragMove="{ dragArea: '.header' }"
      v-dragSize="{ dragArea: '.background', dragAreaWidth: 6 , isSize: window.isSize}"
    ></web-window>
  </div>
</template>

<script setup>
import { useWindowStore } from '@/store/modules/window.js'

import desktopCp from './components/desktopCp.vue'
import taskCp from "./components/taskCp.vue";
import webWindow from './components/webWindow.vue'

const windowStore = useWindowStore()
let { windowArray } = storeToRefs(windowStore)
</script>

<style lang="less" scoped>
#window{
  width: 100vw;
  height: 100vh;
  background-image: url("@/assets/window/background.jpg");
  background-size: 100% 120%;
  overflow: hidden;
  position: relative;
  .desktop{
    height: calc(100% - 50px);
    width: 100%;
  }
  .task{
    height: 50px;
    width: 100%;
    background: #2221365b;
    backdrop-filter: blur(100px);
    border-top: 1px solid #8f8f8f;
    position: relative;
    z-index: 999;
  }
}

.web-window{
  position: absolute;
  left: 0px;
  top: 0px;
}
</style>
