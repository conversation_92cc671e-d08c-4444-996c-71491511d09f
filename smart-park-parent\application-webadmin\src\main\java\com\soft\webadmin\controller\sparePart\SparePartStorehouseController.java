package com.soft.webadmin.controller.sparePart;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.sparePart.SparePartStorehouseDTO;
import com.soft.webadmin.dto.sparePart.SparePartStorehouseQueryDTO;
import com.soft.webadmin.service.sparePart.SparePartStorehouseService;
import com.soft.webadmin.vo.sparePart.SparePartStorehouseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 备品备件仓库控制器类
 * 
 * <AUTHOR>
 * @date 2024-03-05
 */
@Api(tags = "仓库管理")
@RestController
@RequestMapping("/sparePart/storehouse")
public class SparePartStorehouseController {

    @Autowired
    private SparePartStorehouseService sparePartStorehouseService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<SparePartStorehouseVO>> getPage(SparePartStorehouseQueryDTO queryDTO) {
        return ResponseResult.success(sparePartStorehouseService.getPage(queryDTO));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@Validated @RequestBody SparePartStorehouseDTO saveDTO) {
        return sparePartStorehouseService.saveOrUpdate(saveDTO);

    }

    @ApiOperation(value = "更新仓库状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "仓库id", required = true),
            @ApiImplicitParam(name = "state", value = "状态：0锁定，1启用", required = true)
    })
    @PostMapping("/updateState")
    public ResponseResult<Void> updateState(@RequestParam Long id, @RequestParam Integer state) {
        return sparePartStorehouseService.updateStatus(id, state);
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        return sparePartStorehouseService.delete(id);
    }

}
