package com.soft.webadmin.dto.contingency;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * GroupDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("GroupDTO对象")
@Data
public class GroupQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "事件id")
    private Long eventId;

    @ApiModelProperty(value = "小组名称")
    private String name;

}
