<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.CheckTemplateItemMapper">
    <resultMap type="com.soft.webadmin.model.check.CheckTemplateItem" id="SpCheckTemplateItemResult">
        <result property="id" column="id" />
        <result property="templateId" column="template_id" />
        <result property="itemName" column="item_name" />
        <result property="itemContent" column="item_content" />
        <result property="itemType" column="item_type" />
    </resultMap>

    <sql id="selectSpCheckTemplateItemVo">
        select id, template_id, item_name, item_content, item_type from sp_check_template_item
    </sql>

    <insert id="insertBatch">
        insert into sp_check_template_item(id, template_id, item_name, item_content, item_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.templateId}, #{item.itemName}, #{item.itemContent}, #{item.itemType})
        </foreach>
    </insert>

</mapper>