<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef" :width="900">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="120px" label-suffix=":">
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备分类">
            <el-radio-group :model-value="form.classify" class="ml-4" @change="updateType">
              <el-radio label=1 size="large">信息面板</el-radio>
              <el-radio label=2 size="large">配线架</el-radio>
              <el-radio label=3 size="large">交换机</el-radio>
              <el-radio label=4 size="large">机柜</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入设备名称" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="form.classify == 2">
        <el-col :span="24">
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="form.type" class="ml-4">
              <el-radio :label=item.id size="large" v-for="item in state.typeListP">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="端口数量" prop="portQuantity">
            <el-radio-group v-model="form.portQuantity">
              <el-radio label=24 size="large">24</el-radio>
              <el-radio label=48 size="large">48</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="form.classify == 4">
        <el-col :span="24">
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="form.type" class="ml-4">
              <el-radio :label=item.id size="large" v-for="item in state.typeListJ">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="form.classify == 3">
        <el-col :span="24">
          <el-form-item label="端口数量" prop="portQuantity">
            <el-input-number v-model="form.portQuantity" min="1" max="48" :controls="false" placeholder="请输入1-48之间的数字"
              clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row v-if="form.classify == 1"> -->
      <el-card shadow="never" style="margin-bottom: 3%;" v-for="(item, index) in form.portTypes"
        v-if="form.classify == 1">
        <el-row>
          <el-col :span="10">
            <el-form-item label="端口类型" :prop="'portTypes.' + index + '.type'" :rules="{
              required: true,
              message: '端口类型不能为空',
              trigger: 'blur',
            }">
              <el-select v-model="item.type" class="m-2" placeholder="请选择类型">
                <el-option v-for="item in DKoptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="端口编码" :prop="'portTypes.' + index + '.code'" :rules="{
              required: true,
              message: '端口编码不能为空',
              trigger: 'blur',
            }">
              <el-input v-model="item.code" placeholder="请输入端口编码" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-icon size="30" :color="color" @click="delSport(index)">
              <CircleCloseFilled />
            </el-icon>
          </el-col>
        </el-row>
      </el-card>
      <el-col :span="24">
        <el-button type="primary" @click="addPort" v-if="form.classify == 1">添加端口</el-button></el-col>

      <!-- </el-row> -->

      <el-row>
        <el-col :span="form.classify == 1 ? 24 : 12">
          <el-form-item label="所在位置" prop="spaceIds">
            <el-cascader v-model="form.spaceIds" :options="state.treeData" :props="treeProps" collapse-tags
              placeholder="请选择管理间位置" collapse-tags-tooltip clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.classify != 1">
          <el-form-item label="管理间" prop="managementRoomId">
            <el-select v-model="form.managementRoomId" placeholder="请选择管理间" @change="selectGLJName">
              <el-option v-for="item in state.GLJData" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="form.classify == 2 || form.classify == 3">
        <el-col :span="12">
          <el-form-item label="机柜" prop="cabinetIds">
            <el-select v-model="form.cabinetIds" placeholder="请选择管理间" @change="selectGLJName">
              <el-option v-for="item in state.JGata" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机柜位置" prop="cabinetSpaceFullName">
            <el-select v-model="form.cabinetSpaceFullName" class="m-2" placeholder="请选择机柜位置">
              <el-option v-for="item in state.JGWZList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="人员" prop="staff">
            <el-select v-model="form.staff" placeholder="请选择人员">
              <el-option v-for="item in state.RYData" :key="item.userId" :label="item.showName" :value="item.userId" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="描述" prop="remarks">
            <el-input v-model="form.remarks" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入内容" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {
  listManagementRoomAPI
} from "@/api/buildingControl/managementRoom.js";
import { getPageAPI } from "@/api/settingSystem/user.js";
import { dictionListItemAPI } from '@/api/settingSystem/dictionary.js';
import { maintenanceListAPI, saveOrUpdateAPI } from '@/api/buildingControl/maintenance.js';
import { nextTick, reactive, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { treeAPI } from "@/api/iotManagement/space.js";
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
const { title } = toRefs(props);
const treeProps = {
  // multiple: true,
  checkStrictly: true,
  children: 'children',
  label: 'name',
  value: 'id'
}
const emit = defineEmits(['submit']);
const dialog = ref();
const ruleFormRef = ref();
const form = reactive({
  portTypes: [
    {
      code: '',
      type: '',
      equipmentId: '',
      id: '',
      ip: ''
    }
  ],
  managementRoomId: '',
  portQuantity: "",
  spaceIds: [],
  classify: "1"
});
const state = reactive({
  RYData: [],
  JGata: [],
  JGWZList: [],
  GLJData: [],
  treeData: [],
  typeListP: [],
  typeListJ: [],
  rules: {
    classify: [{ required: true, message: '设备分类不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '设备不能为空', trigger: 'blur' }],
    password: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
    portQuantity: [{ required: true, message: '端口数量不能为空', trigger: 'blur' }],
    spaceIds: [{ required: true, message: '所在位置不能为空', trigger: 'blur' }],
    managementRoomId: [{ required: true, message: '管理间不能为空', trigger: 'blur' }],
    cabinetIds: [{ required: true, message: '机柜不能为空', trigger: 'blur' }],
    cabinetSpaceFullName: [{ required: true, message: '机柜位置不能为空', trigger: 'blur' }],
    type: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
  },
});

const DKoptions = [
  {
    value: 1,
    label: '语音点'
  },
  {
    value: 2,
    label: '数据点'
  },
  {
    value: 3,
    label: '光纤点'
  },
  {
    value: 4,
    label: '设备点'
  },
]

watch(() => form.spaceIds, (newVal, oldVal) => {
  selectGLJ();
})

watch(() => form.classify, (newVal, oldVal) => {
  // selectGLJName();
})

watch(() => form.cabinetIds, (newVal, oldVal) => {
  selectU();
})

watch(() => form.managementRoomId, (newVal, oldVal) => {
  selectGLJName();
})

onMounted(() => {
});

const open = () => {
  dialog.value.open();
  nextTick(() => {
    select();
  })
};

const addPort = () => {
  form.portTypes.push({
    code: '',
    type: '',
    equipmentId: '',
    id: '',
    ip: ''
  });
}

const delSport = (index) => {
  if (form.portTypes.length === 1) {
    ElMessage.error('请勿全部删除')
    return;
  }
  form.portTypes.splice(index, 1);
}

const select = () => {
  dictionListItemAPI({ dictCode: '005' }).then((res) => {
    state.typeListP = res.data.cachedResultList;
  });
  dictionListItemAPI({ dictCode: '006' }).then((res) => {
    state.typeListJ = res.data.cachedResultList;
  });
  let query = {
    deep: 4
  }
  treeAPI(query).then(res => {
    state.treeData = res.data
  })
  let data = {
    sysUserDtoFilter: {
      userStatus: 0
    }
  }
  getPageAPI(data).then(res => {
    state.RYData = res.data.dataList
  })
}

const selectGLJ = () => {
  listManagementRoomAPI({
    spacePath: form.spaceIds
  }).then(res => {
    if (res.success) {
      state.GLJData = res.data.dataList;
      if (form.managementRoomId) {
        var clear = true;
        state.GLJData.forEach(e => {
          if (form.managementRoomId == e.id) {
            clear = false
          }
        })
        if (clear) {
          form.managementRoomId = ''
        }
      }
    }
  })
}

const selectGLJName = () => {
  console.log(form.managementRoomId);
  if(form.managementRoomId == '' || !form.managementRoomId){
    form.cabinetIds = ''
    state.JGata  = []
    return;
  }
  maintenanceListAPI({
    classify: 4,
    managementRoomId: form.managementRoomId
  }).then(res => {
    if (res.success) {
      state.JGata = res.data.dataList;
    }
  })
}

const selectU = () => {
  if(form.cabinetIds == '' || !form.cabinetIds){
    state.JGWZList = [];
    form.cabinetSpaceFullName = ''
    return;
  }
  var typeName = ""
  state.JGata.forEach(o => {
    if (o.id == form.cabinetIds) {
      typeName = o.typeName;
    }
  })
  typeName = typeName.substring(0, typeName.length - 1);
  var list = [];
  for (var i = 1; i <= typeName; i++) {
    list.push({
      value: i + "U",
      label: i + "U",
    })
  }
  state.JGWZList = list
}

const updateType = (e) => {
  form.classify = e
  ruleFormRef.value.resetFields()
}

/** 保存 */
const submit = () => {
  if (form.spaceIds) {
    if (typeof form.spaceIds != "string") {
      form.spaceId = form.spaceIds[form.spaceIds.length - 1]
    }else{
      form.spaceId = form.spaceIds
    }
  }
  if (form.cabinetIds) {
    if (typeof form.cabinetIds != "string") {
      form.cabinetId = form.cabinetIds[form.cabinetIds.length - 1]
    }else{
      form.cabinetId = form.cabinetIds
    }
  }
  if (form.id) {
    subHandle(saveOrUpdateAPI, '编辑成功');
  } else {
    subHandle(saveOrUpdateAPI, '添加成功');
  }
  function subHandle(req, title) {
    req(form).then((res) => {
      if (res.success) {
        ElMessage.success(title);
        dialog.value.close();
        emit('submit');
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  }
};

defineExpose({
  form,
  open,
});
</script>
