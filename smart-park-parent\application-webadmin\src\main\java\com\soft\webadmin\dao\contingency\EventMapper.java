package com.soft.webadmin.dao.contingency;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.contingency.EventQueryDTO;
import com.soft.webadmin.model.contingency.Event;
import com.soft.webadmin.vo.contingency.EventVO;

import java.util.List;

/**
 * 应急事件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-17
 */
public interface EventMapper extends BaseMapper<Event> {

    List<EventVO> queryList(EventQueryDTO queryDTO);

}
