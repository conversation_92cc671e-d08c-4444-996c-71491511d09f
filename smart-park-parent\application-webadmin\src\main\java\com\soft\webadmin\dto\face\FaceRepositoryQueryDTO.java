package com.soft.webadmin.dto.face;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
public class FaceRepositoryQueryDTO extends MyPageParam {

    @ApiModelProperty("名单类型，1黑名单；2白名单；")
    private Integer listType;

    @ApiModelProperty("姓名/手机号")
    private String queryWord;

    @ApiModelProperty("性别，1男；2女；3未知；")
    private Integer sex;

    @ApiModelProperty("创建日期（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createDateStart;

    @ApiModelProperty("创建日期（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createDateEnd;

    private String enterType;

}
