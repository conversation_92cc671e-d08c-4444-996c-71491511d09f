package com.soft.webadmin.dto.visitor;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VisitorApplyRecordApproveDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("审核状态：2已通过；3未通过；")
    private Integer approveStatus;

    @ApiModelProperty("驳回理由")
    private String rejectReason;

    @ApiModelProperty("驳回备注")
    private String rejectRemark;

    private String enterType;

}
