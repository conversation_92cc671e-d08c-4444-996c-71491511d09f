import { request } from '@/utils/request.js';

// 分页查询
export const getPageAPI = (data) => {
  return request('get', '/equipment/warningSetting/list', data, 'F');
};

// 保存
export const addWarningSettingAPI = (data) => {
  return request('post', '/equipment/warningSetting/save', data);
};

// 删除
export const deleteWarningSettingAPI = (data) => {
  return request('post', '/equipment/warningSetting/delete', data, 'F');
};

// 能耗设备预警设置分页查询
export const getEnergyPageAPI = (data) => {
  return request('get', '/equipment/warningSetting/energyList', data, 'F');
};


// 获取告警项列表
export const getWarningItemsAPI = (data) => {
  return request('get', '/equipment/warningSetting/warning-items', data, 'F');
};
