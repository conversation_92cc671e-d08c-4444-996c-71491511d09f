<template>
<el-card class="box-card card-textBg">
    <template #header>
      <el-row justify="space-between" align="middle">
          <strong>设备详情</strong>
          <el-button type="primary" icon="Back" @click="onHandle">返回</el-button>
        </el-row>
    </template>
    <el-form label-position="top">
      <div>{{ detailData.equipmentName }}</div>
      <div class="detail-area">
        <el-row :gutter="40">
          <el-col :span="5">
            <el-form-item label="设备状态">
              <span v-if="detailData.equipmentStatus === 1" class="status-circle"
                style="background-color: #67C23A;"></span>
              <span v-if="detailData.equipmentStatus === 0" class="status-circle"
                style="background-color: #F56C6C;"></span>
              <span v-if="detailData.equipmentStatus === 5" class="status-circle"
                style="background-color: #73767a;"></span>
              <span>{{ detailData.equipmentStatus === 1 ? '正常' : detailData.equipmentStatus === 0 ? '故障' :
                detailData.equipmentStatus === 5 ? '已报废' : '' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="在线状态">
              <span v-if="detailData.runStatus === 1" class="status-circle" style="background-color: #67C23A;"></span>
              <span v-else class="status-circle" style="background-color: #F56C6C;"></span>
              <span>{{ detailData.runStatus === 1 ? '正常' : '离线' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="运维状态">
              <template v-if="detailData.maintStatus === 1 || detailData.maintStatus === 2">
                <span v-if="detailData.maintStatus === 1" class="status-circle"
                  style="background-color: #67C23A;"></span>
                <span v-else class="status-circle" style="background-color: #F56C6C;"></span>
                <span>{{ detailData.status === 1 ? '保养中' : '维修中' }}</span>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="设备标签">
              <template v-if="detailData.equipmentTags?.length > 0">
                <el-tag style="margin-right: 10px;" v-for="item in detailData.equipmentTags" :key="item.id">{{
                  item.name}}</el-tag>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="设备编号">
              {{ detailData.equipmentCode }}
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="设备类别">
              {{ detailData.subType }}
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="设备类型">
              {{ detailData.equipmentType }}
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="设备型号">
              {{ detailData.equipmentModel }}
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="负责人">
              {{ detailData.ownerName }}
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <el-tabs v-model="activeName" @tabChange="tabChange">
      <el-tab-pane label="基本信息" name="first">
        <baseInfo :detail-data="detailData"></baseInfo>
      </el-tab-pane>
      <el-tab-pane label="功能信息" name="second">
        <funcInfo ref="funcInfoRef"></funcInfo>
      </el-tab-pane>
      <el-tab-pane label="关联设备" name="third">
        <linkEquip ref="linkEquipmentRef"></linkEquip>
      </el-tab-pane>
      <el-tab-pane label="生命周期" name="fourth">
        <lifeCycle ref="lifeCycleRef" :equipment-id="detailData.equipmentId"></lifeCycle>
      </el-tab-pane>
    </el-tabs>
    </el-card>
</template>

<script setup>
import { viewEquipAPI } from "@/api/iotManagement/equipManage.js";

import baseInfo from "./baseInfo.vue";
import funcInfo from "./functionInfo.vue";
import lifeCycle from "./lifeCycleInfo.vue";
import linkEquip from "./linkEquipment.vue"

const props = defineProps({
  backIndex: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['showPage'])
const activeName = ref('first')

// 功能信息
const funcInfoRef = ref()

const linkEquipmentRef = ref()

const lifeCycleRef = ref()

const equipmentId = ref()

// 设备详情
const detailData = ref({})

const onHandle = () => {
  activeName.value = 'first'

  emit('showPage', props.backIndex);
};

const tabChange = () => {
  if (activeName.value === 'second') {
    funcInfoRef.value.equipmentId = detailData.value.equipmentId
    funcInfoRef.value.init()
  } else if (activeName.value === 'third') {
    linkEquipmentRef.value.init(detailData.value.equipmentRelationList)
  } else if (activeName.value === 'fourth') {
    lifeCycleRef.value.init()
  }
}


const init = () => {
  viewEquipAPI({ equipmentId: equipmentId.value }).then(res => {
    if (res.success) {
      detailData.value = res.data
    }
  })
}

defineExpose({
  equipmentId,
  init
})
</script>
