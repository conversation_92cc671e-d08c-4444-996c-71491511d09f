import { request } from '@/utils/request.js';

// 分页查询
export const getPageAPI = (data) => {
  return request('post', '/admin/upms/person/pageList', data);
};


// 查询岗位
export const getUserInfoAPI = (query) => {
  return request('get','/admin/upms/person/detail',query,'F')
}


// 创建用户
export const addUserAPI = (data) => {
  return request('post', '/admin/upms/person/add', data);
};

// 查询部门
export const getDeptListAPI = (data) => {
  return request('post', '/admin/upms/sysDept/list', data);
};


// 更新用户
export const updateUserAPI = (data) => {
  return request('post', '/admin/upms/person/update', data);
};

// 删除用户
export const deleteUserAPI = (data) => {
  return request('post', '/admin/upms/person/delete', data);
};

// 门禁树
export const entranceTreeAPI = () => {
  return request('get', '/core/space/entranceTree')
}

// 查询梯控控制器及楼层
export const controlInfoAPI = () => {
  return request('get', '/equipment/ladderControl/getEcsDeviceList')
}
