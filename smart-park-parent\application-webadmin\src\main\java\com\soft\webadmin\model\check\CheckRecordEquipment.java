package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.check.CheckRecordEquipmentVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * 智能巡检设备记录对象 sp_check_record_equipment
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@Data
@TableName(value = "sp_check_record_equipment")
public class CheckRecordEquipment {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 检查记录id */
    private Long recordId;

    /** 设备id */
    private Long equipmentId;

    /** 设备名称 */
    private String equipmentName;

    /** 设备编号 */
    private String equipmentCode;

    /** 设备位置 */
    private String equipmentSpace;

    /** 设备状态（1正常，2故障，3离线，5报废） */
    private Integer state;

    /** 上报时间 */
    private Date reportTime;


    @Mapper
    public interface CheckRecordEquipmentModelMapper extends BaseModelMapper<CheckRecordEquipmentVO, CheckRecordEquipment> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        CheckRecordEquipment toModel(CheckRecordEquipmentVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        CheckRecordEquipmentVO fromModel(CheckRecordEquipment entity);
    }

    public static final CheckRecordEquipmentModelMapper INSTANCE = Mappers.getMapper(CheckRecordEquipmentModelMapper.class);
}
