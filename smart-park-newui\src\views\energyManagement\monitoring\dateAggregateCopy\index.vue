<template>
  <div style="height: 100%;">
    <el-tabs style="height: 55px;" v-model="state.activeName" @tab-click="handleClick">
      <el-tab-pane label="水表" name="first"></el-tab-pane>
      <el-tab-pane label="电表" name="second"></el-tab-pane>
      <el-tab-pane label="气表" name="third"></el-tab-pane>
    </el-tabs>
    <div style="height: calc(100% - 55px);">
      <page-common v-model="state.tableHeight">
        <template #query>
          <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
            <el-form-item prop="queryName">
              <el-input v-model="formInline.queryName" placeholder="设备名称或编号" />
            </el-form-item>
            <el-form-item prop="spaceIdList">
              <el-cascader v-model="formInline.spaceIdList" :options="state.treeData" :props="defaultProps" clearable
                placeholder="安装位置" />
            </el-form-item>
            <el-form-item prop="energyTypeId">
              <el-cascader v-model="formInline.energyTypeId" :options="state.typeData" :props="typeProps" clearable
                placeholder="设备分类" />
            </el-form-item>
            <el-form-item prop="date">
              <el-date-picker value-format="YYYY-MM-DD" v-model="formInline.date" type="daterange" range-separator="到" start-placeholder="开始日期"
                :clearable="false" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
              <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </template>
        <template #table>
          <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
            <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
              :label="item.label" />
          </el-table>
          <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
            :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
            @size-change="sizeChange" @current-change="currentChange" />
        </template>
      </page-common>
    </div>
  </div>
</template>

<script setup>
import { energyConsumptionPooling } from '@/api/energyManagement/dateAggregateCopy.js';
import { treeAPI } from "@/api/iotManagement/space.js";
import { getSubitemTreeAPI } from '@/api/energyManagement/subitem.js';
import dayjs from "dayjs";

const firstDay = dayjs().startOf('month').format('YYYY-MM-DD');
const today = dayjs().format("YYYY-MM-DD");

let formInlineRef = ref()
const formInline = reactive({ equipmentType: '电表', date: [firstDay, today] })
const defaultProps = {
  checkStrictly: true,
  children: 'children',
  label: 'name',
  value: 'id',
  emitPath: false
}
const typeProps = {
  checkStrictly: true,
  children: 'children',
  label: 'energyName',
  value: 'id',
  emitPath: false
}

const state = reactive({
  activeName: 'second',
  treeData: [],
  typeData: [],
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'equipmentNo',
      label: '设备编号'
    },
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'spaceFullName',
      label: '安装位置'
    },
    {
      prop: 'energyType',
      label: '设备分类'
    },
    {
      prop: 'minCurrentValue',
      label: '起始数值'
    },
    {
      prop: 'maxCurrentValue',
      label: '截至数值'
    },
    {
      prop: 'currentValue',
      label: '差值'
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList([])
  getTree()
  selectType()
})

const getTree = () => {
  let query = {
    deep: 4
  }
  treeAPI(query).then(res => {
    state.treeData = res.data
  })
}

const handleClick = (TabsPaneContext) => {
  formInline.queryName = ''
  formInline.equipmentType = TabsPaneContext.props.label
  formInline.spaceIdList = ''
  formInline.energyTypeId = ''
  formInline.date = [firstDay, today]
  state.pagetion.pageNum = 1
  state.pagetion.pageSize = 10

  getList();
  selectType();
}

const selectType = () => {
  getSubitemTreeAPI({ equipmentType: formInline.equipmentType }).then((res) => {
    state.typeData = res.data;
  });
}

//分页
const getList = () => {
  let query = {
    queryName: formInline.queryName,
    equipmentType: formInline.equipmentType,
    spaceIdList: formInline.spaceIdList,
    energyTypeId: formInline.energyTypeId,
    startTime: formInline.date[0],
    endTime: formInline.date[1],
    ...state.pagetion,
  }
  energyConsumptionPooling(query).then( res => {
    state.pagetion.total = res.data.totalCount
    state.tableData = res.data.dataList
  })
}

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

//重置方法
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped>
.el-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);

    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
