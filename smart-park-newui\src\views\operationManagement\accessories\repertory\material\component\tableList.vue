<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="nameOrNo">
          <el-input v-model="formInline.nameOrNo" placeholder="备件名称/编号"/>
        </el-form-item>
        <el-form-item prop="classifyId">
          <el-tree-select v-model="formInline.classifyId" :data="state.classifyIdsOptions"
                          :render-after-expand="false"  check-strictly
                          node-key="id" :props="{label: 'classifyName'}" placeholder="备件分类"  clearable/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Download" @click="handleDown">导出</el-button>
      <el-button type="primary" icon="Setting" @click="handleSetting">批量设置</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" ref="table" show-overflow-tooltip>
        <el-table-column type="selection" width="55" />
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="160">
          <template #default="{row}">
            <el-button link type="primary" icon="Setting" @click="handleSetting(row)">设置</el-button>
            <el-button link type="primary" icon="Tickets" @click="handleView(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize"
                     :total="state.pagetion.total"
                     @size-change="sizeChange" @current-change="currentChange"/>
      <modalSetting ref="modal" @submit="handleRefresh"></modalSetting>
    </template>
  </page-common>
</template>

<script setup>
import {ElMessage ,ElTooltip} from 'element-plus';
import { Warning } from '@element-plus/icons-vue'

import modalSetting from '../modal/modalSetting.vue'

import { getCategoryTreeAPI } from "@/api/operationManagement/metarialCategory.js";
import { getMaterialAPI } from '@/api/operationManagement/material.js'

import { exportFile } from '@/utils/down.js'

const emit = defineEmits(['showPage'])

const formInlineRef = ref()
const modal = ref()
const table = ref()

const formInline = reactive({})
const state = reactive({
  classifyIdsOptions: [],
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'sparePartNo',
      label: '备件编号',
      width: 230,
      formatter: (row, column, cellValue) => {
        return row.isWarning ?
            h('div', [h(ElTooltip,{
              placement: 'top',
              content: row.inventoryQuantity > row.earlyWarningMost ? '库存过剩，请及时处理！' : '库存不足，请及时补充！'
            },  () => h(Warning,{style:{width: '1.5em', height: '1.5em',color:'red',verticalAlign:'middle',marginRight: '5px'}})) , cellValue])
            : cellValue
      }
    },
    {
      prop: 'sparePartName',
      label: '备件名称'
    },
    {
      prop: 'classifyName',
      label: '备件分类'
    },
    {
      prop: 'model',
      label: '规格型号'
    },
    {
      prop: 'unit',
      label: '单位'
    },
    {
      prop: 'unitPrice',
      label: '单价（元）'
    },
    {
      prop: '',
      label: '库存预警值',
      formatter: (row) => {
        return `${row.earlyWarningLeast ? '小于' + row.earlyWarningLeast + ',' : ''}${row.earlyWarningMost ? '大于' + row.earlyWarningMost : ''}`
      }
    },
    {
      prop: 'inventoryQuantity',
      label: '当前库存'
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getCategoryTree()
  getList()
})

// 获取分类树形
const getCategoryTree = () => {
  getCategoryTreeAPI().then(res => {
    state.classifyIdsOptions = res.data
  })
}

// 获取设备
const getList = () => {
  let query = {
    ...formInline,
    businessType: 'OPERATIONS',
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
  }

  getMaterialAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 导出
const handleDown = () => {
  let query = {
    ...formInline,
    businessType: 'OPERATIONS',
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
  }
  exportFile('/sparePart/info/export',query,'备件信息.xlsx')
}

// 批量设置
const handleSetting = (row = {}) => {
  if(row.id){ // 单个设置
    modal.value.open(true)
    nextTick(() => {
      modal.value.form.ids = [row.id]
      modal.value.form.sparePartName = row.sparePartName
      modal.value.form.classifyName = row.classifyName
    })
  }else { // 批量
    let ids = (table.value.getSelectionRows() || []).map(item => item.id)
    if(ids && ids.length){
      modal.value.open(false)
      nextTick(() => {
        modal.value.form.ids = ids
      })
    }else {
      ElMessage.warning('请勾选备件');
    }
  }
}

// 刷新
const handleRefresh = () => {
  table.value.clearSelection()
  getList()
}

// 详情
const handleView = ({id}) => {
  emit('showPage', 1, id)
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped></style>
