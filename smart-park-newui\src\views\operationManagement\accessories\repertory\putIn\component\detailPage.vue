<template>
  <div style="height: 100%;">
    <el-card class="box-card card-textBg">
      <template #header>
        <el-row justify="space-between" align="middle">
          <strong>入库信息</strong>
          <el-button type="primary" icon="Back" @click="showPage">返回</el-button>
        </el-row>
      </template>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <div class="detail-area">
        <el-form  label-position="top">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item label="入库单号">
                {{ data.inoutNo }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="入库类型">
                {{ state.typeOptions[data.type] }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="入库日期">
                {{ data.inoutDate }}
              </el-form-item>
            </el-col>
            <el-col :span="5" v-show="data.type == 1">
              <el-form-item label="申请人">
                {{ data.applyUserName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="创建人">
                {{ data.createUserName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="创建日期">
                {{ data.createTime }}
              </el-form-item>
            </el-col>

            <!--            1原始入库-->
            <el-col :span="24" v-show="data.type == 1">
              <el-form-item label="备注">
                {{ data.remark }}
              </el-form-item>
            </el-col>

            <!--            2盘盈入库-->
            <el-col :span="5" v-show="data.type == 2">
              <el-form-item label="关联盘点单">
                {{ data.stocktakingNo }}
              </el-form-item>
            </el-col>

          </el-row>
        </el-form>
      </div>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">入库明细</div>
      </div>
      <div class="detail-area">
        <el-table :data="data.recordVOList">
          <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                           :label="item.label"
                           :align="item.align" :formatter="item.formatter"/>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import {ElMessage, ElMessageBox} from "element-plus";

import {putoutDetailAPI, putoutAuditAPI} from '@/api/operationManagement/putout.js'

const emit = defineEmits(['showPage'])

const data = ref({})

const state = reactive({
  id: '',
  radio: 'spread',
  typeOptions: {
    1: '采购入库',
    2: '盘盈入库',
  },
  tableHeader: [
    {
      prop: 'sparePartVO.sparePartNo',
      label: '备件编号'
    },
    {
      prop: 'sparePartVO.sparePartName',
      label: '备件名称'
    },
    {
      prop: 'sparePartVO.classifyName',
      label: '备件分类'
    },
    {
      prop: 'sparePartVO.model',
      label: '规格型号'
    },
    {
      prop: 'sparePartVO.unitPrice',
      label: '单价（元）'
    },
    {
      prop: 'sparePartVO.unit',
      label: '单位'
    },
    {
      prop: 'storehouseName',
      label: '入库仓库'
    },
    {
      prop: 'changeQuantity',
      label: '入库数量'
    },
  ]
})

const loadPage = (id) => {
  state.id = id
  getDetail()
}

// 获取详情
const getDetail = () => {
  putoutDetailAPI({id: state.id}).then(res => {
    data.value = res.data
  })
}

// 同意
const handleAgree = () => {
  ElMessageBox.confirm(
      '确定同意入库吗？',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    putoutAuditAPI({id: state.id, examineState: 2}).then(res => {
      if (res.success) {
        getDetail()
        ElMessage.success('已审批')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 返回
const showPage = () => {
  emit('showPage', 0)
}

defineExpose({
  loadPage
})
</script>

<style scoped lang="less">
</style>
