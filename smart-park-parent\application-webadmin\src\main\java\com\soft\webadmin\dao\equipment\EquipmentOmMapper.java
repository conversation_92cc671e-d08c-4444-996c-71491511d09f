package com.soft.webadmin.dao.equipment;

import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.webadmin.dto.equipment.EquipmentOmQueryDTO;
import com.soft.webadmin.model.equipment.EquipmentOm;
import com.soft.webadmin.vo.equipment.EquipmentOmVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
public interface EquipmentOmMapper extends BaseDaoMapper<EquipmentOm> {

    List<EquipmentOmVO> queryList(EquipmentOmQueryDTO queryDTO);

    EquipmentOmVO queryById(@Param("equipmentId") Long equipmentId);

    List<EquipmentOmVO> queryFaultList(@Param("equipmentIds")List<Long> equipmentIds);

}
