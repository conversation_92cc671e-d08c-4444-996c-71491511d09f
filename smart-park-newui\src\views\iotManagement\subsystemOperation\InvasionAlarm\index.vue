<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" >
    <el-tab-pane label="设备信息" name="first">
      <tableList></tableList>
    </el-tab-pane>
    <el-tab-pane label="预警记录" name="second">
      <earlyWarning></earlyWarning>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import tableList from './component/tableList.vue';
import earlyWarning from './component/earlyWarning.vue'

import { events } from '@/utils/bus.js'

const activeName = ref('first')


const handleClick = (tab, event) => {
  events.emit('tabClick')
};
</script>
<style lang='less' scoped>
.el-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);

    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
