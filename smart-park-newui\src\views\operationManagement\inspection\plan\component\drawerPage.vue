<template>
  <el-drawer :modelValue="drawer" :before-close="cancelClick" size="750">
    <template #header>
      <h4>巡检计划信息</h4>
    </template>
    <template #default>
      <el-form ref="ruleFormRef" :model="form" label-width="100px" label-suffix=":">
        <div>
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">基本信息</div>
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="计划名称" prop="templateName">
                {{ form.planName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作班组" prop="workGroupId"> {{ form.workGroupName }} </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="排班方式" prop="dateList">
                <div v-if="form.scheduleType === 'MONTH'">
                  <el-tag v-for="date in form.dateList" :key="date" style="margin-left: 5px">每月{{ date }}号</el-tag>
                </div>
                <div v-else-if="form.scheduleType === 'WEEK'">
                  <el-tag v-for="date in form.dateList" :key="date" style="margin-left: 5px"> {{ formatWeek(date) }} </el-tag>
                </div>
                <div v-else>
                  <el-tag v-for="date in form.dateList" :key="date" style="margin-left: 5px">{{ date }}</el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="排班时间" prop="scheduleTime">
                {{ form.scheduleTime }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="派单时间" prop="advanceTime"> 提前 {{ form.advanceTime }} 小时 </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="预计工时" prop="workTime">
                <span v-if="form.workTime">{{ form.workTime }} 小时</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="有效期" prop="beginDate"> {{ form.beginDate }} 至 {{ form.endDate }} </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="状态" prop="state">
                <el-tag :type="form.state == 1 ? 'success' : 'danger'">{{ form.state == 1 ? '启用' : '停用' }}</el-tag>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注" prop="remark">
                {{ form.remark }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div>
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">巡检点</div>
          </div>
          <el-table :data="form.checkPlanPointList">
            <el-table-column label="序号" type="index" width="60" />
            <el-table-column v-for="column in state.columnList" :label="column.label" :prop="column.prop" :key="column.prop">
              <template #default="scope">
                <span v-if="column.prop === 'pointType'">{{ scope.row[column.prop] === 'EQUIPMENT' ? '设备' : '空间' }}</span>
                <span v-else>{{ scope.row[column.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
const props = defineProps({
  drawer: {
    type: Boolean,
    default: false,
  },
});
let { drawer } = toRefs(props);
const emit = defineEmits(['cancelClick']);
const form = ref({});
const state = reactive({
  columnList: [
    { prop: 'pointType', label: '巡检点类型' },
    { prop: 'dataName', label: '设备/空间' },
    { prop: 'spaceFullName', label: '位置' },
    { prop: 'templateName', label: '检查模板' },
  ],
});

const formatWeek = (val) => {
  if (val === '1') {
    return '每周日';
  } else if (val === '2') {
    return '每周一';
  } else if (val == '3') {
    return '每周二';
  } else if (val === '4') {
    return '每周三';
  } else if (val === '5') {
    return '每周四';
  } else if (val === '6') {
    return '每周五';
  } else if (val === '7') {
    return '每周六';
  }
};

const cancelClick = () => {
  emit('cancelClick');
};

defineExpose({
  form,
});
</script>

<style lang="less" scoped>
</style>
