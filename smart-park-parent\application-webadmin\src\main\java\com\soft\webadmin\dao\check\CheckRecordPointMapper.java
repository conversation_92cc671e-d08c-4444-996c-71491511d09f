package com.soft.webadmin.dao.check;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.model.check.CheckRecordPoint;
import com.soft.webadmin.vo.check.CheckRecordPointVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查记录点位Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-12
 */
public interface CheckRecordPointMapper extends BaseMapper<CheckRecordPoint> {

    List<CheckRecordPointVO> queryListByRecordId(@Param("recordId") Long recordId);

}
