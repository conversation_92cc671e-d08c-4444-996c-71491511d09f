<template>
  <el-tabs v-model="state.activeName" type="border-card" stretch class="comAttribute">
    <el-tab-pane label="样式" name="style">
      <component :is="curComponent.component + 'Att'" />
    </el-tab-pane>
    <el-tab-pane label="数据绑定" name="event" v-if="curComponent.params">
      <component-event></component-event>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import componentEvent from './componentEvent.vue';

import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent } = storeToRefs(webtopo)

const state = reactive({
  activeName: 'style'
})

watch(curComponent, (newVal, oldVal) => {
  state.activeName = curComponent.value && curComponent.value.params ? 'event' : 'style' 
}, { immediate : true })
</script>

<style lang='less' scoped>
.comAttribute {
  height: 100%;

  :deep(.is-active.el-tabs__item) {
    color: #2E2E2E !important;
    background-color: #FBFBFB !important;
  }

  :deep(.el-tabs__content) {
    height: calc(100% - 40px);
    background-color: #FBFBFB;
    overflow: auto;
  }
}
</style>