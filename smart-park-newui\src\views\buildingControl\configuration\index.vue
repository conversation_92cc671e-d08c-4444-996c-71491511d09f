<template>
  <div tabindex="0" class="draw">
    <tool-bar></tool-bar>
    <main>
      <section class="left">
        <tool-menu></tool-menu>
      </section>
      <section class="center">
        <tool-main></tool-main>
      </section>
      <section class="right">
        <tool-attribute></tool-attribute>
      </section>
    </main>
  </div>
</template>

<script setup>
import toolBar from './component/toolBar.vue';
import toolMenu from './component/toolMenu.vue'
import toolMain from './component/toolMain.vue';
import toolAttribute from './component/toolAttribute.vue'
import '@/utils/webtopo/ployfill.js'
</script>

<style lang='less' scoped>
.draw{
  width: 100%;
  height: 100%;
  main{
    height: calc(100% - 60px);
    overflow: hidden;
    display: flex;
  }
  .left,.right{
    width: 240px;
    height: 100%; 
    flex-shrink: 0;
  }
  .left{
    border-right: 1px solid #dcdfe6;
  }
  .center{
    flex: 1;
    overflow: hidden;
  }
}
</style>