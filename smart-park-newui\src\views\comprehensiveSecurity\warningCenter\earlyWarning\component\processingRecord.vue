<template>
  <page-common v-model="state.tableHeight" :operate-bool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="no">
          <el-input v-model="formInline.no" placeholder="事件编号"/>
        </el-form-item>
        <el-form-item prop="reportUserName">
          <el-input v-model="formInline.reportUserName" placeholder="上报人"/>
        </el-form-item>
        <el-form-item prop="spaceId">
          <el-cascader
              v-model="formInline.spaceId"
              :options="state.spaceOptions"
              :props="optionsProps"
              clearable
              placeholder="位置"/>
        </el-form-item>
        <el-form-item prop="eventId">
          <el-select v-model="formInline.eventId" filterable clearable placeholder="事件类型">
            <el-option v-for="item in state.eventOptions" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="type">
          <el-select v-model="formInline.type" filterable clearable placeholder="类别">
            <el-option v-for="(value,key) in state.typeOptions" :label="value" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="eventLevel">
          <el-select v-model="formInline.eventLevel" filterable clearable placeholder="事件等级">
            <el-option v-for="(value,key) in state.levelOptions" :label="value" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="formInline.status" filterable clearable placeholder="处理结果">
            <el-option v-for="(value,key) in state.statusOptions" :label="value" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="daterange">
          <el-date-picker
              v-model="formInline.daterange"
              type="daterange"
              range-separator="至"
              start-placeholder="上报开始日期"
              end-placeholder="上报结束日期"
              value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item prop="datehandlerange">
          <el-date-picker
              v-model="formInline.datehandlerange"
              type="daterange"
              range-separator="至"
              start-placeholder="处理开始日期"
              end-placeholder="处理结束日期"
              value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableData" row-key="id" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="80">
          <template #default="scope">
            <el-button link type="primary" icon="Tickets" @click="handleView(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :total="state.pagetion.total"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
      <saveModal ref="save" :eventOptions="state.eventOptions" :spaceOptions="state.spaceOptions" @submit="getList" ></saveModal>
    </template>
  </page-common>
</template>

<script setup>
import saveModal from '../modal/saveModal.vue'

import { warningPageAPI } from '@/api/comprehensiveSecurity/earlyWarning.js'
import { eventsPageAPI } from "@/api/comprehensiveSecurity/events.js";
import { treeAPI } from '@/api/iotManagement/space.js'
import {ElTag} from "element-plus";

// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'id',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};

const emit = defineEmits(['showPage'])

let save = ref()

const formInlineRef = ref();
const formInline = reactive({});

const state = reactive({
  title: '',
  spaceOptions: [],
  eventOptions: [],
  typeOptions:{
    1:'真实事件',
    2:'应急演练'
  },
  statusOptions:{
    1: '误报',
    2: '已解决'
  },
  statusColorOptions:{
    1:'#F59A23',
    2:'#03BF16'
  },
  levelOptions:{
    1:'普通',
    2:'重要',
    3:'严重'
  },
  levelTypeOptions:{
    1:"primary",
    2:"warning",
    3:'danger'
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'no',
      label: '事件编号',
      width: 210
    },
    {
      prop: 'eventVO.name',
      label: '事件类型'
    },
    {
      prop: 'eventVO.level',
      label: '事件等级',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { type: state.levelTypeOptions[cellValue]  }, { default: () => state.levelOptions[cellValue] })
      }
    },
    {
      prop: 'type',
      label: '类别',
      formatter: (row, column, cellValue) => {
        return state.typeOptions[cellValue]
      }
    },
    {
      prop: 'description',
      label: '描述'
    },
    {
      prop: '',
      label: '位置',
      formatter: (row, column, cellValue) => {
        return row.spaceFullName + (row.location ? ' - ' +  row.location :  '');
      }
    },
    {
      prop: 'status',
      label: '处理结果',
      formatter: (row, column, cellValue) => {
        return h('div', [h('span', {
          class: 'status-circle',
          style: 'background-color: ' + state.statusColorOptions[cellValue]
        }), state.statusOptions[cellValue]]);
      }
    },
    {
      prop: 'reportUserName',
      label: '上报人'
    },
    {
      prop: 'createTime',
      label: '上报时间',
      width: 160
    },
    {
      prop: 'handleTime',
      label: '处理时间',
      width: 160
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  }
})

onMounted(() => {
  getList()
  getSelectOptions()
})

// 获取基础下拉数据
const getSelectOptions = () => {
  eventsPageAPI().then(res => {
    state.eventOptions = res.data.dataList
  })

  treeAPI({ deep: 4 }).then((res) => {
    state.spaceOptions = res.data;
  });
}

// 获取事件
const getList = () => {
  let query = {
    ...formInline,
    ...state.pagetion
  };

  if(!query.status){
    query.status = [1,2]
  }

  if (formInline.daterange) {
    query.beginDate = formInline.daterange[0];
    query.endDate = formInline.daterange[1];
  }

  if(formInline.datehandlerange){
    query.handleBeginDate = formInline.datehandlerange[0];
    query.handleEndDate = formInline.datehandlerange[1];
  }

  warningPageAPI(query).then(res => {
    state.tableData = res.data.dataList;
    state.pagetion.total = res.data.totalCount;
  })
}
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};

// 详情
const handleView = ({id}) => {
  emit('showPage',1,id)
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped>
:deep(.status-circle) {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
  margin-bottom: 1px;
}
</style>
