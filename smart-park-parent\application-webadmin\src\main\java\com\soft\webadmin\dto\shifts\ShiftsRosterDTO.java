package com.soft.webadmin.dto.shifts;

import com.soft.common.core.validator.UpdateGroup;
import com.soft.webadmin.model.shifts.ShiftsCertificate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * ShiftsRosterDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsRosterDTO对象")
@Data
public class ShiftsRosterDTO {

    @ApiModelProperty(value = "花名册Id")
    @NotNull(message = "数据验证失败，花名册Id不能为空！", groups = {UpdateGroup.class})
    private Long rosterId;

    @NotNull(message = "业务类型不能为空！")
    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private String businessType;

    @NotNull(message = "姓名不能为空！")
    @ApiModelProperty(value = "姓名")
    private String showName;

    @NotNull(message = "性别不能为空！")
    @ApiModelProperty(value = "性别，1男，2女，3未知")
    private Integer sex;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @NotNull(message = "手机号码不能为空！")
    @ApiModelProperty(value = "手机号码")
    private String phone;

    @NotNull(message = "部门不能为空！")
    @ApiModelProperty(value = "用户所在部门Id")
    private Long deptId;

    @NotEmpty(message = "责任区域不能为空！")
    @ApiModelProperty(value = "责任区域ids")
    private List<Long> spaceIds;

//    @NotEmpty(message = "岗位不能为空！")
    @ApiModelProperty(value = "岗位IDS")
    private List<Long> deptPostIds;


    @ApiModelProperty(value = "操作证书")
    private List<ShiftsCertificate> certificateList;

    @ApiModelProperty(value = "职位状态:1在职 0离职")
    private Integer postStatus;

    @ApiModelProperty(value = "身份证号码")
    private String cardNo;

    @ApiModelProperty(value = "入职日期")
    private Date joinJobDate;

    @ApiModelProperty(value = "离职日期")
    private Date leaveJobDate;

    @ApiModelProperty(value = "离职原因")
    private String leaveJobReason;

    @ApiModelProperty(value = "附件,多个ID用'|'分割")
    private String annexPath;

    @ApiModelProperty(value = "删除标记(1: 正常 -1: 已删除)")
    private Integer deletedFlag;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建者Id")
    private Long createUserId;

    @ApiModelProperty(value = "最后更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新者Id")
    private Long updateUserId;

}
