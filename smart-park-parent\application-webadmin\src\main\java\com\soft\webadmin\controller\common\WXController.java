package com.soft.webadmin.controller.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.soft.admin.upms.config.UserApplicationConfig;
import com.soft.admin.upms.controller.LoginController;
import com.soft.admin.upms.model.SysMenu;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.model.constant.SysMenuType;
import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.PermGroupInfo;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.JwtUtil;
import com.soft.common.core.util.MyCommonUtil;
import com.soft.common.core.util.RedisKeyUtil;
import com.soft.common.wx.entity.UserAccessToken;
import com.soft.common.wx.util.WXUtil;
import com.soft.webadmin.enums.RedisKeyEnums;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 微信公众号服务
 * @date 2017/7/25
 * @since 1.0
 */
@RestController
@RequestMapping("/wx")
public class WXController {
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private WXUtil wxUtil;

    @Resource
    private UserApplicationConfig appConfig;

    /**
     * 获取授权
     *
     * @return
     */
    @GetMapping("auth")
    @NoAuthInterface
    public ResponseResult<String> auth(String code) throws URISyntaxException {
        UserAccessToken userAccessToken;
        //根据缓存取code信息
        RBucket<UserAccessToken> tokenRBucket = redissonClient.getBucket(RedisKeyEnums.WX_KEY.getKey() + code);
        if (tokenRBucket.get() == null) {
            userAccessToken = wxUtil.getOauth2AccessToken(code);

            if (userAccessToken == null || StringUtils.isBlank(userAccessToken.getOpenId())) {
                return ResponseResult.error(ErrorCodeEnum.INVALID_ACCESS_TOKEN, "微信登录失败，请重新进入");
            }
            tokenRBucket.set(userAccessToken, RedisKeyEnums.WX_KEY.getExpirationDuration(), RedisKeyEnums.WX_KEY.getTimeUnit());
        } else {
            userAccessToken = tokenRBucket.get();
        }

        String openId = userAccessToken.getOpenId();

        if(StringUtils.isBlank(openId)){
            return ResponseResult.error(ErrorCodeEnum.INVALID_ACCESS_TOKEN, "微信登录失败，请重新进入");
        }

        // 生成 token
        String sessionId = openId + "_" + MyCommonUtil.generateUuid();
        Map<String, Object> claims = new HashMap<>();
        claims.put("sessionId", sessionId);
        String token = JwtUtil.generateToken(claims, appConfig.getExpiration(), appConfig.getTokenSigningKey());
        TokenData tokenData = new TokenData();
        tokenData.setSessionId(sessionId);
        tokenData.setOpenId(openId);
        tokenData.setUserId(System.currentTimeMillis());
        this.putTokenDataToSessionCache(tokenData);

        // 这里手动将TokenData存入request，便于OperationLogAspect统一处理操作日志。
        TokenData.addToRequest(tokenData);
        return ResponseResult.success(token);
    }

    private void putTokenDataToSessionCache(TokenData tokenData) {
        String sessionIdKey = RedisKeyUtil.makeSessionIdKey(tokenData.getSessionId());
        String sessionData = JSON.toJSONString(tokenData, SerializerFeature.WriteNonStringValueAsString);
        RBucket<String> bucket = redissonClient.getBucket(sessionIdKey);
        bucket.set(sessionData);
        bucket.expire(appConfig.getSessionExpiredSeconds(), TimeUnit.SECONDS);
    }

}
