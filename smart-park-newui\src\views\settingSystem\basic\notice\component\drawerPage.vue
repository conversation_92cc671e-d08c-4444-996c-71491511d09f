<template>
  <el-drawer :modelValue="drawer" :before-close="cancelClick" size="750">
    <template #header>
      <h4>{{ form.type }}</h4>
    </template>
    <template #default>
      <el-form ref="ruleFormRef" :model="form" label-width="100px" label-suffix=":">
        <el-row>
          <el-col>
            <div style="text-align: center">
              <h3>{{ form.title }}</h3>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div style="text-align: center; font-size: 14px; margin: 10px 0">
              <span v-if="form.beginTime && form.endTime" style="margin-right: 30px">有效期：{{ form.beginTime }} 至 {{ form.endTime }}</span>
              <span>创建时间：{{ form.createTime }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div style="line-height: 26px" v-html="form.content"></div>
          </el-col>
        </el-row>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
const props = defineProps({
  drawer: {
    type: Boolean,
    default: false,
  },
});
let { drawer } = toRefs(props);
const emit = defineEmits(['cancelClick']);
const form = ref({});

const cancelClick = () => {
  emit('cancelClick');
};

defineExpose({
  form,
});
</script>

<style lang="less" scoped>
div {
  width: 100%;
  color: #72767b;
}
</style>
