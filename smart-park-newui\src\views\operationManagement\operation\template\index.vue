<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="templateName">
          <el-input v-model="formInline.templateName" placeholder="模板名称" />
        </el-form-item>
        <el-form-item prop="templateType">
          <el-select v-model="formInline.templateType" placeholder="模板类型">
            <el-option v-for="item in templateTypeOptions" :label="item.label" :value="item.value" :key="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新建模板</el-button>
    </template>

    <template #table>
      <el-table :data="state.tableData" show-overflow-tooltip :height="state.tableHeight">
        <el-table-column prop="templateName" label="模板名称" />
        <el-table-column prop="templateType" label="模板类型" :formatter="(row, column, cellValue) => {
          if(cellValue === '1') {
            return '巡检-设备'
          } else if (cellValue === '2') {
            return '巡检-空间'
          } else if (cellValue === '3') {
            return '维保-设备'
          }
        }">
        </el-table-column>
        <el-table-column prop="remark" label="备注" />
        <el-table-column prop="createTime" label="创建时间" width="160"/>
        <el-table-column label="操作" align="center" width="160">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
<!--            <el-button link icon="Document" type="success" @click="viewHandle(scope.row)">详情</el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
      <!-- 新建、编辑模板 -->
      <modal-page ref="modal" :title="state.title" @submit="getList" />
      <!-- 详情 -->
      <drawer-page ref="drawer" :drawer="state.drawer" @cancelClick="state.drawer = false" />
    </template>
  </page-common>
</template>

<script setup>
import { getTemplatePageAPI, getTemplateItemListAPI, deleteTemplateAPI } from '@/api/operationManagement/template.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import modalPage from './component/modalPage.vue';
import drawerPage from './component/drawerPage.vue';
import { calcPageNo } from '@/utils/util.js';
const modal = ref();
const drawer = ref();
const formInlineRef = ref();
const formInline = reactive({});
const state = reactive({
  tableData: [],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  title: '',
  drawer: false,
});
const templateTypeOptions = ref([
  { value: '1', label: '巡检-设备' },
  { value: '2', label: '巡检-空间' },
  { value: '3', label: '维保-设备' },
]);

onMounted(() => {
  getList();
});

const getList = () => {
  let params = {
    templateName: formInline.templateName,
    templateType: formInline.templateType,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };
  getTemplatePageAPI(params).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

/** 新建模版 */
const addHandle = () => {
  state.title = '新建模板';
  modal.value.form.id = undefined;
  modal.value.form.checkTemplateItemList = [];
  modal.value.open();
};

/** 编辑模板 */
const editHandle = (row) => {
  state.title = '编辑模板';
  modal.value.open();
  nextTick(() => {
    Object.assign(modal.value.form, row);
    getTemplateItemListAPI({ templateId: row.id }).then((res) => {
      if (res.success) {
        modal.value.form.checkTemplateItemList = res.data;
      }
    });
  });
};

/** 删除模板 */
const deleteHandle = (row) => {
  ElMessageBox.confirm('是否删除当前模板?', '提醒', {
    type: 'warning',
  }).then(() => {
    state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize);

    const params = {
      id: row.id,
    };
    deleteTemplateAPI(params).then((res) => {
      if (res.success) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};

/** 查看详情 */
const viewHandle = (row) => {
  nextTick(() => {
    drawer.value.form = JSON.parse(JSON.stringify(row));
    getTemplateItemListAPI({ templateId: row.id }).then((res) => {
      if (res.success) {
        drawer.value.form.checkTemplateItemList = res.data;
      }
    });
    state.drawer = true;
  });
};
</script>
