package com.soft.webadmin.dto.contingency;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * EventDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EventDTO对象")
@Data
public class EventQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "事件名称")
    private String name;

    @ApiModelProperty(value = "事件等级：1普通、2重要、3严重")
    private Integer level;

}
