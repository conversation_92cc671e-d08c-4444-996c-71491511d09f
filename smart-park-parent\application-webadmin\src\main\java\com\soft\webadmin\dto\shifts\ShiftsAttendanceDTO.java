package com.soft.webadmin.dto.shifts;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * ShiftsAttendanceDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsAttendanceDTO对象")
@Data
public class ShiftsAttendanceDTO {

    @ApiModelProperty(value = "排班考勤表ID")
    @NotNull(message = "数据验证失败，排班考勤表ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Long rosterId;

    @ApiModelProperty(value = "出勤日期")
    private String attendanceDate;

    @ApiModelProperty(value = "出勤开始时间")
    private String startTime;

    @ApiModelProperty(value = "出勤结束时间")
    private String endTime;

    @ApiModelProperty(value = "上班时间")
    private Date onWorkTime;

    @ApiModelProperty(value = "上班打卡结果")
    private String onWorkResult;

    @ApiModelProperty(value = "下班时间")
    private Date offWorkTime;

    @ApiModelProperty(value = "下班打卡结果")
    private String offWorkResult;

    @ApiModelProperty(value = "打卡设备")
    private String clockEquipmentName;

    @ApiModelProperty(value = "打卡地点")
    private String clockSpace;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "删除标记(1: 正常 -1: 已删除)")
    private Integer deletedFlag;

    @ApiModelProperty(value = "创建者Id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者Id")
    private Long updateUserId;

    @ApiModelProperty(value = "最后更新时间")
    private Date updateTime;

}
