import { request } from "@/utils/request";

// 查询角色
export const roleListAPI = (data) => {
  return request('post','/admin/upms/sysRole/list',data)
}

// 角色添加
export const roleAddAPI = (data) => {
  return request('post','/admin/upms/sysRole/add',data)
}

// 角色编辑
export const roleEditAPI = (data) => {
  return request('post','/admin/upms/sysRole/update',data)
}

// 角色删除
export const roleDelAPI = (data) => {
  return request('post','/admin/upms/sysRole/delete',data)
}


// 查看角色详情。
export const roleViewAPI = (query) => {
  return request('get','/admin/upms/sysRole/view',query,'F')
}

export const roleModifyStatus = (param) => {
  return request('get','/admin/upms/sysRole/modifyStatus', param,'F')
}
