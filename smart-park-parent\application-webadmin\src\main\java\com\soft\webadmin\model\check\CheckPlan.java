package com.soft.webadmin.model.check;

import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.*;
import com.soft.webadmin.vo.check.CheckPlanVO;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * 检查计划对象 sp_check_plan
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_check_plan")
public class CheckPlan extends BaseModel {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 计划类型（PATROL_INSPECTION巡检，MAINTENANCE维保） */
    private String planType;

    /** 计划名称 */
    private String planName;

    /** 巡检方式（1人工巡检，2智能巡检） */
    private Integer planMode;

    /** 工作班组id */
    private Long workGroupId;

    /** 排班类型（MONTH月，WEEK周，CUSTOM自定义） */
    private String scheduleType;

    /** 排班规则/首保日期 */
    private String scheduleRule;

    /** 维保周期，单位：月 */
    private Integer maintenanceCycle;

    /** 下次维保日期 */
    private Date maintenanceTimeNext;

    /** 开始时间 */
    private String startTime;

    /** 提前几小时派单 */
    private Integer advanceTime;

    /** 处理时限，单位：分钟 */
    private Long handleLimitDuration;

    /** 状态（0停用，1启用） */
    private Boolean state;

    /** 智能巡检所关联的设备 */
    private String equipmentIds;

    /** 备注 */
    private String remark;

    /** 删除标记(1: 正常 -1: 已删除) */
    @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;


    @Mapper
    public interface CheckPlanModelMapper extends BaseModelMapper<CheckPlanVO, CheckPlan> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        CheckPlan toModel(CheckPlanVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        CheckPlanVO fromModel(CheckPlan entity);
    }

    public static final CheckPlanModelMapper INSTANCE = Mappers.getMapper(CheckPlanModelMapper.class);
}
