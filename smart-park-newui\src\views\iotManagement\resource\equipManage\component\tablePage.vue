<template>
    <page-common v-model="state.tableHeight">
        <template #query>
            <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
                <el-form-item prop="equipmentWord">
                    <el-input placeholder="设备名称/厂商编号" v-model="formInline.equipmentWord" clearable />
                </el-form-item>
                <el-form-item prop="ip">
                    <el-input placeholder="IP地址" v-model="formInline.ip" clearable />
                </el-form-item>
                <el-form-item prop="spacePath">
                    <el-cascader v-model="formInline.spacePath" :options="state.spaceOptions" :props="state.spaceProps"
                        clearable placeholder="安装位置" />
                </el-form-item>
                <el-form-item prop="subType">
                    <el-select @change="onSubTypeChange" placeholder="设备类别" :options="subTypeOptions"
                        v-model="formInline.subType" clearable>
                        <el-option v-for="item in subTypeOptions" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="equipmentTypeId">
                    <el-select v-model="formInline.equipmentTypeId" placeholder="设备类型" clearable>
                        <el-option v-for="item in state.equipmentTypeOptions" :key="item.id" :label="item.name"
                            :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="equipmentStatus">
                    <el-select v-model="formInline.equipmentStatus" clearable filterable placeholder="设备状态">
                        <el-option v-for="(value, key) in state.equipmentStatusOptions" :label="value" :value="key" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="runStatus">
                    <el-select v-model="formInline.runStatus" clearable filterable placeholder="在线状态">
                        <el-option v-for="(value, key) in state.runStatusOptions" :label="value" :value="key" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="maintStatus">
                    <el-select v-model="formInline.maintStatus" clearable filterable placeholder="运维状态">
                        <el-option v-for="(value, key) in state.maintStatusOptions" :label="value" :value="key" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
                    <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template #operate>
            <el-button type="danger" icon="Delete" @click="deleteEquip">删除</el-button>
            <el-button type="primary" icon="Close" @click="scrapEquip">报废</el-button>
            <el-button type="primary" icon="Download" @click="exportExcel">下载设备二维码</el-button>
            <el-button type="primary" icon="Plus" @click="importFile">导入</el-button>
            <el-button type="primary" icon="Plus" @click="addHandle">新增设备</el-button>
        </template>
        <template #table>
            <el-table ref="table" :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
                <el-table-column type="selection" width="55" />
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                    :label="item.label" :width="item.width" :formatter="item.formatter">
                    <template #default="scope">
                        <div v-if="item.prop === 'equipmentStatus'">
                            <div
                                v-if="scope.row.equipmentStatus === 1 || scope.row.equipmentStatus === 0 || scope.row.equipmentStatus === 5">
                                <span v-if="scope.row.equipmentStatus === 1" class="status-circle"
                                    style="background-color: #67C23A;"></span>
                                <span v-if="scope.row.equipmentStatus === 0" class="status-circle"
                                    style="background-color: #F56C6C;"></span>
                                <span v-if="scope.row.equipmentStatus === 5" class="status-circle"
                                    style="background-color: #73767a;"></span>
                                <span>{{ scope.row.equipmentStatus === 1 ? '正常' : scope.row.equipmentStatus === 0 ? '故障'
                                    : scope.row.equipmentStatus === 5 ? '已报废' : '' }}</span>
                            </div>
                        </div>
                        <div v-else-if="item.prop === 'runStatus'">
                            <div
                                v-if="scope.row.equipmentStatus !== 5 && (scope.row.runStatus === 1 || scope.row.runStatus === 0)">
                                <span v-if="scope.row.runStatus === 1" class="status-circle"
                                    style="background-color: #67C23A;"></span>
                                <span v-else class="status-circle" style="background-color: #F56C6C;"></span>
                                <span>{{ scope.row.runStatus === 1 ? '在线' : '离线' }}</span>
                            </div>
                        </div>
                        <div v-else-if="item.prop === 'maintStatus'">
                            <div v-if="scope.row.equipmentStatus !== 5 && (scope.row.maintStatus === 1 || scope.row.maintStatus === 2)">
                                <span v-if="scope.row.maintStatus === 1" class="status-circle"
                                    style="background-color: #67C23A;"></span>
                                <span v-else class="status-circle" style="background-color: #F56C6C;"></span>
                                <span>{{ scope.row.maintStatus === 1 ? '保养中' : '维修中' }}</span>
                            </div>
                        </div>
                        <div v-else-if="item.prop === 'qrcode'">
                            <div>
                                <img src="@/assets/qrcode.svg" @click="qrCode(scope.row)">
                            </div>
                        </div>
                        <div v-else>
                            <span>
                                {{ scope.row[item.prop] ? scope.row[item.prop] : '' }}
                            </span>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="操作" align="center" width="290">
                    <template v-slot:default="scope">
                        <el-button @click="editHandle(scope.row)" link icon="Edit" type="primary">编辑</el-button>
                        <el-button @click="deleteHandle(scope.row)" link icon="Delete" type="danger">删除</el-button>
                        <el-button link type="primary" icon="Setting" @click.prevent="onHandle(scope.row)">
                            控制
                        </el-button>
                        <el-button link type="primary" icon="Tickets" @click.prevent="onDetail(scope.row.equipmentId)">
                            详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize"
                :total="state.pageParam.total" @size-change="sizeChange" @current-change="currentChange" />

            <el-dialog v-model="dialogVisible" title="预览" width="300">
                <div>
                    <el-col>
                        <el-card>
                            <img :src="equipmentQrCodeRef.qrcodeUrl" alt="二维码">
                        </el-card>
                    </el-col>
                </div>
                <template #footer>
                    <el-button @click="dialogVisible = false">关闭</el-button>
                    <el-button @click="onDownQrCode" type="primary">下载</el-button>
                </template>
            </el-dialog>

            <equip-control ref="equipControlRef" :isReFetchEquipmentAttributes="true" :title="'设备控制'" />


            <ModelPage ref="modalPageRef" @submit="getEquipList"></ModelPage>
            <scrapModal ref="scrapModalRef" @submit="getEquipList"></scrapModal>
            <DialogUpload ref="dialogUploadRef" @submit="getEquipList" :queryForm="formInline"></DialogUpload>
        </template>
    </page-common>
</template>


<script setup>

import { Search, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, formatter } from "element-plus";
import { deleteAPI, getEquipListAPI, getSubSystemTypesAPI, qrCodeAPI, viewEquipAPI } from "@/api/iotManagement/equipManage";
import { treeAPI } from '@/api/iotManagement/space.js';
import { equipemtListAPI } from "@/api/iotManagement/category.js";

import ModelPage from './modelPage.vue';
import DialogUpload from './modelUpload.vue'
import EquipControl from "@/components/equip/equipControl.vue";
import ScrapModal from "./scrapModal.vue";

import { exportFile } from "@/utils/down.js";
import { calcPageNo } from '@/utils/util.js'

const table = ref()
const emit = defineEmits(['showPage'])
const equipmentQrCodeRef = ref({
    qrcodeUrl: '',
    equipmentName: ''
})

const formInlineRef = ref();
// 设备id
const equipmentId = ref()
// 报废弹窗
const scrapModalRef = ref();

// 设备类别（子系统类型）
const subTypeOptions = ref([])

const modalPageRef = ref();
const equipControlRef = ref()
const dialogUploadRef = ref();

const formInline = reactive({ subType: '' });

const state = reactive({
    srcList: [],
    imageVisible: false,
    deptList: [],
    tableData: [],
    tableHeight: 100,
    tableHeader: [
        {
            prop: 'equipmentCode',
            label: '设备编号',
            width: 160
        },
        {
            prop: 'equipmentNo',
            label: '厂商编号',
            width: 160
        },
        {
            prop: 'equipmentName',
            label: '设备名称'
        },
        {
            prop: 'subType',
            label: '设备类别'
        },
        {
            prop: 'equipmentType',
            label: '设备类型'
        },
        {
            prop: 'spaceFullName',
            label: '安装位置'
        },
        {
            prop: 'equipmentStatus',
            label: '设备状态'
        },
        {
            prop: 'runStatus',
            label: '在线状态'
        },
        {
            prop: 'maintStatus',
            label: '运维状态'
        },
        {
            prop: 'ip',
            label: 'IP地址'
        },
        {
            prop: 'qrcode',
            label: '设备二维码',
            width: '130px'
        },
    ],
    pageParam: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
    },
    spaceOptions: [],
    spaceProps: {
        emitPath: false,
        checkStrictly: true,
        label: 'name',
        value: 'path',
        expandTrigger: 'hover',
    },
    equipmentTypeOptions: [],
    equipmentStatusOptions: {
        1: '正常',
        0: '故障',
        5: '报废'
    },
    runStatusOptions: {
        1: '在线',
        0: '离线'
    },
    maintStatusOptions: {
        1: '保养中',
        2: '维修中'
    }
});

onMounted(() => {
    loadSelectOptions()
    getEquipGroup()
    onQueryEquipmentTypes(formInline.subType, true)
    getEquipList()
})

// 安装位置
const loadSelectOptions = () => {
    treeAPI({ deep: 4 }).then((res) => {
        if (res.success) {
            state.spaceOptions = res.data;
        }
    });
}

// 获取设备类别列表
const getEquipGroup = async () => {
    const res = await getSubSystemTypesAPI()
    subTypeOptions.value = res.data.map(e => e.name)
}

const onSubTypeChange = (val) => {
    onQueryEquipmentTypes(val)
}

// 设备类型
const onQueryEquipmentTypes = (subType, init) => {
    if (!init) {
        formInline.equipmentTypeId = null
    }
    if (subType) {
        equipemtListAPI({ subType }).then(res => {
            if (res.success) {
                state.equipmentTypeOptions = res.data
            } else {
                state.equipmentTypeOptions = []
            }
        })
    } else {
        state.equipmentTypeOptions = []
    }
}

/** 查看详情 */
const onDetail = (equipmentId) => {
    emit('showPage', 1, equipmentId);
};

// 设备控制
const onHandle = (row) => {
    equipControlRef.value.open(row)
}


// 获取二维码
const dialogVisible = ref(false)
const qrCode = (row) => {
    equipmentId.value = row.equipmentId
    qrCodeAPI(equipmentId.value).then((res) => {
        equipmentQrCodeRef.value.qrcodeUrl = `data:image/png;base64,${res.data}`
        equipmentQrCodeRef.value.equipmentName = row.equipmentName
        dialogVisible.value = true
    })
}

/**
 * 下载二维码
 * @param scope
 */
const onDownQrCode = async () => {
    // 如果浏览器支持msSaveOrOpenBlob方法（也就是使用IE浏览器的时候），那么调用该方法去下载图片
    const imgUrl = equipmentQrCodeRef.value.qrcodeUrl
    if (window.navigator.msSaveOrOpenBlob) {
        let bstr = atob(imgUrl.split(',')[1])
        let n = bstr.length
        let u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        let blob = new Blob([u8arr])
        window.navigator.msSaveOrOpenBlob(blob, equipmentQrCodeRef.value.equipmentName + '.' + 'png')
    } else {
        // 这里就按照chrome等新版浏览器来处理
        let a = document.createElement('a')
        a.href = imgUrl
        a.setAttribute('download', equipmentQrCodeRef.value.equipmentName)
        a.click()
    }
}

// 分页查询
const getEquipList = () => {
    let query = {
        ...formInline,
        pageNum: state.pageParam.pageNum,
        pageSize: state.pageParam.pageSize
    };
    getEquipListAPI(query).then((res) => {
        state.tableData = res.data.dataList;
        state.pageParam.total = res.data.totalCount;
    });
}


// 查询
const onSubmit = () => {
    state.pageParam = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
    };
    getEquipList();
};

//重置表单
const onReset = () => {
    formInlineRef.value.resetFields();
    onSubmit();
};

// 新增设备
const addHandle = () => {
    modalPageRef.value.state.title = '新增设备'
    modalPageRef.value.form.equipmentId = undefined
    modalPageRef.value.open();
    modalPageRef.value.form.coordinate = {
        x: undefined,
        y: undefined,
        z: undefined,
    };

    nextTick(() => {
        modalPageRef.value.form.equipmentList = []
    })
}

// 编辑
const editHandle = (row) => {
    viewEquipAPI({ equipmentId: row.equipmentId }).then(res => {
        if (res.success) {
            modalPageRef.value.open(res.data);
            modalPageRef.value.state.title = '编辑设备'
        }
    })
}

const sizeChange = (pageSize) => {
    state.pageParam.pageSize = pageSize;
    getEquipList();
};

const currentChange = (pageNum) => {
    state.pageParam.pageNum = pageNum;
    getEquipList();
};

// 导入文件
const importFile = () => {
    dialogUploadRef.value.open()
}

// 导出
const exportExcel = async () => {
    let query = {
        ...formInline
    };
    await exportFile(
        "/equipment/qrcode/export",
        query,
        "物联设备二维码.xlsx"
    );
};

// 删除单个
const deleteHandle = (row) => {
    const equipmentIds = [row.equipmentId];
    deleteFun(equipmentIds);
};


// 批量删除
const deleteEquip = () => {
    let equipmentIds = table.value.getSelectionRows().map((item) => item.equipmentId)
    if (!equipmentIds.length) {
        return ElMessage.warning("请选择设备！");
    }

    deleteFun(equipmentIds);
};

const deleteFun = (equipmentIds) => {
    ElMessageBox.confirm("您确定删除吗?", "提醒", {
        type: "warning",
    }).then(() => {
        if (equipmentIds instanceof Array) {
            state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize, equipmentIds.length)
        } else {
            state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize)
        }
        deleteAPI({ equipmentIds }).then((res) => {
            if (res.success) {
                ElMessage.success("删除成功");
                getEquipList();
            }
        });
    });
};


// 报废
const scrapEquip = () => {
    let list = table.value.getSelectionRows()


    if (!list.length) {
        return ElMessage.warning("请选择设施！");
    }

    if (list.every((item) => item.equipmentStatus == 5)) {
        return ElMessage.warning("已报废的设施不可报废");
    }

    scrapModalRef.value.open();

    // 过滤已报废设施
    let equipmentNames = list.filter((item) => {
        return item.equipmentStatus != 5;
    })

    // 从equipmentNames数组中筛选出equipnentName、id
    const equipmentNameList = equipmentNames.map((item) => {
        return {
            equipmentName: item.equipmentName,
            equipmentId: item.equipmentId,
        };
    });

    const equipmentIds = equipmentNames.map((item) => {
        return item.equipmentId
    });

    nextTick(() => {
        Object.assign(scrapModalRef.value.form, {
            equipmentNameList,
            equipmentIds,
        });
    });
};

defineExpose({
    getEquipList
})

</script>
