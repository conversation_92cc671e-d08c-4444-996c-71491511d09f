package com.soft.webadmin.dto.shifts;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * ShiftsRosterLiabilityDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsRosterLiabilityDTO对象")
@Data
public class ShiftsRosterLiabilityDTO {

    @ApiModelProperty(value = "花名册ID")
    @NotNull(message = "数据验证失败，花名册ID不能为空！", groups = {UpdateGroup.class})
    private Long rosterId;

    @ApiModelProperty(value = "空间表ID")
    @NotNull(message = "数据验证失败，空间表ID不能为空！", groups = {UpdateGroup.class})
    private Long spaceId;

}
