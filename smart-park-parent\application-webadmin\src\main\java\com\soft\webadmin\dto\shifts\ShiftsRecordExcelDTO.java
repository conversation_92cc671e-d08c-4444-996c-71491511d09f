package com.soft.webadmin.dto.shifts;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.soft.common.core.util.easyexcel.ExcelPatternMsg;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * SparePartInoutDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@ApiModel("SparePartInoutDTO对象")
@Data
@ColumnWidth(value = 15)
public class ShiftsRecordExcelDTO {

    @Pattern(regexp = ExcelPatternMsg.DATE6, message = ExcelPatternMsg.DATE6_MSG)
    @ExcelProperty(value = "考勤周期(出勤月份 YYYY-MM)", index = 0)
    private String cycle;

    @NotNull(message = "姓名不能为空！")
    @ExcelProperty(value = "*姓名", index = 1)
    private String showName;

    @NotNull(message = "部门不能为空！")
    @ExcelProperty(value = "部门", index = 2)
    private String deptName;

    /**
     * 责任区域
     */
    @NotNull(message = "责任区域不能为空！")
    @ExcelProperty(value = "责任区域", index = 3)
    private String liabilityArea;

    @ExcelProperty(value = "1", index = 4)
    private String date1;

    @ExcelProperty(value = "2", index = 5)
    private String date2;

    @ExcelProperty(value = "3", index = 6)
    private String date3;

    @ExcelProperty(value = "4", index = 7)
    private String date4;

    @ExcelProperty(value = "5", index = 8)
    private String date5;

    @ExcelProperty(value = "6", index = 9)
    private String date6;

    @ExcelProperty(value = "7", index = 10)
    private String date7;

    @ExcelProperty(value = "8", index = 11)
    private String date8;

    @ExcelProperty(value = "9", index = 12)
    private String date9;

    @ExcelProperty(value = "10", index = 13)
    private String date10;

    @ExcelProperty(value = "11", index = 14)
    private String date11;

    @ExcelProperty(value = "12", index = 15)
    private String date12;

    @ExcelProperty(value = "13", index = 16)
    private String date13;

    @ExcelProperty(value = "14", index = 17)
    private String date14;

    @ExcelProperty(value = "15", index = 18)
    private String date15;

    @ExcelProperty(value = "16", index = 19)
    private String date16;

    @ExcelProperty(value = "17", index = 20)
    private String date17;

    @ExcelProperty(value = "18", index = 21)
    private String date18;

    @ExcelProperty(value = "19", index = 22)
    private String date19;

    @ExcelProperty(value = "20", index = 23)
    private String date20;

    @ExcelProperty(value = "21", index = 24)
    private String date21;

    @ExcelProperty(value = "22", index = 25)
    private String date22;

    @ExcelProperty(value = "23", index = 26)
    private String date23;

    @ExcelProperty(value = "24", index = 27)
    private String date24;

    @ExcelProperty(value = "25", index = 28)
    private String date25;

    @ExcelProperty(value = "26", index = 29)
    private String date26;

    @ExcelProperty(value = "27", index = 30)
    private String date27;

    @ExcelProperty(value = "28", index = 31)
    private String date28;

    @ExcelProperty(value = "29", index = 32)
    private String date29;

    @ExcelProperty(value = "30", index = 33)
    private String date30;

    @ExcelProperty(value = "31", index = 34)
    private String date31;

    @ExcelIgnore
    private Long rosterId;

    @ExcelIgnore
    private Long deptId;
}
