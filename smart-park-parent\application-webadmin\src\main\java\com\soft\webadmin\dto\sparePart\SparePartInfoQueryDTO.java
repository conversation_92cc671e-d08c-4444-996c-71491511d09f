package com.soft.webadmin.dto.sparePart;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SparePartInfoDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartInfoDTO对象")
@Data
public class SparePartInfoQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "备件名称或编号")
    private String nameOrNo;

    @ApiModelProperty(value = "分类id")
    private Long classifyId;

}
