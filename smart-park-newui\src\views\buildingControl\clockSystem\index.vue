<template>
  <tree-page-common
    v-model="state.tableHeight"
    :leftBool="false"
    :operateBool="false"
    :queryBool="false"
  >
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column
          v-for="(item, index) in state.tableHeader"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :align="item.align"
          :formatter="item.formatter"
        />
      </el-table>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum"
        :page-size="state.pagetion.pageSize"
        :total="state.pagetion.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </template>
  </tree-page-common>
</template>

<script setup>
import {  ElTag } from "element-plus";

let formInlineRef = ref();
const formInline = reactive({
  spaceIdList: [],
});

const state = reactive({
  tableHeight: 100,
  tableData: [
    {
      equipmentNo: 2,
      deviceLocation: "3F屏幕控制室",
      deviceType: "五寸数字时钟",
      deviceStatus: 1,
      hardwareVersion: "2",
      softwareVersion: "12",
    },
    {
      equipmentNo: 7,
      deviceLocation: "1F南面计时记分",
      deviceType: "五寸数字时钟",
      deviceStatus: 1,
      hardwareVersion: "2",
      softwareVersion: "12",
    },
    {
      equipmentNo: 11,
      deviceLocation: "1F南面录放室",
      deviceType: "五寸数字时钟",
      deviceStatus: 1,
      hardwareVersion: "2",
      softwareVersion: "12",
    },
    {
      equipmentNo: 13,
      deviceLocation: "1F南面办公室",
      deviceType: "五寸数字时钟",
      deviceStatus: 1,
      hardwareVersion: "2",
      softwareVersion: "12",
    },
    {
      equipmentNo: 17,
      deviceLocation: "1F南面裁判室",
      deviceType: "五寸数字时钟",
      deviceStatus: 1,
      hardwareVersion: "2",
      softwareVersion: "12",
    },
    {
      equipmentNo: 18,
      deviceLocation: "1F南面篮球训练馆门厅",
      deviceType: "指针式时钟",
      deviceStatus: 0,
      hardwareVersion: "1",
      softwareVersion: "1",
    },
    {
      equipmentNo: 22,
      deviceLocation: "1F西南面门厅",
      deviceType: "指针式时钟",
      deviceStatus: 0,
      hardwareVersion: "1",
      softwareVersion: "1",
    },  {
      equipmentNo: 23,
      deviceLocation: "1F西面门厅",
      deviceType: "指针式时钟",
      deviceStatus: 0,
      hardwareVersion: "1",
      softwareVersion: "1",
    },  {
      equipmentNo: 24,
      deviceLocation: "1F北面门厅",
      deviceType: "指针式时钟",
      deviceStatus: 0,
      hardwareVersion: "1",
      softwareVersion: "1",
    },  {
      equipmentNo: 25,
      deviceLocation: "1F南面游泳馆门厅",
      deviceType: "指针式时钟",
      deviceStatus: 0,
      hardwareVersion: "1",
      softwareVersion: "1",
    },
  ],
  tableHeader: [
    {
      prop: "equipmentNo",
      label: "设备编号",
    },
    {
      prop: "deviceType",
      label: "设备类型",
    },
    {
      prop: "deviceLocation",
      label: "设备位置",
    },
    {
      prop: "deviceStatus",
      label: "设备状态",
      formatter: (row, column, cellValue) => {
        if (cellValue == 1) {
          return h(ElTag, { type: "success" }, { default: () => "在线" })
        } else {
          return h(ElTag, { type: "danger" }, { default: () => "离线" })
        }
      }
    },
    {
      prop: "hardwareVersion",
      label: "硬件版本",
    },
    {
      prop: "softwareVersion",
      label: "软件版本",
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 10,
  },
});

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
};

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
};
</script>

<style lang="less" scoped></style>
