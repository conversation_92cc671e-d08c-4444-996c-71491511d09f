<template>
  <dialog-common ref="dialog" title="驳回" @submit="submit" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-form-item label="驳回原因" prop="rejectReason">
        <el-select v-model="form.rejectReason" placeholder="请选择驳回原因">
          <el-option v-for="item in state.reasonOptions" :key="item.value" :label="item.label" :value="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="rejectRemark" v-if="form.rejectReason == '其它'">
        <el-input v-model="form.rejectRemark" :rows="5" type="textarea" :maxlength="50" show-word-limit
          placeholder="请输入备注" />
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { audit } from '@/api/convenientPassage/check.js'
import { ElMessage } from "element-plus";

const emit = defineEmits(["submit"]);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  reasonOptions: [
    {
      label: "场馆预约已满，您的申请未通过！",
      value: 1,
    },
    {
      label: "场馆管制，您的申请未通过！",
      value: 2,
    },
    {
      label: "其它",
      value: 3,
    },
  ],
  rules: {
    rejectReason: [
      { required: true, message: "请选择驳回原因" },
    ],
    rejectRemark: [
      { required: true, message: "请输入备注"}
    ],
  },
});

// 提交表单
const submit = () => {
  if(form.rejectReason == '其它'){
    form.rejectReason = form.rejectRemark + '，您的申请未通过！'
    delete form.rejectRemark
  }

  // console.log(form);
  audit({
    ...form,
    approveStatus: 3
  }).then((res) => {
    if (res.success) {
      ElMessage.success("驳回成功");
      dialog.value.close();
      emit("submit");
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

const open = () => {
  dialog.value.open();
};

defineExpose({
  form,
  open,
});
</script>
