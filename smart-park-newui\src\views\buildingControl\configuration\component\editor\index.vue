<template>
  <!-- 拖拽画布 -->
  <vue3-draggable-resizable v-model:x="state.x" v-model:y="state.y" :w="200" :h="200" :handles="[]" :draggable="state.draggable"
    @mousewheel.prevent="mouseWheel">
    <!-- 画布 -->
    <div tabindex="0" id="container" :style="{
        ...getCanvasStyle(canvasStyle), width: changeStyleWithScale(canvasStyle.width, canvasStyle.scale) + 'px',
        height: changeStyleWithScale(canvasStyle.height, canvasStyle.scale) + 'px',
      }" @dragover="handleDragOver" @drop="handleDrop" @keydown.ctrl.s="saveHandle" @keydown.ctrl.v="pasteHandle">
      <shape-type @dragMove="dragMove" @saveHandle="saveHandle" @pasteHandle="pasteHandle"></shape-type>
    </div>
  </vue3-draggable-resizable>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { v4 as uuidv4 } from 'uuid'

import shapeType from './shapeType.vue'

import Vue3DraggableResizable from 'vue3-draggable-resizable'
import { getStyle } from '@/utils/webtopo/style.js'
import {changeStyleWithScale, changeComponentsSizeWithScale, getOriginStyle} from '@/utils/webtopo/math.js'

import { webtopoStore } from '@/store/modules/webtopo.js'
import {events} from "@/utils/bus.js";

const webtopo = webtopoStore()

let { canvasStyle, componentData, copyCurComponent } = storeToRefs(webtopo)

const emit = defineEmits(['dragging'])

const state = reactive({
  x: 40,
  y: 40,
  draggable: true,
  flag: false // 节流阀
})

onMounted(() => {
  events.on('canvasInit', canvasInit)
  events.on('handleScale', handleScale)
})

onBeforeUnmount(() => {
  events.off('canvasInit')
  events.off('handleScale')
})

const getCanvasStyle = (style) => {
  return getStyle(style, ['width', 'height', 'scale'])
}

// 画布初始化
const canvasInit = ({x, y}) => {
  state.x = x
  state.y = y
}


// 在画布移动
const handleDragOver = (e) => {
  e.preventDefault()
  e.dataTransfer.dropEffect = 'copy'
}

// 目标放在画布上 bool ture 画布拖拽 false 粘贴
const handleDrop = (e) => {
  e.preventDefault()
  e.stopPropagation()
  const component = JSON.parse(e.dataTransfer.getData('info'))
  const rectInfo = document.querySelector('#container').getBoundingClientRect()

  if(component.type === 'svg'){
    component.style.lineWidth = changeStyleWithScale(component.style.lineWidth,canvasStyle.value.scale)
    component.points.forEach(item => {
      item.x = item.x + (e.clientX - rectInfo.x - 150)
      item.y = item.y + (e.clientY - rectInfo.y - 10)
    })
  }else{
    component.style.width = changeStyleWithScale( component.style.width,canvasStyle.value.scale)
    component.style.height = changeStyleWithScale( component.style.height,canvasStyle.value.scale)
    component.style.top = e.clientY - rectInfo.y
    component.style.left = e.clientX - rectInfo.x
  }

  component.id = uuidv4()

  componentData.value.push(component)
}

// 防止拖动组件移动画布
const dragMove = (bool) => {
  state.draggable = bool
}

// 粘贴
const pasteHandle = () => {
  if (copyCurComponent.value) {
    const component = JSON.parse(JSON.stringify(copyCurComponent.value))
    if(component.type === 'svg'){
      if(component.points){
        component.points = [
          {
            "x": 10,
            "y": 10,
            "node": 1
          },
          {
            "x": 150,
            "y": 10,
            "node": 2
          },
          {
            "x": 290,
            "y": 10,
            "node": 1
          }
        ]
      }
    }else {
      component.style.top = 0
      component.style.left = 0
    }

    component.id = uuidv4()
    componentData.value.push(component)
    ElMessage.success('粘贴成功')
  }
}

// 保存
const saveHandle = (event) => {
  event.returnValue = false // 阻止直接保存网页
}

// 放大或缩小
const mouseWheel = (event) => {
  handleScale({
    offsetX: event.offsetX,
    offsetY: event.offsetY,
    scale: canvasStyle.value.scale  + event.deltaY * - 0.01,
    oriScale: canvasStyle.value.scale
  })
}

// 放大或缩小
const handleScale = ({offsetX, offsetY, scale, oriScale}) => {
  if (state.flag) return true
  state.flag = true

  if(scale >= 10 && scale <= 200){
    canvasStyle.value.scale = changeComponentsSizeWithScale(canvasStyle.value, componentData.value ,scale ,oriScale)
    state.x = state.x + parseFloat(changeStyleWithScale(getOriginStyle(offsetX, oriScale),oriScale - scale).toFixed(2))
    state.y = state.y + parseFloat(changeStyleWithScale(getOriginStyle(offsetY, oriScale),oriScale - scale).toFixed(2))
  }

  nextTick(() => {
    state.flag = false
  })
}
</script>

<style lang='less' scoped>
.vdr-container.active {
  border-color: transparent;
}

#container {
  position: relative;
  outline: 0px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain!important;
}
</style>
