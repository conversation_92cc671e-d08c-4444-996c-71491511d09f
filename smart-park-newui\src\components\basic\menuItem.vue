<template>
  <template v-for="(item, index) in menuList" :key="item.menuId">
    <el-sub-menu :index="item.menuId" v-if="item.children?.length" v-show="item.meta.isHidden">
      <template #title>
        <el-icon>
          <component :is="item.meta.icon">
          </component>
        </el-icon>
        <span>{{ item.meta.title }}</span>
      </template>
      <menu-item :menuList="item.children"></menu-item>
    </el-sub-menu>
    <el-menu-item v-else :index="item.path" v-show="item.meta.isHidden">
      <el-icon v-if="item.meta.icon">
        <component :is="item.meta.icon">
        </component>
      </el-icon>
      <template #title>{{ item.meta.title }}</template>
    </el-menu-item>
  </template>
</template>

<script setup>
defineOptions({
  name: 'menuItem'
})
const props = defineProps({
  menuList: {
    type: Array,
    default: []
  }
})

let { menuList } = toRefs(props)
</script>

<style lang='less' scoped></style>
