package com.soft.webadmin.enums;

/**
 * 工单操作类型
 */
public enum WorkOrderOperateEnums {

    CREATE(1, "创建工单"),
    DISPATCH(2, "派单"),
    RESPONSE(3, "响应"),
    RETURN(4, "退回"),
    CLOSE(5, "关闭工单"),
    // QUOTATION(6, "提交报价单"),
    // APPROVED(7, "报价单审批通过"),
    // REFUSE(8, "报价单已驳回"),
    // PENDING(9, "挂单"),
    // RESTART(10, "取消挂单"),
    FINISH(11, "完成工单"),
    EVALUATE(12, "评价工单"),
    // REJECT(13, "驳回"),
    ;

    private Integer value;

    private String name;

    public Integer getValue() {
        return value;
    }

    WorkOrderOperateEnums(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
