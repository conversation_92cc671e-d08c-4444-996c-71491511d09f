<template>
  <dialog-common ref="dialog" title="新增入库" @submit="submit" @onClose="onclose" :formRef="ruleFormRef" :width="1350"
                 class="commonTextarea">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="0" label-suffix=":">
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <el-row>
        <el-col :span="8">
          <el-form-item label="入库单号" prop="inoutNo" label-width="110px">
            <el-input v-model="form.inoutNo" placeholder="请输入入库单号" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="入库日期" prop="inoutDate" label-width="110px">
            <el-date-picker
                v-model="form.inoutDate"
                type="date"
                placeholder="请选择入库日期"
                value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请人" prop="applyUserId" label-width="110px">
            <el-select v-model="form.applyUserId" placeholder="请选择申请人(可直接搜索)" clearable filterable>
              <el-option v-for="item in state.userOptions" :label="item.showName + (item.deptName ? ' - ' + item.deptName : '')"
                         :value="item.userId"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="备注" prop="remark" label-width="110px">
        <el-input v-model="form.remark" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit
                  placeholder="请输入备注" style="width: 96%"/>
      </el-form-item>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">备件信息</div>
      </div>
      <div class="operate">
        <el-button type="primary" icon="Plus" @click="handleRelevance">关联备件</el-button>
        <el-button type="primary" icon="Plus" @click="handleAdd">新增</el-button>
      </div>
      <el-table :data="form.quantityChangeList">
        <el-table-column prop="" label="备件名称">
          <template #default="{row,$index}">
            <el-form-item :prop="'quantityChangeList.' + $index + '.sparePartName'"  :rules="{required: true, message: '请输入备件名称'}" v-if="row.isEdit">
              <el-input v-model="row.sparePartName" placeholder="备件名称" :disabled="Boolean(row.sparePartId)"/>
            </el-form-item>
            <span v-else>{{ row.sparePartName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="备件分类">
          <template #default="{row,$index}">
            <el-form-item :prop="'quantityChangeList.' + $index + '.classifyId'"  :rules="{required: true, message: '请选择备件分类'}" v-if="row.isEdit">
              <el-tree-select v-model="row.classifyId" :data="state.sparePartOptions" @node-click="(node) => { row.classifyName = node.classifyName }"
                              :render-after-expand="false"  node-key="id" :props="{label: 'classifyName'}" placeholder="备件分类" :disabled="Boolean(row.sparePartId)"/>
            </el-form-item>
            <span v-else>{{ row.classifyName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="规格型号">
          <template #default="{row,$index}">
            <el-form-item :prop="'quantityChangeList.' + $index + '.model'"  :rules="{required: true, message: '请输入规格型号'}" v-if="row.isEdit">
              <el-input v-model="row.model" placeholder="规格型号" :disabled="Boolean(row.sparePartId)"/>
            </el-form-item>
            <span v-else>{{ row.model }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="单价（元）">
          <template #default="{row,$index}">
            <el-form-item :prop="'quantityChangeList.' + $index + '.unitPrice'"  :rules="{required: true, message: '请输入单价'}"  v-if="row.isEdit">
              <el-input-number v-model="row.unitPrice" :min="1" :disabled="Boolean(row.sparePartId)"/>
            </el-form-item>
            <span v-else>{{ row.unitPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="单位">
          <template #default="{row,$index}">
            <el-form-item :prop="'quantityChangeList.' + $index + '.unit'"  :rules="{required: true, message: '请输入单位'}" v-if="row.isEdit">
              <el-input v-model="row.unit" placeholder="单位" :disabled="Boolean(row.sparePartId)"/>
            </el-form-item>
            <span v-else>{{ row.unit }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="入库仓库">
          <template #default="{row,$index}">
            <el-form-item :prop="'quantityChangeList.' + $index + '.storehouseId'"  :rules="{required: true, message: '请选择入库仓库'}" v-if="row.isEdit">
              <el-select v-model="row.storehouseId" placeholder="入库仓库" clearable filterable>
                <el-option v-for="item in state.storehouseOptions" :label="item.storehouseName" :value="item.id" @click.native="() => {row.storehouseName = item.storehouseName} " />
              </el-select>
            </el-form-item>
            <span v-else>{{ row.storehouseName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="入库数量">
          <template #default="{row,$index}">
            <el-form-item :prop="'quantityChangeList.' + $index + '.changeQuantity'"  :rules="{required: true, message: '请输入入库数量'}" v-if="row.isEdit">
              <el-input-number v-model="row.changeQuantity" :min="1" step-strictly/>
            </el-form-item>
            <span v-else>{{ row.changeQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="160">
          <template #default="{row,$index}">
            <div>
              <el-button link type="primary" icon="Edit" @click="handleEdit(row)" v-if="!row.isEdit">编辑</el-button>
              <el-button link type="success" icon="Document" @click="handleSave(row,$index)" v-if="row.isEdit">保存</el-button>
              <el-button link type="danger" icon="Delete" @click="handleDelete($index)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <chooseAccessories ref="choose" @submit="handleReceive"></chooseAccessories>
  </dialog-common>
</template>

<script setup>
import {ElMessage, ElInput} from 'element-plus';

import chooseAccessories from '@/views/operationManagement/workOrder/component/chooseAccessories.vue'

import { listUsersAPI } from '@/api/settingSystem/user.js';
import { getCategoryTreeAPI } from "@/api/operationManagement/metarialCategory.js";
import { getStorePageAPI } from '@/api/operationManagement/storeManagement.js'

import { putoutSaveAPI } from '@/api/operationManagement/putout.js'
import dayjs from "dayjs";

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const choose = ref()

const form = reactive({
  quantityChangeList: []
});

const state = reactive({
  userOptions: [], // 人员
  sparePartOptions: [], //备件分类
  storehouseOptions: [], // 入库仓库
  rules: {
    inoutNo: [{required: true, message: '请输入入库单号'},],
    inoutDate: [{required: true, message: '请选择入库日期'}],
    applyUserId: [{required: true, message: '请选择申请人'}]
  },
});

onMounted(() => {
  loadUserList()
  getCategoryTree()
  getStorePage()
})

/** 查询用户 */
const loadUserList = () => {
  listUsersAPI({}).then((res) => {
    if (res.success) {
      state.userOptions = res.data.dataList;
    }
  });
};

// 获取分类树形
const getCategoryTree = () => {
  getCategoryTreeAPI().then(res => {
    state.sparePartOptions = res.data
  })
}

// 获取仓库
const getStorePage = () => {
  getStorePageAPI({state: 1, businessType: 'OPERATIONS'}).then(res => {
    state.storehouseOptions = res.data.dataList
  })
}

// 新增
const handleAdd = () => {
  form.quantityChangeList.push({
    isEdit: true
  })
}

// 编辑
const handleEdit = (row) => {
  console.log(row)
  row.isEdit = true
}

// 保存
const handleSave = (row,index) => {
  ruleFormRef.value.validateField(
      ['sparePartName','classifyId','model','unitPrice','unit','storehouseId','changeQuantity','changeQuantity']
          .map(key => 'quantityChangeList.' + index + '.' + key)).then(valid => {
    row.isEdit = false
  })
}

// 删除
const handleDelete = (index) => {
  form.quantityChangeList.splice(index,1)
}


// 关联备件
const handleRelevance = () => {
  choose.value.open()
}

// 接收配件信息
const handleReceive = (list) => {
  form.quantityChangeList = [...form.quantityChangeList, ...list.filter(item => {
    item.changeQuantity = 1
    item.isEdit = true
    return form.quantityChangeList.findIndex(i => i.id == item.id) == -1
  })]
}

// 关闭
const onclose = () => {
  form.quantityChangeList = []
}

// 提交表单
const submit = () => {
  let subForm = JSON.parse(JSON.stringify(form))

  putoutSaveAPI({
    ...subForm,
    businessType: 'OPERATIONS', // 运维
    operateType: 1 , // 入库
    type: 1,
  }).then(res => {
    if (res.success) {
      ElMessage.success('新增成功');
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = () => {
  dialog.value.open();
  form.inoutNo = 'RK' + dayjs().format('YYYYMMDDHHmmss0SSS')
}

defineExpose({
  form,
  open,
});
</script>

<style scoped lang="less">
.operate {
  margin: 10px 0;
  text-align: right;
}
</style>
