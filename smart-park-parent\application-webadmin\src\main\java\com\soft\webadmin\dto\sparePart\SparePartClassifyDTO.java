package com.soft.webadmin.dto.sparePart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * SparePartClassifyDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartClassifyDTO对象")
@Data
public class SparePartClassifyDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "分类名称")
    @NotBlank(message = "分类名称不能为空！")
    private String classifyName;

    @ApiModelProperty(value = "分类编码")
    @NotBlank(message = "分类编码不能为空！")
    private String classifyCode;

    @ApiModelProperty(value = "父级id")
    private Long parentId = 0L;

    @ApiModelProperty(value = "备注")
    private String remark;

}
