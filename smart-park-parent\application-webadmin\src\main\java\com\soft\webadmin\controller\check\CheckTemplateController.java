package com.soft.webadmin.controller.check;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.check.CheckTemplateDTO;
import com.soft.webadmin.dto.check.CheckTemplateQueryDTO;
import com.soft.webadmin.service.check.CheckTemplateService;
import com.soft.webadmin.vo.check.CheckTemplateDetailVO;
import com.soft.webadmin.vo.check.CheckTemplateVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 检查模板控制器类
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@RestController
@RequestMapping("/check/template")
public class CheckTemplateController {

    @Resource
    private CheckTemplateService checkTemplateService;


    @GetMapping("/list")
    public ResponseResult<MyPageData<CheckTemplateVO>> list(CheckTemplateQueryDTO checkTemplateQueryDTO) {
        MyPageData<CheckTemplateVO> pageData = checkTemplateService.list(checkTemplateQueryDTO);
        return ResponseResult.success(pageData);
    }


    @GetMapping("/detail/{id}")
    public ResponseResult<CheckTemplateDetailVO> detail(@PathVariable Long id) {
        CheckTemplateDetailVO checkTemplateDetailVO = checkTemplateService.detail(id);
        return ResponseResult.success(checkTemplateDetailVO);
    }


    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@RequestBody @Validated CheckTemplateDTO checkTemplateDTO) {
        checkTemplateService.saveOrUpdate(checkTemplateDTO);
        return ResponseResult.success();
    }

    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        checkTemplateService.delete(id);
        return ResponseResult.success();
    }
}
