package com.soft.webadmin.controller.contingency;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.contingency.EmergencyDTO;
import com.soft.webadmin.dto.contingency.EmergencyQueryDTO;
import com.soft.webadmin.service.contingency.EmergencyService;
import com.soft.webadmin.vo.contingency.EmergencyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 应急预案控制器类
 * 
 * <AUTHOR>
 * @date 2024-04-17
 */
@Api(tags = "应急预案")
@RestController
@RequestMapping("/contingency/emergency")
public class EmergencyController {

    @Autowired
    private EmergencyService emergencyService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<EmergencyVO>> getPage(EmergencyQueryDTO queryDTO) {
        return ResponseResult.success(emergencyService.list(queryDTO));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public ResponseResult<EmergencyVO> detail(@RequestParam Long id) {
        return ResponseResult.success(emergencyService.detail(id));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@Validated @RequestBody EmergencyDTO saveDTO) {
        emergencyService.saveOrUpdate(saveDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        emergencyService.delete(id);
        return ResponseResult.success();
    }

}
