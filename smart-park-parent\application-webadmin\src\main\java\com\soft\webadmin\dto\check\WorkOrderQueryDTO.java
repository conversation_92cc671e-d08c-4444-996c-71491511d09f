package com.soft.webadmin.dto.check;

import com.soft.common.core.object.MyPageParam;
import com.soft.webadmin.enums.WorkOrderTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * WorkOrderDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@ApiModel("WorkOrderDTO对象")
@Data
public class WorkOrderQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "工单编号")
    private String orderNo;

    @ApiModelProperty(value = "工单类型（PATROL_INSPECTION巡检，MAINTENANCE维保，REPAIR维修）")
    private WorkOrderTypeEnums orderType;

    @ApiModelProperty(value = "优先级（1普通，2紧急，3特级）")
    private Integer priority;

    @ApiModelProperty(value = "状态（1待派单，2未响应，3处理中，4已关闭，5已完成）")
    private Integer state;

    @ApiModelProperty(value = "开始时间")
    private String beginDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "执行人")
    private String workUserName;

    @ApiModelProperty(value = "执行人", hidden = true)
    private Long workUserId;

    @ApiModelProperty(value = "查询上报的工单", hidden = true)
    private Boolean isReport = Boolean.FALSE;

}
