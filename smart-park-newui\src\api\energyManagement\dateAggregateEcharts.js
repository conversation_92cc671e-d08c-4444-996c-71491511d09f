import { request } from '@/utils/request';

// 用能统计
export const overviewOfEnergyUseAPI = (data) => {
  return request('get', '/energy/data/overviewOfEnergyUse', data, 'F');
};
// 用能趋势
export const energyUseTrendAPI = (data) => {
  return request('get', '/energy/data/energyUseTrend', data, 'F');
};
// 同比分析
export const yearOnYearAnalysisYearAPI = (data) => {
  return request('get', '/energy/data/yearOnYearAnalysisYear', data, 'F');
};
// 用能趋势-区域
export const areaStatisticsAPI = (data) => {
  return request('get', '/energy/data/areaStatistics', data, 'F');
};
// 各区域分项用能统计
export const areaStatisticsSubentryAPI = (data) => {
  return request('get', '/energy/data/areaStatisticsSubentry', data, 'F');
};
// 各区域用能分析-分项
export const energyUseAnalysisByRegionAPI = (data) => {
  return request('get', '/energy/data/energyUseAnalysisByRegion', data, 'F');
};
// 各分项用能统计
export const energyUseDtatisticsOfEachItemAPI = (data) => {
  return request('get', '/energy/data/energyUseDtatisticsOfEachItem', data, 'F');
};

// 能源流向
export const energyFlowAPI = (data) => {
  return request('post', '/energy/data/flowDirection', data);
};