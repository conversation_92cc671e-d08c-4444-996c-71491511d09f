<template>
  <dialog-common ref="dialog" :title="state.title" @submit="submit" :formRef="formRef" :width="900">
    <el-form ref="formRef" :model="form" :rules="state.rules" label-width="120px" label-suffix=":">
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">规则信息</div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input :disabled="form.equipmentTypeId === 0" v-model="form.ruleName" placeholder="请输入规则名称" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.equipmentTypeId !== 0" label="设备类型" prop="equipmentTypeId">
              <el-tree-select
                  v-model="form.equipmentTypeId"
                  :data="props.equipmentTypeTree"
                  :render-after-expand="false"
                  check-strictly
                  node-key="id"
                  :props="{label: 'name'}"
                  placeholder="设备类型"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级">
                <el-option
                    v-for="item in state.priorityOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!--<el-col :span="12">-->
          <!--  <el-form-item label="预计维修时长" prop="predictRepairDuration">-->
          <!--    <el-input v-model="form.predictRepairDuration"-->
          <!--              placeholder="请输入预计维修时长"-->
          <!--              :formatter="(value) => {-->
          <!--                return isNaN(parseFloat(value)) ? `` : Math.max(parseFloat(value), 1)-->
          <!--              }"-->
          <!--              :parser="(value) => {-->
          <!--                return isNaN(parseFloat(value)) ? `` : Math.max(parseFloat(value), 1)-->
          <!--              }"-->
          <!--              maxlength="11">-->
          <!--      <template #append>小时</template>-->
          <!--    </el-input>-->
          <!--  </el-form-item>-->
          <!--</el-col>-->
          <el-col :span="12">
            <el-form-item label="处理时限" prop="handleLimitDuration">
              <el-input v-model="form.handleLimitDuration"
                        placeholder="请输入处理时限"
                        :formatter="(value) => {
                          return isNaN(parseFloat(value)) ? `` : Math.max(parseFloat(value), 1)
                        }"
                        :parser="(value) => {
                          return isNaN(parseFloat(value)) ? `` : Math.max(parseFloat(value), 1)
                        }"
                        maxlength="11">
                <template #append>小时</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="派单方式" prop="dispatchType">
              <el-radio-group v-model="form.dispatchType">
                <el-radio :value="0">自动派单</el-radio>
                <el-radio :value="1">手动派单</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item v-if="form.dispatchType === 0" label="工作组" prop="workGroupIds">
              <el-select
                  v-model="form.workGroupIds"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="3"
                  placeholder="请选择工作组"
              >
                <el-option
                    v-for="item in state.workGroupOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                />
              </el-select>
              <div style="margin-top: 8px;line-height: 25px;color: #73767a;">
                自动派单规则说明：<br/>
                系统会根据所选工作组中的所有成员随机派单。
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">其他</div>
        </div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="处理预案" prop="treatmentPlan">
              <el-input type="textarea"
                        v-model="form.treatmentPlan"
                        show-word-limit
                        maxlength="500"
                        :autosize="{ minRows: 4, maxRows: 6 }"
                        @input="(value) => {
                          if(value.length >= 500) {
                           ElMessage.warning('最多只能输入500个字符！')
                          }
                        }"
                        placeholder="请输入处理预案"
                        clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { getWorkGroupPageAPI } from '@/api/operationManagement/workGroup.js';
import { saveOrUpdateOrderRules } from '@/api/operationManagement/workOrderRule.js';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['onClose']);


let dialog = ref();

let form = reactive({
  priority: 1,
  dispatchType: 0,
  workGroupIds: [],
});
let formRef = ref();

const props = defineProps({
  equipmentTypeTree: {
    type: Array,
    default: []
  },
});

const state = reactive({
  title: '',
  subSystemOptions: [],
  workGroupOptions: [],
  priorityOptions: [
    {
      label: '普通',
      value: 1
    },
    {
      label: '紧急',
      value: 2
    },
    {
      label: '特急',
      value: 3
    }
  ],
  rules: {
    ruleName: [{ required: true, message: '规则名称不能为空！', trigger: 'blur' }],
    equipmentTypeId: [{ required: true, message: '设备类型不能为空！', trigger: 'change' }],
    priority: [{ required: true, message: '优先级不能为空！', trigger: 'change' }],
    // predictRepairDuration: [
    //   { required: true, message: '预计维修时长不能为空！', trigger: 'blur' },
    //   { validator: validatePredictRepairDuration, trigger: 'blur' }
    // ],
    handleLimitDuration: [
      { required: true, message: '处理时限不能为空！', trigger: 'blur' },
      // { validator: validateHandleLimitDuration, trigger: 'blur' }
    ],
    dispatchType: [{ required: true, message: '派单方式不能为空！', trigger: 'change' }],
    workGroupIds: [{ required: true, message: '工作组不能为空！', trigger: 'change' }],
  }
});

// 预计维修时长自定义校验
function validatePredictRepairDuration(rule, value, callback) {
  // 【预计维修时长】的值小于【处理时限】
  if (form.handleLimitDuration && value > form.handleLimitDuration) {
    callback(new Error('预计维修时长不能大于处理时限！'));
  } else {
    formRef.value.clearValidate('handleLimitDuration');
    callback();
  }
}

// 处理时限自定义校验
function validateHandleLimitDuration(rule, value, callback) {
  if (form.predictRepairDuration && value < form.predictRepairDuration) {
    callback(new Error('处理时限不能小于预计维修时长！'));
  } else {
    formRef.value.clearValidate('predictRepairDuration');
    callback();
  }
}

const open = (title) => {
  dialog.value.open();
  nextTick(() => {
    state.title = title;
    queryWorkGroups();
  });
};

const submit = () => {
  let param = {};
  Object.assign(param, form);
  // 手动派单，设置工作组为空
  if (form.dispatchType === 1) {
    form.workGroupIds = [];
    param.workGroupIds = '';
  }
  // 工作组成员不为空，才赋值
  if (form.workGroupIds) {
    param.workGroupIds = form.workGroupIds.join(',');
  }
  // 小时转换成分钟
  if (form.predictRepairDuration) param.predictRepairDuration = form.predictRepairDuration * 60;
  if (form.handleLimitDuration) param.handleLimitDuration = form.handleLimitDuration * 60;
  saveOrUpdateOrderRules(param).then(res => {
    if (res.success) {
      ElMessage.success('保存成功！');
      dialog.value.close();
      // 调用父组件中 该标签中定义的事件
      emit('onClose');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

// 查询工作组列表
const queryWorkGroups = () => {
  getWorkGroupPageAPI().then(res => {
    if (res.success) {
      state.workGroupOptions = res.data.dataList;
    }
  });
};

defineExpose({
  form,
  open
});
</script>

<style scoped>
.labor-cost-input-number {
  width: 45% !important;
}
</style>
