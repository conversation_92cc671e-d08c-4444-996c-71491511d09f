package com.soft.webadmin.dto.check;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * CheckRepairLogDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckRepairLogDTO对象")
@Data
public class CheckRepairLogQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "优先级（1普通，2紧急，3特级）")
    private Integer priority;

    @ApiModelProperty(value = "报修状态（1待派单，2未响应，3处理中，4已关闭，5已完成）")
    private Integer state;

    @ApiModelProperty(value = "开始时间")
    private String beginDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "上报人", hidden = true)
    private Long reportUserId;

}
