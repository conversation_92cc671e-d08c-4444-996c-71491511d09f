package com.soft.webadmin.dto.check;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * SpCheckTemplateDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@ApiModel("CheckTemplateDTO对象")
@Data
public class CheckTemplateDTO {

    @ApiModelProperty(value = "${column.columnComment}")
    @NotNull(message = "数据验证失败，${column.columnComment}不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "检查模板名称")
    @NotBlank(message = "模板名称不能为空！")
    private String templateName;

    @ApiModelProperty(value = "检查模板类型（1巡检-设备；2巡检-空间；3维保-设备）")
    @NotBlank(message = "模板类型不能为空！")
    private String templateType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "检查项")
    private List<CheckTemplateItemDTO> checkTemplateItemList;
}
