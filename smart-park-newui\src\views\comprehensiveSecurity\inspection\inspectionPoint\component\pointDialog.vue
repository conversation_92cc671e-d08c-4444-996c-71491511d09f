<template>
  <dialog-common ref="showDialogRef" :title="state.title" :formRef="ruleFormRef" :width="500" @submit="submit" @onClose="close">
    <el-form :model="state.dataForm" ref="ruleFormRef" :rules="state.rules" label-width="85" label-suffix=":">
      <el-form-item label="编号" prop="code">
        <el-input v-model="state.dataForm.code" placeholder="巡更点编号"></el-input>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="state.dataForm.name" placeholder="巡更点名称"></el-input>
      </el-form-item>
      <el-form-item label="选择位置" prop="spaceIds">
        <el-cascader v-model="state.dataForm.spaceIds" :options="state.spaceOptions" :props="optionsProps"
                     placeholder="请选择所在位置"/>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" v-model="state.dataForm.remark" placeholder="备注" />
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {treeAPI} from "@/api/iotManagement/space.js";
import DialogCommon from "@/components/basic/dialogCommon.vue";
import {saveOrUpdateInspectionPointAPI} from "@/api/comprehensiveSecurity/inspectionPoint.js";
import {ElMessage} from "element-plus";


// 实例化父组件对象
const emits = defineEmits(['onClose'])

// dialog引用
let showDialogRef = ref()

// 表单引用
let ruleFormRef = ref()


const optionsProps = {
  checkStrictly: true,
  label: "name",
  value: "id"
}


const state = reactive({
  title: '',
  dataForm: {
    code: null,
    name: null,
    spaceIds: null,
    remark: null
  },
  rules: {
    code: [
      { required: true, message: '编号不能为空', trigger: 'blur'}
    ],
    name: [
      { required: true, message: '名称不能为空', trigger: 'blur'}
    ],
    spaceIds: [
      { required: true, message: '位置不能为空', trigger: 'blur'}
    ]
  },
  spaceOptions: []
})



const submit = () => {
  let params = {
    id: state.dataForm.id,
    code: state.dataForm.code,
    name: state.dataForm.name,
    remark: state.dataForm.remark
  }
  if (state.dataForm.spaceIds) {
    params.spaceId = state.dataForm.spaceIds[state.dataForm.spaceIds.length - 1]
  }
  saveOrUpdateInspectionPointAPI(params).then(res => {
    if (res.success) {
      ElMessage.success('保存成功！')
    } else {
      ElMessage.error('保存失败，' + res.errorMessage)
    }
    showDialogRef.value.close()
  })
}

// 打开 dialog 弹窗
const open = (title, info) => {
  showDialogRef.value.open()
  nextTick(() => {
    state.title = title
    spaceTree()
    if (info) {
      const data = JSON.parse(JSON.stringify(info))
      state.dataForm.id = data.id
      state.dataForm.code = data.code
      state.dataForm.name = data.name
      state.dataForm.remark = data.remark
      state.dataForm.spaceIds = data.spacePath.split('/')
    } else {
      state.dataForm = {}
    }
  })
}

// 位置级联选择查询
const spaceTree = () => {
  let query = {
    deep: 4
  }
  treeAPI(query).then(res => {
    if (res.success) {
      state.spaceOptions = res.data
    }
  })
}

// 关闭
const close = () => {
  emits('onClose')
}

defineExpose({
  open
})
</script>

<style scoped lang="less">

</style>
