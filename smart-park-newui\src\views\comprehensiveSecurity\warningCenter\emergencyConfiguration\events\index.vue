<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="name">
          <el-input v-model="formInline.name" placeholder="事件名称"/>
        </el-form-item>
        <el-form-item prop="level">
          <el-select v-model="formInline.level" filterable clearable placeholder="事件等级">
            <el-option v-for="(value,key) in state.levelOptions" :label="value" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新建事件</el-button>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableData" row-key="id" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="160">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :total="state.pagetion.total"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
      <saveModal ref="save" :title="state.title" :levelOptions="state.levelOptions"
                 @submit="getList"></saveModal>
    </template>
  </page-common>
</template>

<script setup>
import {ElButton, ElMessage, ElMessageBox,ElTag} from 'element-plus'

import saveModal from './component/saveModal.vue'

import {eventsPageAPI, eventsDeteleAPI} from '@/api/comprehensiveSecurity/events.js'

import {calcPageNo} from "@/utils/util.js";

let save = ref()

const formInlineRef = ref();
const formInline = reactive({});

const state = reactive({
  title: '',
  levelOptions:{
    1:'普通',
    2:'重要',
    3:'严重'
  },
  levelTypeOptions:{
    1:"primary",
    2:"warning",
    3:'danger'
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'name',
      label: '事件名称'
    },
    {
      prop: 'level',
      label: '事件等级',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { type: state.levelTypeOptions[cellValue]  }, { default: () => state.levelOptions[cellValue] })
      }
    },
    {
      prop: 'remark',
      label: '备注'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  }
})

onMounted(() => {
  getList()
})

// 获取应急事件
const getList = () => {
  let query = {
    ...formInline,
    ...state.pagetion
  };
  eventsPageAPI(query).then(res => {
    state.tableData = res.data.dataList;
    state.pagetion.total = res.data.totalCount;
  })
}
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};


// 新建应急事件
const addHandle = () => {
  state.title = '新建事件'
  save.value.open()
}

// 编辑应急事件
const editHandle = (info) => {
  state.title = '编辑事件'
  save.value.open()

  nextTick(() => {
    if(info.img){
      save.value.form.fileList = [{url: import.meta.env.VITE_BASE_URL +  info.img,imgUrl:info.img }]
    }
    Object.assign(save.value.form, info)
  })
}

// 删除应急事件
const deleteHandle = ({id}) => {
  ElMessageBox.confirm(
      '是否删除当前应急事件?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    eventsDeteleAPI({id}).then(res => {
      state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, 1);
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}


</script>

<style lang='less' scoped></style>
