package com.soft.webadmin.controller.visitor;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.visitor.VisitorPassRecordQueryDTO;
import com.soft.webadmin.service.visitor.VisitorPassRecordService;
import com.soft.webadmin.vo.visitor.VisitorPassRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 访客通行记录控制器类
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Api(tags = "访客通行记录接口管理")
@RestController
@RequestMapping("/visitor/pass-record")
public class VisitorPassRecordController {

    @Resource
    private VisitorPassRecordService visitorPassRecordService;

    @ApiOperation("访客记录列表")
    @PostMapping("/list")
    public ResponseResult<MyPageData<VisitorPassRecordVO>> list(@RequestBody VisitorPassRecordQueryDTO visitorPassRecordQueryDTO) {
        MyPageData<VisitorPassRecordVO> pageData = visitorPassRecordService.list(visitorPassRecordQueryDTO);
        return ResponseResult.success(pageData);
    }

}
