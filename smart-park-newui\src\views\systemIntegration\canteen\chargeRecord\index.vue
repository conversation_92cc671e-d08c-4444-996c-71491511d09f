<template>
  <page-common v-model="state.tableHeight" :operate-bool="false">
    <template #query>
      <el-form
          :inline="true"
          ref="formInlineRef"
          :model="formInline"
          class="demo-form-inline">
        <el-form-item prop="customerName">
          <el-input v-model="formInline.customerName" placeholder="姓名"/>
        </el-form-item>
        <el-form-item prop="queryTime">
            <el-date-picker
                    v-model="formInline.queryTime"
                    type="datetimerange"
                    :shortcuts="shortcuts"
                    range-separator="到"
                    format="YYYY-MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    clearable
            />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableData">
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter"/>
      </el-table>
      <el-pagination
          background
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pageParam.pageNum"
          :page-size="state.pageParam.pageSize"
          :total="state.pageParam.total"
          @size-change="sizeChange"
          @current-change="currentChange"/>
    </template>
  </page-common>
</template>

<script setup>
import {getChargeRecordPageApi} from "@/api/systemIntegration/canteen/canteen.js";
import {Plus, Refresh, Search} from "@element-plus/icons-vue";
import {dayjs, ElTag} from "element-plus";
import {nextTick, reactive, ref} from "vue";

let modal = ref()
const formInlineRef = ref();
const formInline = reactive({
  customerName: "",
  queryTime:[]
});
const state = reactive({
  title: "",
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'customerCode',
      label: '账号'
    },
    {
      prop: 'customerName',
      label: '姓名'
    },
    {
        prop: 'canteenName',
        label: '餐厅名称'
    },
    {
        prop: 'shopstallName',
        label: '消费窗口'
    },
    {
        prop: 'intervalName',
        label: '类型'
    },
    {
        prop: 'payType',
        label: '支付类型'
    },
    {
        prop: 'totalAmount',
        label: '消费金额(元)'
    },
    {
      prop: 'payTime',
      label: '消费时间',
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    }
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  }
});

onMounted(() => {
  getList();
});

/**
 * 日期格式化
 */
const dateFormat = (row, column, cellValue) => {
  return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
}


// 时间选择器的快速选择项
const shortcuts = [
    {
        text: '今天',
        value: () => {
            const end = new Date()
            const start = new Date(new Date(new Date().toLocaleDateString()).getTime())
            return [start, end]
        },
    },
    {
        text: '本周',
        value: () => {
            const end = new Date()
            const start = new Date();
            start.setDate(start.getDate() - start.getDay() + 1)
            start.setTime(new Date(start.toLocaleDateString()).getTime())
            return [start, end]
        },
    },
    {
        text: '本月',
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setDate(1);
            start.setTime(new Date(start.toLocaleDateString()).getTime())
            return [start, end]
        },
    }
]

/**
 * 查询列表数据
 */
const getList = () => {
  let query = {
    customerName: formInline.customerName,
    beginPayTime: formInline.queryTime[0],
    endPayTime: formInline.queryTime[1],
    ...state.pageParam,
  };
  getChargeRecordPageApi(query).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

/**
 * 提交查询
 */
const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

/**
 * 重置查询
 */
const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

/**
 * 分页查询（页码）
 * @param pageNum
 */
const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

/**
 * 分页查询（条数）
 * @param pageSize
 */
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

</script>
