package com.soft.webadmin.dto.sparePart;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SparePartQuantityChangeDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartQuantityChangeDTO对象")
@Data
public class SparePartQuantityChangeQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "备件id")
    private Long sparePartId;

    @ApiModelProperty(value = "业务类型：1入库、2出库")
    private Integer operateType;

    @ApiModelProperty(value = "出入库类型：1原始入库、2盘盈入库、3剩余备件归还、6备件领用、7盘亏出库")
    private Integer type;

    @ApiModelProperty(value = "审核状态（1待入库/待出库、2已同意）")
    private Integer examineState;

}
