<template>
    <dialog-common @close="onClose" ref="dialog" title="详情" @submit="submit" :formRef="ruleFormRef" :width="1100"
        class="dialogTextarea" :showButton="form.handleStatus != 3">
        <el-row class="card-textBg">
            <el-col :span="12">
                <el-form label-position="top">
                    <div class="divFlex">
                        <div class="divLeft"></div>
                        <div class="divRight">投诉/建议信息</div>
                    </div>
                    <div class="detail-area">
                        <el-row :gutter="40">
                            <el-col :span="24">
                                <el-form-item label="标题">
                                    {{ form.title }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="编号">
                                    {{ form.code }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="类型">
                                    <el-tag :type="form.type == 1 ? 'primary' : 'warning'">
                                        {{ form.type == 1 ? "投诉" : "建议" }}
                                    </el-tag>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="上报人">
                                    {{ form.reportUserName }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="手机号">
                                    {{ form.reportUserPhone }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="上报时间:">
                                    {{ dayjs(form.reportTime).format("YYYY-MM-DD HH:mm") }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="问题描述">
                                    {{ form.content }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col>
                                <el-form-item class="item__content-noBg" label="附件" v-if="form.imgList?.length > 0">
                                    <img-video :list="form.imgList"></img-video>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </el-form>
            </el-col>
            <el-col :span="12">
                <el-form style="margin-left: 15px;" label-position="top" ref="ruleFormRef" :model="form"
                    :rules="state.rules" label-width="100px" label-suffix=":">
                    <div class="divFlex">
                        <div class="divLeft"></div>
                        <div class="divRight">处理信息</div>
                    </div>
                    <div class="detail-area">
                        <el-row v-if="form.handleStatus == 3" :gutter="40" style="margin: 0">
                            <el-col :span="12">
                                <el-form-item label="处理人">
                                    {{ form.handleUserName }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="处理时间">
                                    {{ dayjs(form.handleTime).format("YYYY-MM-DD HH:mm") }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="24">
                                <el-form-item label="处理结果">
                                    {{ form.handleResult }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row v-else>
                            <el-col :span="24">
                                <el-form-item class="item__content-noBg" label="处理结果" prop="handleResult">
                                    <el-input v-model="form.handleResult" type="textarea" :maxlength="500"
                                        show-word-limit :autosize="{ minRows: 10 }" placeholder="请输入处理结果" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </el-form>
            </el-col>
        </el-row>
    </dialog-common>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { viewAPI, handleOpinionAPI } from '@/api/parkOperation/opinion.js'
import dayjs from "dayjs";
const dialog = ref();
const ruleFormRef = ref();

const emit = defineEmits(["submit"]);

const form = reactive({

});

const state = reactive({
    rules: {
        handleResult: [
            { required: true, message: '请填写处理结果', trigger: 'blur' }
        ]
    }
})

// 处理状态：1待查看；2进行中；3已处理；
const open = () => {
    dialog.value.open();

    if (form.handleStatus == 1) {
        viewAPI(form.id).then((res) => {
            // console.log(res);
        })
    }
};

const onClose = () => {
    if(form.handleStatus == 1){
        emit('submit')
    }
};

const submit = () => {
    handleOpinionAPI({
        id:form.id,
        handleResult:form.handleResult
    }).then((res) => {
        if (res.success) {
            ElMessage.success('处理成功');
            dialog.value.close();
            emit("submit");
        } else {
            ElMessage.error(res.errorMessage);
        }
    })
}

defineExpose({
    state,
    form,
    open,
});
</script>

<style lang="less" scoped>
</style>
