package com.soft.webadmin.controller.visitor;

import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.StringUtils;
import com.soft.webadmin.dto.visitor.VisitorApplyRecordApproveBatchDTO;
import com.soft.webadmin.dto.visitor.VisitorApplyRecordApproveDTO;
import com.soft.webadmin.dto.visitor.VisitorApplyRecordDTO;
import com.soft.webadmin.dto.visitor.VisitorApplyRecordQueryDTO;
import com.soft.webadmin.service.visitor.VisitorApplyRecordService;
import com.soft.webadmin.vo.visitor.VisitorApplyRecordCodeVO;
import com.soft.webadmin.vo.visitor.VisitorApplyRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 访客申请记录控制器类
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Api(tags = "访客预约接口管理")
@RestController
@RequestMapping("/visitor/apply-record")
public class VisitorApplyRecordController {

    @Resource
    private VisitorApplyRecordService visitorApplyRecordService;

    @ApiOperation("列表查询")
    @PostMapping("/list")
    public ResponseResult<MyPageData<VisitorApplyRecordVO>> list(@RequestBody VisitorApplyRecordQueryDTO visitorApplyRecordQueryDTO) {
        MyPageData<VisitorApplyRecordVO> pageData = visitorApplyRecordService.list(visitorApplyRecordQueryDTO);
        return ResponseResult.success(pageData);
    }

    @ApiOperation("预约记录")
    @PostMapping("/visitRecord")
    public ResponseResult<MyPageData<VisitorApplyRecordVO>> visitRecord(@RequestBody VisitorApplyRecordQueryDTO visitorApplyRecordQueryDTO) {
        String openId = TokenData.takeFromRequest().getOpenId();
        if(StringUtils.isBlank(openId)){
            throw new MyRuntimeException("请在微信端打开");
        }
        visitorApplyRecordQueryDTO.setOpenId(openId);
        MyPageData<VisitorApplyRecordVO> pageData = visitorApplyRecordService.list(visitorApplyRecordQueryDTO);
        return ResponseResult.success(pageData);
    }

    @ApiOperation("访客预约")
    @PostMapping("/create")
    public ResponseResult<Void> create(@RequestBody VisitorApplyRecordDTO visitorApplyRecordDTO) {
        String openId = TokenData.takeFromRequest().getOpenId();
        if(StringUtils.isBlank(openId)){
            throw new MyRuntimeException("请在微信端打开");
        }
        visitorApplyRecordDTO.setOpenId(openId);
        visitorApplyRecordService.create(visitorApplyRecordDTO);
        return ResponseResult.success();
    }

    @ApiOperation("访客审核")
    @PostMapping("/approve")
    public ResponseResult<Void> approve(@RequestBody VisitorApplyRecordApproveDTO visitorApplyRecordApproveDTO) {
        visitorApplyRecordService.approve(visitorApplyRecordApproveDTO);
        return ResponseResult.success();
    }

    @ApiOperation("一件审批")
    @PostMapping("/approve/batch")
    public ResponseResult<Void> approveBatch(@RequestBody VisitorApplyRecordApproveBatchDTO visitorApplyRecordApproveBatchDTO) {
        visitorApplyRecordService.approveBatch(visitorApplyRecordApproveBatchDTO);
        return ResponseResult.success();
    }


    @ApiOperation("访客码")
    @GetMapping("/visitor-code/{applyId}")
    public ResponseResult<VisitorApplyRecordCodeVO> visitorCode(@PathVariable Long applyId) {
        VisitorApplyRecordCodeVO visitorApplyRecordCodeVO = visitorApplyRecordService.visitorCode(applyId);
        return ResponseResult.success(visitorApplyRecordCodeVO);
    }

}
