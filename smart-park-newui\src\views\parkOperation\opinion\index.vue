<template>
    <page-common v-model="state.tableHeight">
        <template #query>
            <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
                <el-form-item prop="code">
                    <el-input v-model="formInline.code" placeholder="编号" />
                </el-form-item>
                <el-form-item prop="title">
                    <el-input v-model="formInline.title" placeholder="标题" />
                </el-form-item>
                <el-form-item prop="reportUserId">
                    <el-select v-model="formInline.reportUserId" filterable placeholder="上报人(可直接搜索)" clearable>
                        <el-option v-for="user in state.userList" :key="user.userId"
                            :label="user.showName + (user.deptName ? ' - ' + user.deptName : '')"
                            :value="user.userId" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="type">
                    <el-select v-model="formInline.type" clearable placeholder="类型">
                        <el-option v-for="(val, key) in state.typeOptions" :value="key" :label="val" :key="key" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="handleStatus">
                    <el-select v-model="formInline.handleStatus" clearable placeholder="处理状态">
                        <el-option v-for="(val, key) in state.handleStatusOptions" :value="key" :label="val"
                            :key="key" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-date-picker range-separator="至" v-model="state.date" type="daterange" value-format="YYYY-MM-DD"
                        start-placeholder="开始日期" end-placeholder="结束日期" :disabled-date="disabledDate"
                        :clearable="false" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
                    <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template #table>
            <el-table show-overflow-tooltip :data="state.tableData" :height="state.tableHeight">
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                    :label="item.label" :formatter="item.formatter" :width="item.width" />
                <el-table-column label="操作" align="center" width="230">
                    <template #default="scope">
                        <el-button link type="primary" icon="Tickets" @click="onDetail(scope.row)">
                            详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize"
                :total="state.pagetion.total" @size-change="sizeChange" @current-change="currentChange" />
            <modal-page ref="modal" @submit="getList" />
        </template>
    </page-common>
</template>

<script setup>
import { pictureVideo } from "@/utils/util";
import modalPage from "./component/modalPage.vue";
import { listUsersAPI } from "@/api/settingSystem/user.js";
import { getOpinionListAPI } from '@/api/parkOperation/opinion.js'
import dayjs from "dayjs";
const formInlineRef = ref();
const formInline = reactive({});
const modal = ref();
const state = reactive({
    tableHeight: 100,
    typeOptions: {
        1: '投诉',
        2: '建议'
    },
    handleStatusOptions: {
        1: '待查看',
        2: '处理中',
        3: '已处理'
    },
    handleStatusColors: {
        1: '#E6A23C',
        2: '#409EFF',
        3: '#67C23A'
    },
    date: [],
    pagetion: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
    },
    tableHeader: [
        {
            prop: "code",
            label: "编号",
            width: 210
        },
        {
            prop: "title",
            label: "标题",
            width: 210
        },
        {
            prop: "type",
            label: "类型",
            formatter: (row, column, cellValue) => {
                return state.typeOptions[cellValue]
            }
        },
        {
            prop: 'reportUserName',
            label: '上报人',
        },
        {
            prop: 'handleStatus',
            label: '处理状态',
            formatter: (row, column, cellValue) => {
                let color = state.handleStatusColors[cellValue];
                return h("div", [
                    h("span", {
                        class: "status-circle",
                        style: "background-color: " + color,
                    }),
                    state.handleStatusOptions[cellValue],
                ]);
            },
        },
        {
            prop: 'createTime',
            label: '上报时间'
        }
    ]
})

onMounted(() => {
    getList()
    loadUserList()
})

/** 查询用户 */
const loadUserList = () => {
    listUsersAPI().then((res) => {
        state.userList = res.data.dataList;
    });
};

const disabledDate = (time) => {
    return time.getTime() > dayjs();
}

const getList = () => {
    let query = {
        ...formInline,
        ...state.pagetion,
        reportDateStart: state.date[0],
        reportDateEnd: state.date[1]
    };
    getOpinionListAPI(query).then((res) => {
        state.tableData = res.data.dataList;
        state.pagetion.total = res.data.totalCount;
    });
};

/** 查看详情 */
const onDetail = (row) => {
    Object.assign(modal.value.form,{...row})
    let imgStr = row.sysAnnexVOS?.map(item => item.path).join(',')
    modal.value.form.imgList = pictureVideo(imgStr)
    modal.value.open();
}

const onSubmit = () => {
    state.pagetion = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
    };
    getList();
};

const onReset = () => {
    formInlineRef.value.resetFields();
    state.date = []
    onSubmit();
};

// 分页
const currentChange = (pageNum) => {
    state.pagetion.pageNum = pageNum;
    getList();
};

const sizeChange = (pageSize) => {
    state.pagetion.pageSize = pageSize;
    getList();
};
</script>

<style lang="less" scoped>
:deep .status-circle {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;
    margin-bottom: 1px;
}
</style>
