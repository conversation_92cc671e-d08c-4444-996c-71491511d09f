package com.soft.webadmin.controller.shifts;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.shifts.ShiftsSettingDTO;
import com.soft.webadmin.dto.shifts.ShiftsSettingQueryDTO;
import com.soft.webadmin.service.shifts.ShiftsSettingService;
import com.soft.webadmin.vo.shifts.ShiftsSettingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 班次设置控制器类
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
@Api(tags = "班次设置接口")
@RestController
@RequestMapping("/shifts/setting")
public class ShiftsSettingController {

    @Autowired
    private ShiftsSettingService shiftsSettingService;


    @ApiOperation(value = "查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<ShiftsSettingVO>> list(@Validated ShiftsSettingQueryDTO queryDTO) {
        return ResponseResult.success(shiftsSettingService.getPage(queryDTO));
    }


    @ApiOperation(value = "保存")
    @PostMapping("/save")
    public ResponseResult<Void> save(@Validated @RequestBody ShiftsSettingDTO saveDTO) {
        return shiftsSettingService.saveOrUpdate(saveDTO);
    }


    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        return shiftsSettingService.delete(id);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public ResponseResult<ShiftsSettingVO> detail(@RequestParam Long id) {
        return ResponseResult.success(shiftsSettingService.detail(id));
    }
}
