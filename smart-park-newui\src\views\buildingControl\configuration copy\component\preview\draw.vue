<template>
  <div class="draw">
    <!-- 画布 -->
    <div id="container" :style="{
      ...getCanvasStyle(canvasStyle), width: changeStyleWithScale(canvasStyle.width, canvasStyle.scale) + 'px',
      height: changeStyleWithScale(canvasStyle.height, canvasStyle.scale) + 'px',
    }">
      <template v-for="(item, index) in componentData" :key="item.id">
        <el-tooltip effect="dark" :popper-class="classPrompt(item)" :content="titlePrompt(item)" placement="top-start">
          <component :is="item.component" class="component" :element="item" :style="getStyle(item.style)" />
        </el-tooltip>
      </template>
    </div>
  </div>
</template>

<script setup>
import { getStyle } from '@/utils/webtopo/style.js'
import { changeStyleWithScale } from '@/utils/webtopo/math.js'

const state = reactive({
  drawInfo: JSON.parse(sessionStorage.getItem('webtopo'))
})

let { canvasStyle, componentData } = state.drawInfo

const getCanvasStyle = (style) => {
  return getStyle(style, ['width', 'height', 'scale'])
}

// 是否显示文本框
const classPrompt = computed(() => {
  return function(info){
    if(info && info.params){
      let {classInfo} = info.params
      return (classInfo.checkList || []).includes("topoPrompt") ? '' : 'tooltipVisible'
    }else{
      return 'tooltipVisible'
    }
  }
})

//  提示文字
const titlePrompt = computed(() => {
  return function(info){
    if(info && info.params){
      let {classInfo} = info.params
      let current = (classInfo.classList || []).find(item => item.classType == 'topoPrompt') || {}
      return  current.conent
    }
  }
})
</script>

<style lang='less' scoped>
.draw {
  width: 100%;
  height: 100%;
  overflow: auto;
  display: -webkit-box;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  -webkit-box-orient: vertical;
}

#container {
  margin: auto;
  position: relative;
  background-repeat: no-repeat;
  background-position: center;

  .component {
    position: absolute;
  }
}
</style>