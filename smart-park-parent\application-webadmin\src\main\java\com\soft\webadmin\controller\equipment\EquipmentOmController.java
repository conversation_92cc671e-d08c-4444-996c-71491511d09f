package com.soft.webadmin.controller.equipment;

import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.equipment.EquipmentOmDTO;
import com.soft.webadmin.dto.equipment.EquipmentOmQueryDTO;
import com.soft.webadmin.dto.equipment.EquipmentScrapDTO;
import com.soft.webadmin.service.equipment.EquipmentOmService;
import com.soft.webadmin.vo.equipment.EquipmentOmVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @ClassName EquipmentOmController
 * @description:
 * @date 2024年03月25日
 */
@Api(tags = "运维设备管理")
@RestController
@RequestMapping("/om/equipment")
public class EquipmentOmController {

    @Autowired
    private EquipmentOmService equipmentOmService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<EquipmentOmVO>> getPage(EquipmentOmQueryDTO queryDTO) {
        return ResponseResult.success(equipmentOmService.getPage(queryDTO));
    }

    @ApiOperation(value = "根据设备id查询设备详情")
    @GetMapping("/detail")
    public ResponseResult<EquipmentOmVO> detail(@RequestParam Long equipmentId) {
        EquipmentOmVO equipmentOmVO = equipmentOmService.detail(equipmentId);
        if (equipmentOmVO == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success(equipmentOmVO);
    }

    @ApiOperation(value = "根据设备编号查询设备详情")
    @GetMapping("/detailByCode")
    public ResponseResult<EquipmentOmVO> detailByCode(@RequestParam String equipmentCode) {
        EquipmentOmVO equipmentOmVO = equipmentOmService.detailByCode(equipmentCode);
        if (equipmentOmVO == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success(equipmentOmVO);
    }

    @ApiOperation("设备二维码")
    @GetMapping("/qrcode/{equipmentId}")
    public ResponseResult<String> generatorQrcode(@PathVariable Long equipmentId) {
        return ResponseResult.success(equipmentOmService.generatorQrcode(equipmentId));
    }


    @ApiOperation("导出设施二维码")
    @GetMapping("/qrcode/export")
    public void qrcodeExport(EquipmentOmQueryDTO equipmentOmQueryDTO) {
        equipmentOmService.qrcodeExport(equipmentOmQueryDTO);
    }


    @ApiOperation(value = "保存")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@Valid @RequestBody EquipmentOmDTO saveDTO) {
        return equipmentOmService.saveOrUpdate(saveDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam List<Long> idList) {
        equipmentOmService.delete(idList);
        return ResponseResult.success();
    }

    @ApiOperation(value = "报废")
    @PostMapping("/scrap")
    public ResponseResult<Void> scrap(@Validated @RequestBody EquipmentScrapDTO scrapDTO) {
        return equipmentOmService.scrap(scrapDTO);
    }

    @ApiOperation("下载模板")
    @GetMapping("/exportTemplate")
    public void exportTemplate() {
        equipmentOmService.exportTemplate();
    }

    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public ResponseResult<Map<String, Integer>> importExcel(@RequestParam MultipartFile file) {
        return equipmentOmService.importExcel(file);
    }

}
