package com.soft.webadmin.dto.shifts;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.soft.admin.upms.model.SysDeptPost;
import com.soft.common.core.util.easyexcel.ExcelPatternMsg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * SparePartInoutDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@ApiModel("SparePartInoutDTO对象")
@Data
@ColumnWidth(value = 15)
public class ShiftsRosterExcelDTO {
    @NotNull(message = "人员姓名不能为空！")
    @ExcelProperty(value = "*姓名", index = 0)
    private String showName;

    @NotNull(message = "人员性别不能为空！")
    @ExcelProperty(value = "*性别", index = 1)
    @Pattern(regexp = ExcelPatternMsg.SEX, message = ExcelPatternMsg.SEX_MSG)
    private String sexStr;

    @NotNull(message = "年龄不能为空！")
    @ExcelProperty(value = "*年龄", index = 2)
    @Pattern(regexp = ExcelPatternMsg.NUMBER, message = ExcelPatternMsg.NUMBER_MSG)
    private String age;

    @NotNull(message = "手机号码不能为空！")
    @ExcelProperty(value = "*手机号码", index = 3)
    @Pattern(regexp = ExcelPatternMsg.PHONE, message = ExcelPatternMsg.PHONE_MSG)
    private String phone;

    @NotNull(message = "部门不能为空！")
    @ExcelProperty(value = "*部门", index = 4)
    private String deptName;

//    @NotEmpty(message = "岗位不能为空！")
    @ExcelProperty(value = "岗位", index = 5)
    private String postNames;

    @ExcelProperty(value = "*入职日期 例:2024-02-09", index = 6)
//    @DateTimeFormat("yyyy/MM/dd")
    @NotNull(message = "入职日期不能为空！")
//    @Pattern(regexp = ExcelPatternMsg.DATE1, message = ExcelPatternMsg.DATE1_MSG)
    private String joinJobDate;

    @ExcelProperty(value = "身份证号码", index = 7)
    @Pattern(regexp = ExcelPatternMsg.IDENTITY, message = ExcelPatternMsg.IDENTITY_MSG)
    private String cardNo;

//    @ExcelProperty(value = "责任区域", index = 8)
//    private String liabilityArea;

    @ApiModelProperty(value = "花名册Id")
    @ExcelIgnore
    private Long rosterId;
    @ExcelIgnore
    private Integer sex;
    @ExcelIgnore
    private Long deptId;
    @ExcelIgnore
    private List<SysDeptPost> deptPostList;
    @ExcelIgnore
    private List<Long> spaceIds;
}
