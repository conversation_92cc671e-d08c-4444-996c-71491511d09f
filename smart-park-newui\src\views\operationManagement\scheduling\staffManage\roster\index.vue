<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form ref="formInlineRef" :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item prop="showName">
          <el-input v-model="formInline.showName" placeholder="姓名"/>
        </el-form-item>
        <el-form-item prop="deptId">
          <el-cascader
              v-model="formInline.deptId"
              :clearable="true"
              :loading="state.deptInfo.impl.loading"
              :options="state.deptList"
              :props="{
              value: 'deptId',
              label: 'deptName',
              checkStrictly: true,
              emitPath: false
            }"
              placeholder="部门"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item prop="postId">
          <el-select v-model="formInline.postId" clearable placeholder="岗位">
            <el-option v-for="post in state.postOptions" :key="post.postId" :label="post.postName"
                       :value="post.postId"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="postStatus">
          <el-select v-model="formInline.postStatus" clearable placeholder="职位状态">
            <el-option v-for="(value, key) in state.postStatusOptions" :key="key" :label="value" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="Search" type="primary" @click="onSubmit">查询</el-button>
          <el-button icon="Refresh" type="primary" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Setting" @click="handleSetting">设置</el-button>
      <el-button icon="Plus" type="primary" @click="handleAdd">新建员工</el-button>
      <el-button type="primary" icon="Upload" @click="handleUpload">导入</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="320">
          <template #default="scope">
            <el-button icon="Edit" link type="primary" @click="handleEdit(scope.row)" :disabled="scope.row.postStatus != 1">
              编辑
            </el-button>
            <el-button icon="Notebook" link type="primary" @click="handleLeave(scope.row)"
                       :disabled="scope.row.postStatus != 1">办理离职
            </el-button>
            <el-button icon="Tickets" link type="primary" @click="handleDetail(scope.row)">详情</el-button>
            <!--            <el-button icon="Delete" link type="danger" @click="handleDetele(scope.row)">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="state.pagetion.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
      <modalPage ref="modal" :title="state.title" :is-read="state.isRead" :sexOptions="state.sexOptions"
                 :detpList="state.deptList" @submit="getList"></modalPage>
      <modalDetail ref="detail"  :sexOptions="state.sexOptions"></modalDetail>
      <modalLeave ref="leave" @submit="getList"></modalLeave>
      <modalSetting ref="setting"></modalSetting>
      <modal-upload ref="upload" @submit="getList"></modal-upload>
    </template>
  </page-common>
</template>

<script setup>
import {ElMessage, ElMessageBox, ElTag, ElTooltip} from 'element-plus';
import {Warning} from "@element-plus/icons-vue";
import dayjs from "dayjs";

import modalPage from './component/modalPage.vue'
import modalDetail from './component/modalDetail.vue'
import modalLeave from './component/modalLeave.vue'
import modalSetting from './component/modalSetting.vue'
import modalUpload from "./component/modalUpload.vue";

import {getDeptListAPI} from '@/api/settingSystem/user.js';
import {getPageAPI} from '@/api/settingSystem/post.js';
import {rosterDetailAPI, rosterPageAPI, rosterDeleteAPI} from '@/api/operationManagement/roster.js'

import {calcPageNo} from '@/utils/util.js';
import {DropdownWidget} from '@/utils/widget.js';

/** 查询部门list */
const loadDeptDropdownList = () => {
  return new Promise((resolve, reject) => {
    getDeptListAPI({})
        .then((res) => {
          resolve(res.data.dataList);
        })
        .catch((e) => {
          reject(e);
        });
  });
}

const formInlineRef = ref();
const modal = ref()
const detail = ref()
const leave = ref()
const setting = ref()
const upload = ref()

const formInline = reactive({});
const state = reactive({
  title: '',
  isRead: false,
  deptList: [],
  postOptions: [],
  postStatusOptions: {
    0: '离职',
    1: '在职'
  }, // 职位状态
  postStatusColorOptions: {
    0: 'warning',
    1: 'primary'
  },
  sexOptions: {
    1: '男',
    2: '女',
    3: '未知'
  },  //性别信息
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'showName',
      label: '姓名',
      formatter: (row, column, cellValue) => {
        return row.certificateRemindStatus && row.postStatus == 1 ?
            h('div', [h(ElTooltip, {
              placement: 'top',
              content: `操作证于${dayjs(row.certificateEndDate).format('YYYY年MM月DD日')}到期，请及时处理！`
            }, () => h(Warning, {
              style: {
                width: '1.5em',
                height: '1.5em',
                color: 'red',
                verticalAlign: 'middle',
                marginRight: '5px'
              }
            })), cellValue])
            : cellValue
      }
    },
    {
      prop: 'deptName',
      label: '部门'
    },
    {
      prop: 'postNames',
      label: '岗位'
    },
    {
      prop: 'sex',
      label: '性别',
      formatter: (row, column, cellValue) => {
        return state.sexOptions[cellValue]
      }
    },
    {
      prop: 'age',
      label: '年龄'
    },
    {
      prop: 'phone',
      label: '手机号'
    },
    {
      prop: 'postStatus',
      label: '状态',
      formatter: (row, column, cellValue) => {
        return h(ElTag, {type: state.postStatusColorOptions[cellValue]}, {default: () => state.postStatusOptions[cellValue]})
      }
    },
    {
      prop: 'joinJobDate',
      label: '入职时间',
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD')
      },
      width: 160
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  deptInfo: {
    impl: new DropdownWidget(loadDeptDropdownList, true, 'deptId'),
    value: [],
  },
});

onMounted(() => {
  getList()
  queryPosts()
  state.deptInfo.impl.onVisibleChange(true).then((res) => {
    state.deptList = res;
  });
});

// 查询岗位列表
const queryPosts = () => {
  getPageAPI({}).then(res => {
    if (res.success) {
      state.postOptions = res.data.dataList
    }
  })
}

// 查询员工列表
const getList = () => {
  let query = {
    ...formInline,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
    businessType: 'OPERATIONS'
  }
  rosterPageAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 修改后回调方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

// 重置
const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};

/** 创建员工 */
const handleAdd = () => {
  state.title = '新建员工';
  state.isRead = false
  modal.value.open();
};


/** 编辑员工 */
const handleEdit = (row) => {
  state.title = '编辑员工';
  modal.value.open();
  rosterDetailAPI({id: row.rosterId}).then(res => {
    res.data.certificateVOS.forEach(item => {
      item.certificateTime = [item.certificateStartTime, item.certificateEndTime]
    })
    res.data.certificateList = res.data.certificateVOS

    res.data.fileList = res.data.annexPath ? res.data.annexPath.split('|').map(item => {
      return {
        url:  import.meta.env.VITE_BASE_URL+ item,
        imgUrl: item
      }
    }) : []

    nextTick(() => {
      Object.assign(modal.value.form, res.data)
    })
  })
};

/* 员工详情 */
const handleDetail = (row) => {
  detail.value.open();
  rosterDetailAPI({id: row.rosterId}).then(res => {
    console.log(res)
    nextTick(() => {
      Object.assign(detail.value.form, res.data)
    })
  })
}

// 办理离职
const handleLeave = ({rosterId}) => {
  leave.value.open()
  nextTick(() => {
    leave.value.form.rosterId = rosterId
    console.log(leave.value.form)
  })
}

/** 删除员工 */
const handleDetele = (row) => {
  ElMessageBox.confirm('是否删除当前员工?', '提醒', {
    type: 'warning',
  }).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize);
    rosterDeleteAPI({id: row.rosterId}).then((res) => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    });
  });
};

// 设置
const handleSetting = () => {
  setting.value.open()
}

// 导入
const handleUpload = () => {
  upload.value.open()
}
</script>

<style lang="less" scoped>
</style>
