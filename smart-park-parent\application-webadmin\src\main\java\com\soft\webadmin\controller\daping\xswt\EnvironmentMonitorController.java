package com.soft.webadmin.controller.daping.xswt;

import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.util.MyModelUtil;
import com.soft.sub.model.weather.WeatherForecast24h;
import com.soft.sub.service.weather.WeatherService;
import com.soft.sub.vo.equipment.EquipmentWarningMonthStatVO;
import com.soft.webadmin.service.daping.xswt.EnvironmentService;
import com.soft.webadmin.vo.daping.xswt.environment.WeatherForecast24hVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName EnvironmentMonitorController
 * @description:
 * @date 2025年08月08日
 */
@Api(tags = "环境态势")
@RestController
@RequestMapping("/daping/xswt/environment")
public class EnvironmentMonitorController {

    @Autowired
    private EnvironmentService environmentService;

    @Autowired
    private WeatherService weatherService;

    @ApiOperation(value = "环境告警")
    @GetMapping("/environmentWarnStat")
    public ResponseResult<List<EquipmentWarningMonthStatVO>> environmentWarnStat() {
        List<EquipmentWarningMonthStatVO> list = environmentService.environmentWarnStat();
        return ResponseResult.success(list);
    }

    @ApiOperation(value = "园区环境（温湿度）")
    @GetMapping("/weather24H")
    public ResponseEntity<List<WeatherForecast24hVO>> getWeatherForecast(@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        List<WeatherForecast24h> forecasts = weatherService.getWeatherForecastByDate(date);
        return ResponseEntity.ok(MyModelUtil.copyCollectionTo(forecasts, WeatherForecast24hVO.class));
    }

}
