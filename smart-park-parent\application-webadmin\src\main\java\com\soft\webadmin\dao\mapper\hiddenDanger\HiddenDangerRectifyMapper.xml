<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.hiddenDanger.HiddenDangerRectifyMapper">
    <resultMap type="com.soft.webadmin.model.hiddenDanger.HiddenDangerRectify" id="HiddenDangerRectifyResult">
        <result property="id" column="id" />
        <result property="hiddenDangerId" column="hidden_danger_id" />
        <result property="description" column="description" />
        <result property="handleDuration" column="handle_duration" />
        <result property="rectifyImgs" column="rectify_imgs" />
        <result property="returnReason" column="return_reason" />
        <result property="operate" column="operate" />
        <result property="handleUserId" column="handle_user_id" />
        <result property="handleTime" column="handle_time" />
        <result property="examineUserId" column="examine_user_id" />
        <result property="examineResult" column="examine_result" />
        <result property="examineReason" column="examine_reason" />
        <result property="examineTime" column="examine_time" />
    </resultMap>

    <sql id="selectHiddenDangerRectifyVo">
        select t.id, t.hidden_danger_id, t.description, t.handle_duration, t.rectify_imgs, t.return_reason, t.operate, t.handle_user_id, t.handle_time, t.examine_user_id, t.examine_result, t.examine_reason, t.examine_time from sp_hidden_danger_rectify t
    </sql>
    
</mapper>