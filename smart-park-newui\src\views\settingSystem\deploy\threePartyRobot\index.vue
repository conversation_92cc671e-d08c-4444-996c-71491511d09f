<template>
  <page-common v-model="state.tableHeight" :query-bool="false">
    <template #operate>
      <el-button type="primary" :icon="Plus" @click="addHandle">新建</el-button>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableData" row-key="id">
        <el-table-column label="序号" align="center" type="index" width="55"
                         :index="(state.pageParam.pageNum - 1) * state.pageParam.pageSize + 1"/>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter"/>
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pageParam.pageNum"
          :page-size="state.pageParam.pageSize"
          :total="state.pageParam.total"
          @size-change="sizeChange"
          @current-change="currentChange"/>
      <modal-page ref="modal" :title="state.title" @submit="getList"></modal-page>
    </template>
  </page-common>
</template>

<script setup>
import modalPage from './component/modal.vue'
import {getPageAPI, deleteThreePartyRobotAPI} from "@/api/settingSystem/threePartyRobot.js";
import {Plus, Refresh, Search} from "@element-plus/icons-vue";
import {ElMessage, ElMessageBox, dayjs, ElTag} from "element-plus";
import {nextTick, reactive, ref} from "vue";
import {calcPageNo} from "@/utils/util.js";

let modal = ref()
const formInlineRef = ref();
const formInline = reactive({
  name: "",
});
const state = reactive({
  platforms: [
      {
          value: 'QW',
          label: '企业微信',
          disabled: true
      },
      {
          value: 'DINGDING',
          label: '钉钉'
      }
  ],
  robotTypes: [
      {
          value: 'GLOBAL',
          label: '全局',
      },
  ],
  title: "",
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'name',
      label: '名称'
    },
    {
      prop: 'platform',
      label: '平台',
      formatter: (row, column, cellValue) => {
          return state.platforms.find(item => item.value === cellValue).label
      }
    },
    {
      prop: 'type',
      label: '类型',
      formatter: (row, column, cellValue) => {
          return state.robotTypes.find(item => item.value === cellValue).label
      }
    },
    {
      prop: 'createTime',
      label: '创建时间',
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    }
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  }
});

onMounted(() => {
  getList();
});

/**
 * 日期格式化
 */
const dateFormat = (row, column, cellValue) => {
  return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
}

/**
 * 查询列表数据
 */
const getList = () => {
  getPageAPI({pageSize: state.pageParam.pageSize, pageNum: state.pageParam.pageNum}).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

/**
 * 提交查询
 */
const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

/**
 * 重置查询
 */
const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

/**
 * 分页查询（页码）
 * @param pageNum
 */
const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

/**
 * 分页查询（条数）
 * @param pageSize
 */
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

/** 打开创建岗弹窗 */
const addHandle = () => {
  state.title = '新建机器人配置'
  Object.assign(modal.value.platforms, state.platforms)
  Object.assign(modal.value.robotTypes, state.robotTypes)
  modal.value.form.id = ''
  modal.value.open()
};

/** 打开编辑机器人配置弹窗 */
const editHandle = (row) => {
  state.title = '编辑机器人配置'
  Object.assign(modal.value.platforms, state.platforms)
  Object.assign(modal.value.robotTypes, state.robotTypes)
  modal.value.open()
  nextTick(() => {
    Object.assign(modal.value.form, row)
  })
};

/** 删除机器人配置 */
const deleteHandle = (row) => {
  ElMessageBox.confirm("是否删除当前机器人配置?", "提醒", {
    type: "warning",
  }).then(() => {
    state.pageParam.pageNum = calcPageNo(
        state.pageParam.total,
        state.pageParam.pageNum,
        state.pageParam.pageSize
    );
      deleteThreePartyRobotAPI({id: row.id}).then((res) => {
      ElMessage.success("删除成功");
      getList();
    });
  });
};

</script>
