<template>
  <page-common v-model="state.tableHeight" :queryBool="false">
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column prop="equipmentType" label="设备类型" />
        <el-table-column label="能耗价格(元)">
          <template #default="scope"> {{ scope.row.price }}</template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" />
        <el-table-column prop="updateUser" label="更新人" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column align="center" label="操作" width="230">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 编辑 -->
      <modal-page ref="modal" @submit="getList" />
    </template>
  </page-common>
</template>

<script setup>
import { getPriceListAPI } from '@/api/energyManagement/price.js';
import modalPage from './component/modalPage.vue';

const modal = ref();

const state = reactive({
  tableData: [],
  tableHeight: 100,
});

onMounted(() => {
  getList();
});

const getList = () => {
  getPriceListAPI().then((res) => {
    state.tableData = res.data;
    state.tableData.map((item) => {
      if (item.price) {
        item.price = parseFloat(item.price).toFixed(2);
      }
    });
  });
};

/** 编辑设备 */
const editHandle = (row) => {
  modal.value.open();
  nextTick(() => {
    Object.assign(modal.value.form, row);
  });
};
</script>
