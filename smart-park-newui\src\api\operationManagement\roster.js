import { request } from '@/utils/request';

// 分页查询
export const rosterPageAPI = (query) => {
    return request('get', '/shifts/roster/getPage', query, 'F');
};

// 保存
export const rosterSaveAPI = (data) => {
    return request('post', '/shifts/roster/save', data);
};

// 详情
export const rosterDetailAPI = (query) => {
    return request('get', '/shifts/roster/detail', query, 'F');
};

// 删除
export const rosterDeleteAPI = (query) => {
    return request('post', '/shifts/roster/delete', query, 'F');
};

// 离职
export const rosterLeaveAPI = (data) => {
    return request('post', '/shifts/roster/leaveJob', data);
}

// 操作证到期提醒查询
export const rosterGetRemindAPI = () => {
    return request('get', '/shifts/roster/getRemind');
}

// 操作证到期提设置
export const rosterSetRemindAPI = (query) => {
    return request('get', '/shifts/roster/certificateRemind', query, 'F');
}

// 导入
export const rosterUploadAPI = (data) => {
    return request('post', '/shifts/roster/importExcel?businessType=OPERATIONS', data);
}
