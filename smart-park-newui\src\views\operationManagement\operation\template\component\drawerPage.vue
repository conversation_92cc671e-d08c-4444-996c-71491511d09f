<template>
  <el-drawer :modelValue="drawer" :before-close="cancelClick" size="750">
    <template #header>
      <h4>模板信息</h4>
    </template>
    <template #default>
      <el-form ref="ruleFormRef" :model="form" label-width="100px" label-suffix=":">
        <div>
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">基本信息</div>
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="模板名称" prop="templateName">
                {{ form.templateName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模板类型" prop="templateType">
                {{ form.templateType === 'EQUIPMENT' ? '设备' : '空间' }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注" prop="remark">
                {{ form.remark }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div>
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">检查项目</div>
          </div>
          <el-table :data="form.checkTemplateItemList">
            <el-table-column label="序号" type="index" width="60" />
            <el-table-column v-for="column in state.columnList" :label="column.label" :prop="column.prop" :key="column.prop">
              <template #default="scope">
                <span v-show="scope.$index !== editIndex">{{ scope.row[column.prop] }}</span>
                <el-input v-show="scope.$index === editIndex" v-model="scope.row[column.prop]" />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
const props = defineProps({
  drawer: {
    type: Boolean,
    default: false,
  },
});
let { drawer } = toRefs(props);
const emit = defineEmits(['cancelClick']);
const form = ref({});
const state = reactive({
  columnList: [
    { prop: 'itemName', label: '检查项' },
    { prop: 'itemContent', label: '检查内容' },
  ],
});

const cancelClick = () => {
  emit('cancelClick');
};

defineExpose({
  form,
});
</script>

<style lang="less" scoped>
.divFlex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.divRight {
  margin-left: 8px;
  font-size: 16px;
}
.divLeft {
  width: 7px;
  height: 20px;
  background-color: #27C3CA;
}
</style>
