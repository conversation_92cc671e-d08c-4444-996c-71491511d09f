package com.soft.webadmin.controller.check;

import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.check.CheckRecordPointHandleDTO;
import com.soft.webadmin.dto.check.CheckRecordQueryDTO;
import com.soft.webadmin.service.check.CheckRecordEquipmentService;
import com.soft.webadmin.service.check.CheckRecordPointService;
import com.soft.webadmin.service.check.CheckRecordService;
import com.soft.webadmin.vo.check.CheckRecordEquipmentVO;
import com.soft.webadmin.vo.check.CheckRecordPointVO;
import com.soft.webadmin.vo.check.CheckRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 检查记录控制器类
 * 
 * <AUTHOR>
 * @date 2023-12-12
 */
@Api(tags = "检查记录接口")
@RestController
@RequestMapping("/check/record")
public class CheckRecordController {

    @Autowired
    private CheckRecordService checkRecordService;

    @Autowired
    private CheckRecordPointService checkRecordPointService;

    @Autowired
    private CheckRecordEquipmentService checkRecordEquipmentService;

    @ApiOperation(value = "检查记录树")
    @GetMapping("/tree")
    public ResponseResult<Map<String, List<CheckRecordVO>>> tree(CheckRecordQueryDTO queryDTO) {
        return ResponseResult.success(checkRecordService.queryTreeByPlan(queryDTO));
    }

    @ApiOperation(value = "人工巡检，查询巡检点位")
    @GetMapping("/point/list")
    public ResponseResult<List<CheckRecordPointVO>> pointList(@RequestParam Long recordId) {
        List<CheckRecordPointVO> list = checkRecordPointService.queryListByRecordId(recordId);
        return ResponseResult.success(list);
    }

    @ApiOperation(value = "智能巡检，查询巡检设备")
    @GetMapping("/equipment/list")
    public ResponseResult<List<CheckRecordEquipmentVO>> recordEquipmentList(@RequestParam Long recordId) {
        return ResponseResult.success(checkRecordEquipmentService.getList(recordId));
    }

    @ApiOperation(value = "检查点处理")
    @PostMapping("/point/handle")
    public ResponseResult<Void> handle(@Validated @RequestBody CheckRecordPointHandleDTO handleDTO) {
        checkRecordPointService.handle(handleDTO);
        return ResponseResult.success();
    }

}
