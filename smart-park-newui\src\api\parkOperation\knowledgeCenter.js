import { request } from "@/utils/request.js";

// 树形查询
export const knowledgeTreeAPI = (query) => {
    return request('get', '/knowledge/classify/tree', query, 'F')
}

// 分页查询
export const selectList = (query) => {
    return request('get', '/knowledge/base/list', query, 'F')
}

//新增知识分类
export const knowledgesSave = (data) => {
    return request('post','/knowledge/classify/saveOrUpdate',data)
}
//新增文件
export const knowledgesSaveFile = (data) => {
    return request('post','/knowledge/base/add',data)
}
//删除文件
export const knowledgesDeleteFile = (data) => {
    return request('post','/knowledge/base/delete',data)
}
//编辑文件
export const knowledgesUpdateFile = (data) => {
    return request('post','/knowledge/base/update',data)
}
//删除知识分类
export const knowledgesDelete = (data) => {
    return request('post','/knowledge/classify/delete',data)
}

// 下载
export const downloadFile = (query) => {
    return request('get', '/knowledge/base/downloadFile', query, 'F')
}
