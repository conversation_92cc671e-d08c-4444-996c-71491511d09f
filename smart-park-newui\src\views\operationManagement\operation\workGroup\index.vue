<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="groupName">
          <el-input v-model="formInline.groupName" placeholder="工作组名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新建工作组</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :show-overflow-tooltip="true" :height="state.tableHeight">
        <el-table-column prop="name" label="工作组名称" />
        <el-table-column prop="leaderName" label="负责人" />
        <el-table-column prop="users" label="成员">
          <template #default="{ row }">
            <div v-if="row.users">
              <span>{{ row.users.map((user) => user.userName).join('、') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="tags" label="标签">
          <template #default="{ row }">
            <div v-if="row.tags">
              <span>{{ row.tags.map((tag) => tag.tagName).join('、') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" />
        <el-table-column label="操作" align="center" width="160">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
<!--            <el-button link icon="Document" type="success" @click="viewHandle(scope.row)">详情</el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
      <!-- 新建、编辑 -->
      <modal-page ref="modal" :title="state.title" @submit="getList" />
      <!-- 详情 -->
      <drawer-page ref="drawer" :drawer="state.drawer" @cancelClick="state.drawer = false" />
    </template>
  </page-common>
</template>

<script setup>
import { getWorkGroupPageAPI, saveWorkGroupAPI, deleteWorkGroupAPI } from '@/api/operationManagement/workGroup.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import modalPage from './component/modalPage.vue';
import drawerPage from './component/drawerPage.vue';
import { calcPageNo } from '@/utils/util.js';
const modal = ref();
const drawer = ref();
const formInlineRef = ref();
const formInline = reactive({});
const state = reactive({
  tableData: [],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  title: '',
  drawer: false,
});

onMounted(() => {
  getList();
});

const getList = () => {
  let params = {
    groupName: formInline.groupName,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };
  getWorkGroupPageAPI(params).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

/** 新建 */
const addHandle = () => {
  state.title = '新建工作组';
  modal.value.form.id = undefined;
  modal.value.open();
};

/** 编辑 */
const editHandle = (row) => {
  state.title = '编辑工作组';
  modal.value.open();
  nextTick(() => {
    Object.assign(modal.value.form, row);
    modal.value.form.userIds = row.users.map((item) => {
      return item.userId;
    })
    modal.value.form.tagIds = row.tags.map((item) => {
      return item.tagId;
    });
  });
};

/** 删除 */
const deleteHandle = (row) => {
  ElMessageBox.confirm('是否删除当前工作组?', '提醒', {
    type: 'warning',
  }).then(() => {
    state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize);

    const params = {
      id: row.id,
    };
    deleteWorkGroupAPI(params).then((res) => {
      if (res.success) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};

/** 查看详情 */
const viewHandle = (row) => {
  nextTick(() => {
    drawer.value.form = JSON.parse(JSON.stringify(row));
    state.drawer = true;
  });
};
</script>
