<template>
    <div style="height: 100%;overflow: hidden;">
        <transition name="el-zoom-in-center">
            <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" v-show="pageIndex == 0">
                <el-tab-pane label="设备列表" name="first">
                    <device-list @showPage="showPage" ref="deviceListModel">
                    </device-list>
                </el-tab-pane>
                <el-tab-pane label="平面图" name="second">
                    平面图
                </el-tab-pane>
            </el-tabs>
        </transition>

        <transition name="el-zoom-in-center">
            <detail v-show="pageIndex == 1" @showPage="showPage" ref="detailRef"></detail>
        </transition>
    </div>
</template>

<script setup>
import deviceList from './component/deviceList.vue'
import detail from '@/components/deviceDetail/index.vue'
import { events } from '@/utils/bus.js'

const activeName = ref('first')
const pageIndex = ref(0)

const deviceListModel = ref()
const detailRef = ref()

const handleClick = (tab, event) => {

    nextTick(() => {
        if (activeName == 'first') {
            deviceListModel.value.getList([]);
        } else if (activeName == 'second') {

        }

        events.emit('tabClick')
    })
}

const showPage = (index, equipmentId) => {
    pageIndex.value = index

    if (index == 0) {
        if (activeName == 'first') {
            deviceListModel.value.getList([]);
        } else if (activeName == 'second') {

        }
    } else if (index == 1) {
        detailRef.value.equipmentId = equipmentId
        detailRef.value.init();
    }
}
</script>

<style lang='less' scoped>
.el-tabs {
    height: 100%;

    :deep(.el-tabs__content) {
        height: calc(100% - 55px);

        .el-tab-pane {
            height: 100%;
        }
    }
}
</style>