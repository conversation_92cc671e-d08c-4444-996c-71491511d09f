<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="shiftsName">
          <el-input v-model="formInline.shiftsName" placeholder="班次名称"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新增班次</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" row-key="id" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="160">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="state.pagetion.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
      <modalPage ref="modal" :title="state.title" :cateOptions="state.cateOptions" @submit="getList"></modalPage>
    </template>
  </page-common>
</template>

<script setup>
import {ElMessage, ElMessageBox} from 'element-plus'
import dayjs from 'dayjs'

import modalPage from './component/modalPage.vue'

import { settingPageAPI, settingDeleteAPI} from '@/api/operationManagement/workforceSetting.js'

import {calcPageNo} from "@/utils/util.js";

let formInlineRef = ref()
let modal = ref()

const formInline = reactive({})

const state = reactive({
  title: '',
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'shiftsName',
      label: '班次名称'
    },
    {
      prop: '',
      label: '出勤时间',
      formatter: (row, column, cellValue) => {
        return `${row.startTime}-${row.endTime}`
      }
    },
    {
      prop: 'createUserName',
      label: '创建人'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160,
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

// 获取班次
const getList = () => {
  let query = {
    ...formInline ,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
    businessType: 'OPERATIONS'
  }
  settingPageAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 删除班次
const deleteHandle = ({id}) => {
  ElMessageBox.confirm(
      '是否删除当前班次?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize);
    settingDeleteAPI({id}).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 新增班次
const addHandle = () => {
  state.title = '新增班次'
  modal.value.open()
}

// 编辑班次
const editHandle = (info) => {
  state.title = '编辑班次'
  modal.value.open()
  nextTick(() => {
    Object.assign(modal.value.form, { ...info })
  })
}
</script>

<style lang='less' scoped></style>
