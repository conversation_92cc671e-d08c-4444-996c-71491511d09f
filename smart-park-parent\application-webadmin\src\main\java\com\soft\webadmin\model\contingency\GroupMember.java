package com.soft.webadmin.model.contingency;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.contingency.GroupMemberVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 应急小组成员对象 cm_group_member
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Data
@TableName(value = "cm_group_member")
public class GroupMember {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 小组id */
    private Long groupId;

    /** 用户id */
    private Long userId;

    /** 成员名称 */
    private String name;

    /** 手机号 */
    private String phone;

    /** 手机短号 */
    private String shortPhone;

    /** 组内职务：1组长、2组员 */
    private Integer duties;


    @Mapper
    public interface GroupMemberModelMapper extends BaseModelMapper<GroupMemberVO, GroupMember> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        GroupMember toModel(GroupMemberVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        GroupMemberVO fromModel(GroupMember entity);
    }

    public static final GroupMemberModelMapper INSTANCE = Mappers.getMapper(GroupMemberModelMapper.class);
}
