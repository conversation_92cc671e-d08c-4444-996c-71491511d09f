<template>
  <svg>
    <g style="pointer-events: auto">
      <polyline :points="pointsStr" fill="none" :stroke="style.lineBgColor" :stroke-width="style.lineWidth * 2" class="all-scroll" @mousedown="startLineDrag($event)"/>
      <polyline :points="pointsStr" fill="none" :stroke="style.lineColor" :stroke-dasharray="style.strokeDasharray + ' ' + style.strokeDasharray " :stroke-width="style.lineWidth" class="all-scroll" @mousedown="startLineDrag($event)"/>

      <template v-for="(dot,index) in curComponent.points" v-if="active">
        <circle :cx="dot.x" :cy="dot.y" :r="dot.node === 1 ? 8 : 7" class="circle-dot pointer" @mousedown="startCircleDrag($event,dot,index)"/>
      </template>

      <template v-for="move in moveLine">
        <line v-show="drag" :x1="move.sx" :x2="move.ex" :y1="move.sy" :y2="move.ey"
              stroke-dasharray="8 8" style="stroke:rgb(255,0,0);stroke-width:2"/>
      </template>
    </g>
  </svg>
</template>

<script setup>
import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent } = storeToRefs(webtopo)

const props = defineProps({
  element:{
    type : Object,
    default: () => {
      return {}
    }
  },
  type:{
    type:String,
    default: ""
  },
  active:{
    type: Boolean,
    default: false
  }
})

const { style } = toRefs(props.element)

const drag = ref(false)

const moveLine = reactive([])

// svg polyline路径
const pointsStr = computed(() => {
  return (props.element.points || []).map(line => [line.x, line.y]).flat().join(" ")
})

// 线条拖拽
const startLineDrag = (event) => {
  if(props.type !== 'edit') return

  // 按下时鼠标位置
  let mouseX = event.clientX;
  let mouseY = event.clientY;

  // 鼠标移动事件
  document.onmousemove = (e) => {
    let currentMouseX = e.clientX;
    let currentMouseY = e.clientY;

    // 计算需要偏移的距离
    const left = currentMouseX - mouseX;
    const top = currentMouseY - mouseY;

    curComponent.value.points.forEach(item => {
      item.x = item.x + left
      item.y = item.y + top
    })

    mouseX = currentMouseX;
    mouseY = currentMouseY;

  };

  // 鼠标松开事件
  document.onmouseup = () => {
    document.onmousemove = null;
    document.onmouseup = null;
  };
}


// 改变节点
const startCircleDrag = (event, dot, index) => {
  if(props.type !== 'edit') return

  drag.value = true

  let sIdnex; // 起始点
  let smIndex; // 起始中间点
  let eIndex; // 结束点
  let emIndex; // 结束点中间点

  if (dot.node === 1) {  // 节点一
    sIdnex = index === 0 ? 2 : index - 2
    smIndex = index === 0 ? 1 : index - 1

    moveLine.push({
      sx: curComponent.value.points[sIdnex].x,
      sy: curComponent.value.points[sIdnex].y,
      ex: event.offsetX,
      ey: event.offsetY,
    })

    if (curComponent.value.points.length > 3 && index !== 0 && index !== curComponent.value.points.length - 1) {
      eIndex = index + 2
      emIndex = index + 1
      moveLine.push({
        sx: curComponent.value.points[eIndex].x,
        sy: curComponent.value.points[eIndex].y,
        ex: event.offsetX,
        ey: event.offsetY,
      })
    }

  } else if (dot.node === 2) { // 节点二
    sIdnex = index - 1
    eIndex = index + 1

    moveLine.push({
      sx: curComponent.value.points[sIdnex].x,
      sy: curComponent.value.points[sIdnex].y,
      ex: event.offsetX,
      ey: event.offsetY,
    })

    moveLine.push({
      sx: curComponent.value.points[eIndex].x,
      sy: curComponent.value.points[eIndex].y,
      ex: event.offsetX,
      ey: event.offsetY,
    })
  }

  // 鼠标移动事件
  document.onmousemove = (e) => {
    if (dot.node === 1) {  // 节点一

      moveLine[0].ex = e.offsetX;
      moveLine[0].ey = e.offsetY;

      if (curComponent.value.points.length > 3 && index !== 0 && index !== curComponent.value.points.length - 1) {
        moveLine[1].ex = e.offsetX;
        moveLine[1].ey = e.offsetY;
      }

    } else if (dot.node === 2) { // 节点二

      moveLine[0].ex = e.offsetX;
      moveLine[0].ey = e.offsetY;

      moveLine[1].ex = e.offsetX;
      moveLine[1].ey = e.offsetY;
    }
  };

  // 鼠标松开事件
  document.onmouseup = () => {
    document.onmousemove = null;
    document.onmouseup = null;

    if (dot.node === 1) {  // 节点一
      curComponent.value.points[index].x = moveLine[0].ex
      curComponent.value.points[index].y = moveLine[0].ey

      curComponent.value.points[smIndex].x = (moveLine[0].sx + moveLine[0].ex) / 2
      curComponent.value.points[smIndex].y = (moveLine[0].sy + moveLine[0].ey) / 2

      if (curComponent.value.points.length > 3 && index !== 0 && index !== curComponent.value.points.length - 1) {
        curComponent.value.points[emIndex].x = (moveLine[1].sx + moveLine[1].ex) / 2;
        curComponent.value.points[emIndex].y = (moveLine[1].sy + moveLine[1].ey) / 2;
      } else if (curComponent.value.points.length > 3 && (index === 0 || index === curComponent.value.points.length - 1)) { //删除点位
        if ((curComponent.value.points[index].x > curComponent.value.points[sIdnex].x - 8 && curComponent.value.points[index].x < curComponent.value.points[sIdnex].x + 8) && (curComponent.value.points[index].y > curComponent.value.points[sIdnex].y - 8 && curComponent.value.points[index].y < curComponent.value.points[sIdnex].y + 8)) {
          curComponent.value.points.splice(sIdnex, 2)
        }
      }

    } else if (dot.node === 2) { // 节点二
      curComponent.value.points[index].x = moveLine[0].ex
      curComponent.value.points[index].y = moveLine[0].ey

      // 改变节点类型
      curComponent.value.points[index].node = 1

      // 增加新的节点
      curComponent.value.points.splice(sIdnex + 1, 0, {
        x: (moveLine[0].sx + moveLine[0].ex) / 2,
        y: (moveLine[0].sy + moveLine[0].ey) / 2,
        node: 2
      })

      curComponent.value.points.splice(index + 2, 0, {
        x: (moveLine[1].sx + moveLine[1].ex) / 2,
        y: (moveLine[1].sy + moveLine[1].ey) / 2,
        node: 2
      })
    }

    drag.value = false
    moveLine.splice(0, moveLine.length)
  };

}

</script>

<style scoped>
polyline {
  animation-duration: 60s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-name: flow;
}

@keyframes flow {
  from {
    stroke-dashoffset: v-bind('style.rateOfFlow');
  }
  to {
    stroke-dashoffset: 0;
  }
}

.circle-dot{
  fill:#29b6f2;
  stroke:#eee;
  stroke-width:2;
}
</style>
