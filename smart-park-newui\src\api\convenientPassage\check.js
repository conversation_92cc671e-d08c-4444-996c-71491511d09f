import { request } from "@/utils/request";

// 审核列表
export const visitlist = (data) => {
    return request('post', '/visitor/apply-record/list', data)
}

// 批量通过
export const batchApproveAPI = (data) => {
    return request('post', '/visitor/apply-record/batch', data)
}

// 通过、驳回
export const audit = (data) => {
    return request('post', '/visitor/apply-record/approve', data)
}

// 访客记录
export const visitorRecord = (data) => {
    return request('post', '/visitor/pass-record/list', data)
}

