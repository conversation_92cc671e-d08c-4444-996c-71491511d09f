<template>
    <el-tooltip
        effect="dark"
        :content="task.title"
        placement="top"
    >
      <div class="task-icon" :class="{ 'task-active': isTop && !isMin }" @click="cancelMin">
        <div class="icon">
          <img :src="iconPath">
        </div>
        <div class="underline"></div>
      </div>
    </el-tooltip>
</template>

<script setup>
import { useWindowStore } from '@/store/modules/window.js'

const props = defineProps({
  task:{
    type: Object,
    default: () => {}
  }
})

const windowStore = useWindowStore()

const isTop = computed(() => {
  return windowStore.isTop(props.task.id)
})

const isMin = computed(() => {
  return windowStore.isMin(props.task.id)
})

// 图标地址
const iconPath = computed(() => {
  return new URL(`/src/assets/window/${props.task.icon}.png`, import.meta.url).href
})

const cancelMin = () => {
  windowStore.cancelMin(props.task.id);
  windowStore.goTop(props.task.id);
}
</script>

<style scoped lang="less">
.task-icon{
  padding: 6px 8px 0;
  border-radius: 2px;
  transition: all 0.2s ease-in-out;
  .underline{
    width: 6px;
    height: 3px;
    background-color: #6b6b6b;
    margin: auto;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
  }
}

.task-icon:hover{
  background-color: rgba(250,250,250,0.7);
}

.task-active{
  background-color: rgba(250,250,250,0.7) ;
  .underline{
    width: 16px;
    background-color: #1871d5;
  }
}

.icon img{
  width: 26px;
  height: 26px;
}

.task-icon:active{
  .icon{
    transform: scale(0.8);
  }
}
</style>
