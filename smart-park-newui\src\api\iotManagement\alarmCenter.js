import { request } from "@/utils/request";

export const getAlarmCenterPageAPI = (data) => {
    return request('get', '/check/alarmCenter/getPage', data, 'F');
};

export const alarmHandleAPI = (data) => {
    return request('post', '/check/alarmCenter/handle', data);
};

//计算用时
export const parseTimeUsedNew = (inputTime) => {
    let obj = {
        text: '-',
        day: 0,
        isDanger: false
    };

    if (!inputTime || arguments.length === 0) {
        return obj;
    }

    let curTime = new Date().getTime();
    let resTimt = new Date(inputTime).getTime();

    // console.log(resTimt,"resTimt")
    let time = curTime / 1000 - resTimt / 1000;


    if (time < 60) {
        // let s = parseInt(time)
        obj.text = `一分钟以内`;
    } else if (time < 3600) {
        let m = parseInt(time / 60);
        let s = parseInt(time % 60);
        obj.text = `${m}分${s}秒`;
    } else if (time < 86400) {
        let h = parseInt(time / 3600);
        let min = time - h * 3600;
        let m = parseInt(min / 60);
        obj.text = `${h}小时${m}分`;
    } else if (time > 86400) {
        let day = parseInt(time / 86400);
        let hour = time - day * 86400;
        let h = parseInt(hour / 3600);
        let min = hour - h * 3600;
        let m = parseInt(min / 60);
        if (day > 7 || (day == 7 && h > 0)) {
            obj.isDanger = false;
        }

        obj.day = day;
        obj.text = `${day}天${h}小时`;
    }
    return obj;
};
