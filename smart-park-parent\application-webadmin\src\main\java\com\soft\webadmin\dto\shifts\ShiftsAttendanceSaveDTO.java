package com.soft.webadmin.dto.shifts;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * ShiftsAttendanceDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsAttendanceDTO对象")
@Data
public class ShiftsAttendanceSaveDTO {
    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private String businessType;

    @NotNull(message = "花名册ID不能为空")
    @ApiModelProperty(value = "花名册ID")
    private Long rosterId;

    @NotNull(message = "出勤日期不能为空")
    @ApiModelProperty(value = "出勤日期")
    private String attendanceDate;

    @NotNull(message = "班次设置表ID为空")
    @ApiModelProperty(value = "班次设置表ID")
    private Long shiftsSettingId;

    @NotBlank(message = "责任区域不能为空")
    @ApiModelProperty(value = "责任区域")
    private String liabilityArea;
}
