package com.soft.webadmin.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.sub.enums.EquipmentStatusEnums;
import com.soft.webadmin.model.equipment.EquipmentOm;
import com.soft.webadmin.service.equipment.EquipmentOmService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class OmEquipmentJob {

    @Autowired
    private EquipmentOmService equipmentOmService;

    private static final String SCRAP_HANDLE_NAME = "omEquipmentScrapJob";

    /**
     * 运维设备报废定时任务处理：每30分钟执行一次
     */
    @XxlJob(SCRAP_HANDLE_NAME)
    public void scrapHandle() {
        List<EquipmentOm> equipments = equipmentOmService.list(
                new LambdaQueryWrapper<EquipmentOm>().ne(EquipmentOm::getEquipmentStatus, EquipmentStatusEnums.SCRAP.getValue())
                        .le(EquipmentOm::getScrapDate, DateUtil.today())
        );

        if (CollectionUtil.isNotEmpty(equipments)) {
            List<EquipmentOm> updateList = equipments.stream().map(e -> {
                EquipmentOm equipmentOm = new EquipmentOm();
                equipmentOm.setEquipmentId(e.getEquipmentId());
                equipmentOm.setEquipmentStatus(EquipmentStatusEnums.SCRAP.getValue());
                return equipmentOm;
            }).collect(Collectors.toList());

            equipmentOmService.updateBatchById(updateList);
        }
    }

}
