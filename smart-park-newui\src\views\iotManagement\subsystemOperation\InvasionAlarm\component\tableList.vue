<template>
  <page-common v-model="state.tableHeight" :operateBool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="defenceWord">
          <el-input v-model="formInline.defenceWord" placeholder="防区名称/编号" />
        </el-form-item>
        <el-form-item prop="runStatus">
          <el-select v-model="formInline.runStatus" placeholder="在线状态">
            <el-option v-for="(value, key) in state.runStatusOptions" :key="key" :label="value" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item prop="defenceStatus">
          <el-select v-model="formInline.defenceStatus" placeholder="防区状态">
            <el-option v-for="(value, key) in state.defenceStatusOptions" :key="key" :label="value" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" row-key="equipmentId">
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" />
        <el-table-column align="center" label="操作">
          <template #default="{ row }">
            <!-- 子系统  -->
            <div v-if="row.children">
              <el-button link type="primary" @click="handleSubsystem(row, 1)">布防</el-button>
              <el-button link type="primary" @click="handleSubsystem(row, 0)">撤防</el-button>
              <el-button link type="primary" @click="handleEliminateOperate(row)">消警</el-button>
            </div>
            <!-- 防区 -->
            <div v-else>
              <el-button link type="primary" @click="handleDefence(row, 1)">旁路</el-button>
              <el-button link type="primary" @click="handleDefence(row, 0)">旁路恢复</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />


      <el-dialog v-model="state.dialogFormVisible" title="确认" width="500" class="dialogForm">
        <div>确定消警吗？</div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="state.dialogFormVisible = false">取消</el-button>
            <el-button type="primary" @click="handleEliminate">确定</el-button>
            <el-button type="primary" @click="handleEliminateRecord">确定并记录</el-button>
          </div>
        </template>
      </el-dialog>

      <modalEliminate ref="eliminate" :subSystemId="state.subSystemId"></modalEliminate>
    </template>
  </page-common>
</template>

<script setup>
import { ElTag, ElMessage, ElMessageBox } from 'element-plus'

import modalEliminate from './modal/modalEliminate.vue'

import { getAlarmEquipAPI, subsystemEliminateAPI, subsystemIssueAPI, defenceIssueAPI } from '@/api/iotManagement/InvasionAlarm.js'

const formInlineRef = ref()
const eliminate = ref()

const formInline = reactive({})
const state = reactive({
  dialogFormVisible: false,
  spaceOptions: [],
  statusOptions: {
    0: 'danger',
    1: 'success'
  },
  runStatusOptions: {
    0: '离线',
    1: '正常'
  },
  equipmentStatusOptions: {
    0: '故障',
    1: '正常'
  },
  defenceStatus: {
    '-1': 'info',
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'danger',
    4: 'info'
  },
  defenceStatusOptions: {
    '-1': '未知',
    0: '离线',
    1: '正常',
    2: '故障',
    3: '报警',
    4: '旁路'
  },
  alarmStatus: {
    0: 'success',
    1: 'danger'
  },
  alarmStatusOptions: {
    0: '未告警',
    1: '告警'
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'subSystemNo',
      label: '子系统编号'
    },
    {
      prop: 'equipmentNo',
      label: '防区编号',
    },
    {
      prop: 'equipmentName',
      label: '防区名称'
    },
    {
      prop: 'spaceFullName',
      label: '所在区域'
    },
    {
      prop: 'runStatus',
      label: '在线状态',
      formatter: (row, column, cellValue) => {
        return !row.children && h(ElTag, { type: state.statusOptions[cellValue] }, { default: () => state.runStatusOptions[cellValue] })
      }
    },
    {
      prop: 'defenceStatus',
      label: '防区状态',
      formatter: (row, column, cellValue) => {
        return !row.children && h(ElTag, { type: state.defenceStatus[cellValue] }, { default: () => state.defenceStatusOptions[cellValue] })
      }
    },
    {
      prop: 'alarmStatus',
      label: '子系统状态',
      formatter: (row, column, cellValue) => {
        return row.children && h(ElTag, { type: state.alarmStatus[cellValue] }, { default: () => state.alarmStatusOptions[cellValue] })
      }
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
  subSystemId:'' //消警
})

onMounted(() => {
  getList()
})

// 获取设备
const getList = () => {
  let query = {
    ...formInline,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
  }
  getAlarmEquipAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 消警操作
const handleEliminateOperate = (row) => {
  state.dialogFormVisible = true
  state.subSystemId = row.equipmentId
}

// 消警
const handleEliminate = () => {
  subsystemEliminateAPI({
    subSystemId:state.subSystemId
  }).then(res => {
    if (res.success) {
      state.dialogFormVisible = false
      getList()
      ElMessage.success('消警成功')
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

// 确认并记录
const handleEliminateRecord = () => {
  state.dialogFormVisible = false
  nextTick(() => {
    eliminate.value.open()
  })
}

// 子系统布防撤防
const handleSubsystem = (row, status) => {
  ElMessageBox.confirm(
    status ? '是否开启布防?' : '是否开启撤防?',
    '提醒',
  ).then(() => {
    subsystemIssueAPI({
      subSystemId: row.equipmentId,
      status
    }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success(status ? '开启布防成功' : '开启撤防成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 旁路恢复
const handleDefence = ({ equipmentId }, status) => {
  ElMessageBox.confirm(
    status ? '是否开启旁路?' : '是否旁路恢复?',
    '提醒',
  ).then(() => {
    defenceIssueAPI({
      equipmentIds: [equipmentId],
      status
    }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success(status ? '开启旁路成功' : '旁路恢复成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}
</script>

<style lang='less' scoped></style>
