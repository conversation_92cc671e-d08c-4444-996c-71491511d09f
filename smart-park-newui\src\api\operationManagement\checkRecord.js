import { request } from '@/utils/request';

// 检查记录树
export const checkRecordAPI = (params) => {
  return request('get', '/check/record/tree', params, 'F');
} 

// 人工巡检，查询巡检点位
export const pointInspectionAPI = (params) => {
  return request('get', '/check/record/point/list', params, 'F');
}

// 智能巡检，查询巡检设备
export const pointSmartAPI = (params) => {
  return request('get', '/check/record/equipment/list', params, 'F');
}
