<template>
  <div style="height: 100%;">
    <el-card class="box-card card-textBg">
      <template #header>
        <el-row justify="space-between" align="middle">
          <strong>出库信息</strong>
          <div>
            <el-button type="primary" icon="Check" @click="handleAgree" v-show="data.examineState == 1">同意</el-button>
            <el-button type="primary" icon="Back" @click="showPage">返回</el-button>
          </div>
        </el-row>
      </template>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <div class="detail-area">
        <el-form label-position="top">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item label="出库单号">
                {{ data.inoutNo }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="出库类型">
                {{ state.typeOptions[data.type] }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="出库日期">
                {{ data.inoutDate }}
              </el-form-item>
            </el-col>
            <el-col :span="5" v-show="data.type == 6">
              <el-form-item label="申请人">
                {{ data.applyUserName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="创建人">
                {{ data.createUserName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="创建时间">
                {{ data.createTime }}
              </el-form-item>
            </el-col>

            <!--            6原始出库 备件领用-->
            <el-col :span="24" v-show="data.type == 6 ">
              <el-form-item label="备注">
                {{ data.remark }}
              </el-form-item>
            </el-col>

            <!--            7盘亏出库-->
            <el-col :span="5" v-show="data.type == 7">
              <el-form-item label="关联盘点单">
                {{ data.stocktakingNo }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">出库明细</div>
      </div>
      <div class="detail-area">
        <el-table :data="data.recordVOList">
          <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                           :label="item.label"
                           :align="item.align" :formatter="item.formatter"/>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import {ElMessage, ElMessageBox} from "element-plus";

import {putoutDetailAPI, putoutAuditAPI} from '@/api/operationManagement/putout.js'

const emit = defineEmits(['showPage'])

const data = ref({})

const state = reactive({
  id: '',
  radio: 'spread',
  typeOptions: {
    6: '领用出库',
    7: '盘亏出库'
  },
  tableHeader: [
    {
      prop: 'sparePartVO.sparePartNo',
      label: '备件编号'
    },
    {
      prop: 'sparePartVO.sparePartName',
      label: '备件名称'
    },
    {
      prop: 'sparePartVO.classifyName',
      label: '备件分类'
    },
    {
      prop: 'sparePartVO.model',
      label: '规格型号'
    },
    {
      prop: 'sparePartVO.unitPrice',
      label: '单价（元）'
    },
    {
      prop: 'sparePartVO.unit',
      label: '单位'
    },
    {
      prop: 'storehouseName',
      label: '出库仓库'
    },
    {
      prop: 'changeQuantity',
      label: '出库数量'
    },
  ]
})

const loadPage = (id) => {
  state.id = id
  getDetail()
}

// 获取详情
const getDetail = () => {
  putoutDetailAPI({id: state.id}).then(res => {
    console.log(res)
    data.value = res.data
  })
}

// 同意
const handleAgree = () => {
  ElMessageBox.confirm(
      '确定同意出库吗？',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    putoutAuditAPI({id: state.id, examineState: 2}).then(res => {
      if (res.success) {
        getDetail()
        ElMessage.success('已审批')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 返回
const showPage = () => {
  emit('showPage', 0)
}

defineExpose({
  loadPage
})
</script>

<style scoped lang="less">
</style>
