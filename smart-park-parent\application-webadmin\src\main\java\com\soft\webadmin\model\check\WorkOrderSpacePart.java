package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import com.soft.webadmin.vo.check.WorkOrderSpacePartVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 报价关联备品备件对象 sp_work_quote_relation
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_work_order_spare_part")
public class WorkOrderSpacePart extends BaseModel {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 工单id */
    private Long orderId;

    /** 备件id */
    private Long sparePartId;

    /** 备件名称*/
    private String sparePartName;

    /** 备件分类 */
    private String sparePartClassifyName;

    /** 领用数量 */
    private Integer receiveQuantity;

    @Mapper
    public interface WorkQuoteRelationModelMapper extends BaseModelMapper<WorkOrderSpacePartVO, WorkOrderSpacePart> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        WorkOrderSpacePart toModel(WorkOrderSpacePartVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        WorkOrderSpacePartVO fromModel(WorkOrderSpacePart entity);
    }

    public static final WorkQuoteRelationModelMapper INSTANCE = Mappers.getMapper(WorkQuoteRelationModelMapper.class);
}
