package com.soft.webadmin.dto.check;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * CheckRecordDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckRecordDTO对象")
@Data
public class CheckRecordDTO {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "数据验证失败，主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "检查人id")
    private Long checkUserId;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    @ApiModelProperty(value = "删除标记(1: 正常 -1: 已删除)")
    private Integer deletedFlag;

    @ApiModelProperty(value = "创建者id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者id")
    private Long updateUserId;

    @ApiModelProperty(value = "最后更新时间")
    private Date updateTime;

}
