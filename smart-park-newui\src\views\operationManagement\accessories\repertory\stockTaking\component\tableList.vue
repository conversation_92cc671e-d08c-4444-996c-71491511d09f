<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="noOrName">
          <el-input v-model="formInline.noOrName" placeholder="盘点单号/盘点单名称" />
        </el-form-item>
        <el-form-item prop="result">
          <el-select v-model="formInline.result" placeholder="盘点结果">
            <el-option v-for="(value, key) in state.resultOptions" :label="value" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Upload" @click="handleUpload">导入</el-button>
      <el-button type="primary" icon="Plus" @click="handleAdd">新增盘点</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="240">
          <template #default="{row}">
            <el-button link type="primary" icon="Tickets" @click="handleView(row)" :disabled="!(row.result == 1 || row.result == 2)">详情</el-button>
            <el-button link type="primary" icon="Edit" @click="handleEdit(row)" :disabled="row.result != 0">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="handleDetele(row)" :disabled="row.result != 0">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
                     @size-change="sizeChange" @current-change="currentChange" />
      <modalUpload ref="upload" @submit="getList" :storehouseOptions="state.storehouseOptions.filter(e => e.state == 0)"></modalUpload>
    </template>
  </page-common>
</template>

<script setup>
import { ElTag, ElMessageBox, ElMessage } from 'element-plus';

import modalUpload from '../modal/modalUpload.vue'

import { getStorePageAPI } from '@/api/operationManagement/storeManagement.js'

import { stockPageAPI, stockDeleteAPI } from '@/api/operationManagement/stockTaking.js'

import { calcPageNo } from '@/utils/util.js'

const emit = defineEmits(['showPage'])

const formInlineRef = ref()
const upload = ref()

const formInline = reactive({})
const state = reactive({
  resultOptions: {
    0:'草稿',
    1:'无盈亏',
    2:'有盈亏'
  },
  resultTypeOptions:{
    0:'info',
    1:'warning',
    2:'primary',
  },
  storehouseOptions: [],
  tableHeight: 100,
  tableData: [{}],
  tableHeader: [
    {
      prop: 'stocktakingNo',
      label: '盘点单号',
      width: 210
    },
    {
      prop: 'stocktakingName',
      label: '盘点单名称'
    },
    {
      prop: 'result',
      label: '盘点结果',
      formatter:(row, column, cellValue) => {
        return h(ElTag, {type: state.resultTypeOptions[cellValue]}, {default: () => state.resultOptions[cellValue]})
      }
    },
    {
      prop: 'beginTime',
      label: '盘点开始时间',
      width: 120
    },
    {
      prop: 'endTime',
      label: '盘点结束时间',
      width:  120
    },
    {
      prop: 'intoState',
      label: '入库状态',
      formatter: (row, column, cellValue) => {
        return {
          1:'待入库',
          2:'已入库'
        }[cellValue]
      }
    },
    {
      prop: 'outState',
      label: '出库状态',
      formatter: (row, column, cellValue) => {
        return {
          1:'待出库',
          2:'已出库'
        }[cellValue]
      }
    },
    {
      prop: 'createUserName',
      label: '创建人'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
  getStorePage()
})

// 获取仓库
const getStorePage = () => {
  getStorePageAPI({ businessType: 'OPERATIONS'}).then(res => {
    state.storehouseOptions = res.data.dataList
  })
}

// 获取盘点
const getList = () => {
  let query = {
    ...formInline,
    businessType: 'OPERATIONS',
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
  }
  stockPageAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 导入
const handleUpload = () => {
  upload.value.open()
}

// 新增盘点
const handleAdd = () => {
  emit('showPage',2)
}

// 编辑盘点
const handleEdit = ({id}) => {
  emit('showPage',2,id)
}

// 详情
const handleView = ({id}) => {
  emit('showPage',1,id)
}

// 删除盘点
const handleDetele = ({id}) => {
  ElMessageBox.confirm(
      '是否删除当前盘点?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize);
    stockDeleteAPI({ id }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}
defineExpose({
  getList
})
</script>

<style lang='less' scoped></style>
