import {request} from '@/utils/request';


export const listPowerGroupAPI = (data) => {
    return request('get', '/sys/permGroup/list', data, 'F')
}

// 详情
export const detailPowerGroupAPI = (id) => {
    return request('get', '/sys/permGroup/' + id, {}, 'F')
}


// 新增或修改
export const saveOrUpdatePowerGroupAPI = (data) => {
    return request('post', '/sys/permGroup/saveOrUpdate', data)
}

// 删除
export const deletePowerGroupAPI = (data) => {
  return request('post', '/sys/permGroup/delete', data, 'F')
}


// 查询用户权限组列表
export const listByUserPowerGroupAPI = (data) => {
  return request('get', '/sys/permGroup/listByUser', data, 'F')
}

// 保存用户权限组
export const saveUserPowerGroupAPI = (data) => {
    return request('post', '/sys/permGroup/saveUserPowerGroup', data)
}