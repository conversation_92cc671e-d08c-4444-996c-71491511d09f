<template>
  <div style="height: 100%;overflow: hidden;">
    <!-- 列表 -->
    <transition name="el-zoom-in-center">
      <tableList ref="table" v-show="pageIndex == 0" @showPage="showPage"></tableList>
    </transition>
    <!-- 详情 -->
    <transition name="el-zoom-in-center">
      <detailPage ref="detail" v-show="pageIndex == 1" @showPage="showPage"></detailPage>
    </transition>
  </div>
</template>

<script setup>
import tableList from './component/tableList.vue'
import detailPage from './component/detailPage.vue'

const table = ref()
const detail = ref()
const pageIndex = ref(0)

const showPage = (index, id) => {
  pageIndex.value = index

  if (index == 0) { // 刷新
    table.value.getList()
  } else if(index == 1){
    detail.value.setId(id)
  }
}

</script>

<style scoped lang="less">

</style>