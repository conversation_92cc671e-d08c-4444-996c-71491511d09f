<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" @onClose="onClose" :formRef="ruleFormRef" :width="900" :showButton="!isRead"
                 class="dialogTextarea" >
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="姓名" prop="showName">
            <el-input v-model="form.showName" placeholder="请输入姓名" :disabled="isRead"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-select v-model="form.sex" filterable clearable placeholder="请选择性别" :disabled="isRead">
              <el-option v-for="(value,key) in props.sexOptions" :label="value" :value="Number(key)"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="年龄" prop="age">
            <el-input v-model.number="form.age"  placeholder="请输入年龄" :disabled="isRead" maxlength="2"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model.number="form.phone" placeholder="请输入手机号" :disabled="isRead" maxlength="11"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="cardNo">
            <el-input v-model="form.cardNo" placeholder="请输入身份证号" :disabled="isRead" maxlength="18"/>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">入职信息</div>
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="部门" prop="deptId">
            <el-cascader v-model="form.deptId" :clearable="true" :props="{
              value: 'deptId',
              label: 'deptName',
              checkStrictly: true,
              expandTrigger: 'hover',
            }" :options="props.detpList" @change="onDeptIdValueChange" placeholder="请选择部门" :disabled="isRead">
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位" prop="deptPostIds">
            <el-select v-model="form.deptPostIds" placeholder="请选择岗位" multiple clearable :disabled="isRead">
              <el-option v-for="deptPost in state.deptPostList" :label="deptPost.postShowName"
                         :value="deptPost.deptPostId"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="入职日期" prop="joinJobDate">
            <el-date-picker
                v-model="form.joinJobDate"
                type="date"
                placeholder="请选择入职日期"
                value-format="YYYY-MM-DD"
                :disabled="isRead"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任区域" prop="spaceIds">
            <el-cascader v-model="form.spaceIds" :options="state.spaceOptions" :props="state.spaceProps"
                         clearable placeholder="请选择责任区域" :disabled="isRead" collapse-tags
                         collapse-tags-tooltip/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-for="(item,index) in form.certificateList">
        <el-col :span="12">
          <el-form-item label="操作证名称" :prop="`certificateList.${index}.certificateName`"
          :rules="{required: true, message: '请输入操作证名称'}">
            <el-input v-model="item.certificateName" placeholder="请输入操作证名称" :disabled="isRead"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期" :prop="`certificateList.${index}.certificateTime`"
           :rules="{required: true, message: '请输入有效期'}">
            <el-date-picker
                v-model="item.certificateTime"
                type="daterange"
                range-separator="-"
                start-placeholder="起始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                :disabled="isRead"
            />
            <el-button type="danger" icon="Delete" size="small" style="margin-left: 10px" @click="handleDeleteCert(index)" v-if="!isRead"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-button icon="Plus" type="primary" @click="handleAddCert" style="margin-bottom: 18px" v-if="!isRead">添加操作证</el-button>
      <div class="divFlex" v-if="!(isRead && form.fileList.length == 0)">
        <div class="divLeft"></div>
        <div class="divRight">材料附件</div>
      </div>
      <el-upload v-model:file-list="form.fileList" :action="state.action" :headers="{ Authorization: state.Authorization }" list-type="picture-card"  :on-success="fileSuccess" :disabled="isRead"
                 :class="{'disUpload': isRead || form.fileList.length == 3}" :limit="3" :on-preview="handlePictureCardPreview"  accept="image/*">
        <el-icon><Plus /></el-icon>
        <template #tip v-if="!isRead">
          <div class="el-upload__tip">
            支持格式：jpg、png ，单个文件不能超过5MB，最多支持3张
          </div>
        </template>
      </el-upload>
      <div v-show="form.postStatus == 0">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">离职原因</div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="离职日期" prop="leaveJobDate">
              {{ form.leaveJobDate }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="离职原因" prop="leaveJobReason">
            {{ form.leaveJobReason }}
          </el-form-item>
        </el-row>
      </div>
    </el-form>
  </dialog-common>
  <el-dialog v-model="state.dialogVisible">
    <img w-full :src="state.dialogImageUrl" alt="Preview Image" style="width: 100%;"/>
  </el-dialog>
</template>

<script setup>
import {ElMessage} from 'element-plus';

import {
  getDeptPostListAPI
} from '@/api/settingSystem/user.js';

import {treeAPI} from '@/api/iotManagement/space.js';

import { rosterSaveAPI } from '@/api/operationManagement/roster.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  isRead:{
    type: Boolean,
    default: false
  },
  sexOptions:{
    type: Object,
    default: () => {}
  },
  detpList: {
    type: Array,
    default: () => []
  },
})

const emit = defineEmits(['submit']);

const ruleFormRef = ref();
const dialog = ref();

const form = reactive({
  deptPostIds:[],
  certificateList:[] , // 操作证书
  fileList: [] // 材料
});

// 正则校验
const validateRegular = (value,callback,Reg,tip) => {
  if (value && !Reg.test(value)) {
    callback(new Error(tip))
  } else {
    callback()
  }
}

const state = reactive({
  action: import.meta.env.VITE_BASE_URL+ '/core/file/upload',
  Authorization: localStorage.getItem('Authorization') ,
  dialogVisible: false,
  dialogImageUrl: '',
  deptPostList: [], // 岗位
  spaceOptions: [], // 位置信息
  spaceProps: {
    emitPath: false,
    checkStrictly: false,
    multiple: true,
    label: 'name',
    value: 'id',
    expandTrigger: 'hover',
  },   // 位置级联选择配置
  rules: {
    showName: [{required: true, message: '请输入姓名'}],
    sex: [{required: true, message: '请选择性别'}],
    age: [{validator: (rule,value,callback) => validateRegular(value,callback, /^(?:[1-9]\d?|1[0-2]\d|13[0-1])$/ ,'请输入正确的年龄'),trigger: 'blur'}],
    phone: [
        { required: true, message: '请输入手机号' },
        { validator: (rule,value,callback) => validateRegular(value,callback, /^1[3456789]\d{9}$/ ,'请输入正确的手机号'),trigger: 'blur'}
    ],
    cardNo:[{validator: (rule,value,callback) => validateRegular(value,callback, /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/ ,'请输入正确的身份证'),trigger: 'blur'}],
    deptId: [{required: true, message: '请选择部门'}],
    joinJobDate: [{required: true, message: '请选择入职日期'}],
    spaceIds: [{required: true, message: '请选择责任区域'}]
  },
});

onMounted(() => {
  loadSpaceOptions()
})

/** 部门选中值改变 */
const onDeptIdValueChange = (value) => {
  form.deptId = Array.isArray(value) ? value[value.length - 1] : undefined;
  form.deptPostIds = []
};

/** 查询岗位list */
const loadDeptPostList = (deptId) => {
  if(deptId){
    getDeptPostListAPI({deptId}).then((res) => {
      state.deptPostList = res.data;
    });
  }
};

// 查询位置列表
const loadSpaceOptions = () => {
  /** 查询位置 */
  treeAPI({deep: 4}).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
}

// 添加证书
const handleAddCert = () => {
  form.certificateList.push({})
}

// 删除证书
const handleDeleteCert = (index) => {
  form.certificateList.splice(index,1)
}

// 查看照片
const handlePictureCardPreview = (uploadFile) => {
  state.dialogImageUrl =  import.meta.env.VITE_BASE_URL+ uploadFile.imgUrl
  state.dialogVisible = true
}


// 上传图片
const fileSuccess = (response,file) => {
  file.imgUrl = response.data.filePath
}

// 关闭dialog
const onClose = () => {
  delete form.rosterId
  form.certificateList = []
  form.fileList = []
  form.postStatus = -1
}

// 提交表单
const submit = () => {
  let subForm = {
    ...JSON.parse(JSON.stringify(form)),
    businessType: 'OPERATIONS'
  }

  subForm.certificateList.forEach(item => {
    if(item.certificateTime && item.certificateTime.length){
      item.certificateStartTime = item.certificateTime[0]
      item.certificateEndTime = item.certificateTime[1]
    }
  })

  subForm.annexPath = (subForm.fileList || []).map(item => item.imgUrl).join('|')

  rosterSaveAPI(subForm).then(res => {
    if (res.success) {
      ElMessage.success(form.rosterId ? '保存成功' : '新增成功');
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = () => {
  dialog.value.open();
}

watch(() => form.deptId ,(newValue) => {
  loadDeptPostList(newValue)
})

defineExpose({
  form,
  open
});
</script>

<style scoped lang="less">
</style>
