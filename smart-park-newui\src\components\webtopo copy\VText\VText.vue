<template>
  <div :class="state.equipClass">
    <el-input
      v-if="state.blur"
      v-model="curComponent.propValue"
      autosize
      type="textarea"
      @blur="inputFocus(false)"
      @keyup.delete.stop
      ref="inputELe"
    />
    <span v-else @dblclick="inputFocus(true)" class="preText">{{ type == 'exhibits' ?  state.text  + ' ' + (element.unit ||  '')  :  element.propValue}}</span>
  </div>
</template>

<script setup>
import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent } = storeToRefs(webtopo)

const props = defineProps({
  element:{
    type : Object,
    default: () => {
      return {}
    } 
  },
  type:{
    type:String,
    default: ""
  }
})

let {element, type} = toRefs(props)

let inputELe = ref()

const state = reactive({
  blur: false,
  text:'',
  equipClass: {}
})

const inputFocus = (bool) => {
  if(type.value != 'edit') return true
  state.blur = bool
  nextTick(() => {
    if(bool)
    inputELe.value.focus()
  })
}

// 设备样式设定
const classEquip = (info) => {
  if (type.value != 'exhibits') return false
  state.equipClass = {}
  // 隐藏 值回显
  let { classInfo } = element.value.params;
  (classInfo.classList || []).forEach(item => {
    ; if ((classInfo.checkList || []).includes(item.classType) && item.attribute) {
      var equipmentAttribute = info.equipmentAttributeList.find(i => i.attributeKey == item.attribute);
      let value;
      if(equipmentAttribute?.attributeValueEnum){
        value = JSON.parse(equipmentAttribute.attributeValueEnum)[equipmentAttribute?.attributeValue]
      }else{
        value = equipmentAttribute?.attributeValue
      }
      if (item.classType == 'topoTextShow') {
        state.text = value
      } else {
        state.equipClass[item.classType] = item.list.some(i => value >= i.min && value <= i.max)
      }
    }
  })

  if((classInfo.classList || []).map(item => item.classType).indexOf('topoTextShow') == -1){
    state.text = element.value.propValue
  }
}

// 设备信息
watch(() => element.value.params.equipInfo, (newVal) => {
  classEquip(newVal)
}, { immediate: true })
</script>

<style lang='less' scoped>
.preText{
  display: inline-block;
  width: 100%;
  word-wrap : break-word
}

.toposhowHide {
  display: none;
}
</style>