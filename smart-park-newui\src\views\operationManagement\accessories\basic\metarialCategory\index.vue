<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="nameOrCode">
          <el-input v-model="formInline.nameOrCode" placeholder="分类名称/分类编码"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新增分类</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" row-key="id" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="160">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <modalPage ref="modal" :title="state.title" :cateOptions="state.cateOptions" @submit="getList"></modalPage>
    </template>
  </page-common>
</template>

<script setup>
import {ElMessage, ElMessageBox} from 'element-plus'

import modalPage from './component/modalPage.vue'

import { getCategoryAPI, getCategoryTreeAPI , deleteCategoryAPI} from '@/api/operationManagement/metarialCategory.js'

import { treeDataTranslate } from '@/utils/index.js'

let formInlineRef = ref()
let modal = ref()

const formInline = reactive({})

const state = reactive({
  title: '',
  cateOptions: [],  //分类树形
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'classifyName',
      label: '分类名称'
    },
    {
      prop: 'classifyCode',
      label: '分类编码'
    },
    {
      prop: 'remark',
      label: '备注'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

// 获取分类
const getList = () => {
  let query = {
    ...formInline
  }
  getCategoryAPI(query).then(async res => {
    state.tableData = treeDataTranslate(res.data)
    state.tableData.forEach(item => item.parentId == 0 && delete item.parentId)

    // 获取分类树形
    let {data} = await getCategoryTreeAPI()
    state.cateOptions = data
  })
}

// 删除分类
const deleteHandle = ({id}) => {
  ElMessageBox.confirm(
      '是否删除当前分类?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    deleteCategoryAPI({id}).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 新增分类
const addHandle = () => {
  state.title = '新增分类'
  modal.value.open()
}

// 编辑分类
const editHandle = (info) => {
  state.title = '编辑分类'
  modal.value.open()
  nextTick(() => {
    Object.assign(modal.value.form, { ...info })
  })
}
</script>

<style lang='less' scoped></style>
