package com.soft.webadmin.dto.visitor;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class VisitorPassRecordQueryDTO extends MyPageParam {

    @ApiModelProperty("访客姓名/车牌号")
    private String queryWord;

    @ApiModelProperty("到访状态：1未到访；2已到访；")
    private Integer visitStatus;

    @ApiModelProperty("预计到访日期(开始)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planVisitDateStart;

    @ApiModelProperty("预计到访日期(结尾)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planVisitDateEnd;

    @ApiModelProperty("实际到访日期(开始)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date realVisitDateStart;

    @ApiModelProperty("实际到访日期(结尾)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date realVisitDateEnd;

    private String enterType;

}
