package com.soft.webadmin.dto.contingency;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * EmergencyNodeDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EmergencyNodeDTO对象")
@Data
public class EmergencyNodeDTO {

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "节点任务")
    @NotEmpty(message = "节点任务不能为空！")
    @Valid
    private List<EmergencyTaskDTO> taskDTOList;

}
