<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef" :width="900">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":">
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">基本信息</div>
        </div>
        <el-row>
          <el-col>
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="form.templateName" placeholder="请输入模板名称" clearable />
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="模板类型" prop="templateType">
              <el-select v-model="form.templateType" placeholder="请选择模型类型">
                <el-option v-for="item in state.templateTypeOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="备注" prop="remark">
              <el-input type="textarea" v-model="form.remark" placeholder="请输入备注信息" maxlength="100"
                :show-word-limit="true" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div style="margin-top: 15px">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">检查项目</div>
          <div style="float: right">
            <el-button link icon="Plus" type="primary" @click="addHandle()">新增项目</el-button>
          </div>
        </div>
        <el-table :data="form.checkTemplateItemList">
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column v-for="(column, index) in state.columnList" :label="column.label" :prop="column.prop"
            :key="index">
            <template #default="scope">
              <div v-show="scope.$index !== editIndex">
                <span v-if="column.prop === 'itemType'">
                  {{
                    scope.row[column.prop] === 1 ? '选项' : scope.row[column.prop] === 2 ? '数值' : scope.row[column.prop] ===
                      3 ? '选项数值' : ''
                  }}
                </span>
                <span v-else>{{ scope.row[column.prop] }}</span>
              </div>
              <el-form-item v-show="scope.$index === editIndex"
                :prop="`checkTemplateItemList.${scope.$index}.${column.prop}`" :rules="{
                  required: true,
                  message: `${column.label}不能为空`,
                  trigger: 'blur',
                }" label-width="0">

                <el-select v-model="scope.row[column.prop]" clearable placeholder="请选择类型"
                  v-if="column.prop === 'itemType'">
                  <el-option v-for="item in [
                    {
                      label: '选项',
                      value: 1
                    },
                    {
                      label: '选项数值',
                      value: 3
                    },
                  ]" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>

                <div v-else>
                  <el-input v-model="scope.row[column.prop]" :placeholder="'请输入' + column.label" />
                </div>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180">
            <template #default="scope">
              <div>
                <el-button v-show="scope.$index !== editIndex" link icon="Edit" type="primary"
                  @click="editHandle(scope.row)">编辑
                </el-button>
                <el-button v-show="scope.$index !== editIndex" link icon="Delete" type="danger"
                  @click="deleteHandle(scope.row)">删除
                </el-button>
                <el-button v-show="scope.$index === editIndex" link icon="Document" type="success"
                  @click="saveHandle(scope.row)" style="margin: 0">保存
                </el-button>
                <el-button v-show="scope.$index === editIndex" link icon="Delete" type="danger"
                  @click="closeHandle(scope.row)">删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { saveTemplateAPI } from '@/api/operationManagement/template.js';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
const { title } = toRefs(props);
const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({
  checkTemplateItemList: [],
});
const state = reactive({
  columnList: [
    { prop: 'itemName', label: '检查项' },
    { prop: 'itemContent', label: '检查内容' },
    { prop: 'itemType', label: '类型' }
  ],
  rules: {
    templateName: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
    templateType: [{ required: true, message: '模板类型不能为空', trigger: 'blur' }]
  },
  // 1巡检-设备；2巡检-空间；3维保-设备
  templateTypeOptions: [
    {
      label: '巡检-设备',
      value: '1'
    },
    {
      label: '巡检-空间',
      value: '2'
    },
    {
      label: '维保-设备',
      value: '3'
    }
  ]
});
const editIndex = ref(-1);

/** 检查项目编辑 */
const editHandle = (row) => {
  editIndex.value = form.checkTemplateItemList.indexOf(row);
};

/** 检查项目删除 */
const deleteHandle = (row) => {
  let index = form.checkTemplateItemList.indexOf(row);
  form.checkTemplateItemList.splice(index, 1);
};

/** 检查项目保存 */
const saveHandle = async (row) => {
  let itemNameValid = true;
  await ruleFormRef.value.validateField(`checkTemplateItemList.${editIndex.value}.itemName`, (valid) => {
    itemNameValid = valid;
  });
  let itemContentValid = true;
  await ruleFormRef.value.validateField(`checkTemplateItemList.${editIndex.value}.itemContent`, (valid) => {
    itemContentValid = valid;
  });
  let itemTypeValid = true
  await ruleFormRef.value.validateField(`checkTemplateItemList.${editIndex.value}.itemType`, (valid) => {
    itemTypeValid = valid;
  });
  if (itemNameValid && itemContentValid && itemTypeValid) {
    editIndex.value = -1;
  }
};

/** 检查项目取消保存 */
const closeHandle = (row) => {
  editIndex.value = -1;
  let index = form.checkTemplateItemList.length - 1;
  form.checkTemplateItemList.splice(index, 1);
};

/** 检查项目新增 */
const addHandle = async () => {
  let itemNameValid = true;
  await ruleFormRef.value.validateField(`checkTemplateItemList.${editIndex.value}.itemName`, (valid) => {
    itemNameValid = valid;
  });
  let itemContentValid = true;
  await ruleFormRef.value.validateField(`checkTemplateItemList.${editIndex.value}.itemContent`, (valid) => {
    itemContentValid = valid;
  });
  let itemTypeValid = true
  await ruleFormRef.value.validateField(`checkTemplateItemList.${editIndex.value}.itemType`, (valid) => {
    itemTypeValid = valid;
  });
  if (itemNameValid && itemContentValid && itemTypeValid) {
    const data = { itemName: '', itemContent: '', itemType: '' };
    form.checkTemplateItemList.push(data);
    editIndex.value = form.checkTemplateItemList.length - 1;
  }
};

const open = () => {
  dialog.value.open();
  editIndex.value = -1;
};

/** 提交表单 */
const submit = () => {
  saveTemplateAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success(title.value + '成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

defineExpose({
  form,
  open,
});
</script>

<style lang="less" scoped>
</style>
