package com.soft.webadmin.dto.hiddenDanger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * HiddenDangerRectifyDTO对象
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@ApiModel("HiddenDangerRectifyDTO对象")
@Data
public class HiddenDangerAppointDTO {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空！")
    private Long id;

    @ApiModelProperty(value = "处理人id")
    @NotNull(message = "处理人不能为空！")
    private Long handleUserId;

}
