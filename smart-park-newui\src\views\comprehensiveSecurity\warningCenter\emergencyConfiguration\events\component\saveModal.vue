<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" @onClose="onClose" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="110px" :model="form" :rules="state.rules" label-suffix=":">
      <el-form-item label="事件名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入事件名称"/>
      </el-form-item>
      <el-form-item label="事件等级" prop="level">
        <el-select v-model="form.level" filterable clearable placeholder="请选择事件等级">
          <el-option v-for="(value,key) in props.levelOptions" :label="value" :value="Number(key)"/>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit
                  placeholder="请输入备注"/>
      </el-form-item>
      <el-form-item label="事件图标" prop="fileList">
        <el-upload :action="state.action" :headers="state.headers" v-model:file-list="form.fileList"
                   :on-success="fileSuccess" list-type="picture-card" :on-preview="handlePictureCardPreview"
                   :class="{'disUpload': form.fileList.length == 1 }" accept="image/*">
          <el-icon>
            <Plus/>
          </el-icon>
        </el-upload>
      </el-form-item>
    </el-form>
  </dialog-common>
  <el-dialog v-model="state.dialogVisible" class="imgDialog">
    <img w-full :src="state.dialogImageUrl" alt="Preview Image" style="width: 100%;"/>
  </el-dialog>
</template>

<script setup>
import {ElMessage} from 'element-plus'

import {eventsSaveAPI} from '@/api/comprehensiveSecurity/events.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  levelOptions: {
    type: Object,
    default: () => {
    }
  }
})

const emit = defineEmits(['submit'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({
  fileList: []
})

const state = reactive({
  dialogVisible: false,
  dialogImageUrl: '',
  rules: {
    name: [{required: true, message: '请输入事件名称', trigger: 'blur'},],
    level: [{required: true, message: '请选择事件等级', trigger: 'blur'},]
  },
  action: import.meta.env.VITE_BASE_URL + '/core/file/upload',
  headers: {
    Authorization: localStorage.getItem('Authorization')
  },
})

const open = () => {
  dialog.value.open()
}

// 上传图片
const fileSuccess = (response, file) => {
  file.imgUrl = response.data.filePath
}

// 查看图片
const handlePictureCardPreview = (uploadFile) => {
  state.dialogImageUrl =  import.meta.env.VITE_BASE_URL + uploadFile.imgUrl
  state.dialogVisible = true
}

const handleRemove = (file) => {
  form.fileList.splice(form.fileList.indexOf(file), 1)
}

// 关闭
const onClose = () => {
  form.id = ''
  form.img = ''
}

// 提交
const submit = () => {
  let subForm = JSON.parse(JSON.stringify(form))
  subForm.img = subForm.fileList[0]?.imgUrl

  eventsSaveAPI(subForm).then((res) => {
    if (res.success) {
      ElMessage.success('保存成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}


defineExpose({
  form,
  open
})
</script>

<style lang="less" scoped>
</style>
