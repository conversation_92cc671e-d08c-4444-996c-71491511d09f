<template>
  <dialog-common
      ref="dialog"
      :formRef="ruleFormRef"
      :width="500"
      class="dialogTextarea"
      title="报废"
      @onClose="onClose"
      @submit="submit"
  >
    <el-form
        ref="ruleFormRef"
        :model="form"
        :rules="state.rules"
        label-suffix=":"
        label-width="120px"
    >

      <el-form-item label="实际报废日期" prop="scrapDate">
        <el-date-picker
            v-model="form.scrapDate"
            placeholder="请选择实际报废日期"
            type="date"
            value-format="YYYY-MM-DD"
        />
      </el-form-item>

      <el-form-item label="报废设施" prop="equipmentIds">
        <el-select
            v-model="form.equipmentIds"
            :disabled="true"
            multiple
            placeholder=""
        >
          <el-option
              v-for="item in props.equipmentNameList"
              :key="item.equipmentId"
              :label="item.equipmentName"
              :value="item.equipmentId"
          />
        </el-select>
      </el-form-item>


      <el-form-item label="报废原因" prop="scrapReason">
        <el-input
            v-model="form.scrapReason"
            :autosize="{ minRows: 5 }"
            :maxlength="500"
            placeholder="请输入报废原因"
            show-word-limit
            type="textarea"
            style="width: 85%"
        />
      </el-form-item>


    </el-form>
  </dialog-common>
</template>

<script setup>
import {equipScrapAPI} from "@/api/operationManagement/equipManage.js";
import {ElMessage, ElMessageBox} from "element-plus";

const props = defineProps({
  equipmentNameList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["submit"]);

const dialog = ref();
const ruleFormRef = ref();
const form = reactive({scrapReason: '到期报废'});

const state = reactive({
  rules: {
    scrapDate: [{required: true, message: "实际报废日期不能为空", trigger: "blur"}],
    scrapReason: [{required: true, message: "报废原因不能为空", trigger: "blur"}],
  },
});

const open = () => {
  dialog.value.open();
};

const onClose = () => {
  form.equipmentIds = []
}

/** 保存 */
const submit = () => {
  ElMessageBox.confirm("您确定报废吗?", "提醒", {
    type: "warning",
  }).then(() => {
    equipScrapAPI(form).then((res) => {
      if (res.success) {
        ElMessage.success("报废成功");
        dialog.value.close()
        emit("submit");
      }else {
        ElMessage.error(res.errorMessage)
      }
    });
  });
};

defineExpose({
  form,
  open,
});
</script>

<style lang="less" scoped>
</style>
