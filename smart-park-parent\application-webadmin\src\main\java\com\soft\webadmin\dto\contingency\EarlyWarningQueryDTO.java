package com.soft.webadmin.dto.contingency;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * EarlyWarningDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EarlyWarningDTO对象")
@Data
public class EarlyWarningQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "事件编号")
    private String no;

    @ApiModelProperty(value = "应急事件id")
    private Long eventId;

    @ApiModelProperty(value = "应急事件等级：1普通、2重要、3严重")
    private Integer eventLevel;

    @ApiModelProperty(value = "类别：1真实事件、2应急演练")
    private Integer type;

    @ApiModelProperty(value = "状态：0待处理、1误报、2已解决")
    private List<Integer> status;

    @ApiModelProperty(value = "位置id")
    private Long spaceId;

    @ApiModelProperty(value = "上报人")
    private String reportUserName;

    @ApiModelProperty(value = "上报开始时间")
    private String beginDate;

    @ApiModelProperty(value = "上报结束时间")
    private String endDate;

    @ApiModelProperty(value = "处理开始时间")
    private String handleBeginDate;

    @ApiModelProperty(value = "处理结束时间")
    private String handleEndDate;

}
