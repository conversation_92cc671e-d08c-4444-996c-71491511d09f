<template>
  <div style="height: 100%;">
    <el-tabs style="height: 55px;" v-model="state.activeName" @tab-click="handleClick">
      <el-tab-pane label="水表" name="first"></el-tab-pane>
      <el-tab-pane label="电表" name="second"></el-tab-pane>
      <el-tab-pane label="气表" name="third"></el-tab-pane>
    </el-tabs>
    <div style="height: calc(100% - 55px);">
      <page-common v-model="state.tableHeight">
        <template #query>
          <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
            <el-form-item prop="queryName">
              <el-input v-model="formInline.queryName" placeholder="设备名称或编号" />
            </el-form-item>
            <el-form-item prop="spaceIdList">
              <el-cascader v-model="formInline.spaceIdList" :options="state.treeData" :props="defaultProps" clearable
                placeholder="安装位置" />
            </el-form-item>
            <el-form-item prop="energyTypeId">
              <el-cascader v-model="formInline.energyTypeId" :options="state.typeData" :props="typeProps" clearable
                placeholder="设备分类" />
            </el-form-item>
            <el-form-item prop="recordDate">
              <el-date-picker v-model="formInline.recordDate" type="month" placeholder="年-月-日" value-format="YYYY-MM-DD"
                :clearable="false" :editable="false" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
              <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </template>
        <template #table>
          <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
            <el-table-column fixed v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
              :label="item.label" :width="item.width" />
            <el-table-column v-for="(item, index) in state.days" :key="index" :label="index + 1 + '日'">
              <template #default="scope">
                {{ scope.row.consumptionList[index] }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" prop="sumconsumption" label="合计" />
          </el-table>
          <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
            :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
            @size-change="sizeChange" @current-change="currentChange" />
        </template>
      </page-common>
    </div>
  </div>
</template>

<script setup>
import { energyStatisticsDay } from '@/api/energyManagement/energyStatistics.js';
import { treeAPI } from "@/api/iotManagement/space.js";
import { getSubitemTreeAPI } from '@/api/energyManagement/subitem.js';
import dayjs from "dayjs";

let formInlineRef = ref()
const formInline = reactive({ equipmentType: '电表' })
const defaultProps = {
  checkStrictly: true,
  children: 'children',
  label: 'name',
  value: 'id',
  emitPath: false
}

const typeProps = {
  checkStrictly: true,
  children: 'children',
  label: 'energyName',
  value: 'id',
  emitPath: false
}

const state = reactive({
  typeData: [],
  activeName: 'second',
  days: 0,
  treeData: [],
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'equipmentNo',
      label: '设备编号',
      width: 150
    },
    {
      prop: 'equipmentName',
      label: '设备名称',
      width: 150
    },
    {
      prop: 'spaceFullName',
      label: '安装位置',
      width: 150
    },
    {
      prop: 'energyType',
      label: '设备分类',
      width: 150
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  formInline.recordDate = dayjs().format("YYYY-MM-DD");
  getList()
  getTree()
  selectType();
})

const getTree = () => {
  let query = {
    deep: 4
  }
  treeAPI(query).then(res => {
    state.treeData = res.data
  })
}

const selectType = () => {
  getSubitemTreeAPI({ equipmentType: formInline.equipmentType }).then((res) => {
    state.typeData = res.data;
  });
}

const handleClick = (TabsPaneContext) => {
  formInline.equipmentType = TabsPaneContext.props.label
  formInline.queryName = ''
  formInline.spaceIdList = ''
  formInline.energyTypeId = ''
  state.pagetion.pageNum = 1
  state.pagetion.pageSize = 10

  selectType()
  getList();
}

//分页
const getList = () => {
  let query = {
    ...formInline,
    ...state.pagetion,
    recordYear: formInline.recordDate.split("-")[0],
    recordMonth: formInline.recordDate.split("-")[1]
  }

  energyStatisticsDay(query).then(res => {
    if (res.data.dataList.length > 0) {
      state.days = res.data.dataList[0].consumptionList.length
    }
    state.pagetion.total = res.data.totalCount
    state.tableData = res.data.dataList
  })
}

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

//重置方法
const onReset = () => {
  formInlineRef.value.resetFields()
  formInline.recordDate = dayjs().format("YYYY-MM-DD");
  onSubmit()
}

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped>
.el-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);

    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
