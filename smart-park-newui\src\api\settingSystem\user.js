import { request } from '@/utils/request';

// 分页查询
export const getPageAPI = (data) => {
  return request('post', '/admin/upms/sysUser/list', data);
};


// 查询用户列表
export const listUsersAPI = (data) => {
  return request('get', '/system/user/list', data, 'F');
}

// 修改用户状态
export const updateUserStatusAPI = (id) => {
  return request('get', '/system/user/updateStatus/' + id, {}, 'F');
}


// 创建用户
export const addUserAPI = (data) => {
  return request('post', '/admin/upms/sysUser/add', data);
};

// 查询角色
export const getRoleListAPI = (data) => {
  return request('post', '/admin/upms/sysRole/list', data);
};

// 查询部门
export const getDeptListAPI = (data) => {
  return request('post', '/admin/upms/sysDept/list', data);
};

// 查询岗位
export const getDeptPostListAPI = (query) => {
  return request('get', '/admin/upms/sysDept/listSysDeptPostWithRelation', query, 'F');
};

// 查询数据权限
export const getDataPermListAPI = (data) => {
  return request('post', '/admin/upms/sysDataPerm/list', data);
};

// 查询项目
export const getProjectListAPI = (data) => {
  return request('get', '/core/project/list', data, 'F');
};

// 查询用户
export const getUserInfoAPI = (query) => {
  return request('get', '/admin/upms/sysUser/view', query, 'F');
};

// 更新用户
export const updateUserAPI = (data) => {
  return request('post', '/admin/upms/sysUser/update', data);
};

// 删除用户
export const deleteUserAPI = (data) => {
  return request('post', '/admin/upms/sysUser/delete', data);
};

// 重置密码
export const resetPasswordAPI = (data) => {
  return request('post', '/admin/upms/sysUser/resetPassword', data);
};


// 用户详情
export const detailUserAPI = (userId) => {
  return request('get', '/system/user/detail/' + userId, '{}', 'F');
}

// 新增或修改
export const saveOrUpdateUserAPI = (data) => {
  return request('post', '/system/user/saveOrUpdate', data)
}
