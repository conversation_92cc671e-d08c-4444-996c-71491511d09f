<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.sparePart.SparePartInoutMapper">
    <resultMap type="com.soft.webadmin.model.sparePart.SparePartInout" id="SparePartInoutResult">
        <result property="id" column="id" />
        <result property="businessType" column="business_type" />
        <result property="operateType" column="operate_type" />
        <result property="inoutNo" column="inout_no" />
        <result property="inoutDate" column="inout_date" />
        <result property="type" column="type" />
        <result property="quantity" column="quantity" />
        <result property="examineUserId" column="examine_user_id" />
        <result property="examineTime" column="examine_time" />
        <result property="examineState" column="examine_state" />
        <result property="workOrderNo" column="work_order_no" />
        <result property="outNo" column="out_no" />
        <result property="stocktakingNo" column="stocktaking_no" />
        <result property="remark" column="remark" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectSparePartInoutVo">
        t.id, t.business_type, t.operate_type, t.inout_no, t.inout_date, t.type, t.quantity, t.examine_user_id, t.examine_time, t.examine_state, t.work_order_no, t.out_no, t.stocktaking_no, t.remark, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="queryList" resultType="com.soft.webadmin.vo.sparePart.SparePartInoutVO" parameterType="com.soft.webadmin.dto.sparePart.SparePartInoutQueryDTO">
        select id, business_type, operate_type, inout_no, inout_date, type, quantity, examine_user_id, examine_time,
        examine_state, work_order_no, out_no, stocktaking_no, remark, deleted_flag, create_user_id, create_time,
        update_user_id, update_time, spare_part_names, apply_user_name, create_user_name
        from (select <include refid="selectSparePartInoutVo"/>,
        (select GROUP_CONCAT(spare_part_name separator '、') from sp_spare_part_info where id in
        (select spare_part_id from sp_spare_part_quantity_change where inout_id = t.id)) spare_part_names,
        (select show_name from common_sys_user where user_id = t.apply_user_id) apply_user_name,
        (select show_name from common_sys_user where user_id = t.create_user_id) create_user_name
        from sp_spare_part_inout t) j
        <where>
            <if test="operateType != null">
                and operate_type = #{operateType}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="inoutNo != null and inoutNo != ''">
                and inout_no like concat('%', #{inoutNo}, '%')
            </if>
            <if test="beginDate != null and beginDate != ''">
                and date_format(create_time, '%Y-%m-%d') &gt;= date_format(#{beginDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and date_format(create_time, '%Y-%m-%d') &lt;= date_format(#{endDate}, '%Y-%m-%d')
            </if>
            <if test="createUserName != null and createUserName != ''">
                and create_user_name like concat('%', #{createUserName}, '%')
            </if>
            <if test="sparePartName != null and sparePartName != ''">
                and spare_part_names like concat('%', #{sparePartName}, '%')
            </if>
            <if test="stocktakingNo != null and stocktakingNo != ''">
                and stocktaking_no like concat('%', #{stocktakingNo}, '%')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="detail" resultType="com.soft.webadmin.vo.sparePart.SparePartInoutVO">
        select <include refid="selectSparePartInoutVo"/>,
        (select show_name from common_sys_user where user_id = t.apply_user_id) apply_user_name,
        (select show_name from common_sys_user where user_id = t.create_user_id) create_user_name
        from sp_spare_part_inout t where id = #{id}
    </select>

</mapper>