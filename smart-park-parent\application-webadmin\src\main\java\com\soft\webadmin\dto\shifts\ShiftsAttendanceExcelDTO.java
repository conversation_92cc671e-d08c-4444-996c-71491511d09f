package com.soft.webadmin.dto.shifts;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * SparePartInoutDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@ApiModel("SparePartInoutDTO对象")
@Data
@ColumnWidth(value = 15)
public class ShiftsAttendanceExcelDTO {

    @NotNull(message = "姓名不能为空！")
    @ExcelProperty(value = "姓名", index = 0)
    private String showName;

    @NotNull(message = "部门不能为空！")
    @ExcelProperty(value = "部门", index = 1)
    private String deptName;

    @NotNull(message = "班次不能为空！")
    @ExcelProperty(value = "班次", index = 2)
    private String shiftsName;

    @NotNull(message = "出勤日期不能为空 格式2024-04-13")
    //@Pattern(regexp = ExcelPatternMsg.DATE2, message = ExcelPatternMsg.DATE2_MSG)
    @ExcelProperty(value = "出勤日期", index = 3)
    private String attendanceDate;

    @ExcelProperty(value = "上班打卡时间  格式 2024-04-13 12:00:00", index = 4)
    //@Pattern(regexp = ExcelPatternMsg.DATE_TIME1, message = ExcelPatternMsg.DATE_TIME1_MSG)
    private String onWorkTime;

    @ExcelProperty(value = "上班打卡结果", index = 5)
    @NotNull(message = "上班打卡结果不能为空")
    private String onWorkResult;

    @ExcelProperty(value = "下班打卡时间 格式 2024-04-13 12:00:00", index = 6)
    //@Pattern(regexp = ExcelPatternMsg.DATE_TIME1, message = ExcelPatternMsg.DATE_TIME1_MSG)
    private String offWorkTime;

    @NotNull(message = "下班打卡结果不能为空")
    @ExcelProperty(value = "下班打卡结果", index = 7)
    private String offWorkResult;

    @ExcelProperty(value = "打卡设备", index = 8)
    private String clockEquipmentName;

    @ExcelProperty(value = "打卡地点", index = 9)
    private String clockSpace;

    @ApiModelProperty(value = "迟到时长(秒)")
    @ExcelIgnore
    private Integer delayDuration;

    @ExcelIgnore
    @ApiModelProperty(value = "早退时长(秒)")
    private Integer earlyDepartureDuration;



    @ExcelIgnore
    private Long rosterId;

}
