package com.soft.webadmin.model.contingency;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import com.soft.webadmin.vo.contingency.GroupVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 应急小组对象 cm_group
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cm_group")
public class Group extends BaseModel {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 事件id */
    private Long eventId;

    /** 小组名称 */
    private String name;

    /** 职责 */
    private String duty;

    /** 删除标记(1: 正常 -1: 已删除) */
    @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;


    @Mapper
    public interface GroupModelMapper extends BaseModelMapper<GroupVO, Group> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        Group toModel(GroupVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        GroupVO fromModel(Group entity);
    }

    public static final GroupModelMapper INSTANCE = Mappers.getMapper(GroupModelMapper.class);
}
