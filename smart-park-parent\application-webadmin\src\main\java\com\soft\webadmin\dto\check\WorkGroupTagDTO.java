package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * SpWorkGroupTagDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@ApiModel("WorkGroupTagDTO对象")
@Data
public class WorkGroupTagDTO {

    @ApiModelProperty(value = "班组 id")
    private Long workGroupId;

    @ApiModelProperty(value = "用户标签 id")
    private Long tagId;

}
