<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.WorkGroupTagMapper">
    <resultMap type="com.soft.webadmin.model.check.WorkGroupTag" id="SpWorkGroupTagResult">
        <result property="workGroupId" column="work_group_id" />
        <result property="tagId" column="tag_id" />
    </resultMap>

    <sql id="selectSpWorkGroupTagVo">
        select work_group_id, tag_id from sp_work_group_tag
    </sql>
    <insert id="insertBatch">
        insert into sp_work_group_tag
        values
            <foreach collection="list" item="item" separator=",">
                (#{item.workGroupId}, #{item.tagId})
            </foreach>
    </insert>

</mapper>