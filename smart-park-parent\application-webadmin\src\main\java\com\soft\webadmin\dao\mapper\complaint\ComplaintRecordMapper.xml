<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.complaint.ComplaintRecordMapper">
    <resultMap type="com.soft.webadmin.model.complaint.ComplaintRecord" id="ComplaintRecordResult">
        <result property="id" column="id" />
        <result property="code" column="code" />
        <result property="title" column="title" />
        <result property="type" column="type" />
        <result property="content" column="content" />
        <result property="reportUserId" column="report_user_id" />
        <result property="reportUserName" column="report_user_name" />
        <result property="reportUserPhone" column="report_user_phone" />
        <result property="annexIds" column="annex_ids" />
        <result property="handleStatus" column="handle_status" />
        <result property="handleUserId" column="handle_user_id" />
        <result property="handleUserName" column="handle_user_name" />
        <result property="handleTime" column="handle_time" />
        <result property="handleResult" column="handle_result" />
        <result property="createTime" column="create_time" />
        <result property="createUserId" column="create_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="updateUserId" column="update_user_id" />
    </resultMap>

    <sql id="selectComplaintRecordVo">
        select id, code, title, type, content, report_user_id, report_user_name, report_user_phone, annex_ids, handle_status, handle_user_id, handle_user_name, handle_time, handle_result, create_time, create_user_id, update_time, update_user_id from complaint_record
    </sql>

</mapper>