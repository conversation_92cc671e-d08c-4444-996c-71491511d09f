<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.shifts.ShiftsRosterLiabilityMapper">
    <resultMap type="com.soft.webadmin.model.shifts.ShiftsRosterLiability" id="ShiftsRosterLiabilityResult">
        <result property="rosterId" column="roster_id" />
        <result property="spaceId" column="space_id" />
    </resultMap>

    <sql id="selectShiftsRosterLiabilityVo">
        select roster_id, space_id from sp_shifts_roster_liability
    </sql>
    
</mapper>