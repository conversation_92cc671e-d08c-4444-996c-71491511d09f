<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.CheckTemplateMapper">
    <resultMap type="com.soft.webadmin.model.check.CheckTemplate" id="SpCheckTemplateResult">
        <result property="id" column="id" />
        <result property="templateName" column="template_name" />
        <result property="templateType" column="template_type" />
        <result property="remark" column="remark" />
        <result property="createTime" column="create_time" />
        <result property="createUserId" column="create_user_id" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="deleteFlag" column="delete_flag" />
    </resultMap>

    <sql id="selectSpCheckTemplateVo">
        select id, template_name, template_type, remark, create_time, create_user_id, update_user_id, update_time, delete_flag from sp_check_template
    </sql>

</mapper>