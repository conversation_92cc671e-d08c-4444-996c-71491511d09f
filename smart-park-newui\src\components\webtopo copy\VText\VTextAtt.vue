<template>
  <div class="VTextAtt">
    <common-att></common-att>
    <el-form label-width="75px" label-suffix=":">
      <el-form-item label="单位">
        <el-input v-model="curComponent.unit"  />
      </el-form-item>
    </el-form>
    <el-form label-position="top" label-suffix=":">
      <el-form-item label="文本内容">
        <el-input v-model="curComponent.propValue" :rows="4" type="textarea"  />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent } = storeToRefs(webtopo)
</script>

<style lang='less' scoped></style>