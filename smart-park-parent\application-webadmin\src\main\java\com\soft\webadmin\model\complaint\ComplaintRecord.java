package com.soft.webadmin.model.complaint;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import com.soft.webadmin.vo.complaint.ComplaintRecordVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("complaint_record")
public class ComplaintRecord extends BaseModel {

    @TableId
    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 标题
     */
    private String title;

    /**
     * 投诉类型：1投诉；2建议；
     */
    private Integer type;

    /**
     * 投诉内容
     */
    private String content;

    /**
     * 上报人 id
     */
    private Long reportUserId;

    /**
     * 上报人姓名
     */
    private String reportUserName;

    /**
     * 联系方式
     */
    private String reportUserPhone;

    /**
     * 附件列表
     */
    private String annexIds;

    /**
     * 处理状态：1待查看；2进行中；3已处理；
     */
    private Integer handleStatus;

    /**
     * 处理人id
     */
    private Long handleUserId;

    /**
     * 处理人姓名
     */
    private String handleUserName;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 处理结果
     */
    private String handleResult;


    @Mapper
    public interface ComplaintRecordModelMapper extends BaseModelMapper<ComplaintRecordVO, ComplaintRecord> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        ComplaintRecord toModel(ComplaintRecordVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        ComplaintRecordVO fromModel(ComplaintRecord entity);
    }

    public static final ComplaintRecordModelMapper INSTANCE = Mappers.getMapper(ComplaintRecordModelMapper.class);

}
