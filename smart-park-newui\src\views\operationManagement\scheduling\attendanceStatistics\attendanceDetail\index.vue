<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="showName">
          <el-input v-model="formInline.showName" placeholder="姓名"/>
        </el-form-item>
        <el-form-item prop="deptId">
          <el-cascader
              v-model="formInline.deptId"
              :clearable="true"
              :loading="state.deptInfo.impl.loading"
              :options="state.deptList"
              :props="{
              value: 'deptId',
              label: 'deptName',
              checkStrictly: true,
              emitPath: false
            }"
              placeholder="部门"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item prop="workResult">
          <el-input v-model="formInline.workResult" placeholder="打卡结果"/>
        </el-form-item>
        <el-form-item prop="cycle">
          <el-date-picker
              v-model="formInline.cycle"
              type="month"
              value-format="YYYY-MM"
              placeholder="出勤周期"
              :clearable="false"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Upload" @click="handleUpload">导入</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" row-key="id" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
      </el-table>
      <el-pagination
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="state.pagetion.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
      <modal-upload ref="upload" :cycle="formInline.cycle" @submit="getList"></modal-upload>
    </template>
  </page-common>
</template>

<script setup>
import dayjs from "dayjs";

import { getDeptListAPI } from '@/api/settingSystem/user.js';
import { attendanceDetailPageAPI } from '@/api/operationManagement/attendanceDetail.js'

import {DropdownWidget} from "@/utils/widget.js";
import ModalUpload from "./component/modalUpload.vue";

/** 查询部门list */
const loadDeptDropdownList = () => {
  return new Promise((resolve, reject) => {
    getDeptListAPI({})
        .then((res) => {
          resolve(res.data.dataList);
        })
        .catch((e) => {
          reject(e);
        });
  });
}


let formInlineRef = ref()
let upload = ref()


const formInline = reactive({
  cycle:dayjs().format('YYYY-MM'),
})

const state = reactive({
  deptList: [],
  deptInfo: {
    impl: new DropdownWidget(loadDeptDropdownList, true, 'deptId'),
    value: [],
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'showName',
      label: '姓名',

    },
    {
      prop: 'deptName',
      label: '部门'
    },
    {
      prop: 'postNames',
      label: '岗位'
    },
    {
      prop: 'attendanceDate',
      label: '日期',

    },
    {
      prop: '',
      label: '班次',
      formatter: (row, column, cellValue) => {
        return row.startTime + '-' + row.endTime
      }
    },
    {
      prop: 'onWorkTime',
      label: '上班打卡时间',
      width: 160
    },
    {
      prop: 'onWorkResult',
      label: '上班打卡结果'
    },
    {
      prop: 'offWorkTime',
      label: '下班打卡时间',
      width: 160
    },
    {
      prop: 'offWorkResult',
      label: '下班打卡结果'
    },
    {
      prop: 'clockEquipmentName',
      label: '打卡设备'
    },
    {
      prop: 'clockSpace',
      label: '打卡地点'
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()

  state.deptInfo.impl.onVisibleChange(true).then((res) => {
    state.deptList = res;
  });
})

// 获取记录
const getList = () => {
  let query = {
    ...formInline ,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
    businessType: 'OPERATIONS'
  }
  attendanceDetailPageAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 导入
const handleUpload = () => {
  upload.value.open()
}
</script>

<style lang='less' scoped></style>
