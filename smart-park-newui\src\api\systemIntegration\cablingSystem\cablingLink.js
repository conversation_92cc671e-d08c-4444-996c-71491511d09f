import { request } from "@/utils/request";


/**
 * 查询线路连接列表数据
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export const listCabLinkAPI = (data) => {
  return request('get', '/cablingSystem/link/list', data, 'F')
}


/**
 * 新增或修改线路连接
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export const saveOrUpdateCabLinkAPI = (data) => {
  return request('post', '/cablingSystem/link/saveOrUpdate', data)
}

/**
 * 删除线路连接
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export const deleteCabLinkAPI = (data) => {
  return request('post', '/cablingSystem/link/delete', data, 'F')
}
