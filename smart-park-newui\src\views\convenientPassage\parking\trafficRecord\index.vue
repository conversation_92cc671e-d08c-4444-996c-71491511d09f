<template>
  <tree-page-common v-model="state.tableHeight" :leftBool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
        <el-form-item prop="carNumber">
          <el-input v-model="formInline.carNumber" placeholder="车牌号" />
        </el-form-item>
        <el-form-item prop="carType">
          <el-input v-model="formInline.carType" placeholder="车辆类型" />
        </el-form-item>
        <el-form-item prop="time">
          <el-date-picker v-model="formInline.time" type="datetimerange" range-separator="到" start-placeholder="进场开始时间"
            end-placeholder="进场结束时间" format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" />
        </el-form-item>
        <el-form-item prop="time1">
          <el-date-picker v-model="formInline.time1" type="datetimerange" range-separator="到" start-placeholder="出场开始时间"
            end-placeholder="出场结束时间" format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" :width="item.witdh">
        </el-table-column>
        <el-table-column label="抓拍照片">
          <template #default="scope">
            <img @click="preview(scope.row.inCarPhoto)" :src="scope.row.inCarPhoto" style="width: 30px;height: 30px;margin-right: 5px;" alt="">
            <img @click="preview(scope.row.inCarPhoto)" :src="scope.row.outCarPhoto" style="width: 30px;height: 30px;" alt="">
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />

      <el-dialog v-model="dialogVisible" title="预览">
        <img w-full :src="imgUrl" alt="抓拍照片" style="width: 100%;" />
      </el-dialog>
    </template>
  </tree-page-common>
</template>

<script setup>
import {
  Search, Refresh
} from '@element-plus/icons-vue'
import { pageList } from "@/api/iotManagement/parking.js";

import dayjs from "dayjs";

const imgUrl = ref('')
const dialogVisible = ref(false)
let formInlineRef = ref()

const formInline = reactive({})

const state = reactive({
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'carNumber',
      label: '车牌号'
    },
    {
      prop: 'carColor',
      label: '车辆颜色'
    },
    {
      prop: 'carType',
      label: '车辆类型'
    },
    {
      prop: 'inTime',
      label: '进场时间',
      width: 160,
      formatter: (row, column, cellValue) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm') : ''
      }
    },
    {
      prop: 'inEquipName',
      label: '进场设备'
    },
    {
      prop: 'outTime',
      label: '出场时间',
      width: 160,
      formatter: (row, column, cellValue) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm') : ''
      }
    },
    {
      prop: 'outEquipName',
      label: '出场设备'
    },
    {
      prop: 'diffTime',
      label: '停车时长（分钟）'
    },
    {
      prop: 'ssMoney',
      label: '缴费金额（元）'
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

const preview = (url) => {
  imgUrl.value = url
  dialogVisible.value = true
}

//分页
const getList = () => {
  let query = {
    equipmentName: formInline.equipmentName,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize
  }
  if (formInline.time) {
    query.inStartTime = dayjs(
      formInline.time[0]
    ).format("YYYY-MM-DD HH:mm");
    query.inEndTime = dayjs(
      formInline.time[1]
    ).format("YYYY-MM-DD HH:mm");
  }
  if (formInline.time1) {
    query.outStartTime = dayjs(
      formInline.time1[0]
    ).format("YYYY-MM-DD HH:mm");
    query.outEndTime = dayjs(
      formInline.time1[1]
    ).format("YYYY-MM-DD HH:mm");

  }
  pageList(query).then(res => {
    res.data.dataList.forEach(e => {
      if (e.recordType) {
        if (e.recordType == 1) {
          e.type = '住户'
          e.name = e.ownerName
        } else if (e.recordType == 2) {
          e.type = '访客'
          e.name = e.visitorName
        }
      }
    })
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

//重置方法
const onReset = () => {
  state.inStartTime = null
  state.inEndTime = null
  state.outStartTime = null
  state.outEndTime = null
  formInlineRef.value.resetFields()
  onSubmit()
}

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped></style>