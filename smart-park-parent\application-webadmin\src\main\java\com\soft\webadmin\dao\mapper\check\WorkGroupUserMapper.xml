<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.WorkGroupUserMapper">
    <resultMap type="com.soft.webadmin.model.check.WorkGroupUser" id="SpWorkGroupUserResult">
        <result property="id" column="id" />
        <result property="workGroupId" column="work_group_id" />
        <result property="userId" column="user_id" />
    </resultMap>

    <sql id="selectSpWorkGroupUserVo">
        select id, work_group_id, user_id from sp_work_group_user
    </sql>
    <insert id="insertBatch">
        insert into sp_work_group_user
        values
        <foreach collection="list" item="item" separator="," >
            (#{item.id}, #{item.workGroupId}, #{item.userId})
        </foreach>
    </insert>

    <select id="queryList" resultType="com.soft.admin.upms.vo.SysUserVo">
        select t1.user_id,t1.login_name,t1.show_name,t1.phone,t1.sex,t1.user_status,t1.user_type,
        t1.dept_id,t1.card_no,t1.one_card_no,t1.head_image_url,t1.face_picture,t1.short_phone,t1.user_tags
        from sp_work_group_user t
        inner join common_sys_user t1 on t1.user_id = t.user_id and t1.deleted_flag = 1
        inner join sp_work_group t2 on t2.id = t.work_group_id and t2.delete_flag = 1
        group by t.user_id
    </select>

</mapper>