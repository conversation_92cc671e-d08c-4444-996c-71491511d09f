import {defineStore} from 'pinia'
import router from '@/router'
import {menuGetAPI} from '@/api/settingSystem/menu.js'

export const menuStore = defineStore({
  id: 'menu',
  state: () => {
    return {
      userName: 'menu',
      isCollapse: false,
      systemName: '', // 系统名称
      systemIcon: '', // 系统图标
      menuList: [],  //头部
      activeMenu: '',// 顶部菜单索引
      itemMenu: [], //左边栏
      activeMenuItem: '',//当前所在路由地址
      btnList: [], //按钮
      tabsList: [],
    }
  },
  actions: {
    getMenu(path) {
      return new Promise(async (resolve, reject) => {
        let viewCom = import.meta.glob('@/views/**/*.vue')
        let menuList = await menuGetAPI("pc")

        menuList = menuList.data.find(item => {
          return deep(item.children)
        }) || {children: []}

        this.systemName = menuList.menuName
        this.systemIcon = menuList.icon

        function deep(arrMenu) {
          return (arrMenu || []).some(i => {
            if (i.path == path.slice(1,)) {
              return true
            } else {
              if (i.children) {
                return deep(i.children)
              }
            }
          })
        }

        this.btnList = []
        this.menuList = menuList.children
        this.deepRoutes(menuList.children, viewCom)

        resolve(menuList.children)
      }).catch(err => {
        reject(err)
      })
    },
    deepRoutes(arr, components) {
      (arr instanceof Array ? arr : arr.children).forEach((item, index) => {
        item.meta = {
          title: item.menuName,
          icon: item.icon,
          isHidden: item.isHidden
        }

        if (item.children?.length) {
          this.deepRoutes(item, components)
        }

        if (item.menuType == 0 || item.menuType == 1) {  // 0 目录 1 菜单

          const path = (item.path || '').indexOf('?') === -1 ? item.path : item.path.slice(0,item.path.indexOf('?'))

          item.component = components[`/src/views/${path}-${import.meta.env.VITE_BASE_NAME}/index.vue`] ? components[`/src/views/${path}-${import.meta.env.VITE_BASE_NAME}/index.vue`] : components[`/src/views/${path}/index.vue`]

          if (item.component) {
            router.addRoute('index', {  // item.children需要在添加前删除
              path,
              component: item.component,
            })
          }

        } else if (item.menuType == 2) { //按钮
          this.btnList.push(item.perms)
          if (index == arr.children?.length - 1) // 添加完毕之后删除
            delete arr.children
        }

      })
    }
  }
})
