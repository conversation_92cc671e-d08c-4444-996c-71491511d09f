import { divide, multiply } from 'mathjs'

const needToChangeAttrs = ['top', 'left', 'width', 'height', 'lineWidth']
export function changeComponentsSizeWithScale(canvasStyle ,componentData ,scale , oriScale) {
  componentData.forEach(component => {
    Object.keys(component.style).forEach(key => {
      if (needToChangeAttrs.includes(key)) {
        if (component.style[key] === '') return

        component.style[key] = parseFloat(changeStyleWithScale(getOriginStyle(component.style[key], oriScale), scale).toFixed(2))
      }
    })

    if(component.points){ // svg缩放
      component.points.forEach(point => {
        point.x = parseFloat(changeStyleWithScale(getOriginStyle(point.x, oriScale), scale).toFixed(2))
        point.y = parseFloat(changeStyleWithScale(getOriginStyle(point.y, oriScale), scale).toFixed(2))
      })
    }

  })

  return scale
}

export function getOriginStyle(value, scale) {
  return divide(value, divide(parseFloat(scale), 100))
}


export function changeStyleWithScale(value, scale) {
  return multiply(value, divide(parseFloat(scale), 100))
}
