package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * CheckRecordDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckRecordDTO对象")
@Data
public class CheckRecordQueryDTO {

    @ApiModelProperty(value = "计划类型（PATROL_INSPECTION巡检，MAINTENANCE维保）")
    private String planType;

    @ApiModelProperty(value = "巡检方式（1人工巡检，2智能巡检）")
    private Integer planMode;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "开始时间")
    private String beginDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "计划id", hidden = true)
    private List<Long> planIdList;

}
