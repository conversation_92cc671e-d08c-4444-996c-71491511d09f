package com.soft.webadmin.controller.shifts;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.shifts.ShiftsAttendanceQueryDTO;
import com.soft.webadmin.service.shifts.ShiftsAttendanceService;
import com.soft.webadmin.vo.shifts.ShiftsAttendanceStatisticVO;
import com.soft.webadmin.vo.shifts.ShiftsAttendanceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 排班考勤记录控制器类
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
@Api(tags = "考勤统计")
@RestController
@RequestMapping("/shifts/attendance")
public class ShiftsAttendanceController {

    @Autowired
    private ShiftsAttendanceService shiftsAttendanceService;

    @ApiOperation(value = "查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<ShiftsAttendanceVO>> getPage(@Validated ShiftsAttendanceQueryDTO queryDTO) {
        return ResponseResult.success(shiftsAttendanceService.getPage(queryDTO));
    }
    @ApiOperation(value = "统计")
    @GetMapping("/statistic")
    public ResponseResult<MyPageData<ShiftsAttendanceStatisticVO>> statistic(@Validated ShiftsAttendanceQueryDTO queryDTO) {
        return ResponseResult.success(shiftsAttendanceService.statistic(queryDTO));
    }

    @ApiOperation(value = "导入模板")
    @GetMapping("/excelTemplate")
    public ResponseResult<Void> excelTemplate(String businessType) {
        return shiftsAttendanceService.excelTemplate(businessType);
    }

    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public ResponseResult<Void> importExcel(@RequestParam MultipartFile file,
                                            @RequestParam String businessType) {
        return shiftsAttendanceService.importExcel(businessType,file);
    }
}
