package com.soft.webadmin.dto.equipment;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;


@Data
public class EquipmentScrapDTO {

    @ApiModelProperty("设备id列表")
    @NotEmpty(message = "请选择设备")
    private List<Long> equipmentIds;

    @ApiModelProperty("报废日期")
    @NotNull(message = "报废日期不能为空！")
    private Date scrapDate;

    @ApiModelProperty("报废理由")
    @NotBlank(message = "报废理由不能为空！")
    private String scrapReason;

}
