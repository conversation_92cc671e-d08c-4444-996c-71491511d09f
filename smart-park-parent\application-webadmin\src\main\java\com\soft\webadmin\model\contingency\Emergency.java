package com.soft.webadmin.model.contingency;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import com.soft.webadmin.vo.contingency.EmergencyVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 应急预案对象 cm_emergency
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cm_emergency")
public class Emergency extends BaseModel {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 预案名称 */
    private String name;

    /** 事件id */
    private Long eventId;

    /** 预案附件 */
    private String annex;

    /** 删除标记(1: 正常 -1: 已删除) */
    @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;


    @Mapper
    public interface EmergencyModelMapper extends BaseModelMapper<EmergencyVO, Emergency> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        Emergency toModel(EmergencyVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        EmergencyVO fromModel(Emergency entity);
    }

    public static final EmergencyModelMapper INSTANCE = Mappers.getMapper(EmergencyModelMapper.class);
}
