<template>
  <div style="height: 100%;overflow: hidden;">
    <transition name="el-zoom-in-center">
      <!--组态-->
      <exhibits v-show="0 === pageIndex" @showPage="showPage"></exhibits>
    </transition>
    <transition name="el-zoom-in-center">
      <list-table v-show="1 === pageIndex" ref="table"  @showPage="showPage"></list-table>
    </transition>
    <transition name="el-zoom-in-center">
      <detail v-show="2 === pageIndex" ref="detailRef" @showPage="showPage"  :back-index="1"></detail>
    </transition>
  </div>
</template>

<script setup>
import exhibits from './component/exhibits.vue'
import listTable from './component/listTable.vue'
import detail from '@/components/deviceDetail/index.vue'
import {events} from "@/utils/bus.js";

const pageIndex = ref(0)

const table = ref()
const detailRef = ref()

const showPage = (index, idList) => {
  pageIndex.value = index;
  if(pageIndex.value == 1){
    nextTick(() => {
      if(idList){
        table.value.open(idList)
      }
      events.emit('tabClick')
    })
  }else if(pageIndex.value == 2){
    detailRef.value.equipmentId = idList
    detailRef.value.init();
  }
}
</script>

<style lang='less' scoped>
</style>
