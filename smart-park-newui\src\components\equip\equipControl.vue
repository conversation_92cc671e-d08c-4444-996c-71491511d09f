<template>
  <el-dialog v-model="centerDialogVisible" :title="title" :width="900" @close="close"
             :append-to-body="true">

    <el-form ref="reviseForm" :model="state.equipInfo">
      <el-descriptions size="large" :column="2" labelClassName="descriptionsLabel" border>
        <el-descriptions-item label="设备名称" label-align="center" align="center">{{ state.equipInfo.equipmentName }}
        </el-descriptions-item>
        <el-descriptions-item label="在线状态" label-align="center" align="center">
          <span class="status-circle" :style="{ backgroundColor: state.runStatusColors[state.equipInfo.runStatus] }"></span>
          <span>{{ state.runStatusOptions[state.equipInfo.runStatus] }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="设备状态" label-align="center" align="center">
          <span class="status-circle" :style="{ backgroundColor: state.equipmentStatusColors[state.equipInfo.equipmentStatus] }"></span>
          <span>{{ state.equipmentStatusOptions[state.equipInfo.equipmentStatus] }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="位置" label-align="center" align="center">{{  state.equipInfo.spaceFullName }}
        </el-descriptions-item>
        <el-descriptions-item label="设备编号" label-align="center" align="center">{{ state.equipInfo.equipmentCode }}
        </el-descriptions-item>
        <el-descriptions-item label="设备类别" label-align="center" align="center">{{ state.equipInfo.equipmentType }}
        </el-descriptions-item>
        <el-descriptions-item label="设备型号" label-align="center" align="center">{{ state.equipInfo.equipmentModel }}
        </el-descriptions-item>
        <el-descriptions-item v-for="(item, index) in (state.equipInfo.equipmentAttributeList || [])"
                              :label="item.attributeName ? item.attributeName : item.attributeKey"
                              label-align="center" align="center">
          <el-form-item style="margin:0.05rem 0 0.05rem" :prop="'equipmentAttributeList.' + index + '.attributeValue'"
                        :rules="{
              required: true,
              message: `请选择${item.attributeOperType == 'INPUT' ? '输入' : '选择'}数据`,
              trigger: 'blur',
            }">
            <el-input v-model="item.attributeValue" v-if="item.attributeOperType == 'INPUT'"
                      :disabled="!item.modifiable">
            </el-input>
            <el-switch v-else-if="item.attributeOperType == 'SWITCH'" v-model="item.attributeValue"
                       style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff"
                       :active-text="item.attributeValueEnum[Object.keys(item.attributeValueEnum)[1]]"
                       :active-value="Object.keys(item.attributeValueEnum)[1]"
                       :inactive-text="item.attributeValueEnum[Object.keys(item.attributeValueEnum)[0]]"
                       :inactive-value="Object.keys(item.attributeValueEnum)[0]" :disabled="!item.modifiable" />
            <el-select v-else-if="item.attributeOperType == 'SELECT'" v-model="item.attributeValue" placeholder="Select"
                       :disabled="!item.modifiable">
              <el-option v-for="(value, key) in item.attributeValueEnum" :key="key" :label="value" :value="key" />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sumbit">
          确认
        </el-button>
      </span>
    </template>

  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'

import { equipContorlAPI } from '@/api/settingSystem/topoConnect.js'
import {viewEquipAPI} from "@/api/iotManagement/equipManage.js";

const props = defineProps({
  title: {
    type: String,
    default: '',
  }
})

const { title } = toRefs(props)

const reviseForm = ref()
const centerDialogVisible = ref(false)
const state = reactive({
  equipInfo: {},
  equipmentAttributesBak: null,
  runStatusOptions:{
    0: '离线',
    1: '正常'
  },
  runStatusColors:{
    0:'#F56C6C',
    1:'#67C23A'
  },
  equipmentStatusOptions:{
    0:'故障',
    1:'正常',
    5:'已报废'
  },
  equipmentStatusColors:{
    0:'#F56C6C',
    1:'#67C23A',
    5:'#73767a'
  }
})

// 打开
const open = async ({equipmentId},attribute) => {
  centerDialogVisible.value = true
  viewEquipAPI({equipmentId}).then(res => {
    if (res.success) {
      res.data.equipmentAttributeList.forEach(item => {
        if (typeof item.attributeValueEnum == 'string'){
          item.attributeValueEnum = JSON.parse(item.attributeValueEnum)
        }
      })

      // 控制单个属性
      if(attribute){
        res.data.equipmentAttributeList = res.data.equipmentAttributeList.filter(item => item.attributeKey == attribute)
      }

      state.equipInfo = res.data
      // 设置属性副本（深拷贝，不影响原始数据）
      let attributeArray = JSON.parse(JSON.stringify(state.equipInfo.equipmentAttributeList))
      // 转成 Map
      state.equipmentAttributesBak = new Map(attributeArray.map( (value) => [value.attributeKey, value.attributeValue]))
    }
  })
}

// 关闭
const close = () => {
  state.equipInfo = {}
  reviseForm.value.resetFields()
  centerDialogVisible.value = false
}

// sumbit表单提交
const sumbit = () => {
  reviseForm.value.validate((valid) => {
    if (valid) {
      let attributes = state.equipInfo.equipmentAttributeList.filter((item) => {
        return item.attributeValue !== state.equipmentAttributesBak.get(item.attributeKey);
      }).map(item => {
        return {
          key: item.attributeKey,
          value: item.attributeValue
        }
      })
      equipContorlAPI({ attributes, equipmentId: state.equipInfo.equipmentId }).then(res => {
        if (res.success) {
          ElMessage.success('修改成功')
          close()
        } else {
          ElMessage.error(res.errorMessage)
        }
      })
    } else {
      return false
    }
  })
}

defineExpose({
  open,
  close
})
</script>

<style scoped></style>
