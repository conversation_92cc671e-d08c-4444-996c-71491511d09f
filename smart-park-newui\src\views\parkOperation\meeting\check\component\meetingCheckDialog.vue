<template>
  <el-dialog v-model="showDialogRef" :title="state.title" width="60%" @close="close" class="dialogCommon">
    <template #default>
      <el-form :model="state.dataForm" ref="dataFormRef" :rules="state.rules" :label-width="90" label-suffix=":" class="content">
        <el-container>
          <el-container>
            <el-main>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="名称" prop="roomName">
                    <el-text>{{ state.dataForm.roomName }}</el-text>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="容纳人数" prop="capacity">
                    <el-text>{{ state.dataForm.capacity }}</el-text>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="预约审批" prop="isEnableApproval">
                    <el-tag v-if="state.dataForm.isEnableApproval === 1" type="success">是</el-tag>
                    <el-tag v-else-if="state.dataForm.isEnableApproval === 0" type="success">否</el-tag>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="会议室设备" prop="devices">
                <el-space>
                  <el-tag v-for="device in state.dataForm.devices">{{ device }}</el-tag>
                </el-space>
              </el-form-item>
              <el-form-item label="会议名称" prop="meetingName">
                <el-input v-model="state.dataForm.meetingName" :disabled="true" placeholder="请输入会议名称" />
              </el-form-item>
              <el-form-item label="会议内容" prop="meetingContent">
                <el-input type="textarea" v-model="state.dataForm.meetingContent" :disabled="true" placeholder="请输入会议内容" />
              </el-form-item>
              <el-form-item label="参会人员" prop="meetingUserIds">
                <el-select
                  multiple
                  clearable
                  :disabled="true"
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="5"
                  v-model="state.dataForm.meetingUserIds"
                  placeholder="请选择参会人员">
                  <el-option
                    v-for="item in state.userOptions"
                    :key="item.userId"
                    :label="item.showName"
                    :value="item.userId"
                  />
                </el-select>
              </el-form-item>
            </el-main>
            <el-aside width="30%">
              <div class="demo-image__error">
                <div class="block">
                  <el-form-item label-width="0" prop="roomImg">
                    <el-image :src="imgTransfer(state.dataForm.roomImg)" fit="fill">
                      <template #error>
                        <div class="image-slot">
                          <el-icon>
                            <icon-picture/>
                          </el-icon>
                        </div>
                      </template>
                    </el-image>
                  </el-form-item>
                </div>
              </div>
            </el-aside>
          </el-container>
          <el-footer height="120">
            <el-row :gutter="20">
              <el-col :span="7">
                <el-form-item label="会议日期" prop="meetingDate">
                  <el-date-picker
                    v-model="state.dataForm.meetingDate"
                    type="date"
                    :disabled="true"
                    :clearable="false"
                    :value-format="'YYYY-MM-DD'"
                    placeholder="会议日期"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="开始时间" prop="meetingBeginTime">
                  <el-text>{{ state.dataForm.meetingBeginTime }}</el-text>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="结束时间" prop="meetingEndTime">
                  <el-text>{{ state.dataForm.meetingEndTime }}</el-text>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="会议时长" prop="meetingDuration">
                  <el-text>{{ state.dataForm.meetingDuration ? state.dataForm.meetingDuration >= 60 ? parseInt(state.dataForm.meetingDuration / 60) + ' 小时 ' +  (state.dataForm.meetingDuration % 60 > 0 ? state.dataForm.meetingDuration % 60 + ' 分钟' : '') : state.dataForm.meetingDuration + ' 分钟' : ''  }}</el-text>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="审核状态" prop="approvalStatus">
              <el-radio-group v-if="state.title === '审核'" v-model="state.dataForm.approvalStatus">
                <el-radio :value="1">通过</el-radio>
                <el-radio :value="2">未通过</el-radio>
              </el-radio-group>
              <div v-else-if="state.title !== '审核'">
                <el-tag v-if="state.dataForm.approvalStatus === 0" type="info">待审核</el-tag>
                <el-tag v-else-if="state.dataForm.approvalStatus === 1" type="success">通过</el-tag>
                <el-tag v-else-if="state.dataForm.approvalStatus === 2" type="danger">未通过</el-tag>
              </div>
            </el-form-item>
            <el-form-item label="审核备注" v-if="state.title === '审核'" prop="remark">
              <el-input type="textarea" v-model="state.dataForm.remark"  placeholder="请输入审核备注" />
            </el-form-item>
            <el-form-item label="审核备注" v-else prop="approvalRemark">
              <el-input type="textarea" v-model="state.dataForm.approvalRemark"  :disabled="true" />
            </el-form-item>
          </el-footer>
        </el-container>
      </el-form>
    </template>
    <template #footer>
      <div v-if="state.title === '审核'">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {approvalMeetingAPI} from "@/api/parkOperation/meeting.js";
import {ElMessage} from "element-plus";
import {getPageAPI} from "@/api/settingSystem/user.js";

import { Picture as IconPicture } from '@element-plus/icons-vue'


const emits = defineEmits(['onClose'])

const showDialogRef = ref()

const dataFormRef = ref()

const state = reactive({
  title: '',
  dataForm: {
    approvalStatus: 1
  },
  // 用户列表
  userOptions: [],
  rules: {
    remark: [
      { required: true, message: '备注不能为空！', trigger: 'blur' }
    ]
  }
})

const onSubmit = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      let params = {
        id: state.dataForm.id,
        status: state.dataForm.approvalStatus,
        remark: state.dataForm.remark
      }
      approvalMeetingAPI(params).then(res => {
        if (res.success) {
          ElMessage.success('审批成功！')
        } else {
          ElMessage.error('审批失败，' + res.errorMessage)
        }
        close()
      })
    }
  })
}

const open = (title, val) => {
  state.title = title
  showDialogRef.value = true
  nextTick(() => {
    state.dataForm = val
    state.dataForm.devices = val.devices.split(',')

    console.log(state.dataForm.meetingUserIds)

    if (state.dataForm.meetingUserIds) {
      state.dataForm.meetingUserIds = state.dataForm.meetingUserIds.split(',')
    } else {
      state.dataForm.meetingUserIds = []
    }


    queryUsers()
    if (!state.dataForm.approvalStatus && title === '审核') {
      state.dataForm.approvalStatus = 1
    }
  })
}

const close = () => {
  showDialogRef.value = false
  dataFormRef.value.resetFields()
  emits('onClose')
}

// 查询人员列表
const queryUsers = () => {
  let data = {
    sysUserDtoFilter: {
      userStatus: 0
    }
  }
  getPageAPI(data).then(res => {
    state.userOptions = res.data.dataList
  })
}

const imgTransfer = (name) => {
  if (name) {
    return import.meta.env.VITE_BASE_URL + name
  }
  return name
}

defineExpose({
  open
})
</script>



<style scoped lang="less">
.content {
  margin-top: 10px;

  .el-input {
    width: 90%;
  }

  .el-select, .el-cascader {
    width: 90%;
  }

  .el-input-number {
    width: 90%;

    .el-input {
      width: 100%;
    }

    :deep(.el-input__inner) {
      text-align: left;
    }
  }

  .el-textarea {
    width: 90%;
  }

  :deep(.el-cascader) {
    width: 90%;
    flex-grow: 0;
  }
}


.demo-image__error .block {
  text-align: center;
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
  vertical-align: top;
}

.demo-image__error .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}

.demo-image__error .el-image {
  padding: 0 5px;
  max-width: 300px;
  max-height: 280px;
  width: 100%;
  height: 220px;
}

.demo-image__error .image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}

.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}

.headTimeItem{
  display: inline-block;
  width: 32px;
  text-align: center;
}
</style>
