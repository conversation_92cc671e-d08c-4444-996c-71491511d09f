package com.soft.webadmin.controller.shifts;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.shifts.ShiftsRosterDTO;
import com.soft.webadmin.dto.shifts.ShiftsRosterLeaveDTO;
import com.soft.webadmin.dto.shifts.ShiftsRosterQueryDTO;
import com.soft.webadmin.service.shifts.ShiftsRosterService;
import com.soft.webadmin.vo.shifts.ShiftsRosterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 人员花名册控制器类
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
@Api(tags = "花名册接口")
@RestController
@RequestMapping("/shifts/roster")
public class ShiftsRosterController {

    @Autowired
    private ShiftsRosterService shiftsRosterService;

    @ApiOperation(value = "查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<ShiftsRosterVO>> list(@Validated ShiftsRosterQueryDTO queryDTO) {
        return ResponseResult.success(shiftsRosterService.getPage(queryDTO));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/save")
    public ResponseResult<Void> save(@Validated @RequestBody ShiftsRosterDTO saveDTO) {
        return shiftsRosterService.saveOrUpdate(saveDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        return shiftsRosterService.delete(id);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public ResponseResult<ShiftsRosterVO> detail(@RequestParam Long id) {
        return ResponseResult.success(shiftsRosterService.detail(id));
    }

    @ApiOperation(value = "离职")
    @PostMapping("/leaveJob")
    public ResponseResult<Void> leaveJob(@Validated @RequestBody ShiftsRosterLeaveDTO leaveDTO) {
        return shiftsRosterService.leaveJob(leaveDTO);
    }

    @ApiOperation(value = "操作证到期提醒")
    @GetMapping("/certificateRemind")
    public ResponseResult<Void> certificateRemind(Integer days) {
        return shiftsRosterService.certificateRemind(days);
    }


    @ApiOperation(value = "操作证到期提醒")
    @GetMapping("/getRemind")
    public ResponseResult<Integer> getRemind() {
        return ResponseResult.success(shiftsRosterService.getRemind());
    }
    @ApiOperation(value = "导入模版")
    @GetMapping("/template")
    public void template(String businessType) {
        shiftsRosterService.template(businessType);
    }

    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public ResponseResult<Void> importExcel(@RequestParam MultipartFile file,
                    @RequestParam String businessType) {
        return shiftsRosterService.importExcel(businessType,file);
    }

}
