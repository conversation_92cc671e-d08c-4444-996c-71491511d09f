<template>
  <div style="height: 100%;overflow: hidden;">
    <transition name="el-zoom-in-center">
      <listTable v-show="0 === pageIndex" ref="table" @showPage="showPage"></listTable>
    </transition>
    <transition name="el-zoom-in-center">
      <detail ref="detailRef" v-show="2 === pageIndex" @showPage="showPage"></detail>
    </transition>
  </div>
</template>

<script setup>
import listTable from './component/tablePage.vue'
import detail from './component/detailPage.vue'

const pageIndex = ref(0)

// 设备列表
const table = ref()

// 设备详情
const detailRef = ref()

const showPage = (index, equipmentId) => {
  pageIndex.value = index;

  if (index == 0) { //刷新
    table.value.getEquipList()
  }else if(index == 2){
    detailRef.value.loadPage(equipmentId)
  }
}
</script>

<style scoped lang="less">
</style>
