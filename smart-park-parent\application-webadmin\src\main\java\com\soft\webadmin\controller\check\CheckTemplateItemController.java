package com.soft.webadmin.controller.check;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.model.check.CheckTemplateItem;
import com.soft.webadmin.service.check.CheckTemplateItemService;
import com.soft.webadmin.vo.check.CheckTemplateItemVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 检查模板项目控制器类
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@RestController
@RequestMapping("/check/template/item")
public class CheckTemplateItemController {

    @Resource
    private CheckTemplateItemService checkTemplateItemService;


    @GetMapping("/list")
    public ResponseResult<List<CheckTemplateItemVO>> list(@RequestParam Long templateId) {
        List<CheckTemplateItem> checkTemplateItems = checkTemplateItemService.list(Wrappers.lambdaQuery(CheckTemplateItem.class)
                .eq(CheckTemplateItem::getTemplateId, templateId));
        return ResponseResult.success(CheckTemplateItem.INSTANCE.fromModelList(checkTemplateItems));
    }
}
