<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.sparePart.SparePartClassifyMapper">
    <resultMap type="com.soft.webadmin.model.sparePart.SparePartClassify" id="SparePartClassifyResult">
        <result property="id" column="id" />
        <result property="classifyName" column="classify_name" />
        <result property="classifyCode" column="classify_code" />
        <result property="parentId" column="parent_id" />
        <result property="idPath" column="id_path" />
        <result property="remark" column="remark" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectSparePartClassifyVo">
        select id, classify_name, classify_code, parent_id, id_path, remark, deleted_flag, create_user_id, create_time, update_user_id, update_time from sp_spare_part_classify
    </sql>
    
</mapper>