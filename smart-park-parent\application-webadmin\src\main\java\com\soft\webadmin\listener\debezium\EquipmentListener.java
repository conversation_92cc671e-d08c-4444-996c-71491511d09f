package com.soft.webadmin.listener.debezium;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.debezium.ChangeListenerModel;
import com.soft.common.debezium.event.DebeziumBaseEvent;
import com.soft.common.debezium.event.SpEquipmentEvent;
import com.soft.sub.model.equipment.Equipment;
import com.soft.webadmin.dao.equipment.EquipmentOmMapper;
import com.soft.webadmin.model.equipment.EquipmentOm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @ClassName EquipmentListener
 * @description: 物联设备同步到运维设备
 * @date 2024年07月15日
 */
@Slf4j
@Service
public class EquipmentListener {

    @Autowired
    private EquipmentOmMapper equipmentOmMapper;

    @Async("applicationTaskExecutor")
    @EventListener
    public void handleEvent(DebeziumBaseEvent<SpEquipmentEvent> baseEvent) {
        ChangeListenerModel model = BeanUtil.toBean(baseEvent.getData(), ChangeListenerModel.class);
        Integer eventType = model.getEventType();

        Equipment currentEntity = JSON.parseObject(model.getData(), Equipment.class);
        Equipment beforeEntity = JSON.parseObject(model.getBeforeData(), Equipment.class);
        try {
            EquipmentOm equipmentOm = MyModelUtil.copyTo(currentEntity, EquipmentOm.class);
            equipmentOm.setEquipmentId(null);
            equipmentOm.setCategory("IOT");
            switch (eventType) {
                case 1:
                    equipmentOmMapper.insert(equipmentOm);
                    break;
                case 2:
                    String equipmentCode = currentEntity.getEquipmentCode();
                    int count = equipmentOmMapper.selectCount(
                            new LambdaQueryWrapper<EquipmentOm>().eq(EquipmentOm::getEquipmentCode, equipmentCode)
                    ).intValue();
                    if (count > 0) {
                        LambdaUpdateWrapper<EquipmentOm> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(EquipmentOm::getEquipmentCode, equipmentCode);
                        equipmentOmMapper.update(equipmentOm, updateWrapper);
                    } else {
                        equipmentOmMapper.insert(equipmentOm);
                    }
                    break;
            }
        } catch (Exception e) {
            log.error("EquipmentListener.onMessage error", e);
        }
    }

}
