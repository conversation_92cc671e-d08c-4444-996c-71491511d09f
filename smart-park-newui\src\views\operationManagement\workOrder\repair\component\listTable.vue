<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form ref="formInlineRef" :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item prop="orderNo">
          <el-input v-model="formInline.orderNo" placeholder="工单编号"/>
        </el-form-item>
        <el-form-item prop="workUserName">
          <el-input v-model="formInline.workUserName" placeholder="执行人"/>
        </el-form-item>
        <el-form-item prop="state">
          <el-select v-model="formInline.state" clearable placeholder="工单状态">
            <el-option v-for="(val, key) in state.orderStateOptions" :label="val" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="priority">
          <el-select v-model="formInline.priority" clearable placeholder="优先级">
            <el-option v-for="(val, key) in state.priorityOptions" :label="val" :value="key"/>
          </el-select>
        </el-form-item>
<!--        <el-form-item prop="businessType">-->
<!--          <el-select v-model="formInline.businessType" clearable placeholder="业务类型">-->
<!--            <el-option v-for="(val, key) in state.businessTypeOptions" :value="key" :label="val"/>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item prop="orderType">-->
<!--          <el-select v-model="formInline.orderType" clearable placeholder="工单类别">-->
<!--            <el-option v-for="(val, key) in state.orderTypeOptions" :label="val" :value="key"/>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item prop="daterange">
          <el-date-picker
              v-model="formInline.daterange"
              end-placeholder="结束日期"
              range-separator="至"
              start-placeholder="开始日期"
              type="daterange"
              value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button icon="Search" type="primary" @click="onSubmit">查询</el-button>
          <el-button icon="Refresh" type="primary" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button icon="Plus" type="primary" @click="addHandle">报事报修</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column
            v-for="(item, index) in state.tableHeader"
            :key="index"
            :formatter="item.formatter"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"/>
        <el-table-column align="center" label="操作" width="80">
          <template #default="scope">
            <el-button icon="Tickets" link type="primary" @click="viewHandle(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="state.pagetion.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
      <modal-page ref="modal" @submit="getList"/>
    </template>
  </page-common>
</template>

<script setup>
import modalPage from './modalPage.vue';

import {getRepairPageAPI} from '@/api/operationManagement/repair.js';

import {ElButton, ElTag} from 'element-plus';
import orderStatus from "@/views/operationManagement/workOrder/component/orderStatus.vue";

const modal = ref();

const formInlineRef = ref();
const formInline = reactive({});
const emit = defineEmits(['showPage'])
const state = reactive({
  businessType: '',
  tableData: [],
  tableHeight: 100,
  tableHeader: [
    {
      prop: 'orderNo',
      label: '工单编号'
    },
    {
      prop: 'state',
      label: '工单状态',
      formatter: (row, column, cellValue) => {
        return h('div', [h(orderStatus,{status: cellValue,style: {marginRight: '5px'}}), row.timeout ?  h(ElTag, {type: 'danger'}, {default: () => '超时' + row.timeout + '小时'}) : '' ]);
      }
    },
    {
      prop: 'workUserName',
      label: '执行人',
      formatter: (row, column, cellValue) => {
        return cellValue;
      }
    },
    {
      prop: 'priority',
      label: '优先级',
      formatter: (row, column, cellValue) => {
        if (cellValue === 1) {
          return h(ElTag, {type: 'primary'}, {default: () => state.priorityOptions[cellValue]});
        } else if (cellValue === 2) {
          return h(ElTag, {type: 'warning'}, {default: () => state.priorityOptions[cellValue]});
        } else {
          return h(ElTag, {type: 'danger'}, {default: () => state.priorityOptions[cellValue]});
        }
      }
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160
    }
  ],
  orderStateOptions: {
    1: '待派单',
    2: '未响应',
    3: '处理中',
    4: '已关闭',
    5: '已完成',
  },
  priorityOptions: {
    1: '普通',
    2: '紧急',
    3: '特急',
  },
  orderTypeOptions: {
    'REPAIR': '维修工单',
    'CLEANING_TEMP': '临时保洁',
    'TRANSPORT_TEMP': '临时运送'
  },
  subTypeOptions: {
    'BA': '楼控',
    'ENTRANCE_GUARD': '门禁',
    'FIRE_FIGHTING': '消防',
    'INFORMATION': '信息发布',
    'LADDER_CONTROL': '梯控',
    'LIGHTING': '照明',
    'MONITOR': '监控',
    'PARKING': '停车',
    'ENERGY': '能耗',
  },
  businessTypeOptions: {
    OPERATIONS: '运维',
    CLEANING: '保洁',
    TRANSPORT: '运送',
    PROPERTY: '房产',
  },
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
});

onMounted(() => {
  getList();
});

const getList = () => {
  let query = {
    ...formInline,
    ...state.pagetion
  };
  if (formInline.daterange) {
    query.beginDate = formInline.daterange[0];
    query.endDate = formInline.daterange[1];
  }
  getRepairPageAPI(query).then((res) => {
    state.tableData = res.data.dataList;
    state.pagetion.total = res.data.totalCount;
  });
};

const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};

// 报事报修
const addHandle = () => {
  modal.value.open();
}

// 详情
const viewHandle = ({id}) => {
  emit('showPage', 3, '工单详情', id);
};

defineExpose({
  getList
})
</script>
