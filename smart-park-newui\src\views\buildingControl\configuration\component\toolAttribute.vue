<template>
  <div class="toolAttribute">
    <component-att v-if="curComponent"></component-att>
    <canvas-att v-else></canvas-att>
  </div>
</template>

<script setup>
import canvasAtt from './attribute/canvasAtt.vue'
import componentAtt from './attribute/componentAtt.vue';

import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent } = storeToRefs(webtopo) 
</script>

<style lang='less' scoped>
.toolAttribute {
  height: 100%;
  overflow: auto;
}


</style>