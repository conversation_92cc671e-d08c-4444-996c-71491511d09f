import { request } from "@/utils/request";

// 获取组态
export const topoGetAPI = (query) => {
  return request('get','/equipment/configuration/list',query,'F')
}

// 新增组态
export const topoAddAPI = (data) => {
  return request('post','/equipment/configuration/add',data)
}

// 修改组态
export const topoEditAPI = (data) => {
  return request('post','/equipment/configuration/update',data)
}

// 删除组态
export const topoDeteleAPI = (data) => {
  return request('post','/equipment/configuration/delete',data)
}

// 根据id获取组态内容
export const topoDetailAPI = (query) => {
  return request('get','/equipment/configuration/tab/getById',query,'F')
}

// 获取组态页签
export const topoTabGetAPI = (query) => {
  return request('get','/equipment/configuration/tab/list',query,'F')
}

// 新增或修改组态页签
export const topoTabSaveAPI = (data) => {
  return request('post','/equipment/configuration/tab/save',data)
}

// 删除组态页签
export const topoTabDeleteAPI = (data) => {
  return request('post','/equipment/configuration/tab/delete',data)
}
