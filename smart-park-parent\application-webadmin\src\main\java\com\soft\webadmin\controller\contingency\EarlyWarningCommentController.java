package com.soft.webadmin.controller.contingency;

import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.contingency.EarlyWarningCommentDTO;
import com.soft.webadmin.service.contingency.EarlyWarningCommentService;
import com.soft.webadmin.vo.contingency.EarlyWarningCommentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 预警记录评论控制器类
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Api(tags = "评论管理")
@RestController
@RequestMapping("/contingency/comment")
public class EarlyWarningCommentController {

    @Autowired
    private EarlyWarningCommentService earlyWarningCommentService;

    @ApiOperation(value = "查询")
    @GetMapping("/getList")
    public ResponseResult<List<EarlyWarningCommentVO>> getList(@RequestParam Long warningId) {
        return ResponseResult.success(earlyWarningCommentService.list(warningId));
    }

    @ApiOperation(value = "发表评论")
    @PostMapping("/save")
    public ResponseResult<Void> save(@Validated @RequestBody EarlyWarningCommentDTO saveDTO) {
        earlyWarningCommentService.save(saveDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "删除评论")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        earlyWarningCommentService.delete(id);
        return ResponseResult.success();
    }

}
