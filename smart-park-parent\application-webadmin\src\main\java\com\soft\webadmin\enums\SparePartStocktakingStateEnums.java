package com.soft.webadmin.enums;

/**
 * 盘点状态
 */
public enum SparePartStocktakingStateEnums {

    UNTREATED(1, "待入库/待出库"),
    PROCESSED(2, "已入库/已出库"),
    ;

    private Integer value;

    private String name;

    public Integer getValue() {
        return value;
    }

    SparePartStocktakingStateEnums(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
