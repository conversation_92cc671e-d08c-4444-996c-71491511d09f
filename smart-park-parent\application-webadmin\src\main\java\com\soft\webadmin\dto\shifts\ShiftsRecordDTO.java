package com.soft.webadmin.dto.shifts;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * ShiftsRecordDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsRecordDTO对象")
@Data
public class ShiftsRecordDTO {

    @ApiModelProperty(value = "排班表ID")
    @NotNull(message = "数据验证失败，排班表ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "班次设置表ID")
    private Long shiftsSettingId;

    @ApiModelProperty(value = "班次名称")
    private String shiftsName;

    @ApiModelProperty(value = "花名册ID")
    private Long rosterId;

    @ApiModelProperty(value = "出勤日期")
    private String attendanceDate;

    @ApiModelProperty(value = "出勤开始时间")
    private String startTime;

    @ApiModelProperty(value = "出勤结束时间")
    private String endTime;

    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    @ApiModelProperty(value = "周几")
    private Integer week;

    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private String businessType;

    @ApiModelProperty(value = "调班类型 0正常排班 1排班 -1取消排班")
    private Integer allocateStatus;

    @ApiModelProperty(value = "删除标记(1: 正常 -1: 已删除)")
    private Integer deletedFlag;

    @ApiModelProperty(value = "创建者Id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者Id")
    private Long updateUserId;

    @ApiModelProperty(value = "最后更新时间")
    private Date updateTime;

}
