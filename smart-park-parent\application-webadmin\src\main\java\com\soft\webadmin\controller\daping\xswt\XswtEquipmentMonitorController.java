package com.soft.webadmin.controller.daping.xswt;


import com.soft.common.core.object.ResponseResult;
import com.soft.sub.vo.equipment.EquipmentStatByStatusVO;
import com.soft.webadmin.service.daping.xswt.XswtEquipmentMonitorService;
import com.soft.webadmin.vo.daping.xswt.equipmentMonitor.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Api(tags = "大屏：设备监控")
@RestController
@RequestMapping("/daping/xswt/equipmentMonitor")
public class XswtEquipmentMonitorController {

    @Resource
    private XswtEquipmentMonitorService xswtEquipmentMonitorService;

    @ApiOperation("设备状态")
    @GetMapping("/equipment-status")
    public ResponseResult<XswtEquipmentStatusVO> equipmentStatus() {
        XswtEquipmentStatusVO equipmentStatusVO = xswtEquipmentMonitorService.equipmentStatus();
        return ResponseResult.success(equipmentStatusVO);
    }


    /**
     * 异常设备类型占比
     *
     * @param dateType DAY、WEEK、MONTH
     * @return
     */
    @ApiOperation("异常设备类型占比")
    @GetMapping("/alarm-equipment-type")
    public ResponseResult<List<XswtEquipmentAlarmTypeVO>> alarmEquipmentType(String dateType) {
        List<XswtEquipmentAlarmTypeVO> equipmentAlarmTypeVOS = xswtEquipmentMonitorService.alarmEquipmentType(dateType);
        return ResponseResult.success(equipmentAlarmTypeVOS);
    }


    @ApiOperation("近12个月异常设备统计")
    @GetMapping("/alarm-equipment-12month")
    public ResponseResult<XswtEquipmentAlarm12MonthVO> alarmEquipment12Month() {
        XswtEquipmentAlarm12MonthVO equipmentAlarm12MonthVO = xswtEquipmentMonitorService.alarmEquipment12Month();
        return ResponseResult.success(equipmentAlarm12MonthVO);
    }


    @ApiOperation("设备分布")
    @GetMapping("/equipment-distribute")
    public ResponseResult<List<XswtEquipmentDistributeVO>> equipmentDistribute() {
        List<XswtEquipmentDistributeVO> equipmentDistributeVOS = xswtEquipmentMonitorService.equipmentDistribute();
        return ResponseResult.success(equipmentDistributeVOS);
    }


    @ApiOperation("设备异常排名")
    @GetMapping("/alarm-equipment-rank")
    public ResponseResult<List<XswtEquipmentAlarmRankVO>> alarmEquipmentRank(String dateType) {
        List<XswtEquipmentAlarmRankVO> equipmentAlarmRankVOS = xswtEquipmentMonitorService.alarmEquipmentRank(dateType);
        return ResponseResult.success(equipmentAlarmRankVOS);
    }


    @ApiOperation("异常设备区域分布")
    @GetMapping("/alarm-equipment-area-distribute")
    public ResponseResult<XswtEquipmentAlarmAreaDistributeVO> alarmEquipmentAreaDistribute(String dateType) {
        XswtEquipmentAlarmAreaDistributeVO equipmentAlarmAreaDistributeVO = xswtEquipmentMonitorService.alarmEquipmentAreaDistribute(dateType);
        return ResponseResult.success(equipmentAlarmAreaDistributeVO);
    }

    @ApiOperation("实时工单信息")
    @GetMapping("/workOrderList")
    public ResponseResult<List<WorkOrderInfoVO>> workOrderInfoList() {
        return ResponseResult.success(xswtEquipmentMonitorService.workOrderInfoList());
    }

    @ApiOperation("设备状态")
    @GetMapping("/statStatusCountByType")
    public ResponseResult<List<EquipmentStatByStatusVO.EquipmentTypeVO>> statStatusCountByType() {
        return ResponseResult.success(xswtEquipmentMonitorService.statStatusCountByType());
    }

    @ApiOperation("根据楼幢统计设备状态数量")
    @GetMapping("/statStatusCountByBuilding")
    public ResponseResult<Map<String, Object>> statStatusCountByBuilding(@RequestParam Long equipmentTypeId) {
        return ResponseResult.success(xswtEquipmentMonitorService.statStatusCountByBuilding(equipmentTypeId));
    }

}
