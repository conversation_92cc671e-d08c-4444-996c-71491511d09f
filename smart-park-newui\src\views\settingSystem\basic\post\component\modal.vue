<template>
  <!-- 编辑岗位Dialog -->
  <dialog-common
      ref="dialog"
      :formRef="ruleFormRef"
      :title="title"
      :width="450"
      @submit="submit">
    <el-form
        ref="ruleFormRef"
        :model="form"
        :rules="state.rules"
        label-position="right"
        label-width="110px">

      <el-form-item label="岗位名称:" prop="postName">
        <el-input
            v-model="form.postName"
            clearable
            placeholder="请输入岗位名称"/>
      </el-form-item>

      <el-form-item label="岗位编号:" prop="postCode">
        <el-input
            v-model="form.postCode"
            placeholder="请输入岗位编号"
            clearable/>
      </el-form-item>

      <el-form-item label="所属部门" prop="deptIds">
        <el-cascader
            v-model="form.deptIds"
            :clearable="true"
            :options="state.deptInfo.impl.dropdownList"
            :props="{
                value: 'deptId',
                label: 'deptName',
                multiple: true,
                emitPath: false,
                checkStrictly: true,
                expandTrigger: 'hover',
              }"
            placeholder="请选择所属部门"
        >
        </el-cascader>
      </el-form-item>


      <el-form-item label="岗位层级:" prop="postLevel">
        <el-input-number v-model="form.postLevel"
                         controls-position="right"
                         :min="1" placeholder="请输入岗位层级"
                         :value-on-clear="1"/>
      </el-form-item>
      <el-form-item label="是否领导:" prop="leaderPost">
        <el-switch v-model="form.leaderPost"/>
      </el-form-item>

    </el-form>
  </dialog-common>
</template>

<script setup>
import {addPostAPI, updatePostAPI} from "@/api/settingSystem/post.js";
import {ElMessage} from "element-plus";

import {getDeptListAPI} from "@/api/settingSystem/user.js";
import {DropdownWidget} from "@/utils/widget.js";

const ruleFormRef = ref();
const dialog = ref();

const state = reactive({
  deptInfo: {
    impl: new DropdownWidget(loadDeptDropdownList, true, 'deptId'),
  },
  rules: {
    postName: [{required: true, message: '岗位名称不能为空', trigger: 'blur'}],
    postLevel: [{required: true, message: '岗位层级不能为空', trigger: 'blur'}],
    postCode: [{required: true, message: '岗位编号不能为空', trigger: 'blur'}]
  },
});
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})
const {title} = toRefs(props)
const emit = defineEmits(['submit'])

/**
 * form表单数据
 */
let form = reactive({
  postId: ''
})

/**
 * 打开弹窗
 */
const open = () => {
  dialog.value.open()
}

/**
 * 保存岗位
 */
const submit = () => {
  let data = {
    sysPostDto: form
  }
  if (data.sysPostDto.postId) {
    subHandle(updatePostAPI, "编辑成功");
  } else {
    subHandle(addPostAPI, "添加成功");
  }

  function subHandle(req, title) {
    req(data).then((res) => {
      if (res.success) {
        ElMessage.success(title);
        dialog.value.close()
        emit('submit')
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  }
};


/**
 * 查询部门信息
 */
function loadDeptDropdownList() {
  return new Promise((resolve, reject) => {
    getDeptListAPI({})
        .then((res) => {
          resolve(res.data.dataList);
        })
        .catch((e) => {
          reject(e);
        });
  });
}

onMounted(() => {
  state.deptInfo.impl.onVisibleChange(true);
});

/**
 * 暴露属性，用来外部控制表单内容和弹窗状态
 */
defineExpose({
  form,
  open
})
</script>

<style lang="less" scoped>
.el-input, .el-input-number {
  width: 85%;
}
</style>
