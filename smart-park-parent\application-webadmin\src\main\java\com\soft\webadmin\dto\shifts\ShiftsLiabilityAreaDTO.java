package com.soft.webadmin.dto.shifts;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * ShiftsRosterDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsLiabilityAreaDTO对象")
@Data
public class ShiftsLiabilityAreaDTO {

    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private String businessType;

    @ApiModelProperty(value = "责任区域")
    private String liabilityArea;

    @NotNull(message = "考勤周期不能为空")
    @ApiModelProperty(value = "考勤周期(出勤月份 YYYY-MM)")
    private String cycle;
}
