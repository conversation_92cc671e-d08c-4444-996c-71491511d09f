package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import com.soft.webadmin.vo.check.WorkOrderRuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

/**
 * 工单规则对象 sp_work_order_rule
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_work_order_rule")
public class WorkOrderRule extends BaseModel {

    @TableId(value = "id")
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 设备类型
     */
    private Long equipmentTypeId;

    /**
     * 设备类型idPath
     */
    private String equipmentTypeIdPath;

    /**
     * 优先级，1普通；2紧急；3特急
     */
    private Integer priority;

    /**
     * 预计维修时长，单位：分钟
     */
    private Long predictRepairDuration;

    /**
     * 处理时限，单位：分钟
     */
    private Long handleLimitDuration;

    /**
     * 派单方式：0自动派单；1手动派单
     */
    private Integer dispatchType;

    /**
     * 自动派单工作组 id，逗号分隔
     */
    private String workGroupIds;

    /**
     * 人工费
     */
    private BigDecimal laborCost;

    /**
     * 处理预案
     */
    private String treatmentPlan;

    /**
     * 删除标识，1正常；-1已删除
     */
    @TableLogic
    private Integer deleteFlag;


    @Mapper
    public interface WorkOrderRuleModelMapper extends BaseModelMapper<WorkOrderRuleVO, WorkOrderRule> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        WorkOrderRule toModel(WorkOrderRuleVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        WorkOrderRuleVO fromModel(WorkOrderRule entity);
    }

    public static final WorkOrderRuleModelMapper INSTANCE = Mappers.getMapper(WorkOrderRuleModelMapper.class);
}
