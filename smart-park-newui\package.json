{"name": "managepark", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5000", "dev-1719dk": "vite --port 5001 --mode dev-1719dk", "dev-xswt": "vite --port 5002 --mode dev-xwst", "build": "vite build", "build-1719dk": "vite build --mode pro-1719dk", "build-xswt": "vite build --mode pro-xwst", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@stomp/stompjs": "^7.0.0", "@vueuse/core": "^10.1.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.3.4", "clipboard": "^2.0.11", "dayjs": "^1.11.7", "default-passive-events": "^2.0.0", "dplayer": "^1.27.1", "echarts": "^5.4.2", "element-plus": "^2.3.0", "flv.js": "^1.6.2", "jsencrypt": "^3.3.2", "json-bigint": "^1.0.0", "less": "^4.1.3", "less-loader": "^11.1.0", "lodash": "^4.17.21", "mathjs": "^11.8.0", "mitt": "^3.0.0", "moment": "^2.29.4", "nprogress": "^0.2.0", "pinia": "^2.0.33", "pinia-plugin-persist": "^1.0.0", "qrcode.vue": "^3.4.0", "qs": "^6.11.1", "relation-graph": "^2.1.24", "sortablejs": "^1.15.0", "swiper": "^11.1.4", "uuid": "^9.0.0", "v3-drag-zoom": "^1.1.20", "vue": "^3.2.45", "vue-router": "^4.1.6", "vue3-cron-plus": "^0.1.9", "vue3-draggable-resizable": "^1.6.5", "vue3-json-viewer": "^2.2.2", "vue3-puzzle-vcode": "^1.0.8"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "unplugin-auto-import": "^0.15.1", "unplugin-vue-define-options": "^1.2.4", "vite": "^4.1.0"}}