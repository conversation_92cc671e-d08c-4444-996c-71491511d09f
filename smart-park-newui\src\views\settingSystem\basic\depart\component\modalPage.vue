<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="100px" :model="form" :rules="state.rules" label-suffix=":">
      <el-form-item label="组织名称" prop="deptName">
        <el-input v-model="form.deptName" placeholder="组织名称" />
      </el-form-item>
      <el-form-item label="上级组织" prop="parentId">
        <el-tree-select v-model="form.parentId" :data="treeData" check-strictly :render-after-expand="false"
          node-key="deptId" :props="treeProps" placeholder="请选择上级组织" clearable />
      </el-form-item>
      <el-form-item label="显示顺序" prop="showOrder">
        <el-input v-model="form.showOrder" type="number" placeholder="显示顺序" />
      </el-form-item>
      <el-form-item label="描述" prop="deptDesc">
        <el-input type="textarea" v-model="form.deptDesc" :maxlength="500" show-word-limit :autosize="{ minRows: 5 }"
          placeholder="描述" />
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { departListAPI, departAddAPI, departEidtAPI } from '@/api/settingSystem/depart.js'
import { treeDataTranslate } from '@/utils';

import { ElMessage } from 'element-plus'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
})

const { title } = toRefs(props)
const emit = defineEmits(['submit'])

let ruleFormRef = ref()
let dialog = ref()
const formInline = reactive({})
const form = reactive({})
const state = reactive({
  tableDate: [],
  rules: {
    deptName: [{ required: true, message: '请输入组织名称', trigger: 'blur' }],
    showOrder: [{ required: true, message: '请输入显示顺序', trigger: 'blur' }]
  },
})

const treeData = computed(() => {
  console.log(state.tableDate)
  state.tableDate = state.tableDate.map(item => {
    return {
      ...item,
      disabled: item.deptId == form.deptId,
    }
  })
  return treeDataTranslate(JSON.parse(JSON.stringify(state.tableDate)), 'deptId', 'parentId')
})

const getList = () => {
  let query = {
    orderParam: [{ fieldName: 'showOrder', asc: true }],
    sysDeptDtoFilter: formInline
  }
  departListAPI(query).then(res => {
    state.tableDate = res.data.dataList
  })
}

const open = () => {
  dialog.value.open()
  getList()
}

const treeProps = computed(() => {
  return {
    label: 'deptName'
  }
})
const submit = () => {
  let data = {
    sysDeptDto: form
  }

  if (data.sysDeptDto.deptId) {
    subHandle(departEidtAPI, '编辑成功')
  } else {
    subHandle(departAddAPI, '添加成功')
  }

  function subHandle(req, title) {
    req(data).then(res => {
      if (res.success) {
        ElMessage.success(title)
        dialog.value.close()
        emit('submit')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  }
}

defineExpose({
  form,
  open
})
</script>
