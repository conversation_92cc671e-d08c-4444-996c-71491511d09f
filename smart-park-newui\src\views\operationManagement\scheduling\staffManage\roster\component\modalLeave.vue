<template>
  <dialog-common ref="dialog" title="办理离职" @submit="submit" :formRef="ruleFormRef" :width="450">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-form-item label="离职日期" prop="leaveJobDate">
        <el-date-picker
            v-model="form.leaveJobDate"
            type="date"
            placeholder="请选择离职日期"
            value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="离职原因" prop="leaveJobReason">
        <el-input v-model="form.leaveJobReason" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit
                  placeholder="请输入离职原因"/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {ElMessage} from "element-plus";

import { rosterLeaveAPI } from '@/api/operationManagement/roster.js'

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  rules: {
    leaveJobDate: [{required: true, message: '请选择离职日期'}],
    leaveJobReason: [{required: true, message: '请输入离职原因'}],
  },
});

// 提交表单
const submit = () => {
  rosterLeaveAPI({
    ...form,
    businessType: 'OPERATIONS'
  }).then(res => {
    if (res.success) {
      ElMessage.success('办理离职成功');
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = () => {
  dialog.value.open();
}

defineExpose({
  form,
  open,
});
</script>
