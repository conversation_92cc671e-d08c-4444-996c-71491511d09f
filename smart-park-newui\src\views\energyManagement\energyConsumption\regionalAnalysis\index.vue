<template>
  <div class="page">
    <div class="conent">
      <div class="tai">
        <div class="ju">区域用能分析</div>
        <div class="ju">
          <el-form :inline="true">
            <el-form-item>
              <el-select v-model="formInline.equipmentType" @change="change">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 各区域用能统计、各区域用能排行 -->
      <div style="display: flex;margin: 40px 40px 20px;justify-content: space-between;">
        <div style="display: flex;width:48%;justify-content: space-between;">
          <div style="display: flex;">
            <div class="line"></div>
            <div>各区域用能统计</div>
          </div>
          <div>
            <el-date-picker :clearable="false" value-format="YYYY-MM-DD HH:mm" v-model="formInline1.time"
              type="daterange" range-separator="到" start-placeholder="开始日期" end-placeholder="结束日期"
              @change="selectRangeStatistics" />
          </div>
        </div>

        <div style="display: flex;width:48%;justify-content: space-between;">
          <div style="display: flex;">
            <div class="line"></div>
            <div>各区域用能排行</div>
          </div>
          <div>
            <el-date-picker :clearable="false" value-format="YYYY-MM-DD HH:mm" v-model="formInline5.time"
              type="daterange" range-separator="到" start-placeholder="开始日期" end-placeholder="结束日期"
              @change="selectRankingAreasEnergyConsumption" />
          </div>
        </div>
      </div>

      <div class="flexClass">
        <div ref="leftMounth" style="width: 50%;height: 400px;"></div>
        <div ref="rightYear" style="width: 50%;height: 400px;"></div>
      </div>

      <!-- 各区域用能占比、各区域分项用能统计 -->
      <div style="display: flex;margin: 40px 40px 20px;justify-content: space-between;">
        <div style="display: flex;width:48%;justify-content: space-between;">
          <div style="display: flex;">
            <div class="line"></div>
            <div>各区域用能占比</div>
          </div>
          <div>
            <el-date-picker :clearable="false" value-format="YYYY-MM-DD HH:mm" v-model="formInline2.time"
              type="daterange" range-separator="到" start-placeholder="开始日期" end-placeholder="结束日期"
              @change="selectColumnar" />
          </div>
        </div>
        <div style="display: flex;width:48%;justify-content: space-between;">
          <div style="display: flex;">
            <div class="line"></div>
            <div>各区域分项用能统计</div>
          </div>
          <div>
            <el-date-picker :clearable="false" value-format="YYYY-MM-DD HH:mm" v-model="formInline3.time"
              type="daterange" range-separator="到" start-placeholder="开始日期" end-placeholder="结束日期"
              @change="selectItemizedStatistics" />
          </div>
        </div>
      </div>
      <div class="flexClass">
        <div ref="energyConsumption" style="width: 50%;height: 400px;"></div>
        <div ref="EnergyConsumptionStatistics" style="width: 50%;height: 400px;"></div>
      </div>

      <!-- 各区域用能分析 -->
      <div style="display: flex;margin: 40px 40px 20px;">
        <div style="display: flex;justify-content: space-between;width: 100%;">
          <div style="display: flex;">
            <div class="line"></div>
            <div>各区域用能分析</div>
          </div>
          <div>
            <el-date-picker :clearable="false" value-format="YYYY-MM-DD HH:mm" v-model="formInline4.time" type="year" placeholder="请选择年份" @change="tableChange" />
          </div>
        </div>
      </div>
      <!-- 表格 -->
      <div style="display: flex;justify-content: space-between;">
        <el-table :data="state.tableData">
          <el-table-column prop="area" label="区域/月份" width="120" fixed />
          <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :label="item">
            <el-table-column :prop="'current' + index" label="本期" width="80" />
            <el-table-column :prop="'corresponding' + index" label="上年同期" width="120" />
            <el-table-column :prop="'together' + index" label="同比增长率" width="120" />
            <el-table-column :prop="'priorPeriod' + index" label="上期" width="80" />
            <el-table-column :prop="'loop' + index" label="环比增长率" width="120" />
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs';
import { areaStatisticsAPI, areaStatisticsSubentryAPI, energyUseAnalysisByRegionAPI } from "@/api/energyManagement/dateAggregateEcharts.js";
import { GraphicComponent } from 'echarts/components'

const { proxy } = getCurrentInstance()

const echarts = proxy.$echarts
echarts.use([GraphicComponent]);

onMounted(() => {
  selectDayAndMonth();
  selectRangeStatistics()
  selectColumnar()
  selectItemizedStatistics()
  selectRankingAreasEnergyConsumption()
})

onBeforeUnmount(() => {
  let chartInstances = [myChart1, myChart2,myChart3,myChart4];
  chartInstances.forEach(chartInstance => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
  });
});

const options = [
  {
    value: '水表',
    label: '水表',
  },
  {
    value: '电表',
    label: '电表',
  },
  {
    value: '气表',
    label: '气表',
  }
]

const leftMounth = ref(null)
let myChart1 = null;

const rightYear = ref(null)
let myChart2 = null;

const energyConsumption = ref(null)
let myChart3 = null;

const EnergyConsumptionStatistics = ref(null)
let myChart4 = null;

const formInline = reactive({
  equipmentType: '电表'
});

const formInline1 = reactive({
  time: [
    dayjs().startOf('month').format('YYYY-MM-DD HH:mm'),
    dayjs().format('YYYY-MM-DD HH:mm')
  ]
});

const formInline2 = reactive({
  time: [
    dayjs().startOf('month').format('YYYY-MM-DD HH:mm'),
    dayjs().format('YYYY-MM-DD HH:mm')
  ]
});

const formInline3 = reactive({
  time: [
    dayjs().startOf('month').format('YYYY-MM-DD HH:mm'),
    dayjs().format('YYYY-MM-DD HH:mm')
  ]
});

const formInline4 = reactive({
  time: dayjs().format('YYYY-MM-DD HH:mm')
});

const formInline5 = reactive({
  time: [
    dayjs().startOf('month').format('YYYY-MM-DD HH:mm'),
    dayjs().format('YYYY-MM-DD HH:mm')
  ]
});

const state = reactive({
  tableData: [],
  tableHeader: [],
})

// 各区域用能统计
const selectRangeStatistics = () => {
  if (dayjs(formInline1.time[0]).format('YYYY') != dayjs(formInline1.time[1]).format('YYYY')) {
    return ElMessage.error("查询禁止跨年");
  }

  let query = {
    equipmentType: formInline.equipmentType,
    startTime: formInline1.time[0],
    endTime: formInline1.time[1],
    type: '区域'
  }

  areaStatisticsAPI(query).then(res => {
    if (!myChart1 && leftMounth.value) {
      myChart1 = echarts.init(leftMounth.value);
    }

    myChart1.clear()

    let option1 = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: res.data.x,
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: res.data.y,
          type: 'bar'
        }
      ]
    };

    myChart1.setOption(option1);
  })

}

// 各区域用能排行
const selectRankingAreasEnergyConsumption = () => {
  if (dayjs(formInline5.time[0]).format('YYYY') != dayjs(formInline5.time[1]).format('YYYY')) {
    return ElMessage.error("查询禁止跨年");
  }

  let query = {
    equipmentType: formInline.equipmentType,
    startTime: formInline5.time[0],
    endTime: formInline5.time[1],
    type: '区域'
  }

  areaStatisticsAPI(query).then(res => {

    if (!myChart2 && rightYear.value) {
      myChart2 = echarts.init(rightYear.value);
    }

    myChart2.clear()

    let option2 = {
      xAxis: {
        max: 'dataMax'
      },
      yAxis: {
        type: 'category',
        data: res.data.x,
        inverse: true,
        animationDuration: 300,
        animationDurationUpdate: 300,
        max: 2 // only the largest 3 bars will be displayed
      },
      series: {
        realtimeSort: true,
        name: res.data.x,
        type: 'bar',
        data: res.data.y,
        label: {
          show: true,
          position: 'right',
          valueAnimation: true
        }
      },
      legend: {
        show: true
      },
      animationDuration: 0,
      animationDurationUpdate: 3000,
      animationEasing: 'linear',
      animationEasingUpdate: 'linear'
    };

    myChart2.setOption(option2);
  })

}

// 各区域用能占比
const selectColumnar = () => {
  if (dayjs(formInline2.time[0]).format('YYYY') != dayjs(formInline2.time[1]).format('YYYY')) {
    return ElMessage.error("查询禁止跨年");
  }

  let query = {
    equipmentType: formInline.equipmentType,
    startTime: formInline2.time[0],
    endTime: formInline2.time[1],
    type: '区域'
  }

  areaStatisticsAPI(query).then(res => {
    var list = []
    var tuglie = []
    for (var i = 0; i < res.data.x.length; i++) {
      list.push({ name: res.data.x[i], value: res.data.y[i] })
      tuglie.push(res.data.x[i])
    }
    let sum = 0;
    list.map((item) => {
      sum += Number(item.value);
    });
    sum = sum.toFixed(2)

    if (!myChart3 && energyConsumption.value) {
      myChart3 = echarts.init(energyConsumption.value);
    }

    myChart3.clear()

    let option3 = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      legend: {
        show: true,
        orient: 'horizontal', //图例水平对齐排列
        textStyle: {
          //图例文字的样式
          color: '#000',
          fontSize: 12
        },
        data: tuglie, //图例组件
        formatter: (name) => {
          let num = '';
          list.forEach((item) => {
            //格式化图例文本，支持字符串模板和回调函数两种形式。
            if (item.name === name) {
              num = String(item.value).replace(/(\d)(?=(?:\d{6})+$)/g, '$1.');
              return;
            }
          });
          return name + '：' + num;
        }
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text:
            '总计' + //圆饼中心显示数据，这里是显示得总数
            '\n' +
            String(sum).replace(/(\d)(?=(?:\d{6})+$)/g, '$1.'),
          textAlign: 'center',
          fill: '#000',
          width: 30,
          height: 30,
          fontSize: 12
        }
      },
      series: [
        {
          type: 'pie',
          radius: ['35%', '65%'],
          itemStyle: {
            normal: {
              label: {
                show: true,
                textStyle: { color: '#3c4858', fontSize: '12' },
                formatter: function (val) {
                  //让series 中的文字进行换行
                  return val.name + '\n(' + val.percent + '%)';
                }
              },
              labelLine: {
                show: true,
                lineStyle: { color: '#3c4858' }
              }
            },
            emphasis: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              textColor: '#000'
            }
          },
          data: list //数据
        }
      ]
    };

    myChart3.setOption(option3);
  })
}

// 各区域分项用能统计
const selectItemizedStatistics = () => {
  if (dayjs(formInline3.time[0]).format('YYYY') != dayjs(formInline3.time[1]).format('YYYY')) {
    return ElMessage.error("查询禁止跨年");
  }

  let query = {
    equipmentType: formInline.equipmentType,
    startTime: formInline3.time[0],
    endTime: formInline3.time[1]
  }

  areaStatisticsSubentryAPI(query).then(res => {
    var list = [];
    res.data.y.forEach(e => {
      list.push({
        name: e.name,
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: e.value
      })
    })

    if (!myChart4 && EnergyConsumptionStatistics.value) {
      myChart4 = echarts.init(EnergyConsumptionStatistics.value);
    }

    myChart4.clear()

    let option4 = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // Use axis to trigger tooltip
          type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
        }
      },
      legend: {},
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      yAxis: {
        type: 'value'
      },
      xAxis: {
        type: 'category',
        data: res.data.x
      },
      series: list
    };

    myChart4.setOption(option4);
  })
}

// 各区域用能分析
const selectDayAndMonth = () => {
  let query = {
    equipmentType: formInline.equipmentType,
    time:formInline4.time,
    type: '区域'
  }
  energyUseAnalysisByRegionAPI(query).then(res => {
    state.tableHeader = res.data.tableName
    state.tableData = res.data.tableVale
  })
}

const change = () => {
  selectDayAndMonth();
  selectRangeStatistics()
  selectColumnar()
  selectItemizedStatistics()
  selectRankingAreasEnergyConsumption();
}

const tableChange = () => {
  selectDayAndMonth()
}
</script>

<style lang='less' scoped>
.page {
  overflow: scroll;
  height: 100%;
  display: flex;
  flex-direction: column;

  .conent {
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
  }
}


.conent {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.flexClass {
  display: flex;
  justify-content: space-around;
}

.tai {
  width: 100%;
  height: 70px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: space-between;
  padding-left: 32px;
}

.ju {
  display: flex;
  align-items: center;

  .el-form-item {
    margin-bottom: 0;
  }
}

.title {
  color: #989fc7;
}

.line {
  width: 4px;
  height: 20px;
  background-color: #5470c6;
  margin-right: 10px;
}
</style>
