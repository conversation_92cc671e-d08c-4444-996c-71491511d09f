<template>
  <div :style="{ height: '100%', overflow: 'auto' }">
    <div class="divFlex3" style="height: 25%;">
      <div class="divCard" v-for="(item, index) in state.dataList" :key="index" style="margin-right: 30px;" :style="{
        background: index == 0 ? 'linear-gradient(0deg, #FFFFFF, #ECF7F3)' : index == 1 ? 'linear-gradient(0deg, #FFFFFF, #F2F8FF)' : index == 2 ? 'linear-gradient(0deg, #FFFFFF, #FFF3EF)' : index == 3 ? 'linear-gradient(0deg, #FFFFFF, #FFF9EE)' : '',
        borderColor: index == 0 ? '#C5F9E8' : index == 1 ? '#D7E6FA' : index == 2 ? '#F9D4C7' : index == 3 ? '#F8E7C7' : ''
      }">
        <div class="head" :style="{
          borderBottom: index == 0 ? '1px solid #CDF6E5' : index == 1 ? '1px solid #CFE5FC' : index == 2 ? '1px solid #FBDCD1' : index == 3 ? '1px solid #FAEBD2' : ''
        }">
          <div class="divLeftStyle">{{ item.name }}</div>
        </div>
        <div class="lastDiv">
          <img class="imgDiv" :src="imgTransfer(item.image)" />
          <div v-for="(item1, index1) in item.list" :key="index1" style="margin-left: 10%;">
            <div class="divFlex" style="align-items: center;">
              <div style="font-size: 28px;font-weight:bolder">{{ item1.value }}</div>
              <div class="unit">&nbsp;{{ index == 3 && index1 == 0 ? '元/月' : '' }}</div>
              <div class="unit">{{ index == 3 && index1 == 1 ? '元/时' : '' }}</div>
            </div>
            <div style="font-size: 14px;color: #838383;">{{ item1.name }} </div>
          </div>
        </div>
      </div>
    </div>
    <div class="divFlex" style="margin-top: 2%;padding-bottom: 5%">
      <div class="messageDiv" v-for="(item, index) in state.carList" :key="index">
        <div style="border-radius: 10px;height: 15%;display: flex;align-items: center;justify-content: space-between;">
          <div class="divFlex1" style="align-items: center;">
            <div style="width: 5px;height: 21px;background: #0375FF;margin-top: 15px;"></div>
            <div style="color: #333333;font-size: 16px;margin-top: 15px;margin-left: 10px;">{{
              item.equipment.equipmentName }}</div>
          </div>
          <i class="iconfont icon-shexiangtou"
            style="width: 25px; height: 25px;margin-right: 4%;margin-top: 15px;cursor : pointer;"
            v-if="item.equipment.equipmentRelationList.length != 0" @click="video(item)"></i>
        </div>
        <div style="display: flex;justify-content: space-between;">
          <div class="centerDiv" style="margin-left: 8%;">
            <div>
              <img style="width: 130px; height: 130px" src="@/assets/img/car.png" />
              <div style="display: flex;width: 150px;flex-wrap: wrap;" v-if="item.equipmentAttributeList">
                <el-button style="width: 50px;margin: 5px 12px 10px 0px;"
                  v-for="(value, key, index1) in item.equipmentAttributeList.attributeValueEnum " :key="key"
                  :index="index1"
                  @click="updateStatus(key, item.equipmentAttributeList.attributeKey, item.equipment.equipmentId)">{{
                    value
                  }}</el-button>
              </div>
            </div>
          </div>
          <div class="centerDiv">
            <div v-if="item.parkingAccessRecord">
              <el-image class="imgDiv1" v-if="item.parkingAccessRecord.ioType == 'IN'"
                :src="item.parkingAccessRecord.inCarPhoto" />
              <el-image class="imgDiv1" v-if="item.parkingAccessRecord.ioType == 'OUT'"
                :src="item.parkingAccessRecord.outCarPhoto" />
            </div>
          </div>
          <div class="centerDiv" style="margin-right: 10%;">
            <el-form :model="item.parkingAccessRecord" label-width="60px" v-if="item.parkingAccessRecord">
              <el-form-item label="日期" class="itemClass">
                <div>{{ item.parkingAccessRecord.date }}</div>
              </el-form-item>
              <el-form-item label="时间" class="itemClass">
                <div>{{ item.parkingAccessRecord.time }}</div>
              </el-form-item>
              <el-form-item label="类型" class="itemClass">
                <div>{{ item.parkingAccessRecord.carType }}</div>
              </el-form-item>
              <el-form-item label="车牌" class="itemClass">
                <div>{{ item.parkingAccessRecord.carNumber }}</div>
              </el-form-item>
              <el-form-item label="颜色" class="itemClass">
                <div>{{ item.parkingAccessRecord.carColor }}</div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <el-dialog v-model="state.dialogVisible" title="预览" :width="900" :before-close="handleClose" class="dialogVideo">
      <div id="dplayer" style="width:100%;height: 100%;"></div>
    </el-dialog>
  </div>
</template>

<script setup>

import { doControllerAPI } from '@/api/iotManagement/equipManage.js';
import { perviewURLs } from '@/api/iotManagement/realtime.js'
import { info } from "@/api/iotManagement/parking.js";
import { ElMessage } from 'element-plus'
import DPlayer from 'dplayer';
import flvjs from 'flv.js'
const state = reactive({
  dialogVisible: false,
  dp: {},
  form: {
    name: ' 2023-03-07'
  },
  heightSize: 100,
  dataList: [
  ],
  carList: []
})

onMounted(() => {
  resize()
  window.addEventListener('resize', resize)
  getDate()
})

onUnmounted(() => {
  window.removeEventListener('resize', resize)
})

const imgTransfer = (name) => {
  return new URL(`/src/assets/img/${name}`, import.meta.url).href
}

const video = (item) => {
  if (item.equipment.equipmentRelationList) {
    if (item.equipment.equipmentRelationList.length > 0) {
      state.dialogVisible = true
      initViode(item.equipment.equipmentRelationList[0].relatedEquipmentId);
    } else {
      ElMessage.error('未绑定摄像头');
    }
  } else {
    ElMessage.error('未绑定摄像头');
  }

}

const handleClose = () => {
  videoDestroy();
  state.dialogVisible = false
}

const initViode = (id) => {
  nextTick(() => {
    perviewURLs({ equipmentId: id }).then((o) => {
      state.dp = new DPlayer({
        container: document.getElementById('dplayer'),
        live: true,
        autoplay: true,
        preventClickToggle: true,
        screenshot: true,
        hotkey: true,
        preload: 'auto',
        muted: true,
        video: {
          url: o.data,
          type: 'customFlv',
          customType: {
            customFlv: function (video, player) {
              state.flvPlayer = flvjs.createPlayer({
                type: 'flv',
                url: video.src,
              });
              state.flvPlayer.attachMediaElement(video);
              state.flvPlayer.load();
            },
          },
        },
      });
      state.dp.on('play', function () {
        if (state.flvPlayer.buffered.length) {
          let end = state.flvPlayer.buffered.end(0);//获取当前buffered值
          let diff = end - state.flvPlayer.currentTime;//获取buffered与currentTime的差值
          if (diff >= 2.5) {//如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定
            state.dp.seek(state.flvPlayer.buffered.end(0));
          }
        }
      });
      state.flvPlayer.on(flvjs.Events.ERROR, function (errorType, errorDetail, errorInfo) {
        videoDestroy()
      });
    });


    // dp.play()
  })
}

// 视频销毁
const videoDestroy = () => {
  if (state.flvPlayer && state.dp) {
    state.flvPlayer.pause();
    state.flvPlayer.unload();
    state.flvPlayer.detachMediaElement();
    state.flvPlayer.destroy();
    state.flvPlayer = null;
    state.dp.destroy()
  }
}

const updateStatus = (key, attributeKey, equipmentId) => {

  let attributeUpdate = {
    equipmentId: equipmentId,
    attributes: [
      {
        key: attributeKey,
        value: key
      }
    ]
  }
  doControllerAPI(attributeUpdate).then(res => {
    if (res.success) {
      ElMessage.success('操作成功！')
    } else {
      ElMessage.error('操作失败，' + res.errorMessage);
    }
  })
}

const getDate = () => {

  info().then(async res => {
    if (res.data.parkingEquipments) {
      for (let e of res.data.parkingEquipments) {
        if (e.parkingAccessRecord) {
          if (e.parkingAccessRecord.inTime) {
            e.parkingAccessRecord.date = e.parkingAccessRecord.inTime.split(" ")[0]
            e.parkingAccessRecord.time = e.parkingAccessRecord.inTime.split(" ")[1]
          }
        }
        if (e.equipment.equipmentAttributeList.length > 0) {
          if (e.equipment.equipmentAttributeList[0].attributeValueEnum) {
            e.equipment.equipmentAttributeList[0].attributeValueEnum = JSON.parse(e.equipment.equipmentAttributeList[0].attributeValueEnum)
          }
          e.equipmentAttributeList = e.equipment.equipmentAttributeList[0]
        }
      }

      state.carList = res.data.parkingEquipments
    }
    var list = [];
    if (res.data.park) {
      list.push({ name: '车位数', image: 'parkingSpace.png', list: [{ name: '总车位数', value: res.data.park.totalSpace }, { name: '剩余车位数', value: res.data.park.remainSpace }] })
      // list.push({ name: '客户', image: 'client.png', list: [{ name: '包月车', value: res.data.park.monthlyUserNum }, { name: '固定车', value: res.data.park.fixedUserNum }] })
      list.push({ name: '今日车流', image: 'todayNumber.png', list: [{ name: '进', value: res.data.park.inCount }, { name: '出', value: res.data.park.outCount }] })
      // list.push({ name: '收费标准', image: 'charge.png', list: [{ name: '包月', value: res.data.park.monthChargeStandard.replace("元/月", "") }, { name: '临时', value: res.data.park.chargeStandard.replace("元/时", "") }] })
      state.dataList = list
    } else {
      list.push({ name: '车位数', image: 'parkingSpace.png', list: [{ name: '总车位数', value: 0 }, { name: '剩余车位数', value: 0 }] })
      // list.push({ name: '客户', image: 'client.png', list: [{ name: '包月车', value: 0 }, { name: '固定车', value: 0 }] })
      list.push({ name: '今日车流', image: 'todayNumber.png', list: [{ name: '进', value: 0 }, { name: '出', value: 0 }] })
      // list.push({ name: '收费标准', image: 'charge.png', list: [{ name: '包月', value: 0 }, { name: '临时', value: 0 }] })
      state.dataList = list
    }

  })
}

const resize = () => {
  nextTick(() => {
    nextTick(() => {
      let value
      let table = document.querySelector('.pageTable')
      let pagination = document.querySelector('.el-tabs__nav-scroll')
      value = table.offsetHeight - pagination.offsetHeight

      state.heightSize = value
    })
  })
}
defineExpose({
  getDate
})
</script>

<style lang='less' scoped>
.centerDiv {
  display: flex;
  align-items: center;
  justify-content: center;
}

.marginTopDiv {
  margin-top: 10%;
}

.divCard {
  border-radius: 8px;
  width: 23%;
  border-width: 2px;
  border-style: solid;
  // border-color: #D7E6FA;
}

.head {
  // border-bottom: 1px solid #D7E6FA;
  height: 30%;
  display: flex;
  align-items: center;
}

.iconStyle {
  margin-left: 7%;
}

.divLeftStyle {
  margin-left: 4%;
  height: 16px;
  font-size: 16px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #333333;
}

.divFlex {
  justify-content: space-between;
  display: flex;
  flex-flow: row wrap;
  // max-height: 462px;
  overflow-y: auto;
}

.divFlex1 {
  // justify-content: space-between;
  display: flex;
  // flex-flow: row wrap;
  // max-height: 462px;
  // overflow-y: auto;
}

.divFlex3 {
  display: flex;
  justify-content: center;
}

.messageDiv {
  background: linear-gradient(5deg, #FFFFFF, #F2F8FF);
  width: 49%;
  height: 300px;
  margin-bottom: 20px;
  border-width: 2px;
  border-style: solid;
  border-color: #D7E6FA;
  border-radius: 10px;
}


.itemClass {
  background-color: #E4F0FD;
  width: 170px;
  border-radius: 2px;
}

.unit {
  font-size: 16px;
  color: #838383;
}

.lastDiv {
  margin-top: 10%;
  margin-left: 5%;
  display: flex;
}

.imgDiv {
  width: 60px;
  height: 60px;
  margin-right: 2%;
}

.imgDiv1 {
  width: 220px;
  height: 220px
}
</style>
