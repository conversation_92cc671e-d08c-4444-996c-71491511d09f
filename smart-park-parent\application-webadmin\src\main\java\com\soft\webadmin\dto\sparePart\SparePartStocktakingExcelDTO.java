package com.soft.webadmin.dto.sparePart;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * SparePartStocktakingExcelDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@ApiModel("SparePartStocktakingExcelDTO对象")
@Data
@ColumnWidth(value = 15)
public class SparePartStocktakingExcelDTO {

    @ExcelProperty(value = "*盘点仓库")
    private String storehouseName;

    @ExcelProperty(value = "备件分类")
    private String classifyName;

    @ExcelProperty(value = "备件名称")
    private String sparePartName;

    @ExcelProperty(value = "*备件编号")
    private String sparePartNo;

    @ExcelProperty(value = "规格型号")
    private String model;

    // @ExcelProperty(value = "条码")
    // private String barCode;

    @ExcelProperty(value = "单位")
    private String unit;

    @ExcelProperty(value = "单价（元）")
    private BigDecimal unitPrice;

    @ExcelProperty(value = "*盘点数量")
    private Integer stocktakingQuantity;

    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "*盘点单名称")
    private String stocktakingName;

    @ExcelProperty(value = "*盘点开始时间")
    @ColumnWidth(value = 18)
    private Date beginTime;

    @ExcelProperty(value = "*盘点结束时间")
    @ColumnWidth(value = 18)
    private Date endTime;

    @ExcelProperty(value = "*负责人")
    private String headUserName;

}
