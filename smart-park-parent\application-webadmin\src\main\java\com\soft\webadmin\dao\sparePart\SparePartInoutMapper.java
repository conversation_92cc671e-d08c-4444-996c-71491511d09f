package com.soft.webadmin.dao.sparePart;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.sparePart.SparePartInoutQueryDTO;
import com.soft.webadmin.model.sparePart.SparePartInout;
import com.soft.webadmin.vo.sparePart.SparePartInoutVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备品备件入库Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface SparePartInoutMapper extends BaseMapper<SparePartInout> {

    List<SparePartInoutVO> queryList(SparePartInoutQueryDTO queryDTO);

    SparePartInoutVO detail(@Param("id") Long id);

}
