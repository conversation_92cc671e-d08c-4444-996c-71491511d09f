package com.soft.webadmin.listener.debezium;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.enums.PostEnums;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.service.SysPostService;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.debezium.ChangeListenerModel;
import com.soft.common.debezium.event.DebeziumBaseEvent;
import com.soft.common.debezium.event.WorkOrderEvent;
import com.soft.sub.model.message.MessageRecord;
import com.soft.sub.model.message.MessageRecordContentFacade;
import com.soft.sub.model.message.MessageRecordContentNoticeWorkOrderTemplate;
import com.soft.sub.service.message.MessageRecordService;
import com.soft.webadmin.dao.check.CheckRecordMapper;
import com.soft.webadmin.enums.WorkOrderStateEnums;
import com.soft.webadmin.enums.WorkOrderTypeEnums;
import com.soft.webadmin.model.check.CheckRecord;
import com.soft.webadmin.model.check.CheckRepairLog;
import com.soft.webadmin.model.check.WorkOrder;
import com.soft.webadmin.service.check.CheckRepairLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName WorkOrderListener
 * @description: 工单通知
 * @date 2024年07月22日
 */
@Slf4j
@Service
public class WorkOrderListener {

    @Autowired
    private CheckRecordMapper checkRecordMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private MessageRecordService messageRecordService;

    @Autowired
    private CheckRepairLogService checkRepairLogService;

    @Autowired
    private SysPostService sysPostService;

    @Async("applicationTaskExecutor")
    @EventListener
    public void handleEvent(DebeziumBaseEvent<WorkOrderEvent> baseEvent) {
        ChangeListenerModel model = BeanUtil.toBean(baseEvent.getData(), ChangeListenerModel.class);
        Integer eventType = model.getEventType();

        WorkOrder currentEntity = JSON.parseObject(model.getData(), WorkOrder.class);
        WorkOrder beforeEntity = JSON.parseObject(model.getBeforeData(), WorkOrder.class);
        try {
            Long orderId = currentEntity.getId();
            String orderNo = currentEntity.getOrderNo();
            Integer priority = currentEntity.getPriority();
            String level = "普通";
            if (priority == 2) {
                level = "紧急";
            } else if (priority == 3) {
                level = "特急";
            }
            WorkOrderTypeEnums orderType = currentEntity.getOrderType();
            String orderTypeName = WorkOrderTypeEnums.getWorkOrderTypeName(orderType);
            Integer state = currentEntity.getState();
            Long workUserId = currentEntity.getWorkUserId();
            Long createUserId = currentEntity.getCreateUserId();
            MessageRecordContentNoticeWorkOrderTemplate workOrderMessageTemplate = MessageRecordContentFacade.NOTICE.WORK_ORDER();
            switch (eventType) {
                case 1:
                    if (workUserId == null || state == WorkOrderStateEnums.NO_ALLOT.getValue()) {
                        return;
                    }

                    if (orderType == WorkOrderTypeEnums.REPAIR) {
                        // 维修工单
                        workOrderMessageTemplate.pushOrderOfReport(orderTypeName, orderNo, orderId, level, createUserId, workUserId);

                    } else if (orderType == WorkOrderTypeEnums.MAINTENANCE || orderType == WorkOrderTypeEnums.PATROL_INSPECTION) {
                        // 维保工单&巡检工单
                        Long businessId = currentEntity.getBusinessId();
                        Date startTime = null;
                        CheckRecord checkRecord = checkRecordMapper.selectById(businessId);
                        if (checkRecord.getStartTime() != null) {
                            startTime = checkRecord.getStartTime();
                        }
                        workOrderMessageTemplate.pushOrderOfPlan(orderTypeName, orderNo, startTime, orderId, level, null, workUserId);

                    }
                    break;
                case 2:
                    Long updateUserId = currentEntity.getUpdateUserId();
                    if (state != beforeEntity.getState()) {
                        if (state == WorkOrderStateEnums.NO_RESPONSE.getValue() && workUserId != null) {
                            // 工单派单
                            // 执行人收到通知
                            workOrderMessageTemplate.pushOrderOfReport(orderTypeName, orderNo, orderId, level, updateUserId, workUserId);

                        } else if (state == WorkOrderStateEnums.FINISH.getValue()) {
                            // 工单完成
                            // 工单上报人收到通知
                            if (currentEntity.getBusinessId() != null && currentEntity.getBusinessTable().equals(MyModelUtil.mapToTableName(CheckRepairLog.class))) {
                                CheckRepairLog checkRepairLog = checkRepairLogService.getById(currentEntity.getBusinessId());
                                workOrderMessageTemplate.pushComplete(orderTypeName, orderNo, orderId, level, updateUserId, checkRepairLog.getReportUserId());
                            }

                        } else if (state == WorkOrderStateEnums.NO_ALLOT.getValue() && beforeEntity.getState() == WorkOrderStateEnums.HANDING.getValue()) {
                            // 工单退回
                            // 把该用户有关该工单的消息链接清空，则他将不会再通过消息进入工单详情页
                            List<MessageRecord> messageRecordList = messageRecordService.list(
                                    new LambdaQueryWrapper<MessageRecord>().eq(MessageRecord::getReceiveUserId, updateUserId)
                                            .eq(MessageRecord::getBusiId, orderId)
                            );
                            if (CollUtil.isNotEmpty(messageRecordList)) {
                                List<MessageRecord> updateMessageRecordList = messageRecordList.stream().map(messageRecord -> {
                                    MessageRecord message = new MessageRecord();
                                    message.setId(messageRecord.getId());
                                    message.setHyperlink("");
                                    return message;
                                }).collect(Collectors.toList());
                                messageRecordService.updateBatchById(updateMessageRecordList);
                            }

                            // 岗位为【运维管理员】的用户收到通知
                            List<Long> userIds = sysPostService.getUserIdsByPostCode(PostEnums.OPERATION_ADMIN.getCode());
                            if (CollUtil.isNotEmpty(userIds)) {
                                SysUser sysUser = sysUserMapper.selectById(updateUserId);
                                String userName = "";
                                if (sysUser != null) {
                                    userName = sysUser.getShowName();
                                }
                                workOrderMessageTemplate.pushBack(userName, orderNo, orderId, level, updateUserId, userIds);
                            }
                        }
                    }

                    // 工单评价
                    if (currentEntity.getScore() != null && currentEntity.getScore() != beforeEntity.getScore()) {
                        workOrderMessageTemplate.pushReview(orderTypeName, orderNo, currentEntity.getScore(), orderId, level, updateUserId, workUserId);
                    }

                    break;
            }
        } catch (Exception e) {
            log.error("WorkOrderListener.onMessage error", e);
        }
    }

}
