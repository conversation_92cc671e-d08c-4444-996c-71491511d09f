<template>
  <page-common v-model="state.tableHeight" :operate-bool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="orderNo">
          <el-input v-model="formInline.orderNo" placeholder="工单编号"/>
        </el-form-item>
        <el-form-item prop="workUserName" v-show="type === 'all'">
          <el-input v-model="formInline.workUserName" placeholder="执行人"/>
        </el-form-item>
        <el-form-item prop="orderType">
          <el-select v-model="formInline.orderType" clearable placeholder="工单类别">
            <el-option v-for="(val, key) in state.orderTypeOptions" :value="key" :label="val"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="state">
          <el-select v-model="formInline.state" clearable placeholder="工单状态">
            <el-option v-for="(val, key) in orderStateOptions" :value="key" :label="val"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="priority">
          <el-select v-model="formInline.priority" clearable placeholder="优先级">
            <el-option v-for="(val, key) in state.priorityOptions" :value="key" :label="val"/>
          </el-select>
        </el-form-item>
<!--        <el-form-item prop="businessType">-->
<!--          <el-select v-model="formInline.businessType" clearable placeholder="业务类型">-->
<!--            <el-option v-for="(val, key) in state.businessTypeOptions" :value="key" :label="val"/>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item prop="daterange">
          <el-date-picker
              v-model="formInline.daterange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column
            show-overflow-tooltip
            v-for="(item, index) in state.tableHeader"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            :formatter="item.formatter"
            :className="item.className"
            />
        <el-table-column label="操作" align="center" width="80">
          <template #default="scope">
            <el-button link icon="Tickets" type="primary" @click="viewHandle(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :total="state.pagetion.total"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
    </template>
  </page-common>
</template>

<script setup>
import { getWorkOrderPageAPI, getMyWorkOrderPageAPI, getWorkOrderDetailAPI } from '@/api/operationManagement/workOrder.js';
import { ElButton, ElTag } from 'element-plus';

import orderStatus from "@/views/operationManagement/workOrder/component/orderStatus.vue";

const emit = defineEmits(['showPage'])

const props = defineProps({
  type: {
    type: String,
    default: ''
  }
})

let {type} = toRefs(props);

const modal = ref();
const drawer = ref();
const formInlineRef = ref();
const formInline = reactive({});

const state = reactive({
  tableData: [],
  tableHeight: 100,
  tableHeader: [
    {
      prop: 'orderNo',
      label: '工单编号',
      width: 210,
      className: 'code-font'
    },
    // {
    //   prop: 'businessType',
    //   label: '业务类型',
    //   formatter: (row, column, cellValue) => {
    //     return state.businessTypeOptions[cellValue]
    //   }
    // },
    {
      prop: 'orderType',
      label: '工单类别',
      formatter: (row, column, cellValue) => {
        return state.orderTypeOptions[cellValue]
      }
    },
    {
      prop: 'state',
      label: '工单状态',
      formatter: (row, column, cellValue) => {
        return h('div', [h(orderStatus,{status: cellValue,style: {marginRight: '5px'}}), row.timeout ?  h(ElTag, {type: 'danger'}, {default: () => '超时' + row.timeout + '小时'}) : '' ]);
      }
    },
    {
      prop: 'workUserName',
      label: '执行人',
      formatter: (row, column, cellValue) => {
        return cellValue;
      }
    },
    {
      prop: 'priority',
      label: '优先级',
      formatter: (row, column, cellValue) => {
        if (cellValue === 1) {
          return h(ElTag, {type: 'primary'}, {default: () => state.priorityOptions[cellValue]});
        } else if (cellValue === 2) {
          return h(ElTag, {type: 'warning'}, {default: () => state.priorityOptions[cellValue]});
        } else {
          return h(ElTag, {type: 'danger'}, {default: () => state.priorityOptions[cellValue]});
        }
      }
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160
    },
  ],
  priorityOptions: {
    1: '普通',
    2: '紧急',
    3: '特急',
  },
  orderTypeOptions: {
    'PATROL_INSPECTION': '巡检工单',
    'MAINTENANCE': '维保工单',
    'REPAIR': '维修工单',
    // 'CLEANING': '保洁工单',
    // 'CLEANING_TEMP': '临时保洁',
    // 'TRANSPORT_LOOP': '循环运送',
    // 'TRANSPORT_TEMP': '临时运送'
  },
  businessTypeOptions: {
    OPERATIONS: '运维',
    CLEANING: '保洁',
    TRANSPORT: '运送',
    PROPERTY: '房产',
  },
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
});

onMounted(() => {
  getList();
});

const orderStateOptions = computed(() => {
  return type.value == 'all' ? {
    1: '待派单',
    2: '未响应',
    3: '处理中',
    4: '已关闭',
    5: '已完成',
  } : {
    2: '未响应',
    3: '处理中',
    5: '已完成',
  }
})

const getList = () => {
  let query = {
    ...formInline,
    ...state.pagetion
  };
  if (formInline.daterange) {
    query.beginDate = formInline.daterange[0];
    query.endDate = formInline.daterange[1];
  }
  if (type.value === 'all') {
    // 全部工单
    getWorkOrderPageAPI(query).then((res) => {
      state.tableData = res.data.dataList;
      state.pagetion.total = res.data.totalCount;
    });
  } else {
    // 我的工单
    getMyWorkOrderPageAPI(query).then((res) => {
      state.tableData = res.data.dataList;
      state.pagetion.total = res.data.totalCount;
    });
  }
};

const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};

// 详情
const viewHandle = (row) => {
  if (row.orderType === 'REPAIR') {
    // 维修工单&临时保洁
    emit('showPage', 3, '工单详情', row.id);
  }else {
    // 维保工单&巡检工单&保洁工单
    emit('showPage', 2, '工单详情', row.id);
  }
};

defineExpose({
  getList
})
</script>
<style lang="less" scoped>
:deep(.status-circle) {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
  margin-bottom: 1px;
}
</style>
