import { request } from '@/utils/request';

// 分页查询
export const getTemplatePageAPI = (data) => {
  return request('get', '/check/template/list', data, 'F');
};

// 保存
export const saveTemplateAPI = (data) => {
  return request('post', '/check/template/saveOrUpdate', data);
};

// 删除
export const deleteTemplateAPI = (data) => {
  return request('post', '/check/template/delete', data, 'F');
};

// 查询检查项目
export const getTemplateItemListAPI = (data) => {
  return request('get', '/check/template/item/list', data, 'F');
};
