package com.soft.webadmin.model.contingency;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import com.soft.webadmin.vo.contingency.EventVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 应急事件对象 cm_event
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cm_event")
public class Event extends BaseModel {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 事件名称 */
    private String name;

    /** 事件等级：1普通、2重要、3严重 */
    private Integer level;

    /** 备注 */
    private String remark;

    /** 图片 */
    private String img;

    /** 删除标记(1: 正常 -1: 已删除) */
    @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;


    @Mapper
    public interface EventModelMapper extends BaseModelMapper<EventVO, Event> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        Event toModel(EventVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        EventVO fromModel(Event entity);
    }

    public static final EventModelMapper INSTANCE = Mappers.getMapper(EventModelMapper.class);
}
