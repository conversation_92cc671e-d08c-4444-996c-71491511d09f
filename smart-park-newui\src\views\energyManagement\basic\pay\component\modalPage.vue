<template>
  <dialog-common ref="dialog" title="提醒设置" @submit="submit" :formRef="ruleFormRef" :width="500">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="130px" label-suffix=":">
      <el-row>
        <el-col>
          <el-form-item label="预警值(低于)" prop="warningValue">
            <el-input v-model="form.warningValue" type="number" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="通知人" prop="notifierUserId">
            <el-select v-model="form.notifierUserId" class="m-2" placeholder="请选择" filterable>
              <el-option v-for="item in state.userList" :key="item.userId" :label="item.showName" :value="item.userId" />
            </el-select>
          </el-form-item>
          <el-form-item label="通知" prop="status">
            <el-switch v-model="form.status" active-text="启用" inactive-text="关闭" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { selectWarningAPI, setWarningAPI } from '@/api/energyManagement/pay.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getPageAPI } from "@/api/settingSystem/user.js";

const emit = defineEmits(['submit']);
const dialog = ref();
const ruleFormRef = ref();
const form = ref({});
const state = reactive({
  rules: {
    warningValue: [{ required: true, message: '请输入预警值(低于)', trigger: 'blur' }],
    notifierUserId: [{ required: true, message: '请选择通知人', trigger: 'blur' }],
  },
});

const open = () => {
  dialog.value.open();
  const sysUserDtoFilter = {};
  getPageAPI({ sysUserDtoFilter }).then((res) => {
    state.userList = res.data.dataList;
    selectWarningAPI().then(res => {
      if (res.data) {
        form.value = res.data
      }
    })
  });
};

/** 保存 */
const submit = () => {
  console.log(form.value.status);
  var item = {}
  item.id = form.value.id
  item.notifierUserId = form.value.notifierUserId
  item.phone = form.value.phone
  item.warningValue = form.value.warningValue
  item.status = form.value.status
  setWarningAPI(item).then((res) => {
    if (res.success) {
      ElMessage.success('设置成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

defineExpose({
  form,
  open,
});
</script>
