<template>
  <dialog-common ref="dialog" title="选择物资" @submit="submit" @onClose="onClose" :width="1150">
    <el-form :inline="true" ref="formInlineRef" :model="formInline">
      <el-form-item prop="nameOrNo">
        <el-input v-model="formInline.nameOrNo" placeholder="备件名称/编号" style="width: 100%;"/>
      </el-form-item>
      <el-form-item prop="classifyId">
        <el-tree-select v-model="formInline.classifyId" :data="state.classifyIdsOptions"
                        :render-after-expand="false"
                        node-key="id" :props="{label: 'classifyName'}" placeholder="备件分类" check-strictly clearable/>
      </el-form-item>
      <el-form-item prop="storehouseId">
        <el-select v-model="formInline.storehouseId" filterable clearable placeholder="所在仓库">
          <el-option v-for="item in state.storehouseOptions" :label="item.storehouseName" :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
        <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table ref="table" :data="state.tableData" height="40vh" row-key="id" @selection-change="handleSelectionChange"
              @row-click="rowClick">
      <el-table-column type="selection" width="55"/>
      <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                       :align="item.align" :formatter="item.formatter"/>
    </el-table>
    <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                   :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize"
                   :total="state.pagetion.total"
                   @size-change="sizeChange" @current-change="currentChange"/>
    <div class="accessories">
      <template v-for="(item, index) in state.list" :key="item.id">
        <el-tag class="mx-1" closable @close="closeTagHandle(item)">{{ item.sparePartName }}</el-tag>
      </template>
    </div>
  </dialog-common>
</template>

<script setup>
import {getCategoryTreeAPI} from "@/api/operationManagement/metarialCategory.js";
import {getStorePageAPI} from '@/api/operationManagement/storeManagement.js'
import {accessoriesListAPI} from '@/api/operationManagement/workOrder.js';

const props = defineProps({
  laborCost: {
    type: Number,
    default: 0
  },
  storehouseState:{ // 仓库状态
    type: Number,
    default: 1
  },
  hasInventory:{ // 是否存在库存
    type: Number,
    default: -1
  }
})

const emit = defineEmits(['submit']);

const dialog = ref()
const formInlineRef = ref()
const table = ref()

const formInline = reactive({})
const state = reactive({
  storehouseOptions: [], //仓库
  list: [], // 配品配件
  currrentList: [], // 当前配品
  tableData: [],
  tableHeader: [
    {
      prop: 'sparePartName',
      label: '备件名称'
    },
    {
      prop: 'sparePartNo',
      label: '备件编号'
    },
    {
      prop: 'classifyName',
      label: '备件分类'
    },
    {
      prop: 'model',
      label: '规格型号'
    },
    {
      prop: 'unit',
      label: '单位'
    },
    {
      prop: 'inventoryQuantity',
      label: '库存数量'
    },
    {
      prop: 'storehouseName',
      label: '仓库名称'
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
});

const open = () => {
  dialog.value.open();
  getList()
  getCategoryTree()
  getStorePage()
}

// 获取分类树形
const getCategoryTree = () => {
  getCategoryTreeAPI().then(res => {
    state.classifyIdsOptions = res.data
  })
}

// 获取仓库
const getStorePage = () => {
  getStorePageAPI({state: props.storehouseState, businessType: 'OPERATIONS'}).then(res => {
    console.log(res.data.dataList)
    state.storehouseOptions = res.data.dataList
  })
}

// 获取配品配件
const getList = () => {
  let query = {
    ...formInline,
    businessType: 'OPERATIONS',
    storehouseState: props.storehouseState,
    hasInventory: props.hasInventory,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
  }
  accessoriesListAPI(query).then(res => {
    state.currrentList = []

    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1


    nextTick(() => {  // 赋值勾选
      state.tableData.forEach(item => {
        if (state.list.findIndex(i => i.id == item.id) != -1) {
          table.value.toggleRowSelection(item, true)
        }
      })
    })
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 勾选
const handleSelectionChange = (selection) => {
  if (selection.length > state.currrentList.length) {
    // 去重
    state.list = [...state.list, ...selection].filter((item, index) => {
      return [...state.list, ...selection].findIndex(i => i.id == item.id) == index
    })
  } else {
    let deleteList = state.currrentList.filter(item => selection.findIndex(i => i.id == item.id) == -1)
    state.list = state.list.filter((item, index) => {
      return deleteList.findIndex(i => i.id == item.id) == -1
    })
  }

  // 当前页勾选
  state.currrentList = selection
}

// 行点击
const rowClick = (row) => {
  table.value.toggleRowSelection(row, state.currrentList.findIndex(item => item.id == row.id) == -1)
}

// 删除选择
const closeTagHandle = (info) => {
  state.list = state.list.filter(item => {
    return item.id != info.id
  })
  if (state.currrentList.findIndex(item => item.id == info.id) != -1) {
    table.value.toggleRowSelection(state.currrentList.find(item => item.id == info.id), false)
  }
}

// 关闭弹框
const onClose = () => {
  state.list = []
  state.tableData = []
}

// 提交报价
const submit = () => {
  emit('submit', state.list)
  dialog.value.close()
}

defineExpose({
  open
});
</script>

<style lang="less" scoped>
.accessories {
  flex: 1;
  max-height: 140px;
  overflow: auto;
  border-radius: 5px;
  margin-right: 10px;

  .mx-1 {
    margin: 3px;
  }
}
</style>
