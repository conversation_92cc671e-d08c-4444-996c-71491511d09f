package com.soft.webadmin.dao.shifts;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.shifts.ShiftsRosterQueryDTO;
import com.soft.webadmin.model.shifts.ShiftsRoster;
import com.soft.webadmin.vo.shifts.ShiftsRosterVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人员花名册Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface ShiftsRosterMapper extends BaseMapper<ShiftsRoster> {
    List<ShiftsRosterVO> queryList(ShiftsRosterQueryDTO queryDTO);

    /**
     * 批量插入
     * @param rosterList
     */
    void batchInsert(List<ShiftsRoster> rosterList);

    /**
     * 获取花名册
     * @param showName
     * @param deptName
     * @param businessType
     * @return
     */
    ShiftsRoster getRoster(@Param("showName") String showName,@Param("deptName") String deptName,@Param("businessType") String businessType);
}
