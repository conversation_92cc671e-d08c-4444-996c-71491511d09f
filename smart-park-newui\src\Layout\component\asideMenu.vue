<template>
  <div class="asideMenu" >
    <el-menu :default-active="activeMenuItem" class="el-menu-vertical-demo" :collapse="isCollapse"  :unique-opened="true"
      @select="handleSelect"  @open="handleOpen" :collapse-transition="false">
      <menu-item :menuList="itemMenu"></menu-item>
    </el-menu>
  </div>
</template>

<script setup>
import {inject, ref} from 'vue'
import {menuStore} from '@/store/modules/menu.js'

import { hikLogin } from '@/api/login.js'

let menu = menuStore()
let {isCollapse,itemMenu,activeMenuItem} = storeToRefs(menu)

const router = useRouter()
const route = useRoute()

const reload = inject('reload')

const handleSelect = async (key) =>{
  if(key == '海康管理平台'){
    let {data} = await hikLogin()
    window.open(data, 'foo', 'noopener=yes,noreferrer=yes')
  }else if(key.includes('http') ){
    window.open(key, 'foo', 'noopener=yes,noreferrer=yes')
  }else if(route.fullPath.includes(key)){
    reload()
  } else{
    router.push('/' + key)
  }
}

const handleOpen = (index) => {
  let menuList = itemMenu.value.find(item => item.menuId == index)
  menuList?.children && automaticallySkip(menuList.children)
}

// 自动跳转
const automaticallySkip = (menuList) => {
  let path = ''
  function deep(arrMenu) {
    return (arrMenu || []).some(i => {
      if (i.path == route.fullPath.slice(1,)) {
        return true
      } else if(i.path && !path){
        path = i.path
      } else {
        if (i.children) {
          return deep(i.children)
        }
      }
    })
  }

  if(!deep(menuList) && path){
    router.push('/' + path)
  }
}

watch(itemMenu,(newVal) => {
  automaticallySkip(newVal)
})
</script>

<style lang='less' scoped>
.asideMenu{
  height: 100%;
}
</style>
