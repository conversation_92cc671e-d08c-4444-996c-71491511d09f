<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.sparePart.SparePartStocktakingMapper">
    <resultMap type="com.soft.webadmin.model.sparePart.SparePartStocktaking" id="SparePartStocktakingResult">
        <result property="id" column="id" />
        <result property="businessType" column="business_type" />
        <result property="stocktakingNo" column="stocktaking_no" />
        <result property="stocktakingName" column="stocktaking_name" />
        <result property="beginTime" column="begin_time" />
        <result property="endTime" column="end_time" />
        <result property="headUserId" column="head_user_id" />
        <result property="storehouseId" column="storehouse_id" />
        <result property="classifyIds" column="classify_ids" />
        <result property="classifyNames" column="classify_names" />
        <result property="rule" column="rule" />
        <result property="result" column="result" />
        <result property="remark" column="remark" />
        <result property="intoState" column="into_state" />
        <result property="outState" column="out_state" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectSparePartStocktakingVo">
        select id, business_type, stocktaking_no, stocktaking_name, begin_time, end_time, head_user_id, storehouse_id, classify_ids, classify_names, rule, result, remark, into_state, out_state, deleted_flag, create_user_id, create_time, update_user_id, update_time from sp_spare_part_stocktaking
    </sql>
    
</mapper>