<template>
  <div class="toolBar">
    <div class="toolBar-left">
      <img src="/assets/img/logo.png" class="logo">
    </div>
    <div class="toolBar-right">
      <el-button type="primary" @click="jsonHandle">JSON</el-button>
      <el-button type="primary" @click="preViewHandle">预览</el-button>
      <el-button @click="saveHandle">保存</el-button>
    </div>
  </div>
  <json-data ref="josn"></json-data>
</template>

<script setup>
import jsonData from './preview/jsonData.vue';

import { events } from '@/utils/bus.js'

import { webtopoStore } from '@/store/modules/webtopo.js'
const webtopo = webtopoStore()

let { canvasStyle } = storeToRefs(webtopo)

let josn = ref()

const state = reactive({})

// 查看json数据
const jsonHandle = () => {
  josn.value.open()
}

// 预览模式
const preViewHandle = () => {
  window.open('/webtopoDraw', '_blank')
}

// 保存当前画布数据
const saveHandle = () => {
  events.emit('saveHandle')
}
</script>

<style lang='less' scoped>
.toolBar{
  height: 60px;
  padding: 0 20px;
  border-bottom: 2px solid #E1E2E4;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .logo {
    width: 180;
    max-height: 60px;
    margin-right: 50px;
    vertical-align: middle;
  }
  .toolItem{
    display: inline-block;
    .el-icon{
      font-size: 18px;
      margin-right: 12px;
      :deep(path){
        fill: #4D4D4D;
      }
    }
    .el-icon:hover{
      transform: scale(1.2);
      :deep(path){
        fill: #409EFF;
      }
    }
    .line{
      display: inline-block;
      width: 1px;
      height: 18px;
      margin-right: 12px;
      background-color: #E6E5E5;
    }
  }
}
</style>
