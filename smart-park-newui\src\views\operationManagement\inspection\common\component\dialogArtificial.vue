<template>
  <dialog-common ref="dialog" title="检查项" :width="900" :showButton="false">
    <div class="divFlex">
      <div class="divLeft"></div>
      <div class="divRight">检查信息</div>
    </div>
    <el-table  :data="data.itemVOList" row-key="menuId" style="margin-top: 10px">
      <el-table-column type="index" width="100" label="序号" />
      <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
        :align="item.align" :formatter="item.formatter" />
    </el-table>
    <div class="remark">
      <span>备注：</span>
      <span style="color: #4D5353;">{{ data.remark }}</span>
    </div>
    <div class="attach">
      <span class="title">附件：</span>
      <img-video :list="data.imgList"></img-video>
    </div>
  </dialog-common>
</template>

<script setup>
import { ElTag } from 'element-plus'

const dialog = ref()
const data = ref({
  itemVOList:[]
})

const state = reactive({
  tableHeight:'40vh',
  tableHeader:[
    {
      prop: 'itemName',
      label: '检查项'
    },
    {
      prop: 'itemContent',
      label: '检查内容'
    },
    {
      prop: 'itemResult',
      label: '检查结果',
      formatter: (row, column, cellValue) => {
        return cellValue && h(ElTag, { type: state.resultObj[cellValue]?.type }, { default: () => state.resultObj[cellValue]?.text })
      }
    }
  ],
  resultObj:{
    1:{
      text:'正常',
      type:'success'
    },
    2:{
      text:'异常',
      type:'danger'
    }
  }
})

const open = (info) => {
  dialog.value.open()
  data.value = info
}

defineExpose({
  open
})
</script>

<style lang='less' scoped>
.remark {
  margin: 30px 0;
  color: #9DA3A3;
  margin-left: 15px;
}

.attach {
  display: flex;
  color: #9DA3A3;
  margin-left: 15px;

  .title {
    flex-shrink: 0;
  }
}
</style>
