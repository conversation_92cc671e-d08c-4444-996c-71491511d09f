<template>
  <div style="height: 100%">
    <tree-page-common v-model="state.tableHeight">
      <template #leftTree>
        <el-row justify="space-between" align="middle">
          <strong>公司知识库</strong>
          <el-button type="primary" @click="addDialog" icon="Plus"
            >新建</el-button
          >
        </el-row>
        <el-tree
          ref="treeRef"
          :data="state.treeData"
          :expand-on-click-node="false"
          :props="defaultProps"
          default-expand-all
          node-key="id"
          show-checkbox
          style="margin-top: 10px; position: relative"
          @change="handleCheckChange"
        >
          <template #default="{ node, data }">
            <el-row justify="space-between" style="width: 100%">
              <div>
                {{ node.label }}
                <el-icon @click="edit(node, data)">
                  <Edit />
                </el-icon>
              </div>
              <div>
                <el-icon @click="remove(node, data)">
                  <Close />
                </el-icon>
              </div>
            </el-row>
          </template>
        </el-tree>
      </template>
      <template #query>
        <el-form
          ref="formInlineRef"
          :inline="true"
          :model="formInline"
          label-suffix=":"
        >
          <el-form-item prop="name">
            <el-input v-model="formInline.name" placeholder="文件名称" />
          </el-form-item>
          <el-form-item>
            <el-button icon="Search" type="primary" @click="onSubmit"
              >查询</el-button
            >
            <el-button icon="Refresh" type="primary" @click="onReset"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </template>
      <template #operate>
        <el-button type="primary" @click="addDialogFile" icon="Plus"
          >添加</el-button
        >
      </template>
      <template #table>
        <el-table
          :data="state.tableData"
          :height="state.tableHeight"
          show-overflow-tooltip
        >
          <el-table-column
            v-for="(item, index) in state.tableHeader"
            :key="index"
            :align="item.align"
            :formatter="item.formatter"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
          />
          <el-table-column align="center" label="操作" width="240">
            <template #default="scope">
              <el-button
                icon="Edit"
                link
                type="primary"
                @click="updateDialogFile(scope.row)"
                >编辑</el-button
              >
              <el-button
                icon="Document"
                link
                type="primary"
                @click="download(scope.row)"
                >下载</el-button
              >
              <el-button
                icon="Delete"
                link
                type="primary"
                @click="del(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="state.pagetion.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChange"
          @current-change="currentChange"
        />
      </template>
    </tree-page-common>
    <el-dialog
      v-model="state.dialogVisible"
      :before-close="handleClose"
      :width="450"
      :close-on-click-modal="false"
      class="dialogCommon"
      title="分类"
    >
      <el-form
        ref="ruleFormRef"
        :model="state.classifyForm"
        :rules="state.rules"
        label-width="120px"
      >
        <el-form-item label="上级分类" prop="parentId">
          <el-cascader
            v-model="state.classifyForm.parentId"
            :options="state.treeData"
            :props="defaultProps1"
            clearable
            placeholder="选择上级分类"
            @change="handleChange"
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="classifyName">
          <el-input
            v-model="state.classifyForm.classifyName"
            placeholder="输入分类名称"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="save"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="state.dialogVisibleFile"
      :before-close="handleCloseFile"
      :title="state.title"
      :width="450"
      :close-on-click-modal="false"
      class="dialogCommon"
    >
      <el-form
        ref="ruleFormRefFile"
        :model="state.classifyFormFile"
        :rules="state.rulesFile"
        label-width="120px"
      >
        <el-form-item label="上级分类" prop="classifyId">
          <el-cascader
            ref="cascader"
            v-model="state.classifyFormFile.classifyId"
            :options="state.treeData"
            :props="defaultProps1"
            clearable
            placeholder="选择上级分类"
            @change="handleChangeFile"
          />
        </el-form-item>
        <el-form-item v-if="state.title == '编辑'" label="文件名称" prop="name">
          <el-input
            v-model="state.classifyFormFile.name"
            placeholder="输入文件名称"
          />
        </el-form-item>
        <el-form-item v-if="state.title != '编辑'" label="分类名称" prop="file">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :limit="1"
            :on-change="loadJsonFromFile"
            :on-exceed="handleExceed"
            action="#"
            drag
            style="width: 85%"
          >
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              将文件拖到此处或 <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">单个文件不能超过100M</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeFile">关闭</el-button>
          <el-button type="primary" @click="saveFile"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { exportFile } from "@/utils/down.js";

import {
  downloadFile,
  knowledgesDelete,
  knowledgesDeleteFile,
  knowledgesSave,
  knowledgesSaveFile,
  knowledgesUpdateFile,
  knowledgeTreeAPI,
  selectList,
} from "@/api/parkOperation/knowledgeCenter.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { calcPageNo } from "@/utils/util.js";
let treeRef = ref();
const ruleFormRef = ref();
const cascader = ref();
const ruleFormRefFile = ref();
let formInlineRef = ref();
const formInline = reactive({});
const defaultProps = {
  children: "children",
  label: "classifyName",
};
const defaultProps1 = {
  children: "children",
  label: "classifyName",
  value: "id",
  checkStrictly: true,
};

const state = reactive({
  title: "新增",
  action: import.meta.env.VITE_BASE_URL + "/core/file/upload",
  tableHeader: [
    {
      prop: "name",
      label: "文件名称",
    },
    {
      prop: "size",
      label: "文件大小",
      formatter: (row, column, cellValue) => {
        return cellValue + "kb";
      },
    },
    {
      prop: "downloads",
      label: "下载次数",
    },
    {
      prop: "createUserName",
      label: "创建人",
    },
    {
      prop: "createTime",
      label: "创建时间",
      width: 160,
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  rules: {
    classifyName: [
      { required: true, message: "分类名称不能为空", trigger: "change" },
    ],
  },
  rulesFile: {
    classifyId: [
      { required: true, message: "分类名称不能为空", trigger: "change" },
    ],
    file: [{ required: true, message: "文件不能为空", trigger: "change" }],
    name: [{ required: true, message: "文件名称不能为空", trigger: "change" }],
  },
  classifyForm: {
    classifyName: "",
  },
  classifyFormFile: {},
  dialogVisible: false,
  dialogVisibleFile: false,
  treeData: [],
  tableHeight: 100,
  tableData: [],
});

onMounted(() => {
  getList();
  getTree();
});

const closeFile = () => {
  state.dialogVisibleFile = false;
  handleCloseFile();
};

const download = async (row) => {
  downloadFile({ id: row.id }).then((res) => {
    if (res.success) {
      exportFile(row.path, {}, row.name + "." + row.suffix);
      getList();
    } else {
      ElMessage.error("下载失败，" + res.errorMessage);
    }
  });
};

const save = () => {
  ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      knowledgesSave(state.classifyForm).then((res) => {
        if (res.success) {
          ElMessage.success("新增成功");
          getTree();
          handleClose();
        } else {
          ElMessage.error("保存失败，" + res.errorMessage);
        }
      });
    }
  });
};
const uploadRef = ref();
const handleExceed = () => {
  ElMessage.warning(`当前限制选择 1个文件`);
};
const saveFile = () => {
  ruleFormRefFile.value.validate((valid, fields) => {
    if (valid) {
      if (state.title == "新增") {
        let fd = new FormData();
        fd.append("file", state.classifyFormFile.file.raw); // 传文件
        fd.append("classifyId", state.classifyFormFile.classifyId);
        fd.append("classifyName", cascader.value.getCheckedNodes()[0].label);
        knowledgesSaveFile(fd).then((res) => {
          if (res.success) {
            ElMessage.success("新增成功");
            getTree();
            handleCloseFile();
            getList();
          } else {
            ElMessage.error("新增失败，" + res.errorMessage);
          }
        });
      } else {
        knowledgesUpdateFile(state.classifyFormFile).then((res) => {
          if (res.success) {
            ElMessage.success("编辑成功");
            getTree();
            handleCloseFile();
            getList();
          } else {
            ElMessage.error("编辑失败，" + res.errorMessage);
          }
        });
      }
    }
  });
};
const del = (row) => {
  ElMessageBox.confirm("是否删除当前文件?", "提醒", {
    type: "warning",
  }).then(() => {
    state.pagetion.pageNum = calcPageNo(
      state.pagetion.total,
      state.pagetion.pageNum,
      state.pagetion.pageSize
    );

    let fd = new FormData();
    fd.append("id", row.id);
    knowledgesDeleteFile(fd).then((res) => {
      if (res.success) {
        ElMessage.success("删除成功");
        getList();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};

const remove = (node, data) => {
  ElMessageBox.confirm("是否删除当前分类?", "提醒", {
    type: "warning",
  }).then(() => {
    let fd = new FormData();
    fd.append("id", data.id);
    knowledgesDelete(fd).then((res) => {
      if (res.success) {
        ElMessage.success("删除成功");
        getTree();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};

const loadJsonFromFile = (file) => {
  state.classifyFormFile.file = file;
};

const handleClose = () => {
  state.classifyForm = {};
  ruleFormRef.value.clearValidate();
  ruleFormRef.value.resetFields();
  state.dialogVisible = false;
};

const handleCloseFile = () => {
  state.classifyFormFile = {};
  ruleFormRefFile.value.clearValidate();
  ruleFormRefFile.value.resetFields();
  state.dialogVisibleFile = false;
};

const getTree = () => {
  knowledgeTreeAPI().then((res) => {
    state.treeData = res.data;
  });
};

//选择知识分类
const handleChange = (e) => {
  if (!e) return;
  state.classifyForm.parentId = e[e.length - 1];
  e.forEach((e) => {
    state.classifyForm.fullIdPath += e + "/";
  });
  state.classifyForm.fullIdPath = state.classifyForm.fullIdPath.substring(
    0,
    state.classifyForm.fullIdPath.length - 1
  );
};
//选择知识分类
const handleChangeFile = (e) => {
  state.classifyFormFile.classifyId = e[e.length - 1];
};

//分页
const getList = () => {
  var array = [];
  if (treeRef.value) {
    treeRef.value.getCheckedNodes().forEach((e) => {
      array.push(e.id);
    });
  }
  let query = {
    classifyIdList: array,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
    name: formInline.name,
  };
  selectList(query).then(async (res) => {
    state.tableData = res.data.dataList;
    state.pagetion.total = res.data.totalCount;
  });
};

const handleCheckChange = (data, checked, indeterminate) => {
  getList();
};

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

//重置方法
const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};
//新增方法
const addDialog = () => {
  state.dialogVisible = true;
  nextTick(() => {
    ruleFormRef.value.resetFields();
  });
};

// 编辑
const edit = ({ data }) => {
  state.dialogVisible = true;
  nextTick(() => {
    Object.assign(state.classifyForm, data);
  });
};

//新增方法
const addDialogFile = () => {
  state.title = "新增";
  state.dialogVisibleFile = true;
  nextTick(() => {
    uploadRef.value.clearFiles(); //该方法就是清理上传列表
  });
};
//编辑方法
const updateDialogFile = (row) => {
  state.title = "编辑";
  state.dialogVisibleFile = true;
  nextTick(() => {
    state.classifyFormFile.classifyId = row.classifyId;
    state.classifyFormFile.name = row.name;
    state.classifyFormFile.id = row.id;
  });
};

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};

defineExpose({
  getList,
});
</script>

<style lang="less" scoped></style>
