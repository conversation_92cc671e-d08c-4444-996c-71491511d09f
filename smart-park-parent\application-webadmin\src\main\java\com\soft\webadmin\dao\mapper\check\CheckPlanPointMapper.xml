<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.CheckPlanPointMapper">
    <resultMap type="com.soft.webadmin.model.check.CheckPlanPoint" id="CheckPlanPointResult">
        <result property="id" column="id" />
        <result property="planId" column="plan_id" />
        <result property="pointType" column="point_type" />
        <result property="dataId" column="data_id" />
        <result property="templateId" column="template_id" />
    </resultMap>

    <sql id="selectCheckPlanPointVo">
        id, plan_id, point_type, data_id, template_id
    </sql>

    <select id="queryListByPlanIdList" resultType="com.soft.webadmin.vo.check.CheckPlanPointVO">
        select <include refid="selectCheckPlanPointVo" />,
        (
        case when t.point_type = 'EQUIPMENT' then (select equipment_name from sp_equipment_om where equipment_id = t.data_id)
        when t.point_type = 'SPACE' then (select name from sp_space where id = t.data_id) end
        ) data_name,
        (
        case when t.point_type = 'EQUIPMENT' then (select equipment_code from sp_equipment_om where equipment_id = t.data_id)
        when t.point_type = 'SPACE' then '' end
        ) equipment_code,
        (
        case when t.point_type = 'EQUIPMENT' then (select space_full_name from sp_equipment_om where equipment_id = t.data_id)
        when t.point_type = 'SPACE' then (select full_name from sp_space where id = t.data_id) end
        ) data_space,
        (select template_name from sp_check_template where id = t.template_id) template_name
        from sp_check_plan_point t
        <where>
            t.plan_id in
            <foreach collection="planIdList" item="planId" open="(" separator="," close=")">
                #{planId}
            </foreach>
        </where>
    </select>

    <select id="queryItemByIdList" resultType="com.soft.webadmin.vo.check.CheckPlanPointItemVO">
        select t.id point_id, i.item_name, i.item_content, i.item_type
        from sp_check_plan_point t, sp_check_template_item i
        where t.template_id = i.template_id
        and t.id in
        <foreach collection="pointIdList" item="pointId" open="(" separator="," close=")">
            #{pointId}
        </foreach>
    </select>
    
</mapper>