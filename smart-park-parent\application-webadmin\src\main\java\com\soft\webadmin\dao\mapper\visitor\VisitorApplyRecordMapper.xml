<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.visitor.VisitorApplyRecordMapper">
    <resultMap type="com.soft.webadmin.model.visitor.VisitorApplyRecord" id="VisitorApplyRecordResult">
        <result property="id" column="id" />
        <result property="visitorName" column="visitor_name" />
        <result property="visitorPhone" column="visitor_phone" />
        <result property="visitorIdCard" column="visitor_id_card" />
        <result property="licenceNumber" column="licence_number" />
        <result property="visitReason" column="visit_reason" />
        <result property="planVisitTime" column="plan_visit_time" />
        <result property="approveStatus" column="approve_status" />
        <result property="approveUserId" column="approve_user_id" />
        <result property="approveTime" column="approve_time" />
        <result property="rejectReason" column="reject_reason" />
        <result property="rejectRemark" column="reject_remark" />
        <result property="applyUserId" column="apply_user_id" />
        <result property="applyTime" column="apply_time" />
        <result property="openId" column="open_id" />
    </resultMap>

    <sql id="selectVisitorApplyRecordVo">
        select id, visitor_name, visitor_phone, visitor_id_card, licence_number, visit_reason, plan_visit_time, approve_status, approve_user_id, approve_time, reject_reason, reject_remark, apply_user_id, apply_time,open_id from visitor_apply_record
    </sql>

</mapper>