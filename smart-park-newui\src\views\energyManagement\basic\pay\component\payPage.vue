<template>
  <dialog-common ref="dialog" title="充值" @submit="submit" :formRef="ruleFormRef" :width="500">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":">
      <el-row>
        <el-col>
          <el-form-item label="缴费金额" prop="price">
            <el-input v-model="form.price" type="number" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { rechargeAPI } from '@/api/energyManagement/pay.js';
import { ElMessage, ElMessageBox } from 'element-plus';

const emit = defineEmits(['submit']);
const dialog = ref();
const ruleFormRef = ref();
const form = reactive({});
const state = reactive({
  rules: {},
});

const open = () => {
  dialog.value.open();
};

/** 保存 */
const submit = () => {
  rechargeAPI(form).then((res) => {
    console.log(res);
    if (res.success) {
      ElMessage.success('设置成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

defineExpose({
  form,
  open,
});
</script>
