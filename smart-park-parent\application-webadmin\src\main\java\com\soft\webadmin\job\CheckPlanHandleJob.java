package com.soft.webadmin.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.model.SysUser;
import com.soft.common.core.util.DateUtils;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.sequence.wrapper.IdGeneratorWrapper;
import com.soft.sub.dto.equipment.IotDBEquipmentAttributeQueryParam;
import com.soft.sub.enums.EquipmentRunStatusEnums;
import com.soft.sub.model.equipment.EquipmentLifeCycle;
import com.soft.sub.model.equipment.IotDBEquipmentAttribute;
import com.soft.sub.service.equipment.EquipmentLifeCycleService;
import com.soft.sub.service.equipment.IotDbEquipmentAttributeServer;
import com.soft.webadmin.dao.equipment.EquipmentOmMapper;
import com.soft.webadmin.enums.EnableStateEnums;
import com.soft.webadmin.enums.WorkOrderOperateEnums;
import com.soft.webadmin.enums.WorkOrderStateEnums;
import com.soft.webadmin.enums.WorkOrderTypeEnums;
import com.soft.webadmin.model.check.*;
import com.soft.webadmin.model.equipment.EquipmentOm;
import com.soft.webadmin.service.check.*;
import com.soft.webadmin.vo.check.CheckPlanPointItemVO;
import com.soft.webadmin.vo.check.CheckPlanPointVO;
import com.soft.webadmin.vo.equipment.EquipmentOmVO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName CheckPlanHandleJob
 * @description: 检查计划生成工单定时任务处理
 * @date 2023年12月13日
 */
@Service
@Slf4j
public class CheckPlanHandleJob {

    @Autowired
    private CheckPlanService checkPlanService;

    @Autowired
    private CheckPlanPointService checkPlanPointService;

    @Autowired
    private CheckRecordService checkRecordService;

    @Autowired
    private CheckRecordEquipmentService checkRecordEquipmentService;

    @Autowired
    private CheckRecordPointService checkRecordPointService;

    @Autowired
    private CheckRecordPointItemService checkRecordPointItemService;

    @Autowired
    private WorkOrderService workOrderService;

    @Autowired
    private WorkOrderLogService workOrderLogService;

    @Autowired
    private WorkGroupService workGroupService;

    @Autowired
    private IdGeneratorWrapper idGeneratorWrapper;

    @Autowired
    private EquipmentOmMapper equipmentOmMapper;

    @Autowired
    private EquipmentLifeCycleService equipmentLifeCycleService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private IotDbEquipmentAttributeServer iotDbEquipmentAttributeServer;

    // 设备状态，故障
    public final static Integer FAULT = 2;
    // 设备状态：离线
    public final static Integer OFFLINE = 3;

    /**
     * 检查计划定时任务
     * 每10分钟执行一次
     */
    @XxlJob("checkPlanHandler")
    public void checkPlanHandler() {
        // 当前时间
        Date now = DateUtil.date();
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isNotEmpty(jobParam)) {
            now = DateUtil.parse(jobParam);
        }
        // Date now = DateUtil.parse("2024-04-09 13:00:00");
        Date dateTime = now;
        log.info("检查计划定时任务开始处理，处理时间：{} ", dateTime);

        // 查询启用的检查计划
        List<CheckPlan> planList = checkPlanService.list(
                new LambdaQueryWrapper<CheckPlan>().eq(CheckPlan::getState, EnableStateEnums.ENABLE.getValue())
        );

        List<CheckPlan> dataList = Lists.newArrayList();
        planList.forEach(checkPlan -> {
            String scheduleType = checkPlan.getScheduleType();
            String scheduleRule = checkPlan.getScheduleRule();
            String startTime = checkPlan.getStartTime();
            Integer advanceTime = checkPlan.getAdvanceTime();
            Date date = DateUtil.offsetHour(dateTime, advanceTime);
            if ("PATROL_INSPECTION".equals(checkPlan.getPlanType())) {
                // 排班日期
                List<String> dateList = Arrays.asList(scheduleRule.split(","));
                // 日期比对
                String targetDate;
                switch (scheduleType) {
                    case "MONTH":
                        // 月排班
                        targetDate = DateUtils.getDayOfMonth(date).toString();
                        break;
                    case "WEEK":
                        // 周排班
                        targetDate = DateUtils.getDayOfWeek(date).toString();
                        break;
                    default:
                        // 自定义
                        targetDate = DateUtil.format(date, "yyyy-MM-dd");
                        break;
                }

                // 任务执行开始时间
                LocalTime startLocalTime = LocalTime.parse(startTime, DateTimeFormatter.ofPattern("HH:mm"));
                if (dateList.contains(targetDate) && Objects.equals(startLocalTime.getHour(), DateUtil.hour(date, true))
                        && (startLocalTime.getMinute() / 10 == DateUtil.minute(date) / 10)) {
                    dataList.add(checkPlan);
                }
            } else {
                Date maintenanceTimeNext = checkPlan.getMaintenanceTimeNext();
                if (DateUtil.format(date, "yyyyMMddHHmm").equals(DateUtil.format(maintenanceTimeNext, "yyyyMMddHHmm"))) {
                    dataList.add(checkPlan);
                }
            }
        });
        this.batchInsertData(dataList, now);
    }

    public void batchInsertData(List<CheckPlan> checkPlanList, Date now) {
        if (CollectionUtil.isEmpty(checkPlanList)) {
            return;
        }

        List<Long> planIdList = checkPlanList.stream().map(CheckPlan::getId).collect(Collectors.toList());
        // 检查点位
        List<CheckPlanPointVO> planPointList = checkPlanPointService.queryListByPlanIdList(planIdList);

        List<CheckRecord> checkRecordList = Lists.newArrayList();
        List<CheckRecordPoint> checkRecordPointList = Lists.newArrayList();
        List<CheckRecordPointItem> checkRecordPointItemList = Lists.newArrayList();
        List<CheckRecordEquipment> checkRecordEquipmentList = Lists.newArrayList();
        List<WorkOrder> workOrderList = Lists.newArrayList();
        List<WorkOrderLog> workOrderLogList = Lists.newArrayList();
        List<CheckPlan> updateCheckPlanList = Lists.newArrayList();
        List<EquipmentLifeCycle> insertEquipmentLiftCycleList = Lists.newArrayList();
        for (CheckPlan checkPlan : checkPlanList) {
            Long planId = checkPlan.getId();
            String planType = checkPlan.getPlanType();
            Integer planMode = checkPlan.getPlanMode();
            // 检查人
            Long checkUserId = null;
            List<Long> userIdList = workGroupService.getUserIdList(Collections.singletonList(checkPlan.getWorkGroupId()));
            if (CollUtil.isNotEmpty(userIdList)) {
                Collections.shuffle(userIdList);
                checkUserId = userIdList.get(0);
            }

            // 检查记录
            Long recordId = idGeneratorWrapper.nextLongId();
            CheckRecord checkRecord = new CheckRecord();
            checkRecord.setId(recordId);
            checkRecord.setPlanId(planId);
            checkRecord.setPlanName(checkPlan.getPlanName());
            checkRecord.setWorkGroupId(checkPlan.getWorkGroupId());
            checkRecord.setCheckUserId(checkUserId);
            checkRecord.setStartTime(now);
            checkRecord.setCreateTime(now);

            if (planMode == 2) {
                // 智能巡检
                List<EquipmentOm> equipmentList = checkPlanService.getEquipmentList(checkPlan.getEquipmentIds());
                if (CollUtil.isNotEmpty(equipmentList)) {
                    // 查询设备是否有未完成的维修工单（设备故障）
                    List<Long> equipmentIds = equipmentList.stream().map(EquipmentOm::getEquipmentId).collect(Collectors.toList());
                    List<EquipmentOmVO> faultEquipmentList = equipmentOmMapper.queryFaultList(equipmentIds);
                    Map<String, EquipmentOmVO> faultEquipmentMap = faultEquipmentList.stream().collect(Collectors.toMap(EquipmentOmVO::getEquipmentCode, e -> e));

                    // 离线的设备
                    Integer offlineValue = EquipmentRunStatusEnums.OFFLINE.getValue();
                    List<EquipmentOm> offlineEquipmentList = equipmentList.stream().filter(e ->
                            e.getRunStatus() != null && e.getRunStatus() == offlineValue
                    ).collect(Collectors.toList());
                    Map<String, EquipmentOm> offlineEquipmentMap = offlineEquipmentList.stream().collect(Collectors.toMap(EquipmentOm::getEquipmentCode, e -> e));

                    equipmentList.forEach(equipment -> {
                        Long equipmentId = equipment.getEquipmentId();
                        String equipmentCode = equipment.getEquipmentCode();
                        CheckRecordEquipment checkRecordEquipment = new CheckRecordEquipment();
                        checkRecordEquipment.setRecordId(recordId);
                        checkRecordEquipment.setEquipmentId(equipmentId);
                        checkRecordEquipment.setEquipmentName(equipment.getEquipmentName());
                        checkRecordEquipment.setEquipmentCode(equipmentCode);
                        checkRecordEquipment.setEquipmentSpace(equipment.getSpaceFullName());
                        checkRecordEquipment.setState(equipment.getEquipmentStatus());
                        checkRecordEquipment.setReportTime(new Date());

                        if (faultEquipmentMap.containsKey(equipmentCode)) {
                            checkRecordEquipment.setState(FAULT);
                            checkRecordEquipment.setReportTime(faultEquipmentMap.get(equipmentCode).getUpdateTime());
                        } else {
                            if (offlineEquipmentMap.containsKey(equipmentCode)) {
                                // iotdb查询设备的离线时间
                                IotDBEquipmentAttributeQueryParam iotDBEquipmentAttributeQueryParam = new IotDBEquipmentAttributeQueryParam();
                                iotDBEquipmentAttributeQueryParam.setEquipmentCode(equipmentCode);
                                iotDBEquipmentAttributeQueryParam.setAttributeKey("rt_run_status");
                                iotDBEquipmentAttributeQueryParam.setAttributeValue(offlineValue.toString());
                                try {
                                    List<IotDBEquipmentAttribute> iotDBEquipmentAttributeList = iotDbEquipmentAttributeServer.query(iotDBEquipmentAttributeQueryParam);
                                    if (CollUtil.isNotEmpty(iotDBEquipmentAttributeList)) {
                                        checkRecordEquipment.setState(OFFLINE);
                                        checkRecordEquipment.setReportTime(iotDBEquipmentAttributeList.get(0).getTime());
                                    }
                                } catch (Exception ex) {
                                    throw new RuntimeException(ex);
                                }
                            }
                        }
                        checkRecordEquipmentList.add(checkRecordEquipment);
                    });
                    checkRecord.setFinishTime(DateUtil.date());
                }

            } else {
                // 检查点位
                List<CheckPlanPointVO> pointList = planPointList.stream().filter(
                        checkPlanPoint -> checkPlanPoint.getPlanId().equals(planId)
                ).collect(Collectors.toList());

                // 检查项
                List<Long> pointIdList = pointList.stream().map(CheckPlanPointVO::getId).collect(Collectors.toList());
                List<CheckPlanPointItemVO> pointItemList = checkPlanPointService.queryItemByIdList(pointIdList);

                for (CheckPlanPointVO planPoint : pointList) {
                    Long recordPointId = idGeneratorWrapper.nextLongId();
                    CheckRecordPoint recordPoint = new CheckRecordPoint();
                    recordPoint.setId(recordPointId);
                    recordPoint.setRecordId(recordId);
                    recordPoint.setPointName(planPoint.getDataName());
                    recordPoint.setPointType(planPoint.getPointType());
                    recordPoint.setDataId(planPoint.getDataId());
                    checkRecordPointList.add(recordPoint);

                    List<CheckPlanPointItemVO> itemList = pointItemList.stream().filter(
                            item -> item.getPointId().equals(planPoint.getId())
                    ).collect(Collectors.toList());
                    for (CheckPlanPointItemVO itemVO : itemList) {
                        CheckRecordPointItem recordPointItem = new CheckRecordPointItem();
                        recordPointItem.setPointId(recordPointId);
                        recordPointItem.setItemName(itemVO.getItemName());
                        recordPointItem.setItemContent(itemVO.getItemContent());
                        recordPointItem.setItemType(itemVO.getItemType());
                        checkRecordPointItemList.add(recordPointItem);
                    }
                }

                // 工单
                Long orderId = idGeneratorWrapper.nextLongId();
                String orderNo = "GD" + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSSS");
                WorkOrder workOrder = new WorkOrder();
                workOrder.setId(orderId);
                workOrder.setOrderNo(orderNo);
                workOrder.setOrderType("MAINTENANCE".equals(planType) ? WorkOrderTypeEnums.MAINTENANCE : WorkOrderTypeEnums.PATROL_INSPECTION);
                workOrder.setBusinessId(recordId);
                workOrder.setBusinessTable(MyModelUtil.mapToTableName(CheckRecord.class));
                workOrder.setWorkUserId(checkUserId);
                workOrder.setWorkTime(checkRecord.getStartTime());
                workOrder.setHandleLimitDuration(checkPlan.getHandleLimitDuration());
                workOrder.setState(checkUserId != null ? WorkOrderStateEnums.NO_RESPONSE.getValue() : WorkOrderStateEnums.NO_ALLOT.getValue());
                workOrder.setCreateTime(now);
                workOrderList.add(workOrder);

                // 工单操作记录
                WorkOrderLog workOrderLog = new WorkOrderLog();
                workOrderLog.setOrderId(orderId);
                workOrderLog.setOperate(WorkOrderOperateEnums.CREATE.getValue());
                workOrderLog.setCreateTime(now);
                workOrderLogList.add(workOrderLog);

                // 工单操作记录，派单
                if (checkUserId != null) {
                    SysUser sysUser = sysUserMapper.selectById(checkUserId);
                    String workUserName = sysUser != null ? sysUser.getShowName() : "";

                    workOrderLog = new WorkOrderLog();
                    workOrderLog.setOrderId(orderId);
                    workOrderLog.setOperate(WorkOrderOperateEnums.DISPATCH.getValue());
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("workUserId", checkUserId);
                    jsonObject.put("workUserName", workUserName);
                    jsonObject.put("remark", "自动派单");
                    workOrderLog.setContent(jsonObject.toJSONString());
                    workOrderLog.setCreateTime(now);
                    workOrderLogList.add(workOrderLog);
                }

                if ("MAINTENANCE".equals(planType)) {
                    // 更新维保计划的下次维保日期
                    CheckPlan plan = new CheckPlan();
                    plan.setId(planId);
                    plan.setMaintenanceTimeNext(DateUtil.offsetMonth(checkPlan.getMaintenanceTimeNext(), checkPlan.getMaintenanceCycle()));
                    updateCheckPlanList.add(plan);

                    // 维保的设备
                    List<CheckPlanPointVO> equipmentList =
                            pointList.stream().filter(e -> e.getPointType().equals("EQUIPMENT")).collect(Collectors.toList());
                    equipmentList.forEach(equipment -> {
                        // 生成设备生命周期维保记录，维保记录
                        EquipmentLifeCycle equipmentLifeCycle = new EquipmentLifeCycle();
                        equipmentLifeCycle.setEquipmentId(equipment.getDataId());
                        equipmentLifeCycle.setEquipmentCode(equipment.getEquipmentCode());
                        equipmentLifeCycle.setCycleType(4);
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("planName", checkPlan.getPlanName());
                        jsonObject.put("orderId", orderId);
                        jsonObject.put("orderNo", orderNo);
                        equipmentLifeCycle.setBusiData(jsonObject.toJSONString());
                        equipmentLifeCycle.setCreateTime(DateUtil.date());
                        insertEquipmentLiftCycleList.add(equipmentLifeCycle);
                    });
                }
            }
            checkRecordList.add(checkRecord);
        }

        if (CollectionUtil.isNotEmpty(checkRecordList)) {
            checkRecordService.saveBatch(checkRecordList);
        }
        if (CollectionUtil.isNotEmpty(checkRecordPointList)) {
            checkRecordPointService.saveBatch(checkRecordPointList);
        }
        if (CollectionUtil.isNotEmpty(checkRecordPointItemList)) {
            checkRecordPointItemService.saveBatch(checkRecordPointItemList);
        }
        if (CollectionUtil.isNotEmpty(checkRecordEquipmentList)) {
            checkRecordEquipmentService.saveBatch(checkRecordEquipmentList);
        }
        if (CollectionUtil.isNotEmpty(workOrderList)) {
            workOrderService.saveBatch(workOrderList);
        }
        if (CollectionUtil.isNotEmpty(workOrderLogList)) {
            workOrderLogService.saveBatch(workOrderLogList);
        }
        if (CollectionUtil.isNotEmpty(updateCheckPlanList)) {
            checkPlanService.updateBatchById(updateCheckPlanList);
        }
        if (CollectionUtil.isNotEmpty(insertEquipmentLiftCycleList)) {
            equipmentLifeCycleService.saveBatch(insertEquipmentLiftCycleList);
        }
    }

}
