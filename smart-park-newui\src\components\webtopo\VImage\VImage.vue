<template>
  <div class="imageCom" :class="{ ...state.equipClass, topoAlarm: element.params.equipInfo.alarmStatus === 1 }" @click="eventClick">
    <el-icon v-show="element.params.equipInfo.alarmStatus === 1" class="warn" color="red" @click.stop="warnHandle">
      <WarningFilled />
    </el-icon>
    <img :src="imgTransfer(state.image)" alt="">
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const props = defineProps({
  element: {
    type: Object,
    default: () => {
      return {}
    }
  },
  type: {
    type: String,
    default: ""
  }
})

const emit = defineEmits(['warnHandle','videoPre','attributeTransfer','jumpHandle'])

let { element, type } = toRefs(props)

let router = new useRouter()

const state = reactive({
  title: '',
  warnRecord: [],
  equipClass: {},
  image: element.value.image
})

const imgTransfer = (name) => {
  return new URL(`/src/assets/webtopoImg/${name}`, import.meta.url).href
}

// 设备样式设定
const classEquip = (info) => {
  if (type.value !== 'exhibits' || !info.equipmentId) return false
  state.equipClass = {}

  // 离线
  state.equipClass.gray = info.status === 0

  // 隐藏  闪烁 切换
  let { classInfo } = element.value.params;

  (classInfo.classList || []).forEach(item => {
    ; if ((classInfo.checkList || []).includes(item.classType) && item.attribute) {
      let value = info.equipmentAttributeList.find(i => i.attributeKey === item.attribute)?.attributeValue
      if (item.classType === 'topoCheckout') {
        let imageName = item.list.find(i => value >= i.min && value <= i.max)?.picture
        state.image = imageName || element.value.image
      } else {
        state.equipClass[item.classType] = item.list.some(i => value >= i.min && value <= i.max)
      }
    }
  })
}

// 报警记录查询
const warnHandle = () => {
  emit('warnHandle',element.value.params.equipInfo.equipmentId)
}

// 事件点击
const eventClick = () => {
  if (type.value != 'exhibits') return false

  let { eventInfo, equipInfo } = element.value.params

  if(equipInfo.delete){
    return ElMessage.error('当前设备已被删除')
  }

  if (eventInfo.eventType && equipInfo.equipmentId) {
    switch (eventInfo.eventType) {
      case 'videoPre': // 视频预览
        emit('videoPre',equipInfo)
        break;
      case 'equipControl': // 设备控制
        emit('attributeTransfer', '设备控制',equipInfo)
        break;
      case 'attributeControl': // 属性控制
        emit('attributeTransfer', '属性控制' ,equipInfo, equipInfo.attribute)
        break;
      case 'pageJump': // 页面跳转
        emit('jumpHandle',eventInfo)
        break;
      default:
        break;
    }
  }
}

// 设备信息
watch(() => element.value.params.equipInfo, (newVal) => {
  classEquip(newVal)
}, { immediate: true, deep: true })
</script>

<style lang='less' scoped>
.imageCom {
  position: relative;
  cursor: pointer;
}

img {
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.warn {
  width: 40%;
  height: 40%;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  svg{
    width: 100%;
    height: 100%;
  }
}

.gray {
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  filter: grayscale(100%);
  filter: gray;
}

.toposhowHide {
  display: none;
}

.topoAlarm {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border: 2px solid #EF2D02;
    background: red;
    border-radius: 50%;
    animation: warn 1s ease-out infinite;
    box-shadow: 1px 1px 10px red;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border: 2px solid #EF2D02;
    background: red;
    border-radius: 50%;
    animation: warn1 1s ease-out infinite;
    box-shadow: 1px 1px 10px red;
    z-index: 0;
  }
}

@keyframes warn {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.7;
  }

  100% {
    transform: translate(-50%, -50%) scale(2.2);
    opacity: 0;
  }
}

@keyframes warn1 {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.7;
  }

  100% {
    transform: translate(-50%, -50%) scale(1.6);
    opacity: 0;
  }
}

.topoTwinkle {
  animation: twinkle 0.7s ease-in infinite;
}

@keyframes twinkle {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}
</style>
