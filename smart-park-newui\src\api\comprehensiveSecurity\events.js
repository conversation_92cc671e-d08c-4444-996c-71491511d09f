import { request } from '@/utils/request';

// 分页查询
export const eventsPageAPI = (params) => {
    return request('get', '/contingency/event/getPage', params, 'F');
};

// 保存
export const eventsSaveAPI = (data) => {
    return request('post', '/contingency/event/saveOrUpdate', data);
};

// 删除
export const eventsDeteleAPI = (params) => {
    return request('post', '/contingency/event/delete', params, 'F');
};