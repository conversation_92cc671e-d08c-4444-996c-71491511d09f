import { request } from "@/utils/request";

//入侵报警设备列表
export const getAlarmEquipAPI = (params) => {
  return request('get', '/intrusion-alarm/equipment/list', params, 'F');
}

// 子系统消警
export const subsystemEliminateAPI = (data) => {
  return request('post', '/intrusion-alarm/subsystem/eliminate', data);
}

// 子系统布防撤防
export const subsystemIssueAPI = (data) => {
  return request('post', '/intrusion-alarm/subsystem/issue', data);
}

// 防区旁路及旁路恢复
export const defenceIssueAPI = (data) => {
  return request('post', '/intrusion-alarm/defence/issue', data);
}
