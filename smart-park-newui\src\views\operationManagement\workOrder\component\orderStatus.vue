<template>
  <div class="status">
    <img :src="state.orderStateImg[status]">
    <span :style="{color: state.orderStateColor[status]}">{{ state.orderStateOptions[status] }}</span>
  </div>
</template>

<script setup>
const props = defineProps({
  status:{
    type: Number,
    default: -1
  }
})

const state = reactive({
  orderStateOptions: {
    1: '待派单',
    2: '未响应',
    3: '处理中',
    4: '已关闭',
    5: '已完成',
  },
  orderStateColor: {
    1: '#EA9518',
    2: '#A6A3A3',
    3: '#409eff',
    4: '#A6A3A3',
    5: '#409eff',
  },
  orderStateImg:{
    1: new URL('/src/assets/img/order-status1.png', import.meta.url).href,
    2: new URL('/src/assets/img/order-status2.png', import.meta.url).href,
    3: new URL('/src/assets/img/order-status3.png', import.meta.url).href,
    4: new URL('/src/assets/img/order-status4.png', import.meta.url).href,
    5: new URL('/src/assets/img/order-status5.png', import.meta.url).href,
  }
})
</script>

<style scoped lang="less">
.status{
  display: inline-block;
  font-size: 14px;
  img{
    width: 16px;
    height: 16px;
    margin-right: 5px;
    vertical-align: middle;
  }
}
</style>
