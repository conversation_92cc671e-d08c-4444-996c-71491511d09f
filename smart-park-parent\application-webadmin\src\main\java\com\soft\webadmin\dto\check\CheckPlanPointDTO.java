package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * CheckPlanPointDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckPlanPointDTO对象")
@Data
public class CheckPlanPointDTO {

    @ApiModelProperty(value = "检查点类型（EQUIPMENT设备，SPACE空间）")
    private String pointType = "EQUIPMENT";

    @ApiModelProperty(value = "设备id/空间id")
    @NotNull(message = "设备/空间不能为空！")
    private Long dataId;

    @ApiModelProperty(value = "检查模板id")
    @NotNull(message = "检查模板不能为空！")
    private Long templateId;

}
