package com.soft.webadmin.dao.shifts;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.shifts.ShiftsAttendanceQueryDTO;
import com.soft.webadmin.model.shifts.ShiftsAttendance;
import com.soft.webadmin.vo.shifts.ShiftsAttendanceStatisticVO;
import com.soft.webadmin.vo.shifts.ShiftsAttendanceVO;

import java.util.List;

/**
 * 排班考勤记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface ShiftsAttendanceMapper extends BaseMapper<ShiftsAttendance> {
    /**
     * 查询排班考勤记录列表
     *
     * @param queryDTO 查询参数
     * @return 排班考勤记录集合
     */
    List<ShiftsAttendanceVO> queryList(ShiftsAttendanceQueryDTO queryDTO);

    /**
     * 统计
     * @param queryDTO
     * @return
     */
    List<ShiftsAttendanceStatisticVO> statistic(ShiftsAttendanceQueryDTO queryDTO);

    /**
     * 批量导入
     * @param attendanceList
     */
    void batchInsert(List<ShiftsAttendance> attendanceList);
}
