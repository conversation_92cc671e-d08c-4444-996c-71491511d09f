package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * WorkOrderDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@ApiModel("WorkOrderEvaluateDTO对象")
@Data
public class WorkOrderEvaluateDTO {

    @ApiModelProperty(value = "工单id")
    @NotNull(message = "工单id不能为空！")
    private Long id;

    @ApiModelProperty(value = "评价分数")
    @NotNull(message = "评价分数不能为空！")
    private Integer score;

    @ApiModelProperty(value = "评价内容")
    private String content;

}
