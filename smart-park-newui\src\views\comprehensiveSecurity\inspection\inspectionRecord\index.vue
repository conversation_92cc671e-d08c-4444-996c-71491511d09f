<template>
  <page-common v-model="state.tableHeight" :operate-bool="false">
    <template #query>
      <el-form :inline="true" ref="queryFormRef" :model="state.queryForm">
        <el-form-item prop="queryTime">
          <el-date-picker
            v-model="state.queryForm.queryTime"
            type="daterange"
            range-separator="-"
            start-placeholder="巡更开始日期"
            end-placeholder="巡更结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="queryList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column v-for="item of state.tableHeader" :label="item.label" :prop="item.prop">
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Document"
              @click.prevent="onView(scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />

      <record-dialog ref="recordDialogRef" :title="'查看'" @onClose="queryList" />
    </template>
  </page-common>
</template>

<script setup>


import {Refresh, Search} from "@element-plus/icons-vue";
import { useDateFormat } from '@vueuse/core'
import {listInspectionRecordAPI} from "@/api/comprehensiveSecurity/inspectionRecord.js";
import RecordDialog from "@/views/comprehensiveSecurity/inspection/inspectionRecord/component/recordDialog.vue";

let queryFormRef = ref()

// 子组件
let recordDialogRef = ref()


const state = reactive({
  tableHeight: 100,
  queryForm: {
    queryTime: null
  },
  tableData: [],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
  tableHeader: [
    { label: '巡更计划名称', prop: 'planName' },
    { label: '巡更人员', prop: 'inspectionUsernames' },
    { label: '巡更日期', prop: 'inspectionDate' },
    { label: '巡更时间段', prop: 'timeRange' },
    { label: '应巡更点数', prop: 'shouldCheckPointNum' },
    { label: '已巡更点数', prop: 'checkedPointNum' },
    { label: '未巡更点数', prop: 'unCheckPointNum' },
    { label: '完成率', prop: 'rate' }
  ]
})


// 查询列表数据
const queryList = () => {
  let query = {
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  if (state.queryForm.queryTime != null) {
    query.startDate = state.queryForm.queryTime[0]
    query.endDate = state.queryForm.queryTime[1]
  }
  listInspectionRecordAPI(query).then(res => {
    state.tableData = res.data.dataList
    // 处理完成率
    for (let item of state.tableData) {
      // 计算完成率
      let shouldCheckPointNum = item.shouldCheckPointNum
      let checkedPointNum = item.checkedPointNum
      let rate = checkedPointNum / shouldCheckPointNum
      item.rate = rate.toFixed(2) * 100 + '%'

      // 格式化数据
      item.inspectionDate = useDateFormat(item.inspectionDate, 'YYYY-MM-DD')
    }
    state.pageParam.total = res.data.totalCount
  })
}

// 重置
const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  queryList()
}


const sizeChange = (val) => {
  state.pageParam.pageSize = val
  queryList()
}

const currentChange = (val) => {
  state.pageParam.pageNum = val
  queryList()
}

// 打开子组件页面
const onView = (val) => {
  recordDialogRef.value.open(val.id, val.inspectionUserIds);
}


onMounted(() => {
  queryList()
})
</script>


<style scoped lang="less">
</style>
