<template>
  <dialog-common ref="dialogRef" :title="state.title" @submit="submit" :formRef="ruleFormRef" :width="700"
    @onClose="clearCache" class="dialogTextarea">
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.rules" label-width="100px" label-suffix=":">
      <el-row>
        <el-col :span="24">
          <el-form-item label="分组名称" prop="groupName">
            <el-input v-model="state.form.groupName" placeholder="请输入分组名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="desc">
            <el-input v-model="state.form.desc" :autosize="{ minRows: 3, maxRows: 5 }" maxlength="300" show-word-limit
              type="textarea" placeholder="请输入描述" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="关联设备" prop="equipmentList">
            <el-tag v-for="tag in state.form.equipmentList" :key="tag.equipmentId" class="tag" closable
              @close="handleClose(tag)">
              {{ tag.equipmentName }}
            </el-tag>
            <el-button class="button-new-tag ml-1" size="small" @click="openSelectPage"> + 选择设备</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
  <select-iot ref="equipmentSelectPageRef" @submit="selectedEquipment" />
</template>

<script setup>
import { ElMessage } from 'element-plus';

import { saveOrUpdateEquipmentGroupControlAPI } from "@/api/iotManagement/groupControl.js";

const props = defineProps({
  groupControlType: {
    type: Object,
    default: {
      name: '',
      type: ''
    }
  }
})
const { groupControlType } = toRefs(props)


const emit = defineEmits(['onClose']);

const ruleFormRef = ref();
const dialogRef = ref();


const equipmentSelectPageRef = ref();

const state = reactive({
  title: '',
  form: {
    equipmentList: []
  },
  rules: {
    groupName: [{ required: true, message: '分组名称不能为空', trigger: 'blur' }],
    equipmentList: [{ required: true, message: '关联设备不能为空', trigger: 'change' }],
  },
});


const open = (title, val) => {
  dialogRef.value.open();
  nextTick(() => {
    state.title = title
    if (val) {
      state.form = val
      state.form.equipmentList = val.equipmentGroupControlRelationVOS
    }
  })
};



/** 打开选择设备窗口 */
const openSelectPage = () => {
  equipmentSelectPageRef.value.selectedList = JSON.parse(JSON.stringify(state.form.equipmentList));
  equipmentSelectPageRef.value.open();
};

/** 接收已选的设备 */
const selectedEquipment = (list) => {
  state.form.equipmentList = list;
  // 校验非空
  ruleFormRef.value.validateField('equipmentList');
};

/** 删除所选的关联设备标签 */
const handleClose = (tag) => {
  state.form.equipmentList.splice(
    state.form.equipmentList.findIndex((item) => item.equipmentId === tag.equipmentId),
    1
  );
};

/** 保存 */
const submit = () => {
  let param = {
    id: state.form.id,
    groupName: state.form.groupName,
    desc: state.form.desc,
    type: groupControlType.value.type
  }
  let equipmentList = state.form.equipmentList;
  if (equipmentList) {
    param.equipmentGroupControlRelations = equipmentList.map(equipment => {
      return {
        groupControlId: state.form.id,
        equipmentId: equipment.equipmentId,
        equipmentName: equipment.equipmentName
      }
    })
  }
  saveOrUpdateEquipmentGroupControlAPI(param).then(res => {
    if (res.success) {
      ElMessage.success('保存成功！');
      dialogRef.value.close();
      emit('onClose');
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
};


const clearCache = () => {
  state.form.id = ''
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.dialogCommon {
  .conent {

    .el-input,
    .el-date-editor {
      width: 90%;
    }

    .el-textarea {
      width: 90%;
    }
  }
}

.tag {
  margin: 5px 5px 5px 0;
}
</style>
