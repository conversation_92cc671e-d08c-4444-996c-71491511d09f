package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import com.soft.webadmin.vo.check.CheckTemplateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 检查模板对象 sp_check_template
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_check_template")
public class CheckTemplate extends BaseModel {

    @TableId(value = "id")
    private Long id;

    /**
     * 检查模板名称
     */
    private String templateName;

    /**
     * 检查模板类型（1巡检-设备；2巡检-空间；3维保-设备）
     */
    private String templateType;


    private String remark;

    /**
     * 删除标识，1正常；-1已删除
     */
    @TableLogic
    private Integer deleteFlag;


    @Mapper
    public interface CheckTemplateModelMapper extends BaseModelMapper<CheckTemplateVO, CheckTemplate> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        CheckTemplate toModel(CheckTemplateVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        CheckTemplateVO fromModel(CheckTemplate entity);
    }

    public static final CheckTemplateModelMapper INSTANCE = Mappers.getMapper(CheckTemplateModelMapper.class);
}
