<template>
  <el-table :data="state.tableData" >
    <el-table-column v-for="item in state.tableHeader" :key="item.prop" :prop="item.prop" :label="item.label">
    </el-table-column>
  </el-table>
</template>

<script setup>
const state = reactive({
  tableData: [],
  tableHeader: [
    {
      prop: 'relatedEquipmentCode',
      label: '设备编号'
    },
    {
      prop: 'relatedEquipmentName',
      label: '设备名称'
    },
    {
      prop: 'relatedSubType',
      label: '设备类别'
    },
    {
      prop: 'relatedEquipmentSpaceFullName',
      label: '安装位置'
    },
  ],
});


const init = (equipmentRelationList) => {
  state.tableData = equipmentRelationList
}

defineExpose({
  init
})
</script>

<style lang="less" scoped></style>