<template>
  <dialog-common ref="dialog" title="设置" @submit="submit" @onClose="onClose" :formRef="ruleFormRef" :width="450">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <!--      单个设置-->
      <div v-show="state.single">
        <el-form-item label="备件名称" prop="sparePartName">
          <el-input v-model="form.sparePartName" disabled/>
        </el-form-item>
        <el-form-item label="备件分类" prop="classifyName">
          <el-input v-model="form.classifyName" disabled/>
        </el-form-item>
      </div>
      <el-form-item label="最低库存" prop="earlyWarningLeast">
        <el-input-number v-model="form.earlyWarningLeast" :min="1" step-strictly/>
      </el-form-item>
      <el-form-item label="最高库存" prop="earlyWarningMost">
        <el-input-number v-model="form.earlyWarningMost" :min="1" step-strictly/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {ElMessage} from 'element-plus';

import {setMaterialAPI} from '@/api/operationManagement/material.js'

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  single: false , // 单个设置
  rules: {
    earlyWarningLeast: [{required: true, message: '请输入最低库存'},],
    earlyWarningMost: [
      {required: true, message: '请输入最高库存'},
      {
        validator: (rule, value, callback) => {
          return value <= form.earlyWarningLeast ? callback(new Error('最高库存不能小于等于最低库存')) : callback()
        }
      }
    ]
  },
});

// 关闭dialog
const onClose = () => {
  delete form.ids
}


// 提交表单
const submit = () => {
  setMaterialAPI({
    ...form,
  }).then(res => {
    if (res.success) {
      ElMessage.success('设置成功');
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = (bool) => {
  state.single = bool
  dialog.value.open();
}

defineExpose({
  form,
  open,
});
</script>
