<template>
  <div>
    <el-descriptions :column="2" border>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            安装位置
          </div>
        </template>
        {{ detailData.spaceFullName }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            管理部门
          </div>
        </template>
        {{ detailData.deptName }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            生产厂商
          </div>
        </template>
        {{ detailData.factory }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            启用时间
          </div>
        </template>
        {{ detailData.activationDate }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            保修开始时间
          </div>
        </template>
        {{ detailData.warrantyBeginDate }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            保修结束时间
          </div>
        </template>
        {{ detailData.warrantyEndDate }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            IP地址
          </div>
        </template>
        {{ detailData.ip }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            Port端口
          </div>
        </template>
        {{ detailData.port }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            三维坐标
          </div>
        </template>
        {{ detailData.coordinate }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            所属子系统
          </div>
        </template>
        {{ detailData.subType }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            是否可控
          </div>
        </template>
        {{ detailData.controller == 0 ? '否' : '是' }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup>

const props = defineProps({
  detailData: {
    type: Object,
    default: {}
  }
})
const {detailData} = toRefs(props)
</script>

<style scoped>

</style>
    