<template>
  <dialog-common ref="dialog" :title="props.title" @submit="submit" @onClose="onClose"  :formRef="ruleFormRef" :width="450">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入分类名称"/>
      </el-form-item>
      <el-form-item label="上级分类" prop="parentId">
        <el-tree-select v-model="form.parentId" :data="props.cateOptions" check-strictly :render-after-expand="false"
                        node-key="id" :props="treeProps" placeholder="请选择上级分类" clearable/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {ElMessage} from "element-plus";

import { equipTypSaveAPI } from '@/api/operationManagement/equipType.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  cateOptions: {
    type: Array,
    default: []
  },
})

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  rules: {
    name: [{required: true, message: '请输入分类名称'}],
  },
});

const treeProps = computed(() => {
  return {
    label: 'name'
  }
})

// 关闭dialog
const onClose = () => {
  delete form.id
}

// 提交表单
const submit = () => {
  equipTypSaveAPI({
    ...form,
  }).then(res => {
    if (res.success) {
      ElMessage.success(form.id ? '保存成功' : '新增成功');
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = () => {
  dialog.value.open();
}

defineExpose({
  form,
  open,
});
</script>
