import { request } from "@/utils/request";

/**
 * 获取菜单(用户)
 * @param type pc 端菜单,app 移动端菜单
 * @returns {*}
 */
export const menuGetAPI = (type) => {
  let menuId = type=='pc'?"1":"2";
  return request('get','/admin/upms/login/getRouter?menuId='+menuId)
}

// 获取菜单
export const menuListAPI = () => {
  return request('get','/admin/upms/sysMenu/tree')
}

// 菜单添加
export const menuAddAPI = (data) => {
  return request('post','/admin/upms/sysMenu/add',data)
}

// 菜单编辑
export const menuEidtAPI = (data) => {
  return request('post','/admin/upms/sysMenu/update',data)
}

// 菜单删除
export const menuDelAPI = (data) => {
  return request('post','/admin/upms/sysMenu/delete',data)
}
