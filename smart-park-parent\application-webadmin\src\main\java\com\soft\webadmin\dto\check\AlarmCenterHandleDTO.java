package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName AlarmCenterHandleDTO
 * @description:
 * @date 2024年01月30日
 */
@Data
public class AlarmCenterHandleDTO {

    @ApiModelProperty(value = "告警id")
    @NotNull(message = "告警id不能为空！")
    private Long id;

    @ApiModelProperty(value = "处理类型：1生成工单、2取消告警")
    @NotNull(message = "处理类型不能为空！")
    private Integer operate;

}
