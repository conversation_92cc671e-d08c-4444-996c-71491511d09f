<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="inoutNo">
          <el-input v-model="formInline.inoutNo" placeholder="出库单号" />
        </el-form-item>
        <el-form-item prop="sparePartName">
          <el-input v-model="formInline.sparePartName" placeholder="出库备件" />
        </el-form-item>
        <el-form-item prop="createUserName">
          <el-input v-model="formInline.createUserName" placeholder="创建人" />
        </el-form-item>
        <el-form-item prop="type">
          <el-select v-model="formInline.type" clearable filterable placeholder="出库类型">
            <el-option v-for="(value,key) in state.typeOptions" :label="value" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="date">
          <el-date-picker
              v-model="formInline.date"
              type="daterange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="handleAdd">新增出库</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="80">
          <template #default="{row}">
            <el-button link type="primary" icon="Tickets" @click="handleView(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize"
                     :total="state.pagetion.total"
                     @size-change="sizeChange" @current-change="currentChange"/>
      <modalRepertory ref="repertory" @submit="getList"></modalRepertory>
    </template>
  </page-common>
</template>

<script setup>
import {ElTag} from 'element-plus';

import modalRepertory from '../modal/modalRepertory.vue'

import {putoutPageAPI} from '@/api/operationManagement/putout.js'

const emit = defineEmits(['showPage'])

const formInlineRef = ref()
const upload = ref()
const repertory = ref()

const formInline = reactive({})
const state = reactive({
  typeOptions: {
    6: '领用出库',
    7: '盘亏出库'
  },
  tableHeight: 100,
  tableData: [{}],
  tableHeader: [
    {
      prop: 'inoutNo',
      label: '出库单号',
      width: 210
    },
    {
      prop: 'type',
      label: '出库类型',
      formatter: (row, column, cellValue) => {
        return state.typeOptions[cellValue]
      }
    },
    {
      prop: 'sparePartNames',
      label: '出库备件'
    },
    {
      prop: 'quantity',
      label: '出库数量'
    },
    {
      prop: 'createUserName',
      label: '创建人'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

// 获取设备
const getList = () => {
  let query = {
    ...formInline,
    operateType: 2, // 出库
    businessType: 'OPERATIONS', // 运维
    examineState: 1, //待出库/待出库
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
  }

  if (query.date && query.date.length) {
    query.beginDate = query.date[0]
    query.endDate = query.date[1]
  }

  putoutPageAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 新增出库
const handleAdd = () => {
  repertory.value.open()
}

// 详情
const handleView = ({id}) => {
  emit('showPage', 1, id)
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped></style>
