import { request } from '@/utils/request';

// 分页查询
export const getAuditPageAPI = (query) => {
  return request('get', '/check/workQuote/getPage', query, 'F');
};

// 历史报价
export const getWorkQuoteListAPI = (data) => {
  return request('get', '/check/workQuote/list', data, 'F');
};

// 费用汇总
export const getQuoteTotalAPI = (query) => {
  return request('get', '/check/workQuote/collect', query, 'F');
}

// 审批
export const approveQuoteAPI = (data) => {
  return request('post', '/check/workQuote/examine', data);
}
