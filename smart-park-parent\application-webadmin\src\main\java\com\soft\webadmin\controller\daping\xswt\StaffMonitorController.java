package com.soft.webadmin.controller.daping.xswt;

import com.soft.common.core.object.ResponseResult;
import com.soft.sub.model.equipment.EntranceGuardAccessRecord;
import com.soft.webadmin.service.daping.xswt.StaffService;
import com.soft.webadmin.vo.daping.xswt.staffMonitor.StaffDistributionVO;
import com.soft.webadmin.vo.daping.xswt.staffMonitor.StaffFlow24HVO;
import com.soft.webadmin.vo.face.FaceRepositoryVO;
import com.soft.webadmin.vo.visitor.VisitorPassRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @ClassName StaffMonitorController
 * @description:
 * @date 2025年08月06日
 */
@Api(tags = "人员态势")
@RestController
@RequestMapping("/daping/xswt/staff")
public class StaffMonitorController {

    @Autowired
    private StaffService staffService;

    @ApiOperation(value = "人员出入记录")
    @GetMapping("/entranceGuardAccessRecord")
    public ResponseResult<List<EntranceGuardAccessRecord>> entranceGuardAccessRecordList() {
        return ResponseResult.success(staffService.entranceGuardAccessRecordList());
    }

    @ApiOperation(value = "访客记录")
    @GetMapping("/visitorPassRecord")
    public ResponseResult<List<VisitorPassRecordVO>> visitorPassRecordList() {
        return ResponseResult.success(staffService.visitorPassRecordList());
    }

    @ApiOperation(value = "人员黑白名单")
    @GetMapping("/faceRepository")
    public ResponseResult<List<FaceRepositoryVO>> faceRepositoryList() {
        return ResponseResult.success(staffService.faceRepositoryList());
    }

    @ApiOperation(value = "人员类型统计")
    @GetMapping("/staffTypeStat")
    public ResponseResult<StaffDistributionVO> staffTypeStat(String startDate, String endDate) {
        return ResponseResult.success(staffService.staffTypeStat(startDate, endDate));
    }

    @ApiOperation(value = "24小时人员流量")
    @GetMapping("/staffFlow24H")
    public ResponseResult<List<StaffFlow24HVO>> staffFlow24H() {
        return ResponseResult.success(staffService.staffFlow24H());
    }

    @ApiOperation(value = "人员分布")
    @GetMapping("/staffDistribution")
    public ResponseResult<Map<String, StaffDistributionVO>> staffDistribution() {
        return ResponseResult.success(staffService.staffDistribution());
    }
}
