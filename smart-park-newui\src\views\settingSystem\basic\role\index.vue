<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
        <el-form-item prop="roleName">
          <el-input v-model="formInline.roleName" placeholder="角色名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" :icon="Plus" @click="addHandle">新建角色</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" :width="item.width">
          <template #default="{ row }">
            <div v-if="item.prop === 'status'">
              <el-switch v-model="row.status" inline-prompt
                active-text="启用"
                inactive-text="禁用" :active-value="1" :inactive-value="0" @change="onModifyStatus(row)" />
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="160">
          <template #default="scope">
            <el-button @click="editHandle(scope.row)" link icon="Edit" type="primary">编辑</el-button>
            <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <modal-page ref="modal" :title="state.title" :treeData="state.treeData" @submit="getList"></modal-page>
    </template>
  </page-common>
</template>

<script setup>
import modalPage from './component/modalPage.vue'
import {
  Delete, Plus, Search, Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import {roleListAPI, roleDelAPI, roleViewAPI, roleModifyStatus} from '@/api/settingSystem/role.js'
import { menuListAPI } from '@/api/settingSystem/menu.js'
import { calcPageNo } from '@/utils/util.js'

let formInlineRef = ref()
let modal = ref()

const formInline = reactive({})

const state = reactive({
  roleId: '',
  treeData: [],
  title: '',
  drawer: false,
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'roleName',
      label: '角色名称'
    },
    {
      prop: 'roleDesc',
      label: '角色描述'
    },
    {
      prop: 'status',
      label: '角色状态',
      width: 80
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
  rules: {
    roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' },]
  }
})

onMounted(() => {
  getList()
})

//分页
const getList = () => {
  let query = {
    sysRoleDtoFilter: formInline,
    pageParam: state.pagetion
  }
  roleListAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

//查询树形
const selectTree = () => {
  menuListAPI().then(res => {
    state.treeData = res.data
    if (state.roleId) {
      roleViewAPI({ roleId: state.roleId }).then(res => {
        var list = []
        res.data.sysRoleMenuList.forEach(e => {
          list.push(e.menuId)
        })
        modal.value.state.strictly = true;
        modal.value.open()
        nextTick(() => {
          modal.value.form.defaultChecked = list
          Object.assign(modal.value.form, { ...res.data })
          setTimeout(() => {
            modal.value.state.strictly = false;
          }, 200);
        })
      })
    } else {
      modal.value.open()
    }
  })
}

//新建按钮事件
const addHandle = () => {
  state.roleId = ''
  modal.value.form.roleId = ''
  state.title = '新建角色'
  selectTree()
}

//编辑按钮事件
const editHandle = (info) => {
  state.title = '编辑角色'
  state.roleId = info.roleId
  selectTree()
}

//删除事件
const deleteHandle = (info) => {
  ElMessageBox.confirm("是否删除当前角色?", "提醒", {
    type: "warning",
  }).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize)
    roleDelAPI({ roleId: info.roleId }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  });
}

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

//重置方法
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}


const onModifyStatus = (row) => {
  roleModifyStatus({ roleId: row.roleId }).then(res => {
    if (res.success) {
      ElMessage.success('状态修改成功！');
    } else {
      ElMessage.error("状态修改失败！")
    }
  })
}


</script>

<style lang='less' scoped></style>
