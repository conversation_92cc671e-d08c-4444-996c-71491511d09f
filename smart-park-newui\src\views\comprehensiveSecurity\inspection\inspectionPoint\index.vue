<template>
  <tree-page-common v-model="state.tableHeight">
    <template #leftTree>
      <el-input placeholder="请输入关键词" v-model="filterText"/>
      <el-tree ref="treeRef" class="filter-tree" :data="state.treeData" node-key="id" :props="defaultProps"
               style="margin-top: 10px;" default-expand-all show-checkbox @change="handleCheckChange" :filter-node-method="filterNode"/>
    </template>
    <template #query>
      <el-form :inline="true" ref="queryFormRef" :model="state.queryForm" label-suffix=":">
        <el-form-item prop="queryName">
          <el-input v-model="state.queryForm.queryName" placeholder="巡更点编号或者名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="queryList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <div class="tab-header-btn">
        <el-button type="primary" @click="onAdd">
          <el-icon>
            <Plus/>
          </el-icon>
          <span>新建巡更点</span>
        </el-button>
      </div>
      <el-table :data="state.tableData" :height="state.tableHeight - 50" tooltip-effect="light">
        <template v-for="(item, index) in state.tableHeader" :key="index">
          <el-table-column :prop="item.prop" :label="item.label"
                           :show-overflow-tooltip="true">
          </el-table-column>
        </template>
        <el-table-column label="巡更点二维码" align="center">
          <template #default="scope">
            <el-popover placement="top" :width="100" trigger="click">
              <template #reference>
                <el-button link type="primary" :icon="Check">查看</el-button>
              </template>
              <qrcode-vue :value="scope.row.qrCode" :size="122" level="H" />
            </el-popover>
            <el-button link type="primary" :icon="Download" @click="onDownQrCode(scope)">下载</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click.prevent="onEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.prevent="onDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize" :total="state.pageParam.total"
                     @size-change="sizeChange" @current-change="currentChange" />

      <point-dialog ref="pointDialogRef" @onClose="queryList"/>
    </template>
  </tree-page-common>
</template>

<script setup>

import {Check, Download, Refresh, Search} from "@element-plus/icons-vue";
import {treeAPI} from "@/api/iotManagement/space.js";
import {deleteInspectionPointAPI, listInspectionPointAPI} from "@/api/comprehensiveSecurity/inspectionPoint.js";
import PointDialog from "@/views/comprehensiveSecurity/inspection/inspectionPoint/component/pointDialog.vue";
import {ElMessage, ElMessageBox} from "element-plus";
import QrcodeVue from 'qrcode.vue'


// 侧边树形对象
let treeRef = ref();
// 过滤关键词
const filterText = ref('')

// 新增或编辑组件
let pointDialogRef = ref()


// 表单查询对象
let queryFormRef = ref()


const state = reactive({
  tableHeight: 100,
  // 树形数据
  treeData: [],
  queryForm: {
    queryName: null
  },
  tableData: [],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
  tableHeader: [
    { label: '巡更点编号', prop: 'code' },
    { label: '巡更点名称', prop: 'name' },
    { label: '备注', prop: 'remark' }
  ]
})

const defaultProps = {
  children: 'children',
  label: 'name',
}

/**
 * 监听侧边树形选项关键词过滤
 */
watch(filterText, (val) => {
  treeRef.value.filter(val)
})

/**
 * 选择侧边树形选项
 * @param data
 * @param checked
 * @param indeterminate
 */
const handleCheckChange = (data, checked, indeterminate) => {
  queryList();
}

/**
 * 侧边树形选项关键词过滤
 * @param value
 * @param data
 * @returns {*|boolean}
 */
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value)
}


const sizeChange = (val) => {
  state.pageParam.pageSize = val
  queryList()
}

const currentChange = (val) => {
  state.pageParam.pageNum = val
  queryList()
}

/**
 * 下载二维码
 * @param scope
 */
const onDownQrCode = async (scope) => {
  //获取canvas标签
  let canvas = document.getElementsByTagName('canvas')
  //创建a标签
  let a = document.createElement('a')
  //获取二维码的 url并赋值为 a.href
  a.href = canvas[scope.$index].toDataURL('img/png')
  //设置下载文件的名字
  let val = scope.row
  a.download = val.code + '-' + val.name + '-' + '巡更点'
  //点击事件，相当于下载
  a.click()
  //提示信息
  ElMessage.success('下载中，请稍后...')
  a.remove()
}

const onEdit = (val) => {
  pointDialogRef.value.open('编辑巡更点', val)
}

const onAdd = () => {
  pointDialogRef.value.open('新建巡更点', null)
}


const onDelete = (val) => {
  ElMessageBox.confirm(
    '是否删除当前巡更点?',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    let params = {
      id: val.id
    }
    deleteInspectionPointAPI(params).then(res => {
      if (res.success) {
        ElMessage.success('删除成功！')
      } else {
        ElMessage.error('删除失败！' + res.errorMessage)
      }
      queryList()
    });
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除！',
    })
  })
}

/**
 * 查询表单列表
 */
const queryList = () => {
  let query = {
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  if (state.queryForm.queryName) {
    query.queryName = state.queryForm.queryName
  }
  let spaceIds = []
  if (treeRef.value) {
    treeRef.value.getCheckedNodes().forEach(e => {
      spaceIds.push(e.id)
    })
    if (spaceIds.length > 0) {
      query.spaceIds = spaceIds.join(',')
    }
  }
  listInspectionPointAPI(query).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList
      // 设置二维码
      for (let data of state.tableData) {
        let qrData = {
          id: data.id + '',
          type: 'inspection-point'
        }
        data.qrCode = JSON.stringify(qrData)
      }
      state.pageParam.total = res.data.totalCount
    }
  })
}

/**
 * 重置
 */
const onReset = () => {
  queryFormRef.value.resetFields()
  treeRef.value.setCheckedNodes([])

  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  queryList()
}

/**
 * 查询侧边空间树形栏
 */
const getTree = () => {
  return new Promise((resolve, reject) => {
    nextTick(() => {
      let query = {
        deep: 4
      }
      treeAPI(query).then(res => {
        state.treeData = res.data
        resolve()
      })
    })
  })
}



onMounted(async () => {
  await getTree()
  queryList()
})

</script>

<style scoped lang="less">
.tab-header-btn {
  margin-bottom: 18px;
}

.page {
  height: 100% !important;
}
</style>
