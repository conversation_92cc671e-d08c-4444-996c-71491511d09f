<template>
  <div style="height: 100%;overflow: hidden;">
    <transition name="el-zoom-in-center">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-change="onTabChange" v-show="pageIndex == 0">
        <el-tab-pane label="设备信息" name="first">
          <tableListEquip @showPage="showPage" equipmentTypeId="100022"></tableListEquip>
        </el-tab-pane>
        <el-tab-pane label="告警设置" name="second">
          <warning-setting ref="warningSettingRef" :sub-type="''"
            :equipment-type="{ id: '100022', name: '环境监测', type: 'ENVIRONMENT_MONITOR' }"
            :job="'environmentMonitorHandler'" />
        </el-tab-pane>
        <el-tab-pane label="告警记录" name="third">
          <alarm-record ref="alarmRecordRef" :equipment-type="{ id: '100022', name: '环境监测' }" />
        </el-tab-pane>
      </el-tabs>
    </transition>
    <transition name="el-zoom-in-center">
      <detail v-show="pageIndex == 1" @showPage="showPage" ref="detailRef"></detail>
    </transition>
  </div>
</template>

<script setup>
import tableListEquip from '../component/tablelistequip.vue'
import WarningSetting from "@/views/iotManagement/subsystemOperation/environmental/component/warningSetting.vue";
import AlarmRecord from "@/views/iotManagement/subsystemOperation/component/alarmRecord.vue";

import detail from '@/components/deviceDetail/index.vue'

import { events } from "@/utils/bus.js";

const activeName = ref('first')
const pageIndex = ref(0)

const warningSettingRef = ref()
const alarmRecordRef = ref()
const detailRef = ref()

const onTabChange = () => {
  nextTick(() => {

    if (activeName.value === 'second') {
      warningSettingRef.value.init()
    } else if (activeName.value === 'third') {
      alarmRecordRef.value.init()
    }

    events.emit('tabClick')
  })
}

const showPage = (index, equipmentId) => {
  pageIndex.value = index

  if (index == 0) { //刷新
    if (activeName.value === 'second') {
      warningSettingRef.value.init()
    } else if (activeName.value === 'third') {
      alarmRecordRef.value.init()
    }
  } else if (index == 1) {
    detailRef.value.equipmentId = equipmentId
    detailRef.value.init();
  }
}

</script>
<style lang='less' scoped>
.el-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);

    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>