package com.soft.webadmin.dto.face;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * FaceRepositoryDTO对象
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@ApiModel("FaceRepositoryDTO对象")
@Data
public class FaceRepositoryDTO {

    @ApiModelProperty(value = "人脸id")
    @NotNull(message = "数据验证失败，人脸id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "人脸库类型，1黑名单；2白名单")
    private Integer listType;

    @ApiModelProperty(value = "姓名")
    private String username;

    @ApiModelProperty(value = "性别，1男；2女；3未知")
    private Integer sex;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "证书类型")
    private String certificateType;

    @ApiModelProperty(value = "证书号码")
    private String certificateNum;

    @ApiModelProperty(value = "人脸图片url")
    private String faceUrl;

    @ApiModelProperty(value = "人脸唯一标识")
    private String faceIndexCode;

    @ApiModelProperty(value = "人脸库分组唯一标识")
    private String faceGroupIndexCode;

    @ApiModelProperty(value = "人脸库分组名称")
    private String faceGroupName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "删除标识，1未删除；-1已删除")
    private Integer deletedFlag;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateUserId;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private String enterType;
}
