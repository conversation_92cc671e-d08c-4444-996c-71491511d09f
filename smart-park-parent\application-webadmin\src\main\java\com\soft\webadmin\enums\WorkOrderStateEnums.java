package com.soft.webadmin.enums;

/**
 * 工单状态
 */
public enum WorkOrderStateEnums {

    NO_ALLOT(1, "待派单"),
    NO_RESPONSE(2, "未响应"),
    HANDING(3, "处理中"),
    CLOSE(4, "已关闭"),
    FINISH(5, "已完成"),
    ;

    private Integer value;

    private String name;

    public Integer getValue() {
        return value;
    }

    WorkOrderStateEnums(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
