<template>
  <div>
    <el-dialog v-model="showDialogRef" :title="state.title" align-center width="60%" @close="close" style="overflow: hidden" class="dialogCommon">
      <template #default>
        <el-form :model="state.dataForm" ref="dataFormRef" :rules="state.rules" :label-width="90" label-suffix=":" class="content">
          <el-container>
            <el-container>
              <el-main>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="名称" prop="roomName">
                      <el-text>{{ state.dataForm.roomName }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="容纳人数" prop="capacity">
                      <el-text>{{ state.dataForm.capacity }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="预约审批" prop="isEnableApproval">
                      <el-text v-if="state.dataForm.isEnableApproval === 1" type="success">是</el-text>
                      <el-text v-else type="info">否</el-text>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="会议室设备" prop="devices">
                  <el-space>
                    <el-tag v-for="device in state.dataForm.devices">{{ device }}</el-tag>
                  </el-space>
                </el-form-item>
                <el-form-item label="会议名称" prop="meetingName">
                  <el-input v-model="state.dataForm.meetingName" placeholder="请输入会议名称" />
                </el-form-item>
                <el-form-item label="会议内容" prop="meetingContent">
                  <el-input type="textarea" v-model="state.dataForm.meetingContent" placeholder="请输入会议内容" />
                </el-form-item>
                <el-form-item label="参会人员" prop="meetingUserIds">
                  <el-select
                    multiple
                    clearable
                    collapse-tags
                    collapse-tags-tooltip
                    :max-collapse-tags="5"
                    v-model="state.dataForm.meetingUserIds"
                    placeholder="请选择参会人员">
                    <el-option
                      v-for="item in state.userOptions"
                      :key="item.userId"
                      :label="item.showName"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-main>
              <el-aside width="30%">
                <div class="demo-image__error">
                  <div class="block">
                    <el-form-item label-width="0" prop="roomImg">
                      <el-image :src="imgTransfer(state.dataForm.roomImg)" fit="fill">
                        <template #error>
                          <div class="image-slot">
                            <el-icon>
                              <icon-picture/>
                            </el-icon>
                          </div>
                        </template>
                      </el-image>
                    </el-form-item>
                  </div>
                </div>
              </el-aside>
            </el-container>
            <el-footer height="120">
              <el-row :gutter="20">
                <el-col :span="7">
                  <el-form-item label="会议日期" prop="meetingDate">
                    <el-date-picker
                      v-model="meetingDate"
                      type="date"
                      :clearable="false"
                      :value-format="'YYYY-MM-DD'"
                      placeholder="会议日期"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="开始时间" prop="meetingBeginTime">
                    <el-text>{{ state.dataForm.meetingBeginTime }}</el-text>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="结束时间" prop="meetingEndTime">
                    <el-text>{{ state.dataForm.meetingEndTime }}</el-text>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="会议时长" prop="meetingDuration">
                    <el-text>{{ state.dataForm.meetingDuration ? state.dataForm.meetingDuration >= 60 ? parseInt(state.dataForm.meetingDuration / 60) + ' 小时 ' +  (state.dataForm.meetingDuration % 60 > 0 ? state.dataForm.meetingDuration % 60 + ' 分钟' : '') : state.dataForm.meetingDuration + ' 分钟' : '' }}</el-text>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :offset="17">
                  <el-space>
                    <el-tag type="info">不可预定</el-tag>
                    <el-tag type="danger">已预定</el-tag>
                    <el-tag type="success">可预定</el-tag>
                    <el-tag>已选择</el-tag>
                  </el-space>
                </el-col>
              </el-row>


              <div style="margin-left: 30px">
                <!-- 横坐标表头 -->
                <el-row style="margin-top: 10px;">
                  <el-space>
                    <div v-for="j in 25">
                      <span class="headTimeItem" v-if="j !== 1">{{ j-2 }}</span>
                      <span style="width: 50px; text-align: center; margin-left: 6px" v-else>时间段</span>
                    </div>
                  </el-space>
                </el-row>

                <el-row class="time-node-row">
                  <!-- 纵坐标表头 -->
                  <el-space direction="vertical">
                    <div v-for="j in 4" style="width: 50px;text-align: center;margin-right: 8px;height: 32px;line-height: 32px">
                      {{ `${(j-1)*15}-${j*15}` }}
                    </div>
                  </el-space>
                  <!-- 单元格 -->
                  <el-space>
                    <div v-for="hNode in state.timeNodes">
                      <el-space direction="vertical">
                        <div v-for="mNode in hNode.mNodes">
                          <el-button-group @click="checkNode(mNode)" @mouseover="decideNode(mNode,true)" @mouseleave ="decideNode(mNode,false)">
                            <el-button v-if="mNode.status === 0" :disabled="true" color="#f5f7fa" />
                            <el-button v-else-if="mNode.status === 1" :disabled="true" color="#f89191" />
                            <el-button v-if="mNode.status === 2" color="#71c647" :disabled="mNode.disabled"/>
                            <el-button v-if="mNode.status === 3" color="#6fb5ff" />
                          </el-button-group>
                        </div>
                      </el-space>
                    </div>
                  </el-space>
                </el-row>
              </div>
            </el-footer>
          </el-container>
        </el-form>
      </template>
      <template #footer>
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="onSave">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>


<script setup>
import { Picture as IconPicture } from '@element-plus/icons-vue'

import {getPageAPI} from "@/api/settingSystem/user.js"
import dayjs from 'dayjs'
import {ElMessage} from "element-plus";
import {listMeetingTimeUsedAPI, saveMeetingAPI} from "@/api/parkOperation/meeting.js";


const emits = defineEmits(['onClose'])

const showDialogRef = ref(false)

const dataFormRef = ref()

let meetingDate = ref()

const state = reactive({
  // 标题
  title: '',
  // 表单数据
  dataForm: {},
  // 已选择节点
  checkTimeNodes: [],
  // 已预定的节点
  usedTimeNodes: [],
  // 用户列表
  userOptions: [],
  // 时间节点数组
  timeNodes: [],
  // 定时器
  timer: null,
  rules: {
    meetingName: [
      { required: true, message: '会议名称不能为空！', trigger: 'blur' }
    ],
    meetingContent: [
      { required: true, message: '会议内容不能为空！', trigger: 'blur' }
    ],
    meetingDate: [
      { required: true, message: '会议日期不能为空！', trigger: 'blur' }
    ],
    meetingDuration: [
      { required: true, message: '会议时长不能为空', trigger: 'blur' }
    ]
  }
})

// 监听会议日期
watch(meetingDate, (newValue, olValue) => {
  state.dataForm.meetingDate = newValue
  // 初始化已预定时间节点
  initUsedTimeNodes();
  // 初始化选择节点
  initCheckTimeNode()
  // 监控节点状态
  monitorTimeNodeStatus()
})

// 初始化已被预定时间节点
const initUsedTimeNodes = () => {
  // 初始化节点
  state.usedTimeNodes.forEach(node => node.status = 0)
  state.usedTimeNodes = []
  // 监听
  monitorUsedTimeNodes()
}

// 监听被使用的节点
const monitorUsedTimeNodes = async () => {
  // 查询已被使用的节点
  await listMeetingTimeUsed(meetingDate.value)
}


// 查询已被使用的时间
const listMeetingTimeUsed = (meetingDate) => {
  if (meetingDate) {
    listMeetingTimeUsedAPI({roomId: state.dataForm.roomId, date: meetingDate}).then(res => {
      if (res.success) {
        // 循环已使用的 时间段
        res.data.forEach(usedTime => {
          // 开始时间
          let meetingBeginTime = usedTime.meetingBeginTime;
          // 结束时间
          let meetingEndTime = usedTime.meetingEndTime;

          // 计算 分钟的 差值
          let diffMinute = (dayjs(meetingEndTime, 'HH:mm:ss') - dayjs(meetingBeginTime, 'HH:mm:ss')) / 60000;

          // 开始时间
          let startTime = meetingBeginTime.split(':');
          let startHour = parseInt(startTime[0]);
          let startMinute = parseInt(startTime[1]);

          // 遍历次数
          let passesCount = diffMinute / 15;
          // 计算出节点
          for (let i = 0; i < passesCount; i++) {
            startMinute = i === 0 ? startMinute : startMinute + 15
            if (startMinute === 60) {
              startMinute = 0
              startHour = startHour + 1
            }

            // 判断已被预约的节点，添加到已被预约的数组中
            state.timeNodes.forEach(hNode => {
              let hTime = hNode.hTime;
              if (hTime === startHour) {
                let mNodes = hNode.mNodes;
                mNodes.forEach(mNode => {
                  if (mNode.mTime.start === startMinute) {
                    if (mNode.status !== 0) {
                      mNode.status = 1
                    }
                    state.usedTimeNodes.push(mNode)
                  }
                })
              }
            })
          }
        })
      }
    })
  }
}


// 初始化时间节点
const initTimeNodes = () => {
  for (let i = 0; i < 24; i++) {
    let hNode = {
      hTime: i,
      mNodes: []
    }
    for (let j = 0; j < 4; j++) {
      let mNode = {
        mTime: {},
        hTime: i,
        // 节点状态：0 不可预定；1 已预定；2 可预定；3 已选择
        status: 0
      }
      switch (j) {
        case 0:
          mNode.mTime.start = 0
          mNode.mTime.end = 15
          break;
        case 1:
          mNode.mTime.start = 15
          mNode.mTime.end = 30
          break;
        case 2:
          mNode.mTime.start = 30
          mNode.mTime.end = 45
          break;
        case 3:
          mNode.mTime.start = 45
          mNode.mTime.end = 60
          break;
        default: break
      }
      hNode.mNodes.push(mNode)
    }
    state.timeNodes.push(hNode)
  }
}


// 监听节点状态
const monitorTimeNodes = () => {
  monitorTimeNodeStatus()

  // 清除定时器
  clearInterval(state.timer)
  state.timer = null

  let count = 1

  // 开启定时任务
  state.timer = setInterval(() => {
    monitorTimeNodeStatus()
    monitorUsedTimeNodes()
    monitorCheckTimeNode()

    if (count++ === 18) {
      ElMessage.error('长时间未预约，请重新打开预约界面！')
      close()
    }
  }, 10000);
}

// 监控时间节点状态
const monitorTimeNodeStatus = () => {
  // 选择的会议时间
  let meetingDateNow = meetingDate.value
  if (meetingDateNow) {
    // 当前时间
    let now = new Date()
    let nowDate = dayjs(now).format('YYYY-MM-DD')

    // 判断时间节点列表
    state.timeNodes.forEach(hNode => {
      let hTime = hNode.hTime;
      // 当前日期 大于 会议日期，则 所有的节点都不可选
      if (nowDate > meetingDateNow) {
        hNode.mNodes.forEach(mNode => mNode.status = 0)
        // 当前日期 等于 会议日期，则判断 小时和 分钟
      } else if (nowDate === meetingDateNow) {
        let hours = now.getHours();
        // 节点时间 大于 当前时间，则所有节点都可以选择
        if (hTime > hours) {
          hNode.mNodes.forEach(mNode => {
            if (mNode.status !== 1 && mNode.status !== 3) {
              mNode.status = 2
            }
          })
          // 节点时间 等于 当前时间，则判断分钟
        } else if (hTime === hours){
          hNode.mNodes.forEach(mNode => {
            let mTime = mNode.mTime;
            let minutes = now.getMinutes();
            // 当前节点的起始分钟 大于 当前时间的起始分钟，才可以选择
            if (mTime.start > minutes) {
              if (mNode.status !== 1 && mNode.status !== 3) {
                mNode.status = 2
              }
            } else {
              mNode.status = 0
            }
          })
          // 节点时间 小于 当前时间，则所有节点都不可以选择
        } else {
          hNode.mNodes.forEach(mNode => mNode.status = 0)
        }
        // 当前日期 小于 会议日期，则所有节点都可选，注意：需要再判断节点是否已被预定
      } else {
        hNode.mNodes.forEach(mNode => {
          if (mNode.status !== 1 && mNode.status !== 3) {
            mNode.status = 2
          }
        })
      }
    })
  }
}

// 监控选择节点
const monitorCheckTimeNode = () => {
  state.checkTimeNodes = state.checkTimeNodes.filter(mNode => mNode.status === 3)
  // 计算选择节点时间
  computerCheckTimeNode()
}

// 选择节点
const checkNode = (mNode) => {
  let status = mNode.status
  // 节点状态为 2，可选状态，改为已选状态
  if (status === 2) {
    mNode.status = 3
    state.checkTimeNodes.push(mNode);
    // 节点状态为 3，已选状态，改为可选状态
  } else if (status === 3) {
    // 如果当前节点不是首、尾节点，则不可移除
    if (mNode !== state.checkTimeNodes[0] && mNode !== state.checkTimeNodes[state.checkTimeNodes.length -1]) {
      ElMessage.warning('中间节点不可移除！')
      return
    }

    state.checkTimeNodes = state.checkTimeNodes.filter(node => node !== mNode);
    // 移除节点，再修改状态
    mNode.status = 2
    monitorTimeNodeStatus()
  }

  // 排序
  state.checkTimeNodes.sort((node1, node2) => {
    // 节点 1 的时间
    let hTime1 = node1.hTime;
    let mTime1 = node1.mTime;

    // 节点 2 的时间
    let hTime2 = node2.hTime;
    let mTime2 = node2.mTime;

    // 比较时间
    return Date.parse(meetingDate.value + ' ' + hTime1 + ':' + mTime1.start) - Date.parse(meetingDate.value + ' ' + hTime2 + ':' + mTime2.start)
  })

  // 计算时间
  computerCheckTimeNode()
}


// 决策节点是否可以点击
const decideNode = (mNode, bool) => {
  if (!state.checkTimeNodes.includes(mNode)) {
    if (state.checkTimeNodes.length > 0) {
      // 当前节点
      let hTime = mNode.hTime
      let start = mNode.mTime.start

      // 和首节点比较，当前节点的开始时间 + 15分钟，等于首节点的开始时间
      let firstTimeNode = state.checkTimeNodes[0]

      let isFirst = Date.parse(meetingDate.value + ' ' + hTime + ':' + start) + 15 * 60 * 1000 ===
        Date.parse(meetingDate.value + ' ' + firstTimeNode.hTime + ':' + firstTimeNode.mTime.start);

      // 和尾节点比较，尾节点的开始时间 + 15分钟，等于当前节点的开始时间
      let lastTimeNode = state.checkTimeNodes[state.checkTimeNodes.length - 1];

      let isLast = Date.parse(meetingDate.value + ' ' + hTime + ':' + start) ===
        Date.parse(meetingDate.value + ' ' + lastTimeNode.hTime + ':' + lastTimeNode.mTime.start) + 15 * 60 * 1000;

      if (!isFirst && !isLast) {
        mNode.disabled = bool
      }
    }
  }
}

// 计算选择节点时间
const computerCheckTimeNode = () => {
  if (state.checkTimeNodes != null && state.checkTimeNodes.length > 0) {
    // 计算时间
    let nodeSize = state.checkTimeNodes.length
    let firstNode = state.checkTimeNodes[0]
    let lastNode = state.checkTimeNodes[nodeSize - 1]
    state.dataForm.meetingBeginTime = meetingDate.value + ' ' + firstNode.hTime + ':' + firstNode.mTime.start
    if (firstNode.mTime.start === 0) {
      state.dataForm.meetingBeginTime = state.dataForm.meetingBeginTime + 0
    }

    let lastHTime = lastNode.hTime
    let lastMTime = lastNode.mTime.end
    if (lastNode.mTime.end === 60) {
      lastHTime = lastHTime + 1
      lastMTime = '00'
    }
    state.dataForm.meetingEndTime = meetingDate.value + ' ' + lastHTime + ':' + lastMTime

    state.dataForm.meetingDuration = nodeSize * 15
  } else {
    state.dataForm.meetingBeginTime = null
    state.dataForm.meetingEndTime = null
    state.dataForm.meetingDuration = null
  }
}


// 修改会议日期
const initCheckTimeNode = () => {
  state.checkTimeNodes.forEach(mNode => {
    if (mNode.status !== 1) {
      mNode.status = 0
    }
  })
  state.checkTimeNodes = []
  // 计算初始化后的时间
  computerCheckTimeNode()
}



const onSave = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      let params = {
        roomId: state.dataForm.roomId,
        meetingName: state.dataForm.meetingName,
        meetingDate: meetingDate.value,
        meetingBeginTime: state.dataForm.meetingBeginTime,
        meetingEndTime: state.dataForm.meetingEndTime,
        meetingDuration: state.dataForm.meetingDuration,
        meetingContent: state.dataForm.meetingContent,
        meetingUserIds: state.dataForm.meetingUserIds.join(',')
      }
      saveMeetingAPI(params).then(res => {
        if (res.success) {
          if (state.dataForm.isEnableApproval) {
            ElMessage.success('预约成功，等待审批！')
          } else {
            ElMessage.success('预约成功！')
          }
          close()
        }
      })
    }
  })
}


// 查询人员列表
const queryUsers = () => {
  let data = {
    sysUserDtoFilter: {
      userStatus: 0
    }
  }
  getPageAPI(data).then(res => {
    state.userOptions = res.data.dataList
  })
}

const open = (title, val) => {
  showDialogRef.value = true
  nextTick(() => {
    state.title = title
    if (val) {
      state.dataForm = val
      state.dataForm.roomId = val.id
      state.dataForm.devices = val.devices.split(',')
    }

    meetingDate.value = dayjs().format('YYYY-MM-DD')
    queryUsers()

    // 监听节点状态
    monitorTimeNodes()
  })
}

const close = () => {
  showDialogRef.value = false

  // 清除定时器
  clearInterval(state.timer)
  state.timer = null

  state.dataForm = {}
  meetingDate.value = null

  // 重置表单
  dataFormRef.value.resetFields()

  // 调用父组件事件
  emits('onClose')
}


const imgTransfer = (name) => {
  if (name) {
    return import.meta.env.VITE_BASE_URL + name
  }
  return name
}


onUnmounted(() => {
  // 清除定时器
  clearInterval(state.timer)
  state.timer = null
})

onMounted(() => {
  // 初始化节点
  initTimeNodes()
})

defineExpose({
  open
})
</script>

<style scoped>
.content {
    margin-top: 10px;

    .el-input {
        width: 90%;
    }

    .el-select, .el-cascader {
        width: 90%;
    }

    .el-input-number {
        width: 90%;

        .el-input {
            width: 100%;
        }

        :deep(.el-input__inner) {
            text-align: left;
        }
    }

    .el-textarea {
        width: 90%;
    }

    :deep(.el-cascader) {
        width: 90%;
        flex-grow: 0;
    }
}


.demo-image__error .block {
    text-align: center;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
    vertical-align: top;
}

.demo-image__error .demonstration {
    display: block;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-bottom: 20px;
}

.demo-image__error .el-image {
    padding: 0 5px;
    max-width: 300px;
    max-height: 280px;
    width: 100%;
    height: 220px;
}

.demo-image__error .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: var(--el-fill-color-light);
    color: var(--el-text-color-secondary);
    font-size: 30px;
}

.demo-image__error .image-slot .el-icon {
    font-size: 30px;
}

.headTimeItem{
    display: inline-block;
    width: 32px;
    text-align: center;
}

.time-node-row {
    margin-top: 10px;
    flex-wrap: nowrap;
}
</style>


