package com.soft.webadmin.controller.sparePart;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.sparePart.SparePartStocktakingDTO;
import com.soft.webadmin.dto.sparePart.SparePartStocktakingQueryDTO;
import com.soft.webadmin.enums.BusinessTypeEnums;
import com.soft.webadmin.service.sparePart.SparePartStocktakingService;
import com.soft.webadmin.vo.sparePart.SparePartStocktakingFullVO;
import com.soft.webadmin.vo.sparePart.SparePartStocktakingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 备品备件盘点控制器类
 * 
 * <AUTHOR>
 * @date 2024-03-05
 */
@Api(tags = "库存盘点")
@RestController
@RequestMapping("/sparePart/stocktaking")
public class SparePartStocktakingController {

    @Autowired
    private SparePartStocktakingService sparePartStocktakingService;

    @ApiOperation(value = "保存")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@Validated @RequestBody SparePartStocktakingDTO saveDTO) {
        return sparePartStocktakingService.saveOrUpdate(saveDTO);
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<SparePartStocktakingVO>> getPage(SparePartStocktakingQueryDTO queryDTO) {
        return ResponseResult.success(sparePartStocktakingService.getPage(queryDTO));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public ResponseResult<SparePartStocktakingFullVO> detail(Long id) {
        return ResponseResult.success(sparePartStocktakingService.detail(id));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/detele")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        return sparePartStocktakingService.delete(id);
    }

    @ApiOperation(value = "入库")
    @PostMapping("/putIn")
    public ResponseResult<Void> putIn(@RequestParam Long id) {
        return sparePartStocktakingService.putIn(id);
    }

    @ApiOperation(value = "出库")
    @PostMapping("/out")
    public ResponseResult<Void> out(@RequestParam Long id) {
        return sparePartStocktakingService.out(id);
    }

    @ApiOperation("下载模板")
    @GetMapping("/exportTemplate")
    public void exportTemplate(BusinessTypeEnums businessType, Long storehouseId) {
        sparePartStocktakingService.exportTemplate(businessType, storehouseId);
    }

    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public ResponseResult<Map<String, Integer>> importExcel(@RequestParam MultipartFile file,
                                                            @RequestParam BusinessTypeEnums businessType) {
        return sparePartStocktakingService.importExcel(file, businessType);
    }

}
