<template>
  <div class="page">
    <div class="conent">
      <div class="tai">
        <div class="ju" style="margin-left: 2%;">能源流向图</div>
        <div class="ju" style="margin-right: 2%;">
          <img v-show="show" @click="showMap" src="@/assets/img/map.png" alt="">
          <img v-show="!show" @click="showMap" src="@/assets/img/用途.png" alt="">
          <el-select @change="changeEquipmentType" v-model="formInline.equipmentType"
                     style="width: 100px;margin: 0 10px;" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>

          <el-date-picker @change="changeDate" :disabled-date="disabledDate" :clearable="false"
                          v-model="formInline.time" type="daterange" range-separator="到" start-placeholder="开始日期"
                          end-placeholder="结束日期" value-format="YYYY-MM-DD" />
        </div>
      </div>

      <div ref="main" style="width: 100%;height: 100%;"></div>
    </div>
  </div>
</template>

<script setup>
import { energyFlowAPI } from "@/api/energyManagement/dateAggregateEcharts.js";
import dayjs from 'dayjs';

const { proxy } = getCurrentInstance()
const echarts = proxy.$echarts

let testData = {
  "data": {
    "children": [
      {
        "children": [
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "1F",
            "energyType": "区域",
            "energyTypeId": "1826465207858237440",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [
              {
                "consumption": 0.10,
                "currentValue": 75.90,
                "equipmentId": "1825732153799876611",
                "equipmentName": "1H2F125（2ALG4）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.60,
                "currentValue": 236.60,
                "equipmentId": "1825732153799876614",
                "equipmentName": "1H2F124（2APKT2，东北角）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.20,
                "currentValue": 121.40,
                "equipmentId": "1825732153799876619",
                "equipmentName": "1H2F125（2ATALE2，东北角）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.10,
                "currentValue": 299.90,
                "equipmentId": "1825732153799876621",
                "equipmentName": "1H2F123（2ALG2，东北角）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.10,
                "currentValue": 178.60,
                "equipmentId": "1825732153799876628",
                "equipmentName": "1H2F121（2ALG3，北面电梯边上强电井）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.00,
                "currentValue": 22.90,
                "equipmentId": "1825732153799876654",
                "equipmentName": "1H2F127（2ATALE5）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.20,
                "currentValue": 300.70,
                "equipmentId": "1825732153799876658",
                "equipmentName": "1H2F126（2ALG5）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 5.20,
                "currentValue": 2190.90,
                "equipmentId": "1825732153799876671",
                "equipmentName": "1H2F110（2APKT1）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.00,
                "currentValue": 241.40,
                "equipmentId": "1825732153799876674",
                "equipmentName": "1H2F109（2ALG1）",
                "recordDate": "2024-08-29"
              }
            ],
            "energyName": "2F",
            "energyType": "区域",
            "energyTypeId": "1826465239852388352",
            "energyValue": 6.50
          },
          {
            "children": [],
            "energyFlowData": [
              {
                "consumption": 0.20,
                "currentValue": 1458.80,
                "equipmentId": "1825732153799876610",
                "equipmentName": "1H3F126（2APKT4）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.50,
                "currentValue": 211.00,
                "equipmentId": "1825732153799876618",
                "equipmentName": "1H3F129（3ALG2，东北角）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.10,
                "currentValue": 64.40,
                "equipmentId": "1825732153799876622",
                "equipmentName": "1H3F127（3APKT3，北面电梯边上强电井）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.30,
                "currentValue": 91.60,
                "equipmentId": "1825732153799876663",
                "equipmentName": "1H3F128（3ALG5）",
                "recordDate": "2024-08-29"
              }
            ],
            "energyName": "3F",
            "energyType": "区域",
            "energyTypeId": "1826465334836596736",
            "energyValue": 1.10
          },
          {
            "children": [],
            "energyFlowData": [
              {
                "consumption": 0.00,
                "currentValue": 61.60,
                "equipmentId": "1825732153799876664",
                "equipmentName": "1H4F101（4APKT1）",
                "recordDate": "2024-08-29"
              }
            ],
            "energyName": "4F",
            "energyType": "区域",
            "energyTypeId": "1826465359650099200",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "5F",
            "energyType": "区域",
            "energyTypeId": "1826465385851916288",
            "energyValue": 1
          }
        ],
        "energyFlowData": [],
        "energyName": "1#",
        "energyType": "区域",
        "energyTypeId": "1826464198717083648",
        "energyValue": 1
      },
      {
        "children": [
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "1F",
            "energyType": "区域",
            "energyTypeId": "1826465479527501824",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "2-2F",
            "energyType": "区域",
            "energyTypeId": "1826465505070813184",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "3-3F",
            "energyType": "区域",
            "energyTypeId": "1826465524205228032",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "4-4F",
            "energyType": "区域",
            "energyTypeId": "1826465552160264192",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "5-5F",
            "energyType": "区域",
            "energyTypeId": "1826465576353009664",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "6-6F",
            "energyType": "区域",
            "energyTypeId": "1826465601858572288",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [
              {
                "consumption": 1.20,
                "currentValue": 158.90,
                "equipmentId": "1825732209584119831",
                "equipmentName": "2H7F102（7AP1）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 5.30,
                "currentValue": 277.50,
                "equipmentId": "1825732209584119833",
                "equipmentName": "2H7F103（7APKT1）",
                "recordDate": "2024-08-29"
              }
            ],
            "energyName": "7-7F",
            "energyType": "区域",
            "energyTypeId": "1826465632531517440",
            "energyValue": 6.50
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "8-8F",
            "energyType": "区域",
            "energyTypeId": "1826465657227579392",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [
              {
                "consumption": 0.10,
                "currentValue": 39.80,
                "equipmentId": "1825732209584119834",
                "equipmentName": "2H9F111（9APK1）",
                "recordDate": "2024-08-29"
              }
            ],
            "energyName": "9-9F",
            "energyType": "区域",
            "energyTypeId": "1826465684196954112",
            "energyValue": 0.10
          },
          {
            "children": [],
            "energyFlowData": [
              {
                "consumption": 0.10,
                "currentValue": 46.00,
                "equipmentId": "1825732209584119841",
                "equipmentName": "2H10F116（10APK1）",
                "recordDate": "2024-08-29"
              }
            ],
            "energyName": "10-10F",
            "energyType": "区域",
            "energyTypeId": "1826465708683300864",
            "energyValue": 0.10
          },
          {
            "children": [],
            "energyFlowData": [
              {
                "consumption": 0.00,
                "currentValue": 11.80,
                "equipmentId": "1825732209584119844",
                "equipmentName": "2H11F119（11APK1）",
                "recordDate": "2024-08-29"
              }
            ],
            "energyName": "11-11F",
            "energyType": "区域",
            "energyTypeId": "1826465732515336192",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "12-12F",
            "energyType": "区域",
            "energyTypeId": "1826465753298112512",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [
              {
                "consumption": 0.10,
                "currentValue": 42.70,
                "equipmentId": "1825732209584119854",
                "equipmentName": "2H13F127（13APK1）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.00,
                "currentValue": 3.40,
                "equipmentId": "1825732209584119859",
                "equipmentName": "2H13F125（13ALG1）",
                "recordDate": "2024-08-29"
              }
            ],
            "energyName": "13-13F",
            "energyType": "区域",
            "energyTypeId": "1826465777885122560",
            "energyValue": 0.10
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "14-14F",
            "energyType": "区域",
            "energyTypeId": "1826465797501882368",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "15-15F",
            "energyType": "区域",
            "energyTypeId": "1826465818892832768",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "16-16F",
            "energyType": "区域",
            "energyTypeId": "1826465845287587840",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "17-17F",
            "energyType": "区域",
            "energyTypeId": "1826465870717652992",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "18-18F",
            "energyType": "区域",
            "energyTypeId": "1826465954326908928",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "19-19F",
            "energyType": "区域",
            "energyTypeId": "1826465977479467008",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "20-1F",
            "energyType": "区域",
            "energyTypeId": "1826466009607835648",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "21-1F",
            "energyType": "区域",
            "energyTypeId": "1826466035864178688",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "22-2F",
            "energyType": "区域",
            "energyTypeId": "1826466060585406464",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "23-7-5F",
            "energyType": "区域",
            "energyTypeId": "1826466079048732672",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "24F",
            "energyType": "区域",
            "energyTypeId": "1826466099638571008",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "25-7F",
            "energyType": "区域",
            "energyTypeId": "1826466131779522560",
            "energyValue": 1
          }
        ],
        "energyFlowData": [],
        "energyName": "2#",
        "energyType": "区域",
        "energyTypeId": "1826464227901050880",
        "energyValue": 1
      },
      {
        "children": [
          {
            "children": [],
            "energyFlowData": [
              {
                "consumption": 0.20,
                "currentValue": 863.70,
                "equipmentId": "1825732153799876678",
                "equipmentName": "1H1F114（1ALZ1）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 5.80,
                "currentValue": 2351.10,
                "equipmentId": "1825732153799876676",
                "equipmentName": "1H1F117（1APKTZ1）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.10,
                "currentValue": 46.00,
                "equipmentId": "1825732153799876672",
                "equipmentName": "1H1F118（1APKT1）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 2.00,
                "currentValue": 1061.20,
                "equipmentId": "1825732153799876627",
                "equipmentName": "1H1F104（1ATG2，东北角）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.60,
                "currentValue": 213.30,
                "equipmentId": "1825732153799876629",
                "equipmentName": "1H1F106（1APKT2，东北角）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 12.90,
                "currentValue": 5035.80,
                "equipmentId": "1825732153799876631",
                "equipmentName": "1H1F103（1ALZ2，东北角）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.00,
                "currentValue": 24.70,
                "equipmentId": "1825732153799876635",
                "equipmentName": "1H1F115（1ATALE2，东北角）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.00,
                "currentValue": 30.00,
                "equipmentId": "1825732153799876636",
                "equipmentName": "1H1F102（1APKTZ2，东北角）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 1.70,
                "currentValue": 1993.40,
                "equipmentId": "1825732153799876644",
                "equipmentName": "1H1F107（1APKTZ5）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 1.90,
                "currentValue": 762.70,
                "equipmentId": "1825732153799876650",
                "equipmentName": "1H1F103（1ALG5）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 0.40,
                "currentValue": 364.50,
                "equipmentId": "1825732153799876652",
                "equipmentName": "1H1F106（1ALZ5）",
                "recordDate": "2024-08-29"
              },
              {
                "consumption": 2.70,
                "currentValue": 1189.90,
                "equipmentId": "1825732153799876656",
                "equipmentName": "1H1F104（1ATG5）",
                "recordDate": "2024-08-29"
              }
            ],
            "energyName": "1-0F",
            "energyType": "区域",
            "energyTypeId": "1826466644554158080",
            "energyValue": 28.30
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "2-0F",
            "energyType": "区域",
            "energyTypeId": "1826466671775191040",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "3-0F",
            "energyType": "区域",
            "energyTypeId": "1826466723402878976",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "4-0F",
            "energyType": "区域",
            "energyTypeId": "1826466748128301056",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "5-0F",
            "energyType": "区域",
            "energyTypeId": "1826466778943852544",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "6-0F",
            "energyType": "区域",
            "energyTypeId": "1826466840046473216",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "7-0F",
            "energyType": "区域",
            "energyTypeId": "1826466865241657344",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "8-0F",
            "energyType": "区域",
            "energyTypeId": "1826466897483272192",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "9-0F",
            "energyType": "区域",
            "energyTypeId": "1826466923622174720",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "10-0F",
            "energyType": "区域",
            "energyTypeId": "1826466963560337408",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "11-0F",
            "energyType": "区域",
            "energyTypeId": "1826467002718359552",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "12-0F",
            "energyType": "区域",
            "energyTypeId": "1826467150248808448",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "13-0F",
            "energyType": "区域",
            "energyTypeId": "1826467177591476224",
            "energyValue": 1
          },
          {
            "children": [],
            "energyFlowData": [],
            "energyName": "14-0F",
            "energyType": "区域",
            "energyTypeId": "1826467213268226048",
            "energyValue": 1
          }
        ],
        "energyFlowData": [],
        "energyName": "3#",
        "energyType": "区域",
        "energyTypeId": "1826464248843210752",
        "energyValue": 1
      }
    ],
    "energyName": "园区能耗",
    "energyValue": 4
  },
  "errorCode": "NO-ERROR",
  "errorMessage": "NO-MESSAGE",
  "success": true
}

const show = ref(true)
const state = reactive({
  treeData: {
    children: []
  }
})
const formInline = reactive({
  energyType: '区域',
  equipmentType: '电表',
  time: [
    dayjs().startOf('month').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ]
});

const options = [
  {
    value: '水表',
    label: '水表',
  },
  {
    value: '电表',
    label: '电表',
  },
  {
    value: '气表',
    label: '气表',
  }
]

const main = ref(null)
let myChart = null;

const list = {
  energyName: '园区用电',
  value: 4,
  children: [
    {
      energyName: '商业服务区',
      value: 2,
      children: [
        {
          energyName: 'B区-1楼',
          value: 1
        },
        {
          energyName: 'B区-2楼',
          value: 1
        }
      ]
    },
    {
      energyName: '办公区',
      value: 2,
      children: [
        {
          energyName: 'A区-3楼',
          value: 1
        },
        {
          energyName: 'A区-6楼',
          value: 1
        }
      ]
    }
  ]
}

function updateEnergyNames(data, parentName = '') {
  // 如果有父节点名称，则拼接当前节点名称
  if (parentName) {
    data.energyName = `${parentName}/${data.energyName}`;
  }

  // 遍历子节点并递归调用
  if (Array.isArray(data.children)) {
    data.children.forEach(child => {
      updateEnergyNames(child, data.energyName);
    });
  }
}

const list2 = {
  energyName: '园区用电',
  value: 4,
  children: [
    {
      energyName: '园区用电/商业服务区',
      value: 2,
      children: [
        {
          energyName: '商业服务区/B区-1楼',
          value: 1
        },
        {
          energyName: '商业服务区/B区-2楼',
          value: 1
        }
      ]
    },
    {
      energyName: '园区用电/办公区',
      value: 2,
      children: [
        {
          energyName: '办公区/A区-3楼',
          value: 1
        },
        {
          energyName: '办公区/A区-6楼',
          value: 1
        }
      ]
    }
  ]
}

// 限制日期选择范围
const disabledDate = (time) => {
  return time.getTime() > dayjs().subtract(0, "day");
}

onMounted(() => {
  getEnergyFlowTree()

  // const data = {
  //     nodes: [
  //         {
  //             name: '园区用电'
  //         },
  //         {
  //             name: '商业服务区'
  //         },
  //         {
  //             name: '办公区'
  //         },
  //         {
  //             name: 'A区-3楼'
  //         },
  //         {
  //             name: 'A区-6楼'
  //         },
  //         {
  //             name: 'B区-1楼'
  //         },
  //         {
  //             name: 'B区-2楼'
  //         }
  //     ],
  //     links: [
  //         {
  //             source: '园区用电',
  //             target: '办公区',
  //             value: 2
  //         },
  //         {
  //             source: '园区用电',
  //             target: '商业服务区',
  //             value: 2
  //         },
  //         {
  //             source: '办公区',
  //             target: 'A区-3楼',
  //             value: 1
  //         },
  //         {
  //             source: '办公区',
  //             target: 'A区-6楼',
  //             value: 1
  //         },
  //         {
  //             source: '商业服务区',
  //             target: 'B区-1楼',
  //             value: 1
  //         },
  //         {
  //             source: '商业服务区',
  //             target: 'B区-2楼',
  //             value: 1
  //         }
  //     ]
  // }

  // initChart(data)
})

onBeforeUnmount(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});

// 树形结构转换为[{name:'',...}]
const getEnergyNames = (node) => {
  let names = [];
  if (node.energyName) {
    names.push({ name: node.energyName });
  }
  if (node.children && Array.isArray(node.children)) {
    node.children.forEach(child => {
      names = names.concat(getEnergyNames(child));
    });
  }
  return names;
};

// 树形结构转换为表示父子节点关系的数组
const transformListToLinks = (parent, list) => {
  let links = [];

  if (list && Array.isArray(list)) {
    list.forEach((child) => {
      links.push({
        source: parent.energyName,
        target: child.energyName,
        value: child.energyValue
      });

      // 递归处理子节点
      if (child.children && child.children.length > 0) {
        links = links.concat(transformListToLinks(child, child.children));
      }
    });
  }

  return links;
};

// 获取树形数据
const getEnergyFlowTree = () => {
  const query = {
    startDate: formInline.time[0],
    endDate: formInline.time[1],
    energyType: formInline.energyType,
    equipmentType: formInline.equipmentType
  }

  energyFlowAPI(query).then(res => {
    updateEnergyNames(res.data)

    state.treeData = res.data
    const data = {}
    data.nodes = getEnergyNames(res.data)
    data.links = transformListToLinks(res.data, res.data.children)

    initChart(data)

    // const data = {}
    // data.nodes = getEnergyNames(testData.data)
    // data.links = transformListToLinks(testData.data, testData.data.children)
    // console.log(data);
    // initChart(data)
  })
}

// 区域、用途切换
const showMap = () => {
  show.value = !show.value
  formInline.energyType = show.value ? '区域' : '用途'
  getEnergyFlowTree()
}

// 水表、电表、气表切换
const changeEquipmentType = () => {
  getEnergyFlowTree()
}

// 日期范围改变
const changeDate = () => {
  getEnergyFlowTree()
}

const initChart = (data) => {
  if (!myChart && main.value) {
    myChart = echarts.init(main.value);
  }

  myChart.clear();//清空图形数据，myChart对象仍然存在，重绘图形时显示动画
  let options = {}

  if (state.treeData.children.length > 0) {
    options = {
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove'
      },
      series: {
        type: 'sankey',
        // layoutIterations:0,
        layout: 'none',
        emphasis: {
          focus: 'adjacency'
        },
        data: data.nodes,
        links: data.links
      }
    }
  } else {
    options = {
      title: {
        text: '分类未维护，暂无数据',
        x: 'center',
        y: 'center',
        textStyle: {
          fontSize: '16',
          fontWeight: 'normal',
        }
      }
    }
  }

  myChart.setOption(options);
}
</script>

<style lang='less' scoped>
.page {
  overflow: scroll;
  height: 100%;
  display: flex;
  flex-direction: column;

  .conent {
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .tai {
      width: 100%;
      height: 70px;
      background-color: #f9f9f9;
      display: flex;
      justify-content: space-between;
    }

    .ju {
      display: flex;
      align-items: center;

      img {
        width: 30px;
      }
    }
  }
}
</style>
