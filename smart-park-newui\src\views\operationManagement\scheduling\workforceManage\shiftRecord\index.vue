<template>
  <page-common v-model="state.tableHeight" :operate-bool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="showName">
          <el-input v-model="formInline.showName" placeholder="值班人"/>
        </el-form-item>
        <el-form-item prop="allocateDate">
          <el-date-picker
              v-model="formInline.allocateDate"
              type="date"
              placeholder="调班日期"
              value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item prop="allocateStatus">
          <el-select v-model="formInline.allocateStatus" clearable placeholder="调班类型">
            <el-option v-for="(value, key) in state.allocateStatusOptions" :label="value" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" row-key="id" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
      </el-table>
      <el-pagination
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="state.pagetion.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
    </template>
  </page-common>
</template>

<script setup>
import {ElMessage, ElMessageBox, ElTag} from 'element-plus'
import dayjs from "dayjs";

import { shiftRecordPageAPI } from '@/api/operationManagement/shiftRecord.js'

let formInlineRef = ref()

const formInline = reactive({})

const state = reactive({
  allocateStatusOptions:{
    '-1':'取消排班',
    1:'排班'
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'attendanceDate',
      label: '调班日期'
    },
    {
      prop: 'shiftsName',
      label: '班次名称'
    },
    {
      prop: '',
      label: '调班班次',
      formatter: (row, column, cellValue) => {
        return row.startTime + '-' + row.endTime
      }
    },
    {
      prop: 'showName',
      label: '值班人'
    },
    {
      prop: 'allocateStatus',
      label: '调班类型',
      formatter: (row, column, cellValue) => {
        return state.allocateStatusOptions[cellValue]
      }
    },
    {
      prop: 'operateName',
      label: '操作人'
    },
    {
      prop: 'updateTime',
      label: '操作时间',
      width: 160,
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

// 获取记录
const getList = () => {
  let query = {
    ...formInline ,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
    businessType: 'OPERATIONS'
  }
  shiftRecordPageAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}
</script>

<style lang='less' scoped></style>
