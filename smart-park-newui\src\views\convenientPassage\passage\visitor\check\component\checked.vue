<template>
    <page-common v-model="state.tableHeight">
        <template #query>
            <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
                <el-form-item prop="queryWord">
                    <el-input placeholder="访客姓名/车牌号" v-model="formInline.queryWord" />
                </el-form-item>
                <el-form-item prop="approveStatus">
                    <el-select v-model="formInline.approveStatus" clearable placeholder="审批状态">
                        <el-option v-for="(item, index) in state.statusOptions" :value="item.value"
                            :label="item.label" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="applyDate">
                    <el-date-picker v-model="formInline.applyDate" type="date" value-format="YYYY-MM-DD"
                        placeholder="申请时间" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
                    <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
            </el-form>
        </template>

        <template #table>
            <el-table ref="table" show-overflow-tooltip :data="state.tableData" :height="state.tableHeight">
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                    :label="item.label" :formatter="item.formatter" :width="item.width" />
            </el-table>

            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize"
                :total="state.pagetion.total" @size-change="sizeChange" @current-change="currentChange" />
        </template>
    </page-common>
</template>

<script setup>
import { visitlist } from '@/api/convenientPassage/check.js'

import dayjs from 'dayjs'
import { formatter } from 'element-plus';

const route = useRoute()

const table = ref()

const formInlineRef = ref();
const formInline = reactive({});

const state = reactive({
    tableHeight: 100,
    tableData: [],
    statusOptions: [
        { label: '已通过', value: 2, color: '#67C23A' },
        { label: '未通过', value: 3, color: '#F56C6C' },
        { label: '已过期', value: 4, color: '#909399' },
    ],
    options: {
        2: '已通过',
        3: '未通过',
        4: '已过期'
    },
    colors: {
        2: '#67C23A',
        3: '#F56C6C',
        4: '#909399'
    },
    tableHeader: [{
        prop: 'visitorName',
        label: '访客姓名'
    },
    {
        prop: 'visitorIdCard',
        label: '身份证号'
    },
    {
        prop: 'visitorPhone',
        label: '手机号'
    }, {
        prop: 'licenceNumber',
        label: '车牌号'
    }, {
        prop: 'planVisitTime',
        label: '预计到访时间',
        formatter: (row, column, cellValue) => {
            return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
        }
    }, {
        prop: 'visitReason',
        label: '到访事由'
    },
    {
        prop: 'approveStatus',
        label: '审核状态',
        formatter: (row, column, cellValue) => {
            let color = state.colors[cellValue];
            return h('div', [h('span', {
                class: 'status-circle',
                style: 'background-color: ' + color
            }), state.options[cellValue]]);
        }
    },
    {
        prop: 'rejectReason',
        label: '驳回原因'
    },
    {
        prop: 'applyTime',
        label: '申请时间'
    }],
    pagetion: {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
})

onMounted(() => {
    getList();
})

const getList = () => {
    console.log(formInline);

    const query = {
        ...route.query,
        ...state.pagetion,
        ...formInline,
        approveType: 2
    }

    visitlist(query).then(res => {
        state.tableData = res.data.dataList
        state.pagetion.total = res.data.totalCount;
    })
}

const onSubmit = () => {
    state.pagetion = {
        pageNum: 1,
        pageSize: 10,
        total: 0
    };
    getList();
}

const onReset = () => {
    formInlineRef.value.resetFields();
    onSubmit();
};

// 分页
const currentChange = (pageNum) => {
    state.pagetion.pageNum = pageNum;
    getList();
};

const sizeChange = (pageSize) => {
    state.pagetion.pageSize = pageSize;
    getList();
};

defineExpose({
    getList
})
</script>

<style lang="less" scoped>
:deep .status-circle {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}
</style>
