<template>
  <dialog-common ref="dialog" title="密码修改" @submit="submit" :formRef="ruleFormRef" :width="600">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="120px" label-suffix=":">
      <el-row>
        <el-col :span="20">
          <el-form-item label="原密码" prop="oldPass">
            <el-input v-model="form.oldPass" type="password" autocomplete="new-password" placeholder="请输入登录密码" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="新密码" v-if="!form.userId" prop="newPass">
            <el-input v-model="form.newPass" type="password" placeholder="请再次输入登录密码" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="确认密码" v-if="!form.userId" prop="passwordRepeat">
            <el-input v-model="form.passwordRepeat" type="password" placeholder="请再次输入登录密码" clearable />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {reactive, ref} from "vue";
import {ElMessage,ElMessageBox} from "element-plus";
import { changePassword } from '@/api/login'
const router = useRouter()
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({

});
const state = reactive({
  rules: {
    oldPass: [{ required: true, message: '用户密码不能为空', trigger: 'blur' }],
    newPass: [{ required: true, message: '用户密码不能为空', trigger: 'blur' }],
    passwordRepeat: [{ required: true, message: '重输密码不能为空', trigger: 'blur' }],
  }
})

const open = () => {
  dialog.value.open();
};

const submit = () => {
  if (form.newPass !== form.passwordRepeat) {
    ElMessage.error('两次密码输入不一致，请重新输入');
    return;
  }

  let passwordreg = /(?=.*\d)(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,12}/
  let isValid = passwordreg.test(form.newPass);
  if(isValid != true){
    ElMessage.error("密码必须是大写字母，小写字母，数字，特殊字符组成，且长度为8到12位！");
    return;
  }
  changePassword({oldPass:form.oldPass,newPass:form.newPass}).then(async res => {
    if(res.success){
      ElMessageBox.alert('密码修改成功,请重新登录', '提示', {
        confirmButtonText: 'OK',
        showClose:false,
        callback: (action) => {
          router.push('/login')
        },
      })

     // ElMessage.success('密码修改成功,请重新登录')

    }else{
      ElMessage.error(res.errorMessage)
    }
  });
}


defineExpose({
  open
});
</script>

<style scoped>

</style>