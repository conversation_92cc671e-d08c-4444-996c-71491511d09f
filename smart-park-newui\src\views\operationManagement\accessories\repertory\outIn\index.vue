<template>
  <div style="height: 100%;overflow: hidden;">
    <!-- 列表 -->
    <transition name="el-zoom-in-center">
      <listTable v-show="pageIndex == 0" @showPage="showPage" ref="table"></listTable>
    </transition>
    <!-- 详情 -->
    <transition name="el-zoom-in-center">
      <detailPage v-show="pageIndex == 1" @showPage="showPage" ref="datail"></detailPage>
    </transition>
  </div>
</template>

<script setup>
import listTable from './component/listTable.vue'
import detailPage from './component/detailPage.vue';

const table = ref()
const datail = ref()
const pageIndex = ref(0)
const activeName = ref('first')


const showPage = (index, id) => {
  pageIndex.value = index

  if (index == 0) { //刷新
    table.value.getList()
  }else if(index == 1){
    datail.value.loadPage(id)
  }
}
</script>

<style lang='less' scoped>
.el-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);

    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
