<template>
  <div style="height: 100%;overflow: hidden;">
    <transition name="el-zoom-in-center">
      <!--报修列表-->
      <listTable v-show="0 === pageIndex" @showPage="showPage" ref="table"></listTable>
    </transition>
    <transition name="el-zoom-in-center">
      <!--维修工单详情-->
      <detail2 v-if="3 === pageIndex" @showPage="showPage" :from="3" :title="state.title" :id="state.id"></detail2>
    </transition>
  </div>
</template>

<script setup>
import listTable from './component/listTable.vue';
import detail2 from '../order/component/detail2.vue';

const pageIndex = ref(0)
const table = ref()

const state = reactive({
  title: '',
  id: ''
})

const showPage = (index, title, id) => {
  pageIndex.value = index;
  if (index === 0) {
    table.value.getList();
  } else {
    state.title = title;
    state.id = id
  }
}
</script>
