<template>
  <page-common v-model="state.tableHeight" :queryBool="false">
    <template #operate>
      <el-button type="primary" :icon="Plus" @click="addHandle" v-permsBtn="'menu:add'">新建菜单</el-button>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableData" row-key="menuId" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" />
        <el-table-column align="center" label="操作" width="160">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <modal-page ref="modal" :title="state.title" :tableData="state.tableData" :menuType="state.menuType" @submit="getList"></modal-page>
    </template>
  </page-common>
</template>

<script setup>
import modalPage from './component/modal.vue'

import {
  Plus
} from '@element-plus/icons-vue'
import { ElTag, ElMessage, ElMessageBox } from 'element-plus'

import { menuListAPI, menuDelAPI } from '@/api/settingSystem/menu.js'
import { dictionListItemAPI } from '@/api/settingSystem/dictionary.js'

let modal = ref()

const state = reactive({
  title:'',
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'menuName',
      label: '菜单名称'
    },
    {
      prop: 'path',
      label: '组件路径'
    },
    {
      prop: 'perms',
      label: '权限标识'
    },
    {
      prop: 'menuType',
      label: '菜单类型',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { type: state.menuTagType.find(item => item.itemId == cellValue)?.name }, { default: () => state.menuType.find(item => item.itemId == cellValue)?.name })
      }
    }
  ],
  menuType: [],
  menuTagType: []
})

onMounted(() => {
  getList()
  getDictionaryAll()
})

// 获取全部菜单
const getList = () => {
  menuListAPI().then(res => {
    state.tableData = res.data
  })
}

// 获取当前需要的字典
const getDictionaryAll = () => {
  getDictionary('001', 'menuType')
  getDictionary('002', 'menuTagType')
}

// 封装字典方法
const getDictionary = (params, objName) => {
  dictionListItemAPI({ dictCode: params }).then(res => {
    state[objName] = res.data.cachedResultList
  })
}

// 新建菜单
const addHandle = () => {
  state.title = '新建菜单'
  modal.value.form.menuId = ''
  modal.value.open()
}

// 编辑菜单
const editHandle = (info) => {
  if (!Number(info.parentId)) {
    info.parentId = ''
  }
  state.title = '编辑菜单'
  modal.value.open()
  nextTick(() => {
    Object.assign(modal.value.form, info)
  })
}

// 删除菜单
const deleteHandle = (info) => {
  ElMessageBox.confirm(
    '是否删除当前菜单?',
    '提醒',
    {
      type: "warning"
    }
  ).then(() => {
    menuDelAPI({ menuId: info.menuId }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}


</script>

<style lang='less' scoped></style>
