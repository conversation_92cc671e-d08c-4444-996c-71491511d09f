<template>
  <div class="page">
    <div class="conent">
      <!-- 用能概览 -->
      <div class="tai">
        <div class="ju">用能概览</div>
        <div class="ju">
          <el-form :inline="true">
            <el-form-item>
              <el-select v-model="formInline.equipmentType" placeholder="请选择" @change="selectSix">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-cascader ref="spaceRef" v-model="formInline.equipmentSpaceId" :options="state.treeData"
                @change="changeSpace" :props="defaultProps" clearable placeholder="请选择区域" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div style="display: flex;margin: 40px 40px 20px;">
        <div style="display: flex;width:50%">
          <div class="line"></div>
          <div>近6个月用能环比</div>
          <el-tooltip effect="dark" placement="right">
            <template #content>
              <div style="width: 450px;">{{ tip1 }}</div>
            </template>
            <img style="width: 15px;height: 15px;" src="@/assets/img/help.png" alt="">
          </el-tooltip>
        </div>
        <div style="display: flex;">
          <div class="line"></div>
          <div>近6年用能同比</div>
          <el-tooltip effect="dark" placement="right">
            <template #content>
              <div style="width: 450px;">{{ tip2 }}</div>
            </template>
            <img style="width: 15px;height: 15px;" src="@/assets/img/help.png" alt="">
          </el-tooltip>
        </div>
      </div>
      <!-- 近6个月用能环比、近6年用能同比 -->
      <div class="flexClass">
        <div ref="leftMounth" style="width: 50%;height: 400px;"></div>
        <div ref="rightYear" style="width: 50%;height: 400px;"></div>
      </div>
      <!-- 表格 -->
      <div style="display: flex;justify-content: space-between;">
        <el-table :data="state.monthData" style="width: 48%;">
          <el-table-column prop="month" label="月份" />
          <el-table-column prop="consumption" :label="state.dosage" />
          <el-table-column prop="compare" label="环比(%)" />
        </el-table>
        <el-table :data="state.yearData" style="width: 48%;">
          <el-table-column prop="year" label="年份" />
          <el-table-column prop="consumption" :label="state.dosage" />
          <el-table-column prop="compare" label="同比(%)" />
        </el-table>
      </div>

      <!-- 用能趋势 -->
      <div class="tai" style="margin: 20px 0;">
        <div class="ju">
          <!-- <div class="line"></div> -->
          <div>用能趋势</div>
        </div>
        <div class="ju">
          <el-form :inline="true">
            <el-form-item>
              <el-select v-model="formInline1.equipmentType" placeholder="请选择" @change="selectTendency">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker value-format="YYYY-MM-DD" :clearable="false" v-model="formInline1.timeSelect" type="date"
                v-if="formInline1.changeType == 'day'" placeholder="请选择日期" @change="selectTendency" />
              <el-date-picker value-format="YYYY-MM" :clearable="false" v-model="formInline1.timeSelect" type="month"
                v-if="formInline1.changeType == 'month'" placeholder="请选择月份" @change="selectTendency" />
              <el-date-picker value-format="YYYY" :clearable="false" v-model="formInline1.timeSelect" type="year"
                v-if="formInline1.changeType == 'year'" placeholder="请选择年份" @change="selectTendency" />
            </el-form-item>
            <el-form-item>
              <el-radio-group v-model="formInline1.changeType" @change="changeRadio">
                <el-radio-button label="日" value="day" />
                <el-radio-button label="月" value="month" />
                <el-radio-button label="年" value="year" />
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div>
        <div ref="lineDay" id="lineDay" style="width: 100%;height: 400px;"></div>
      </div>

      <!-- 同比分析 -->
      <div class="tai" style="margin: 20px 0;">
        <div class="ju">
          <!-- <div class="line"></div> -->
          <div>同比分析</div>
        </div>
        <div class="ju">
          <el-form :inline="true">
            <el-form-item>
              <el-select v-model="formInline2.equipmentType" placeholder="请选择" @change="selectAnalyse">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker value-format="YYYY" :clearable="false" v-model="formInline2.timeSelect" type="year" placeholder="请选择年份"
                @change="selectAnalyse" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="flexClass">
        <div ref="leftMounth2" style="width: 50%;height: 400px;"></div>
        <div ref="rightYear2" style="width: 50%;height: 400px;"></div>
      </div>
      <!-- 表格 -->
      <div style="display: flex;justify-content: space-between;">
        <el-table :data="state.yearTableData" style="width: 48%;height: 38%;">
          <el-table-column prop="time" label="时间" />
          <el-table-column prop="now" :label="state.now" />
          <el-table-column prop="formerly" :label="state.formerly" />
          <el-table-column prop="calculate" label="同比(%)" />
        </el-table>
        <el-table :data="state.quarterTableData" style="width: 48%;height: 38%;">
          <el-table-column prop="time" label="时间" />
          <el-table-column prop="now" :label="state.now" />
          <el-table-column prop="formerly" :label="state.formerly" />
          <el-table-column prop="calculate" label="同比(%)" />
        </el-table>
      </div>

    </div>
  </div>
</template>

<script setup>
import { treeAPI } from "@/api/iotManagement/space.js";
import { overviewOfEnergyUseAPI, energyUseTrendAPI, yearOnYearAnalysisYearAPI } from "@/api/energyManagement/dateAggregateEcharts.js";
import dayjs from 'dayjs';

const { proxy } = getCurrentInstance()

const echarts = proxy.$echarts

const tip1 = '环比是与上一个相邻统计周期相比较，即n月与第 n-1月的比较。如2018年4月份与2018年3月份比较， 环比增长率=(本期数-上期数)÷上期数×100%'
const tip2 = '同比是以上年同期为基期相比较，即本期某一时间段与上年某一时间段相比，如2018年4月份与2017年4月份比较，同比增长率=(本期数-上期数)÷上期数×100%'

const formInline = reactive({
  equipmentType: '电表'
});

const formInline1 = reactive({
  changeType: 'day',//day,month,year
  equipmentType: '电表',
  time: '',//2024-07-18 00:00
  timeSelect: dayjs().format('YYYY-MM-DD')
});

const formInline2 = reactive({
  equipmentType: '电表',
  timeSelect: dayjs().format('YYYY')
});

const spaceRef = ref(null)

const defaultProps = {
  checkStrictly: true,
  children: 'children',
  label: 'name',
  value: 'id',
  emitPath: false,
  expandTrigger: 'hover'
}

const options = [
  {
    value: '水表',
    label: '水表',
  },
  {
    value: '电表',
    label: '电表',
  },
  {
    value: '气表',
    label: '气表',
  }
]

const leftMounth = ref(null)
let myChart = null;

const rightYear = ref(null)
let myChart1 = null;

const lineDay = ref(null)
let myChart2 = null;

const leftMounth2 = ref(null)
let myChart3 = null;

const rightYear2 = ref(null)
let myChart4 = null;

onMounted(() => {
  selectSix()
  selectTendency()
  selectAnalyse()
  getTree()
})

onBeforeUnmount(() => {
  let chartInstances = [myChart, myChart1, myChart2,myChart3,myChart4];
  chartInstances.forEach(chartInstance => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
  });
});

const state = reactive({
  monthData: [],
  yearData: [],
  dosage: '用量',
  treeData: [],
  tableData: []
})

const getTree = () => {
  let query = {
    deep: 4
  }
  treeAPI(query).then(res => {
    state.treeData = res.data
  })
}

const changeSpace = () => {
  spaceRef.value.togglePopperVisible()
  selectSix()
}

const selectSix = () => {
  overviewOfEnergyUseAPI(formInline).then(res => {
    state.monthData = []
    state.yearData = []
    state.dosage = '用量(' + res.data.loopSix[0].unit + ")"
    var monthList = []
    var monthValueList = []
    var yearList = []
    var yearValueList = []
    res.data.loopSix.forEach(e => {
      monthList.push(e.recordMonth + '月')
      monthValueList.push(e.consumption)
      state.monthData.push({ month: e.recordMonth + '月', consumption: e.consumption, compare: e.compare })
    })
    res.data.togetherSix.forEach(e => {
      yearList.push(e.recordYear + '年')
      yearValueList.push(e.consumption)
      state.yearData.push({ year: e.recordYear + '年', consumption: e.consumption, compare: e.compare })
    })

    if (!myChart && leftMounth.value) {
      myChart = echarts.init(leftMounth.value);
    }

    myChart.clear()

    let option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: monthList,
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: monthValueList,
          type: 'bar'
        }
      ]
    };

    myChart.setOption(option);



    

    if (!myChart1 && rightYear.value) {
      myChart1 = echarts.init(rightYear.value);
    }

    myChart1.clear()

    let option1 = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: yearList,
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: yearValueList,
          type: 'bar'
        }
      ]
    };

    myChart1.setOption(option1);
  })

}

const changeRadio = (e) => {
  if (e == 'day') {
    formInline1.timeSelect = dayjs().format('YYYY-MM-DD')
  } else if (e == 'month') {
    formInline1.timeSelect = dayjs().format('YYYY-MM')
  } else if (e == 'year') {
    formInline1.timeSelect = dayjs().format('YYYY')
  }
  selectTendency()
}

const selectTendency = () => {

  if (formInline1.changeType == 'day') {

    formInline1.time = formInline1.timeSelect + ' 00:00'
  } else if (formInline1.changeType == 'month') {

    formInline1.time = formInline1.timeSelect + '-01 00:00'
  } else if (formInline1.changeType == 'year') {

    formInline1.time = formInline1.timeSelect + '-01-01 00:00'
  }

  energyUseTrendAPI(formInline1).then(res => {
    if (!myChart2 && lineDay.value) {
      myChart2 = echarts.init(lineDay.value);
    }

    myChart2.clear()

    let option2 = {
      title: {
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: [res.data[0].date, res.data[1].date]
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: res.data[0].x
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: res.data[0].date,
          type: 'line',
          stack: 'Total',
          areaStyle: {},
          emphasis: {
            focus: 'series'
          },
          data: res.data[0].y
        },
        {
          name: res.data[1].date,
          type: 'line',
          stack: 'Total',
          areaStyle: {},
          emphasis: {
            focus: 'series'
          },
          data: res.data[1].y
        }
      ]
    };

    myChart2.setOption(option2);
  })
}

const selectAnalyse = () => {
  formInline2.time = formInline2.timeSelect + '-01-01 00:00'

  yearOnYearAnalysisYearAPI(formInline2).then(res => {
    state.now = res.data.yearEcharts[0].date + "的用量(" + res.data.yearEcharts[0].unit + ")"
    state.formerly = res.data.yearEcharts[1].date + "的用量(" + res.data.yearEcharts[1].unit + ")"
    state.yearTableData = res.data.yearTable
    state.quarterTableData = res.data.quarterTable

    var list = []
    list.push(['product', res.data.yearEcharts[0].date, res.data.yearEcharts[1].date])
    for (var i = 0; i < res.data.yearEcharts[0].x.length; i++) {
      list.push([res.data.yearEcharts[0].x[i], res.data.yearEcharts[0].y[i], res.data.yearEcharts[1].y[i]])
    }

    var list1 = []
    list1.push(['product', res.data.quarterEcharts[0].date, res.data.quarterEcharts[1].date])
    for (var i = 0; i < res.data.quarterEcharts[0].x.length; i++) {
      list1.push([res.data.quarterEcharts[0].x[i], res.data.quarterEcharts[0].y[i], res.data.quarterEcharts[1].y[i]])
    }

    if (!myChart3 && leftMounth2.value) {
      myChart3 = echarts.init(leftMounth2.value);
    }

    myChart3.clear()

    let option3 = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {},
      dataset: {
        source: list
      },
      xAxis: {
        type: 'category',
        // boundaryGap: false,
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {},
      series: [{ type: 'bar' }, { type: 'bar' }]
    };

    myChart3.setOption(option3);



    if (!myChart4 && rightYear2.value) {
      myChart4 = echarts.init(rightYear2.value);
    }

    myChart4.clear()

    let option4 = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {},
      dataset: {
        source: list1
      },
      xAxis: {
        type: 'category',
        // boundaryGap: false,
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {},
      series: [{ type: 'bar' }, { type: 'bar' }]
    };
    myChart4.setOption(option4);
  })
}
</script>

<style lang='less' scoped>
.page {
  overflow: scroll;
  height: 100%;
  display: flex;
  flex-direction: column;

  .conent {
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
  }
}


.conent {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.flexClass {
  display: flex;
  justify-content: space-around;
}

.tai {
  width: 100%;
  height: 70px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: space-between;
  padding-left: 32px;
}

.ju {
  display: flex;
  align-items: center;

  .el-form-item {
    margin-bottom: 0;
  }
}

.title {
  color: #989fc7;
}

.line {
  width: 4px;
  height: 20px;
  background-color: #5470c6;
  margin-right: 10px;
}
</style>
