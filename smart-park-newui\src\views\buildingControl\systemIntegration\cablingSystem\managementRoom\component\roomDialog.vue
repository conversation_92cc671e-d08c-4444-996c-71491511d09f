<template>
  <el-dialog :close-on-click-modal="false" v-model="showDialogRef" :title="state.title" :width="500" :before-close="close" align-center class="dialogCommon">
    <template #default>
      <div class="conent">
        <el-form ref="dataFormRef" :model="dataForm" :rules="state.rules" label-suffix=":" label-width="90">
          <el-form-item label="名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="管理间名称"/>
          </el-form-item>
          <el-form-item label="所在位置" prop="spaceIds">
            <el-cascader v-model="dataForm.spaceIds" :options="spaceOptions" :props="optionsProps"
                         placeholder="请选择所在位置"/>
          </el-form-item>
          <el-form-item label="备注" prop="remarks">
            <el-input type="textarea" v-model="dataForm.remarks" placeholder="备注" />
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">关闭</el-button>
        <el-button type="primary" @click="onSave">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>

// 父组件方法
import {saveOrUpdateManagementRoomAPI} from "@/api/buildingControl/managementRoom.js";
import {ElMessage} from "element-plus";

const emits = defineEmits(['onClose'])

// 父组件参数
const props = defineProps(['spaceOptions'])
let { spaceOptions } = toRefs(props)


// dialog组件
const showDialogRef = ref(false)

// 表单
const dataFormRef = ref()
// 空间选项
let dataForm = ref({
  id: null,
  name: null,
  spaceIds: null,
  remarks: null
})

const state = reactive({
  title: '',
  rules: {
    name: [
      { required: true, message: '名称不能为空', trigger: 'blur' }
    ],
    spaceIds: [
      { required: true, message: '位置不能为空', trigger: 'blur' }
    ]
  }
})


// 级联选择配置
const optionsProps = {
  checkStrictly: true,
  label: "name",
  value: "id"
}


const onSave = () => {
  dataFormRef.value.validate((valid, fields) => {
    if (valid) {
      let param = {
        id: dataForm.value.id,
        name: dataForm.value.name,
        remarks: dataForm.value.remarks
      }
      if (dataForm.value.spaceIds) {
        param.spaceId = dataForm.value.spaceIds[dataForm.value.spaceIds.length - 1]
      }
      saveOrUpdateManagementRoomAPI(param).then(res => {
        if (res.success) {
          ElMessage({
            type: 'success',
            message: '保存成功！'
          })
          close()
        } else {
          ElMessage({
            type: 'error',
            message: '保存失败！'
          })
        }
      })
    }
  })
}

const onCancel = () => {
  close()
}


const open = (title, val) => {
  state.title = title
  showDialogRef.value = true
  nextTick(() => {
    if (val) {
      dataForm.value.id = val.id
      dataForm.value.name = val.name
      dataForm.value.remarks = val.remarks
      dataForm.value.spaceIds = val.spacePath.split('/')
    }
  })
}

const close = () => {
  dataFormRef.value.clearValidate()
  dataFormRef.value.resetFields()
  showDialogRef.value = false
  emits('onClose')
}


defineExpose({
  open
})
</script>


<style scoped lang="less">

</style>
