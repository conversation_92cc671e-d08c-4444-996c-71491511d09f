<template>
  <div class="draw">
    <!-- 画布 -->
    <div id="container" :style="{
      ...getCanvasStyle(canvasStyle), width: changeStyleWithScale(canvasStyle.width, canvasStyle.scale) + 'px',
      height: changeStyleWithScale(canvasStyle.height, canvasStyle.scale) + 'px',
    }">
      <template v-for="(item, index) in commonComponent" :key="item.id">
        <component :is="item.component" class="component" :element="item" :style="getStyle(item.style)" @mouseover="handleMouseover($event,item)" @mouseout="handleMouseOut(item)"/>
      </template>
      <template v-for="(item,index) in svgComponent" :key="item.id">
        <component class="svg-preview" :is="item.component" :element="item" :style="{zIndex: item.style.zIndex}"/>
      </template>
    </div>

    <!-- 组件提示框   -->
    <el-tooltip
        v-model:visible="state.visible"
        :content="state.equipName"
        virtual-triggering
        :virtual-ref="triggerRef"
    />
  </div>
</template>

<script setup>
import { getStyle } from '@/utils/webtopo/style.js'
import { changeStyleWithScale } from '@/utils/webtopo/math.js'

import useSubTypeOptions from '@/hooks/option/useSubTypeOptions.js'

// 设备类别（子系统类型）
const { subTypeDictionaries } = useSubTypeOptions()

const triggerRef = ref({
  getBoundingClientRect() {
    return position.value
  },
})

const position = ref({
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
})

const state = reactive({
  visible: false,
  equipName: '',
  drawInfo: JSON.parse(sessionStorage.getItem('webtopo'))
})

let { canvasStyle, componentData } = state.drawInfo

// 普通组件
const commonComponent = computed(() => {
  return componentData.filter(item => item.type !== 'svg')
})

// svg组件
const svgComponent = computed(() => {
  return componentData.filter(item => item.type === 'svg')
})


const getCanvasStyle = (style) => {
  return getStyle(style, ['width', 'height', 'scale'])
}

const handleMouseover = (e,equip) => {
  if (equip.type === 'evnet' && Object.keys(equip.params.equipInfo).length){
    state.equipName = subTypeDictionaries[equip.params.equipInfo.subType] + '-' + equip.params.equipInfo.equipmentName

    let {x,y,width,height} = e.srcElement.getBoundingClientRect()

    position.value = DOMRect.fromRect({
      x: x + (width/2),
      y: y + height,
    })

    state.visible = true
  }
}

const handleMouseOut = () => {
  state.visible = false
}
</script>

<style lang='less' scoped>
.draw {
  width: 100%;
  height: 100%;
  overflow: auto;
  display: -webkit-box;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  -webkit-box-orient: vertical;
}

#container {
  margin: auto;
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain!important;

  .component {
    position: absolute;
    min-width: 14px;
    min-height: 14px;
  }
}

.svg-preview{
  width: 100%;
  height: 100%;
  position: absolute;
  pointer-events: none;
}
</style>
