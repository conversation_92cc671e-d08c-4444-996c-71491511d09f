<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" @close="onClose" :formRef="ruleFormRef" :width="900">
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.rules" label-width="120px" label-suffix=":">
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户姓名" prop="showName">
            <el-input v-model="state.form.showName" placeholder="请输入用户姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="登录账号" prop="loginName">
            <el-input v-model="state.form.loginName" placeholder="请输入登录账号" clearable :disabled="state.form.userId" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="登录密码" v-if="!state.form.userId" prop="password">
            <el-input v-model="state.form.password" type="password" autocomplete="new-password" placeholder="请输入登录密码"
              clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="再次输入密码" v-if="!state.form.userId" prop="passwordRepeat">
            <el-input v-model="state.form.passwordRepeat" type="password" placeholder="请再次输入登录密码" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="state.form.phone" :controls="false" placeholder="请输入手机号码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="短号" prop="shortPhone">
            <el-input v-model="state.form.shortPhone" :controls="false" placeholder="请输入短号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="所属部门" prop="deptId">
            <el-cascader v-model="state.form.deptId" :clearable="true" placeholder="请选择所属部门"
              :loading="state.deptInfo.impl.loading" :props="{
                value: 'deptId',
                label: 'deptName',
                emitPath: false,
                checkStrictly: true,
                expandTrigger: 'hover',
              }" @visible-change="onDeptIdVisibleChange" :options="state.deptInfo.impl.dropdownList"
              @change="onDeptIdValueChange">
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户岗位" prop="deptPostIds">
            <el-select v-model="state.form.deptPostIds" multiple placeholder="请选择用户岗位">
              <el-option v-for="deptPost in state.deptPostOptions" :key="deptPost.deptPostId"
                :label="deptPost.postShowName" :value="deptPost.deptPostId" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="用户标签" prop="tagIds">
            <el-select v-model="state.form.tagIds" multiple placeholder="请选择用户标签">
              <el-option v-for="tag in state.tagOptions" :key="tag.id" :label="tag.name" :value="tag.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="cardNo">
            <el-input v-model="state.form.cardNo" placeholder="请输入身份证号" clearable maxlength="18" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-select v-model="state.form.sex" placeholder="请选择用户性别">
              <el-option v-for="sex in state.sexOptions" :key="sex.value" :label="sex.label" :value="sex.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>


      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">权限信息</div>
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户角色" prop="roleIds">
            <el-select v-model="state.form.roleIds" multiple placeholder="请选择用户角色">
              <el-option v-for="role in state.roleOptions" :key="role.roleId" :label="role.roleName"
                :value="role.roleId" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="权限组" prop="powerGroupIds">
            <el-select v-model="state.form.powerGroupIds" multiple placeholder="请选择权限组">
              <el-option v-for="powerGroup in state.powerGroupOptions" :key="powerGroup.id" :label="powerGroup.name"
                :value="powerGroup.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {
  getRoleListAPI,
  getDeptListAPI,
  getDeptPostListAPI,
  detailUserAPI,
  saveOrUpdateUserAPI
} from '@/api/settingSystem/user.js';
import { pageListAPI } from '@/api/settingSystem/tag.js'
import { DropdownWidget } from '@/utils/widget.js';
import { listPowerGroupAPI, listByUserPowerGroupAPI, saveUserPowerGroupAPI } from '@/api/settingSystem/powerGroup.js'
import { nextTick, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
const { title } = toRefs(props);

const emit = defineEmits(['submit']);

const dialog = ref();
const ruleFormRef = ref();

const validateRegular = (value,callback,Reg,tip) => {
  if (value && !Reg.test(value)) {
    callback(new Error(tip))
  } else {
    callback()
  }
}

const state = reactive({
  form: {
    deptId: null,
    deptPostIds: [],
    tagIds: [],
    roleIds: [],
    powerGroupIds: []
  },
  deptInfo: {
    impl: new DropdownWidget(loadDeptDropdownList, true, 'deptId'),
  },
  sexOptions: [
    {
      label: '男',
      value: 1
    },
    {
      label: '女',
      value: 2
    },
    {
      label: '未知',
      value: 3
    }
  ],
  roleOptions: [],
  powerGroupOptions: [],
  tagOptions: [],
  deptPostOptions: [],
  rules: {
    showName: [{ required: true, message: '用户姓名不能为空', trigger: 'blur' }],
    loginName: [{ required: true, message: '登录账号不能为空', trigger: 'blur' }],
    password: [{ required: true, message: '用户密码不能为空', trigger: 'blur' }],
    passwordRepeat: [{ required: true, message: '重输密码不能为空', trigger: 'blur' }],
    phone: [
      { required: true, message: '手机号码不能为空', trigger: 'blur' },
      { pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/, message: '手机号码格式不正确！', trigger: 'blur' }
    ],
    deptId: [{ required: true, message: '所属部门不能为空', trigger: 'blur' }],
    postIds: [{ required: true, message: '用户岗位不能为空', trigger: 'blur' }],
    roleIds: [{ required: true, message: '用户角色不能为空', trigger: 'blur' }],
    cardNo: [
      { validator: (rule, value, callback) => validateRegular(value, callback, /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, '身份证号格式不正确！'), trigger: 'blur' }
    ]
  },
});

onMounted(() => {
  state.deptInfo.impl.onVisibleChange(true);
});

const open = (userId) => {
  dialog.value.open();
  nextTick(() => {
    if (userId) {
      detailUserAPI(userId).then(res => {
        if (res.success) {
          Object.assign(state.form, res.data)
          state.form.deptId = res.data.sysDeptVO.deptId
          if (res.data.sysDeptPosts) {
            state.form.deptPostIds = res.data.sysDeptPosts.map(deptPost => deptPost.deptPostId)
          }
          if (res.data.sysRoles) {
            state.form.roleIds = res.data.sysRoles.map(role => role.roleId)
          }
          if (res.data.sysTags) {
            state.form.tagIds = res.data.sysTags.map(tag => tag.id)
          }
          loadDeptPostList(state.form.deptId);
          // 加载用户权限组
          queryPowerGroupByUser(state.form.userId)
        }
      })
    }
    loadUserRelList();
    queryPowerGroups()
    loadUserTags()
  })
}

/** 查询部门list */
function loadDeptDropdownList() {
  return new Promise((resolve, reject) => {
    getDeptListAPI({})
      .then((res) => {
        resolve(res.data.dataList);
      })
      .catch((e) => {
        reject(e);
      });
  })
}

/** 查询岗位list */
const loadDeptPostList = (deptId) => {
  if (deptId) {
    getDeptPostListAPI({ deptId: deptId }).then((res) => {
      state.deptPostOptions = res.data;
    });
  } else {
    state.deptPostOptions = []
  }
}

/** 获取和用户相关的数据 */
const loadUserRelList = () => {
  // 查询用户角色list
  getRoleListAPI({sysRoleDtoFilter:{status:1}}).then((res) => {
    state.roleOptions = res.data.dataList;
  })
}

// 查询用户权限组列表
const queryPowerGroupByUser = (userId) => {
  listByUserPowerGroupAPI({ userId: userId }).then(res => {
    if (res.success) {
      state.form.powerGroupIds = res.data?.map(permGroup => permGroup.id)
    }
  })
}


// 查询权限组列表
const queryPowerGroups = () => {
  listPowerGroupAPI().then(res => {
    if (res.success) {
      state.powerGroupOptions = res.data.dataList
    }
  })
}

const loadUserTags = () => {
  pageListAPI({ type: 1 }).then(res => {
    if (res.success) {
      state.tagOptions = res.data.dataList
    }
  })
}

/** 部门下拉框显隐 */
const onDeptIdVisibleChange = (show) => {
  state.deptInfo.impl.onVisibleChange(show).catch((e) => { });
}

/** 部门选中值改变 */
const onDeptIdValueChange = (value) => {
  loadDeptPostList(value);
}

const onClose = () => {
  state.form = {}
}

/** 保存用户 */
const submit = () => {
  let param = {
    userId: state.form.userId,
    loginName: state.form.loginName,
    showName: state.form.showName,
    deptId: state.form.deptId,
    phone: state.form.phone,

    shortPhone: state.form.shortPhone,

    sex: state.form.sex,
    roleIds: state.form.roleIds,
    deptPostIds: state.form.deptPostIds,
    tagIds: state.form.tagIds,

    cardNo: state.form.cardNo
  }
  if (!state.form.userId) {
    if (state.form.password !== state.form.passwordRepeat) {
      ElMessage.error('两次密码输入不一致，请重新输入!');
      return;
    }
    param.password = state.form.password
    param.confirmPassword = state.form.passwordRepeat
  }

  saveOrUpdateUserAPI(param).then(res => {
    if (res.success) {
      saveUserPowerGroupAPI({ userId: res.data, permGroupIds: state.form.powerGroupIds })
        .then(res => {
          if (res.success) {
            ElMessage.success("用户保存成功！");
            dialog.value.close();
            emit('submit');
          } else {
            ElMessage.error(res.errorMessage);
          }
        })
    } else {
      ElMessage.error(res.errorMessage);
    }
  })


  // function subHandle(req, title) {
  //   req(data).then((res) => {
  //     if (res.success) {
  //       ElMessage.success(title);
  //       dialog.value.close();
  //       emit('submit');
  //     } else {
  //       ElMessage.error(res.errorMessage);
  //     }
  //   });
  // }
}


defineExpose({
  open
})

</script>

<style scoped>
.divFlex {
  margin-bottom: 10px;
}

.divRight {
  margin-left: 8px;
  font-size: 16px;
  display: inline-block;
}

.divLeft {
  width: 7px;
  height: 20px;
  background-color: #3f9eff;
  display: inline-block;
  vertical-align: top;
}
</style>
