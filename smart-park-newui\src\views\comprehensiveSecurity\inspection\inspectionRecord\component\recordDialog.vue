<template>
  <el-dialog v-model="showRecordDialogRef" :title="title" @close="close" width="60%" class="dialogCommon">
    <el-table :data="state.tableData" :height="state.tableHeight">
      <el-table-column v-for="item of state.tableHeader" :label="item.label" :prop="item.prop" align="center">
        <template #default="scope">
          <div v-if="item.prop === 'status'">
            <span>{{ scope.row['status'] === 1 ? '已巡' : '未巡' }}</span>
          </div>
          <div v-else-if="!scope.row[item.prop]">
            /
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <el-button
            link
            type="primary"
            :disabled="scope.row.status === 1 || !state.hasConfirm"
            @click.prevent="onConfirm(scope.row)"
          >
            确认
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="close">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script setup>


import {
  completeInspectionRecordDetailAPI,
  listInspectionRecordDetailAPI
} from "@/api/comprehensiveSecurity/inspectionRecordDetail.js";
import {useDateFormat} from "@vueuse/core";
import {ElMessage, ElMessageBox} from "element-plus";

// 父组件
const emits = defineEmits(['onClose'])

// 父组件中的属性
const props = defineProps(['title'])
let { title } = toRefs(props)

// dialog引用
const showRecordDialogRef = ref(false)

const state = reactive({
  // 表格高度
  tableHeight: 580,
  // 是否拥有确认权限
  hasConfirm: false,
  recordId: null,
  // 表格数据
  tableData: [],
  // 表格头
  tableHeader: [
    { label: '巡更点名称', prop: 'pointName' },
    { label: '巡更人', prop: 'inspectionUsername' },
    { label: '巡更日期', prop: 'inspectionDate' },
    { label: '巡更时间段', prop: 'timeRange' },
    { label: '实际巡更时间', prop: 'completionTime' },
    { label: '巡更状态', prop: 'status' },
  ]
})


const open = (id, inspectionUserIds) => {
  showRecordDialogRef.value = true
  state.recordId = id
  queryList(id)


  // 获取登录用户id
  let logUserId = JSON.parse(localStorage.getItem('userInfo')).userId
  // 是否是巡更人员
  if (inspectionUserIds.split(',').includes(logUserId)) {
    state.hasConfirm = true
  }
}


const close = () => {
  showRecordDialogRef.value = false
  state.recordId = null
  state.hasConfirm = false
  state.tableData = []
  emits('onClose')
}

// 查询巡更记录中巡更点的详情信息
const queryList = (id) => {
  let param = {
    recordId: id
  }
  listInspectionRecordDetailAPI(param).then(res => {
    if (res.success) {
      state.tableData = res.data
      state.tableData.forEach(d => {
        if (d.completionTime) {
          d.completionTime = useDateFormat(d.completionTime, 'YYYY-MM-DD HH:mm').value
        }
      })
    }
  })
}

// 确认巡更完成
const onConfirm = (val) => {
  ElMessageBox.confirm(
    '是否确认?',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
  }).then(() => {
    let param = {
      id: val.id
    }
    completeInspectionRecordDetailAPI(param).then(res=> {
      if (res.success) {
        queryList(state.recordId);
        ElMessage({
          type: 'success',
          message: '确认成功！'
        })
      } else {
        ElMessage({
          type: 'error',
          message: '确认失败！' + res.errorMessage
        })
      }
    })

  }).catch(() => {
      ElMessage({
        type: 'info',
        message: '取消确认！',
      })
  })
}

// 暴露方法到父组件
defineExpose({
  open
})
</script>

<style scoped lang="less">

</style>
