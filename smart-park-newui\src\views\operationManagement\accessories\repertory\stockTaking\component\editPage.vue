<template>
  <div style="height: 100%;">
    <el-card class="box-card">
      <template #header>
        <el-row justify="space-between" align="middle">
          <strong>{{ state.title }}</strong>
          <el-button type="primary" icon="Back" @click="showPage">返回</el-button>
        </el-row>
      </template>
      <el-form label-position="top" ref="ruleFormRef" :model="form" :rules="state.rules" label-width="0" label-suffix=":">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">基本信息</div>
        </div>
        <div class="detail-area">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item  label="盘点单号" prop="stocktakingNo" label-width="120px">
                <el-input v-model="form.stocktakingNo" placeholder="请输入盘点单号" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="盘点单名称" prop="stocktakingName" label-width="120px">
                <el-input v-model="form.stocktakingName" placeholder="请输入盘点单名称" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="盘点开始时间" prop="beginTime" label-width="120px">
                <el-date-picker v-model="form.beginTime" type="date" placeholder="请选择盘点开始时间" value-format="YYYY-MM-DD"
                  style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="盘点结束时间" prop="endTime" label-width="120px">
                <el-date-picker v-model="form.endTime" type="date" placeholder="请选择盘点结束时间" value-format="YYYY-MM-DD"
                  style="width: 100%;" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="盘点仓库" prop="storehouseId" label-width="120px">
                <el-select v-model="form.storehouseId" placeholder="盘点前请先锁定仓库" clearable filterable
                  @change="storeChange">
                  <el-option v-for="item in state.storehouseOption" :label="item.storehouseName" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="备件类别" prop="classifyIds" label-width="120px">
                <el-tree-select v-model="form.classifyIds" :data="state.classifyIdsOption" collapse-tags show-checkbox
                  multiple clearable :max-collapse-tags="1" collapse-tags-tooltip :render-after-expand="false"
                  node-key="id" :props="{ label: 'classifyName' }" placeholder="请选择备件分类" @change="classifyChange" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="负责人" prop="headUserId" label-width="120px">
                <el-select v-model="form.headUserId" filterable clearable placeholder="请选择负责人(可直接搜索)">
                  <el-option v-for="item in state.headUserOption"
                    :label="item.showName + (item.deptName ? ' - ' + item.deptName : '')" :value="item.userId" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="盘点规则" prop="rule" label-width="120px">
                <el-select v-model="form.rule" filterable clearable placeholder="请选择盘点规则">
                  <el-option v-for="(value, key) in state.ruleOption" :label="value" :value="Number(key)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item  label="备注" prop="remark" label-width="120px">
                <el-input v-model="form.remark" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500"
                  show-word-limit placeholder="请输入备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">盘点资产</div>
        </div>
        <div class="detail-area">
          <el-table :data="form.detailDTOList">
            <el-table-column prop="sparePartName" label="备件名称">
            </el-table-column>
            <el-table-column prop="sparePartNo" label="备件编号">
            </el-table-column>
            <el-table-column prop="classifyName" label="备件分类">
            </el-table-column>
            <el-table-column prop="model" label="规格型号">
            </el-table-column>
            <el-table-column prop="unit" label="单位">
            </el-table-column>
            <el-table-column prop="unitPrice" label="单价（元）">
            </el-table-column>
            <el-table-column prop="storehouseName" label="所在仓库">
            </el-table-column>
            <el-table-column prop="inventoryQuantity" label="库存数量">
            </el-table-column>
            <el-table-column prop="stocktakingQuantity" label="盘点数量" width="180">
              <template #default="{ row, $index }">
                <el-input-number v-model="row.stocktakingQuantity" :min="0" step-strictly />
              </template>
            </el-table-column>
            <el-table-column prop="resultQuantity" label="盈亏数量" :formatter="resultQuantityFormatter">
            </el-table-column>
            <el-table-column prop="result" label="盘点结果" :formatter="resultFormatter">
            </el-table-column>
            <el-table-column prop="remark" label="备注">
              <template #default="{ row, $index }">
                <el-input v-model="row.remark" />
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="160">
              <template #default="{ row, $index }">
                <el-upload v-model:file-list="row.fileList" :action="state.action"
                  :headers="{ Authorization: state.Authorization }" :on-success="fileSuccess" :on-preview="onPreview"
                  :limit="3" :on-exceed="onExceed" class="upload-noTip">
                  <el-link type="primary">上传图片</el-link>
                </el-upload>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-form-item class="form-btn item__content-noBg">
          <el-button @click="showPage">取消</el-button>
          <el-button type="primary" @click="onSubmit">保存</el-button>
          <el-button type="primary" @click="onSubmit(0)">保存为草稿</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-dialog v-model="state.dialogVisible">
      <img w-full :src="imgTransfer(state.dialogImageUrl)" alt="Preview Image" style="width: 100%;" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ElInput, ElMessage, ElMessageBox, ElTag } from "element-plus";

import { listUsersAPI } from '@/api/settingSystem/user.js';
import { getCategoryTreeAPI } from "@/api/operationManagement/metarialCategory.js";
import { getStorePageAPI } from '@/api/operationManagement/storeManagement.js'

import { stockSaveAPI, stockDetailAPI } from '@/api/operationManagement/stockTaking.js'

import dayjs from "dayjs";
import { accessoriesListAPI } from "@/api/operationManagement/workOrder.js";

const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['showPage'])

const ruleFormRef = ref();
const dialog = ref();
const form = reactive({
  rule: 1,
  detailDTOList: [],
  classifyIds: []
});

const state = reactive({
  action: import.meta.env.VITE_BASE_URL+ '/core/file/upload',
  Authorization: localStorage.getItem('Authorization'),
  title: '',
  storehouseOption: [], // 仓库
  classifyIdsOption: [], // 分类
  headUserOption: [], //负责人
  ruleOption: {
    1: '不需要拍照',
    2: '需要拍照'
  },
  resultOption: {
    1: '无盈亏',
    2: '盘盈',
    3: '盘亏'
  },
  resultTypeOption: {
    1: 'primary',
    2: 'success',
    3: 'danger'
  },
  rules: {
    stocktakingNo: [{ required: true, message: '请输入盘点单号' }],
    stocktakingName: [{ required: true, message: '请输入盘点单名称' }],
    beginTime: [{ required: true, message: '请选择盘点开始时间' }],
    endTime: [{ required: true, message: '盘点结束时间' }],
    storehouseId: [{ required: true, message: '请选择盘点仓库' }],
    classifyIds: [{ required: true, message: '请选择备件分类' }],
  },
  dialogVisible: false, //图片
  dialogImageUrl: ''
});

// 图片转化
const imgTransfer = (param) => {
  return import.meta.env.VITE_BASE_URL+ param
}

const loadPage = (id) => {
  form.id = id
  if (form.id) {
    state.title = '编辑盘点'
    getDetail()
  } else {
    state.title = '新增盘点'
    form.stocktakingNo = 'PD' + dayjs().format('YYYYMMDDHHmmss0SSS')
  }
  getStorePage()
  getCategoryTree()
  loadUserList()
}

// 获取仓库
const getStorePage = () => {
  getStorePageAPI({ state: 0, businessType: 'OPERATIONS' }).then(res => {
    state.storehouseOption = res.data.dataList
  })
}

// 获取分类树形
const getCategoryTree = () => {
  getCategoryTreeAPI().then(res => {
    state.classifyIdsOption = res.data
  })
}

/** 查询用户 */
const loadUserList = () => {
  listUsersAPI({}).then((res) => {
    if (res.success) {
      state.headUserOption = res.data.dataList;
    }
  });
};

// 获取详情
const getDetail = () => {
  stockDetailAPI({
    id: form.id
  }).then(res => {
    // 处理数据
    res.data.classifyIds = res.data.classifyIds ? res.data.classifyIds.split(',') : []
    res.data.detailDTOList = res.data.detailList.map(item => {
      item.fileList = item.imgs ? item.imgs.split(',').map((item, index) => {
        return {
          name: '图片' + (index + 1),
          url: item
        }
      }) : []
      return {
        ...item,
        ...(item.sparePartVO || {})
      }
    })
    Object.assign(form, res.data)
  })
}

// 返回
const showPage = () => {
  delete form.id
  form.detailDTOList = []
  ruleFormRef.value.clearValidate()
  ruleFormRef.value.resetFields()
  emit('showPage', 0)
}

// 仓库改变
const storeChange = () => {
  getSparePart()
}

// 树形
const classifyChange = () => {
  nextTick(() => {
    getSparePart()
  })
}

// 获取备件
const getSparePart = () => {
  form.detailDTOList = []
  if (form.storehouseId && form.classifyIds && form.classifyIds.length) {
    accessoriesListAPI({
      storehouseId: form.storehouseId,
      classifyIds: form.classifyIds,
      businessType: 'OPERATIONS',
      storehouseState: 0,
    }).then(res => {
      form.detailDTOList = res.data.dataList.map(item => {
        return {
          ...item,
          stocktakingQuantity: item.inventoryQuantity
        }
      })
    })
  }
}

// 盈亏数量
const resultQuantityFormatter = (row, column, cellValue) => {
  row.resultQuantity = row.stocktakingQuantity - row.inventoryQuantity
  if (row.resultQuantity > 0) {
    row.result = 2
    return h('span', { style: { color: '#87cf63' } }, "+" + row.resultQuantity)
  } else if (row.resultQuantity == 0) {
    row.result = 1
    return h('span', { style: { color: '#4ea5ff' } }, row.resultQuantity)
  } else if (row.resultQuantity < 0) {
    row.result = 3
    return h('span', { style: { color: '#f78686' } }, row.resultQuantity)
  }
}

// 盘点结果
const resultFormatter = (row, column, cellValue) => {
  return cellValue && h(ElTag, { type: state.resultTypeOption[cellValue] }, { default: () => state.resultOption[cellValue] })
}

// 限制数量
const onExceed = () => {
  ElMessage.warning('最多可支持上传三张照片')
}

// 上传图片
const fileSuccess = (response, file) => {
  file.url = response.data.filePath
}

// 预览
const onPreview = (uploadFile) => {
  state.dialogImageUrl = uploadFile.url
  state.dialogVisible = true
}

// 提交
const onSubmit = (result) => {
  if (!form.detailDTOList || !form.detailDTOList.length) {
    return ElMessage.error('请添加盘点清单')
  }

  let subForm = JSON.parse(JSON.stringify(form));
  (subForm.detailDTOList || []).forEach(item => {
    item.imgs = (item.fileList || []).map(i => i.url).toString()
  })

  if (result == 0) {
    subForm.result = 0
  } else {
    delete subForm.result
  }

  ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      stockSaveAPI({
        ...subForm,
        businessType: 'OPERATIONS',
      }).then((res) => {
        if (res.success) {
          ElMessage.success(result == 0 ? '保存草稿成功' : '保存成功');
          showPage()
        } else {
          ElMessage.error(res.errorMessage);
        }
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}

defineExpose({
  getDetail,
  loadPage
})
</script>

<style scoped lang="less">
.form-btn {
  margin-top: 10px;

  :deep(.el-form-item__content) {
    display: flex;
    justify-content: center;
  }
}

:deep(.el-select__selected-item) {
  .el-tag {
    max-width: inherit !important;
  }
}
</style>
