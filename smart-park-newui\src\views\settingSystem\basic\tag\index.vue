<template>
  <page-common v-model="state.tableHeight" :queryBool="false" :operate-bool="false">
    <template #table>
      <el-tabs v-model="state.activeName" class="space-tabs" @tab-click="handleClick">
        <el-tab-pane :name="tab" v-for="{ tab, columns } of state.tagTabs">
          <template #label>
            <span class="space-tabs-label" v-if="tab === 1">
              <el-icon>
                <Location />
              </el-icon>
              <span>用户标签</span>
            </span>
            <span class="space-tabs-label" v-else-if="tab === 2">
              <el-icon>
                <OfficeBuilding />
              </el-icon>
              <span>设备标签</span>
            </span>
          </template>

          <div class="space-tabs-search">
            <el-form :inline="true" :model="state.queryForm" class="space-tabs-form-inline">
              <template v-if="tab === 1">
                <el-form-item>
                  <el-input v-model="state.queryForm.key" placeholder="标签名称/编号" />
                </el-form-item>
              </template>
              <template v-else-if="tab === 2">
                <el-form-item>
                  <el-input v-model="state.queryForm.key" placeholder="标签名称/编号" />
                </el-form-item>
              </template>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="queryList" class="search-query">
                    <el-icon>
                      <Search />
                    </el-icon>
                    <span>查询</span>
                  </el-button>
                  <el-button type="primary" @click="resetQuery" class="search-reset">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    <span>重置</span>
                  </el-button>

                </el-button-group>
              </el-form-item>
            </el-form>
          </div>
          <div class="space-tabs-btn">
            <el-button type="primary" @click="editBtn">
              <el-icon>
                <Plus />
              </el-icon>
              <span>新增标签</span>
            </el-button>
          </div>
          <div class="space-tabs-table">
            <el-table :data="tableData" :height="state.tableHeight - 152" tooltip-effect="light">
              <el-table-column v-for="column of columns" :label="column.label" :prop="column.prop"
                :formatter="column.formatter" :width="column.width" show-overflow-tooltip />
              <el-table-column label="操作" align="center" width="160">
                <template #default="scope">
                  <el-button link type="primary" icon="Edit" @click.prevent="editBtn(scope.row)">
                    编辑
                  </el-button>
                  <el-button link type="danger" icon="Delete" @click.prevent="deleteRow(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
              :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize"
              :total="state.pageParam.total" @size-change="sizeChange" @current-change="pageChange" />
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 子组件，编辑和新建空间 -->
      <edit ref="edit" @closeClick="queryList"></edit>
    </template>
  </page-common>
</template>
<script setup>
import { pageListAPI, deleteAPI } from "@/api/settingSystem/tag.js";
import dayjs from "dayjs";
import { Search } from '@element-plus/icons-vue'
import { ElTable, ElMessage, ElMessageBox } from 'element-plus'
import Edit from "./component/edit.vue"
import { calcPageNo } from '@/utils/util.js';
//定义子组件实例，名称要和子组件中的 ref 属性值一致
const edit = ref(null);

// 定义属性
const state = reactive({

  tableHeight: 100,
  // 激活的标签页
  activeName: ref(1),

  tagTabs: [
    {
      tab: 1,
      columns: [
        { label: "标签编号", prop: "code" },
        { label: "标签名称", prop: "name" },
        { label: "备注", prop: "remark" },
        {
          label: "创建时间", prop: "createTime", width: 160, formatter: (row, column, cellValue) => {
            return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
          }
        }
      ]
    },
    {
      tab: 2,
      columns: [
        { label: "标签编号", prop: "code" },
        { label: "标签名称", prop: "name" },
        { label: "备注", prop: "remark" },
        {
          label: "创建时间", prop: "createTime", width: 160, formatter: (row, column, cellValue) => {
            return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
          }
        }
      ]
    }
  ],

  // 查询表单
  queryForm: {
    key: '',
    selectId: []
  },

  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
})

// 表格列表数据
const tableData = ref([])

// 标签页点击事件，查询选择列表
const handleClick = (tab, event) => {
  let activeName = tab.props.name
  state.activeName = activeName
  state.queryForm.selectId = []
  state.queryForm.key = ''
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10


  queryList();
}


// 变更分页记录数
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  queryList();
}
// 变更分页页数
const pageChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  queryList()
}

// 重置查询条件
const resetQuery = () => {
  state.queryForm.selectId = []
  state.queryForm.key = ''
  queryList()
}

// 提交查询表单
const queryList = () => {
  let spaceQuery = {
    type: state.activeName,
    key: state.queryForm.key,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }

  if (state.queryForm.selectId != null && state.queryForm.selectId.length > 0) {
    spaceQuery.path = state.queryForm.selectId.join('/')
  }

  pageListAPI(spaceQuery).then(res => {
    if (res.success) {
      tableData.value = res.data.dataList
      state.pageParam.total = res.data.totalCount
    }
  })
}

// 删除
const deleteRow = (row) => {
  ElMessageBox.confirm(
    '是否删除当前标签?',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize);

    let params = {
      tagId: row.id
    }
    deleteAPI(params).then(res => {
      if (res.success) {
        ElMessage.success('删除成功！')
      } else {
        ElMessage.error('删除失败！' + res.errorMessage)
      }
      queryList();
    }).catch(e => {
      ElMessage.error('删除失败！')
    })
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除！',
    })
  })
}

// 编辑
const editBtn = (val) => {
  edit.value.openDialog(val.id == null ? 'CREATE' : 'UPDATE', state.activeName, JSON.parse(JSON.stringify(val)))
}

onMounted(() => {
  queryList();
})
</script>


<style>
/* 标签页 标题 icon */
.space-tabs .space-tabs-label .el-icon {
  vertical-align: middle;
}

/* 标签页 标题名称 */
.space-tabs .space-tabs-label span {
  vertical-align: middle;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
}

/* 标签页内容 */
.space-tabs>.el-tabs__content {
  margin-left: 2px;
  /*color: #6b778c;*/
  /*font-weight: 600;*/
}

/* 查询按钮 */
.space-tabs .space-tabs-search .search-query {
  border-radius: 5px
}

/* 重置按钮 */
.space-tabs .space-tabs-search .search-reset {
  border-radius: 5px;
  margin-left: 8px
}

/* 表格 */
.space-tabs .space-tabs-table {
  margin-top: 15px;
}
</style>
