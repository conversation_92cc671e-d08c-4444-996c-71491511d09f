<template>
  <div v-show="!isMin" class="web-window" :class="!window.isSize && 'iphone'" :style="{ zIndex: zIndex }" @mousedown="goTop" ref="webWindow">
    <div class="background"  @mousedown="resizing">
    </div>
    <el-row align="middle" class="header" justify="end" @mousedown="dragging" @dblclick="maximize">
      <el-icon size="13" @click="minimum" @mousedown.stop>
        <Minus/>
      </el-icon>
      <el-icon size="12" style="transform: rotate(180deg)" @click="maximize" @mousedown.stop v-show="window.isSize">
        <CopyDocument />
      </el-icon>
      <el-icon size="15" class="error" @click="close" @mousedown.stop>
        <Close/>
      </el-icon>
    </el-row>
    <div class="body">
      <iframe  class="iframe" :src="window.url"></iframe>
      <div v-show="!isTop || state.isDragging || state.isResizing" class="body-mask"></div>
    </div>
  </div>
</template>

<script setup>
import { useWindowStore } from '@/store/modules/window.js';

import { events } from '@/utils/bus.js'

const props = defineProps({
  window: {
    type: Object,
    default: () => {}
  }
})

// 创建store对象
const windowStore = useWindowStore();

const webWindow = ref()

const state = reactive({
  isDragging: false,
  isResizing: false,
  beforeLeft: 0,
  beforeTop: 0,
  beforeWidth: 0,
  beforeHeight: 0
})

window.onblur = () => {
  closeWindowPopup()
};

onMounted(() => {
  initSize()
})

// 初始化尺寸
const initSize = () => {
  if(!props.window.isSize){
    webWindow.value.style.width = '375px'
    webWindow.value.style.height = '727px'
    webWindow.value.style.left = (document.documentElement.offsetWidth - 475) + 'px'
    webWindow.value.style.top = '100px'
  }
}

// 是否最小化
const isMin = computed(() => {
  return windowStore.isMin(props.window.id)
})

// 层级
const zIndex = computed(() => {
  return windowStore.getZIndex(props.window.id)
})

// 是否置顶
const isTop = computed(() => {
  return windowStore.isTop(props.window.id)
})

// 关闭弹框
const closeWindowPopup = () => {
  events.emit('closeWindowPopup')
}

// 置顶
const goTop = () => {
  events.emit('closeWindowPopup')
  windowStore.goTop(props.window.id);
}

// 最小化
const minimum = () => {
  windowStore.min(props.window.id)
}

// 关闭
const close = () => {
  windowStore.remove(props.window.id)
}

// 最大化
const maximize = () => {
  if(!props.window.isSize){
    return false
  }

  windowStore.goTop(props.window.id);

  // 判断全屏唯一标准
  let isMax = (webWindow.value.style.left == '0px' || !webWindow.value.style.left) &&
      (webWindow.value.style.top == '0px' || !webWindow.value.style.top) &&
      webWindow.value.offsetWidth == document.documentElement.offsetWidth &&
      webWindow.value.offsetHeight == document.documentElement.offsetHeight - 50

  maxFunDir('left','beforeLeft', isMax)
  maxFunDir('top','beforeTop', isMax)

  maxFunLen('width','beforeWidth', 0 , isMax)
  maxFunLen('height','beforeHeight' , 50, isMax)
}

// 最大化方位
const maxFunDir = (diretion , beforeDir , isMax) => {
  if(!isMax){
    state[beforeDir] = webWindow.value.style[diretion]
    webWindow.value.style[diretion] = '0px'
  }else {
    if(state[beforeDir]){
      webWindow.value.style[diretion] = state[beforeDir]
    }else {
      webWindow.value.style[diretion] = diretion == 'left' ? 200 +  Math.random() * 100 + 'px'  : 50 +  Math.random() * 100 + 'px'
    }
  }
}

// 最大长度
const maxFunLen = (variable, beforeDir,diminution,isMax) => {
  let offsetVal = 'offset' + variable.toLowerCase().replace(/\b[a-z]/g, function(match) {
    return match.toUpperCase();
  });

  if(!isMax){
    state[beforeDir] = webWindow.value[offsetVal]
    webWindow.value.style[variable] = document.documentElement[offsetVal] - diminution + 'px'
  }else {
    if(state[beforeDir]){
      webWindow.value.style[variable] = state[beforeDir] + 'px'
    }else {
      webWindow.value.style[variable] = variable == 'width' ? '1000px' : '600px'
    }
  }
}

// 鼠标按下头部，进入拖拽中状态
const dragging = function () {
  state.isDragging = true;
  // 鼠标松开事件
  parent['document'].addEventListener('mouseup', () => {
    state.isDragging = false;
  });
};

// 鼠标按下大小改变元素，进入大小改变中状态
const resizing = function () {
  state.isResizing = true;
  // 鼠标松开事件
  parent['document'].addEventListener('mouseup', () => {
    state.isResizing = false;
  });
};
</script>

<style lang="less" scoped>
.web-window {
  width: 100vw;
  height: calc(100vh - 50px);
  box-shadow: 0 0 5px rgba(60, 60, 60, 70%);
  -webkit-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
}

.background {
  height: calc(100% + 10px);
  width: calc(100% + 10px);
  position: absolute;
  top: -5px;
  left: -5px;
}

.header {
  height: 32px;
  background: #d3e3fd;
  border-bottom: 1px solid rgba(60, 60, 60, 30%);
  .el-icon{
    width: auto;
    height: 100%;
    padding: 0 12px;
  }
  .el-icon:hover{
    background-color: #b9cae8;
  }
  .el-icon.error:hover{
    color: #fff;
    background-color: #eb4e4e;
  }
}

.body {
  height: calc(100% - 32px);
  position: relative;
  background-color: #FFFFFF;
  .iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
  .body-mask {
    width: 100%;
    height: 100%;
    background-color: transparent;
    position: absolute;
    top: 0;
    left: 0;
  }
}

.iphone{
  border-radius: 20px;
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.5);
  .header{
    border-radius: 20px 20px 0 0;
  }
  .body{
    position: relative;
    border-radius: 0 0 20px 20px;
    .iframe{
      height: calc(100% - 4px);
      border-radius: 0 0 20px 20px;
    }
  }
  .body::before{
    content: '';
    position: absolute;
    height: 4px;
    background-color: #7a7a7a;
    bottom: 3px;
    left: 130px;
    right: 130px;
    border-radius: 2px;
  }
  .el-icon.error{
    border-radius: 0 20px  0 0;
  }
}
</style>
