package com.soft.webadmin.dto.sparePart;

import com.soft.common.core.object.MyPageParam;
import com.soft.webadmin.enums.BusinessTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * SparePartInfoDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartInfoDTO对象")
@Data
public class SparePartDetailQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "备件名称或编号")
    private String nameOrNo;

    @ApiModelProperty(value = "分类id")
    private Long classifyId;

    @ApiModelProperty(value = "分类ids")
    private List<Long> classifyIds;

    @ApiModelProperty(value = "所在仓库")
    private Long storehouseId;

    @ApiModelProperty(value = "仓库状态：0锁定，1启用")
    private Integer storehouseState;

    @ApiModelProperty(value = "是否校验库存：1是，筛选库存数量大于0的备件")
    private Integer hasInventory;

}
