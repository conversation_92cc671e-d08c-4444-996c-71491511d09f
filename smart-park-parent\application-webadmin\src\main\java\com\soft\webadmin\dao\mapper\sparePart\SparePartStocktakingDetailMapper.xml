<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.sparePart.SparePartStocktakingDetailMapper">
    <resultMap type="com.soft.webadmin.model.sparePart.SparePartStocktakingDetail" id="SparePartStocktakingDetailResult">
        <result property="id" column="id" />
        <result property="stocktakingId" column="stocktaking_id" />
        <result property="sparePartId" column="spare_part_id" />
        <result property="storehouseId" column="storehouse_id" />
        <result property="inventoryQuantity" column="inventory_quantity" />
        <result property="stocktakingQuantity" column="stocktaking_quantity" />
        <result property="resultQuantity" column="result_quantity" />
        <result property="result" column="result" />
        <result property="remark" column="remark" />
        <result property="imgs" column="imgs" />
    </resultMap>

    <sql id="selectSparePartStocktakingDetailVo">
        select id, stocktaking_id, spare_part_id, inventory_quantity, stocktaking_quantity, result_quantity, result, remark, imgs from sp_spare_part_stocktaking_detail
    </sql>
    
</mapper>