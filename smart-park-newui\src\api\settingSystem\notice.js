import { request } from '@/utils/request';

// 分页查询
export const getNoticePageAPI = (data) => {
  return request('get', '/core/notice/list', data, 'F');
};

// 保存
export const saveNoticeAPI = (data) => {
  return request('post', '/core/notice/save', data);
};

// 删除
export const deleteNoticeAPI = (data) => {
  return request('post', '/core/notice/delete', data, 'F');
};

// 修改状态
export const updateStatusNoticeAPI = (data) => {
  return request('post', '/core/notice/updateStatus', data, 'F');
};
