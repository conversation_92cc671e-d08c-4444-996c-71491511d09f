<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.contingency.EmergencyMapper">
    <resultMap type="com.soft.webadmin.model.contingency.Emergency" id="EmergencyResult">
        <result property="id" column="id" />
        <result property="eventId" column="event_id" />
        <result property="name" column="name" />
        <result property="annex" column="annex" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectEmergencyVo">
        select t.id, t.event_id, t.name, t.annex, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time from cm_emergency t
    </sql>
    
</mapper>