<template>
  <div style="height: 100%;overflow: hidden;">
    <transition name="el-zoom-in-center">
      <!--我的工单-->
      <listTable v-show="0 === pageIndex" @showPage="showPage" ref="table" type="my"></listTable>
    </transition>
    <transition name="el-zoom-in-center">
      <!--维保&巡检工单详情-->
      <detail v-if="2 === pageIndex" @showPage="showPage" :title="state.title" :id="state.id"></detail>
    </transition>
    <transition name="el-zoom-in-center">
      <!-- 报事报修-->
      <detail2 v-if="3 === pageIndex" @showPage="showPage" :from="1" :title="state.title" :id="state.id"></detail2>
    </transition>
  </div>
</template>

<script setup>
import listTable from '../order/component/listTable.vue';
import detail from '../order/component/detail.vue';
import detail2 from '../order/component/detail2.vue';

const pageIndex = ref(0)
const table = ref()

const state = reactive({
  title: '',
  id: ''
})

const showPage = (index, title, id) => {
  pageIndex.value = index;
  if (index === 0) {
    table.value.getList();
  } else {
    state.title = title;
    state.id = id
  }
}
</script>
