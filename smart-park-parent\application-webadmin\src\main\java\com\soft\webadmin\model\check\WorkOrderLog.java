package com.soft.webadmin.model.check;

import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.*;
import com.soft.webadmin.vo.check.WorkOrderLogVO;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * 工单操作记录对象 sp_check_operate_log
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_work_order_log")
public class WorkOrderLog extends BaseModel {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 工单id */
    private Long orderId;

    /** 操作类型：1创建工单、2派单、3响应、4退回、5关闭工单、6报价、7审计通过、8审计驳回、9挂单、10取消挂单、11完成工单、12评价工单、13驳回 */
    private Integer operate;

    /** 内容 */
    private String content;

    /** 删除标记(1: 正常 -1: 已删除) */
    // @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;


    @Mapper
    public interface CheckOperateLogModelMapper extends BaseModelMapper<WorkOrderLogVO, WorkOrderLog> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        WorkOrderLog toModel(WorkOrderLogVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        WorkOrderLogVO fromModel(WorkOrderLog entity);
    }

    public static final CheckOperateLogModelMapper INSTANCE = Mappers.getMapper(CheckOperateLogModelMapper.class);
}
