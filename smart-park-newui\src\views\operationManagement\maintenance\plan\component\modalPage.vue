<template>
  <el-card class="box-card">
    <template #header>
      <el-row justify="space-between" align="middle">
        <strong>{{ title }}</strong>
        <el-button type="primary" icon="Back" @click="showPage">返回</el-button>
      </el-row>
    </template>

    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":"
      label-position="top">
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">基本信息</div>
        </div>
        <div class="detail-area">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item  label="计划名称" prop="planName">
                <el-input v-model="form.planName" placeholder="请输入计划名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="工作班组" prop="workGroupId">
                <el-select v-model="form.workGroupId" filterable placeholder="请选择工作班组">
                  <el-option v-for="item in state.workGroupList" :value="item.id" :label="item.name" :key="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="首保时间" prop="scheduleRule">
                <el-date-picker v-model="form.scheduleRule" type="date" value-format="YYYY-MM-DD"
                  placeholder="请选择首保时间" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="维保周期" prop="maintenanceCycle">
                <el-input-number v-model="form.maintenanceCycle" :min="1" :max="12" :precision="0"
                  controls-position="right" placeholder="请输入维保周期" clearable />
                &nbsp;月
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="开始时间" prop="startTime">
                <el-time-select v-model="form.startTime" start="00:00" step="00:10" end="23:50" placeholder="请选择开始时间"
                  clearable />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="处理时限" prop="handleLimitDuration">
                <el-input-number v-model="form.handleLimitDuration" :min="1" :precision="0" controls-position="right"
                  placeholder="请输入处理时限" clearable />
                &nbsp;小时
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="派单时间" prop="advanceTime">
                <el-input v-model.number="form.advanceTime" placeholder="请输入派单时间" clearable>
                  <template #prepend>提前</template>
                </el-input>
                &nbsp;小时
              </el-form-item>
            </el-col>
            <el-col :span="8"></el-col>
            <el-col :span="8"></el-col>
          </el-row>
        </div>
      </div>
      <div>
        <el-row justify="space-between" align="middle">
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">维保点</div>
          </div>
          <el-button @click="addHandle()" style="margin-bottom: 10px">添加</el-button>
        </el-row>
        <div class="detail-area">
          <el-table :data="form.checkPlanPointList">
            <el-table-column label="序号" type="index" width="60" />
            <el-table-column v-for="(column, index) in state.columnList" :label="column.label" :prop="column.prop"
              :key="index">
              <template #default="scope">
                <div v-if="column.prop === 'dataId'">
                  <span>{{ scope.row.dataName }}</span>
                  <el-form-item  v-show="scope.$index === editIndex"
                    :prop="`checkPlanPointList.${scope.$index}.${column.prop}`" :rules="{
                      required: true,
                      message: `请选择${column.label}`,
                      trigger: 'blur',
                    }" label-width="0">
                    <el-button class="button-new-tag ml-1" size="small" @click="openSelectPage"> + 选择设备</el-button>
                  </el-form-item>
                </div>
                <div v-if="column.prop === 'templateId'">
                  <span v-show="scope.$index !== editIndex">{{ scope.row.templateName }}</span>
                  <el-form-item  v-show="scope.$index === editIndex"
                    :prop="`checkPlanPointList.${scope.$index}.${column.prop}`" :rules="{
                      required: true,
                      message: `请选择${column.label}`,
                      trigger: 'blur',
                    }" label-width="0">
                    <el-select v-model="scope.row[column.prop]" filterable placeholder="请选择检查模板"
                      @change="templateChange($event)">
                      <el-option v-for="item in state.templateList" :key="item.id" :value="item.id"
                        :label="item.templateName" />
                    </el-select>
                  </el-form-item>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="210">
              <template #default="scope">
                <div>
                  <el-button v-show="scope.$index !== editIndex" link icon="Edit" type="primary"
                    @click="editHandle(scope.row)">编辑
                  </el-button>
                  <el-button v-show="scope.$index !== editIndex" link icon="Delete" type="danger"
                    @click="deleteHandle(scope.row)">删除
                  </el-button>
                  <!--<el-button v-show="scope.$index !== editIndex" link icon="Document" type="success" @click="viewHandle(scope.row)">详情</el-button>-->
                  <el-button v-show="scope.$index === editIndex" link icon="Document" type="success"
                    @click="saveHandle(scope.row)" style="margin: 0">保存
                  </el-button>
                  <el-button v-show="scope.$index === editIndex" link icon="Delete" type="danger"
                    @click="closeHandle(scope.row)">删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div style="text-align: center; margin-top: 20px;">
        <el-button type="info" @click="showPage">取消</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </div>
    </el-form>
    <select-operate ref="modal" :multiple="false" @rowClick="equipSelect" />
    <drawer-point-page ref="drawer" :drawer="state.drawer" @cancelClick="state.drawer = false" />
  </el-card>
</template>

<script setup>
import { saveCheckPlanAPI } from '@/api/operationManagement/checkPlan.js';
import { getWorkGroupPageAPI } from '@/api/operationManagement/workGroup.js';
import { getTemplatePageAPI, getTemplateItemListAPI } from '@/api/operationManagement/template.js';
import { viewEquipAPI } from '@/api/operationManagement/equipManage.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import drawerPointPage from './drawerPointPage.vue';

const emit = defineEmits(['showPage'])

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  info: {
    type: Object,
    default: {}
  }
});
const { title, info } = toRefs(props);
const ruleFormRef = ref();
const dialog = ref();
const drawer = ref();
const form = reactive({
  scheduleType: 'MONTH',
  planType: 'MAINTENANCE',
  checkPlanPointList: [],
});

/** 校验派单时间 */
const checkAdvanceTime = (rule, value, callback) => {
  if (!value && isNaN(value)) {
    callback(new Error('派单时间不能为空'));
  } else if (isNaN(value)) {
    callback(new Error('请输入数字值'));
  } else if (value < 0) {
    callback(new Error('请输入不小于0的数字'));
  } else {
    callback();
  }
};

const state = reactive({
  workGroupList: [],
  templateList: [],
  columnList: [
    { prop: 'dataId', label: '设备' },
    { prop: 'templateId', label: '检查模板' },
  ],
  rules: {
    planName: [{ required: true, message: '计划名称不能为空', trigger: 'blur' }],
    workGroupId: [{ required: true, message: '请选择工作班组', trigger: 'change' }],
    scheduleRule: [{ required: true, message: '首保时间不能为空', trigger: 'blur' }],
    maintenanceCycle: [{ required: true, message: '维保周期不能为空', trigger: 'blur' }],
    startTime: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],
    handleLimitDuration: [{ required: true, message: '处理时限不能为空', trigger: 'blur' }],
    advanceTime: [{ required: true, validator: checkAdvanceTime, trigger: 'blur' }],
  },
  drawer: false,
});
const editIndex = ref(-1);
const modal = ref();

onMounted(() => {
  Object.assign(form, props.info);
  // 处理时限，分钟转换成小时
  if (form.handleLimitDuration) {
    form.handleLimitDuration = form.handleLimitDuration / 60;
  }
  loadWorkGroupList();
  loadTemplateList();
})

/** 查看维保点详情 */
const viewHandle = (row) => {
  nextTick(() => {
    viewEquipAPI({ equipmentId: row.dataId }).then((res) => {
      drawer.value.form.equipment = res.data;
    });
    getTemplateItemListAPI({ templateId: row.templateId }).then((res) => {
      drawer.value.form.templateItemList = res.data;
    });
    state.drawer = true;
  });
};

// 校验函数
const validateFun = async () => {
  let dataIdValid = true;
  let templateIdValid = true;

  await ruleFormRef.value.validateField(
    `checkPlanPointList.${editIndex.value}.dataId`,
    (valid) => {
      dataIdValid = valid;
    }
  );

  await ruleFormRef.value.validateField(
    `checkPlanPointList.${editIndex.value}.templateId`,
    (valid) => {
      templateIdValid = valid;
    }
  );

  return dataIdValid && templateIdValid
}

/** 维保点新增 */
const addHandle = async () => {
  let res = await validateFun();

  if (res) {
    const data = {
      pointType: "EQUIPMENT",
      dataId: undefined,
      templateId: undefined,
    };
    form.checkPlanPointList.push(data);
    editIndex.value = form.checkPlanPointList.length - 1;
  }
};


/** 维保点编辑 */
const editHandle = async (row) => {
  let res = await validateFun();

  if(res){
    editIndex.value = form.checkPlanPointList.indexOf(row);
  }
};

/** 维保点删除 */
const deleteHandle = async (row) => {
  let res = await validateFun();
  if(res){
    let index = form.checkPlanPointList.indexOf(row);
    form.checkPlanPointList.splice(index, 1);
  }
};

/** 维保点保存 */
const saveHandle = async (row) => {
  let res = await validateFun();

  if(res){
    editIndex.value = -1;
  }
};

/** 编辑状态时删除 */
const closeHandle = (row) => {
  let index = form.checkPlanPointList.length - 1;
  form.checkPlanPointList.splice(index, 1);
};

const open = () => {
  dialog.value.open();
  loadWorkGroupList();
  loadTemplateList();
  editIndex.value = -1;
};

const showPage = () => {
  emit('showPage', 0)
}

/** 查询工作班组 */
const loadWorkGroupList = () => {
  getWorkGroupPageAPI().then((res) => {
    state.workGroupList = res.data.dataList;
  });
};

/** 打开选择设备窗口 */
const openSelectPage = () => {
  modal.value.open();
};

// 设备选择
const equipSelect = (row) => {
  form.checkPlanPointList[editIndex.value].dataId = row.equipmentId;
  form.checkPlanPointList[editIndex.value].dataName = row.equipmentName;
};

/** 查询维保模板 */
const loadTemplateList = () => {
  getTemplatePageAPI({ templateType: 3 }).then((res) => {
    state.templateList = res.data.dataList;
  });
};

/** 选择维保模板 */
const templateChange = (val) => {
  let obj = {};
  obj = state.templateList.find((item) => {
    return item.id === val;
  });
  form.checkPlanPointList[editIndex.value].templateName = obj.templateName;
};

/** 提交表单 */
const submit = () => {
  ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      if (form.checkPlanPointList.length === 0) {
        ElMessageBox.alert('请至少添加一个维保点', '提醒', {
          type: 'warning',
        });
        return false;
      }
      saveCheckPlanAPI(form).then((res) => {
        if (res.success) {
          ElMessage.success(title.value + '成功');
          // dialog.value.close();
          emit('showPage', 0)
        } else {
          ElMessage.error(res.errorMessage);
        }
      });
    } else {
      console.log('error submit!', fields)
    }
  })
};

defineExpose({
  form,
  open,
});
</script>

<style lang="less" scoped>
:deep(.el-input__inner::placeholder) {
  text-align: left;
}
</style>
