<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.contingency.EarlyWarningMapper">
    <resultMap type="com.soft.webadmin.model.contingency.EarlyWarning" id="EarlyWarningResult">
        <result property="id" column="id" />
        <result property="no" column="no" />
        <result property="eventId" column="event_id" />
        <result property="emergencyId" column="emergency_id" />
        <result property="type" column="type" />
        <result property="businessId" column="business_id" />
        <result property="businessTable" column="business_table" />
        <result property="spaceId" column="space_id" />
        <result property="spacePath" column="space_path" />
        <result property="spaceFullName" column="space_full_name" />
        <result property="location" column="location" />
        <result property="description" column="description" />
        <result property="reportUserId" column="report_user_id" />
        <result property="reportUserPhone" column="report_user_phone" />
        <result property="status" column="status" />
        <result property="handleTime" column="handle_time" />
        <result property="summary" column="summary" />
        <result property="reason" column="reason" />
        <result property="improve" column="improve" />
        <result property="annex" column="annex" />
        <result property="summaryUserId" column="summary_user_id" />
        <result property="summaryTime" column="summary_time" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectEarlyWarningVo">
        t.id, t.no, t.event_id, t.emergency_id, t.type, t.business_id, t.business_table, t.space_id, t.space_path, t.space_full_name, t.location, t.description, t.report_user_id, t.report_user_phone, t.status, t.handle_time, t.summary, t.reason, t.improve, t.annex, t.summary_user_id, t.summary_time, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="queryList" resultType="com.soft.webadmin.vo.contingency.EarlyWarningVO">
        select <include refid="selectEarlyWarningVo" />, t.id event_id, u.show_name report_user_name
        from cm_early_warning t
        left join cm_event e on t.event_id = e.id
        left join common_sys_user u on t.report_user_id = u.user_id
        <where>
            and t.deleted_flag = 1
            <if test="no != null and no != ''">
                and t.no like concat('%', #{no}, '%')
            </if>
            <if test="eventId != null">
                and t.event_id = #{eventId}
            </if>
            <if test="eventLevel != null">
                and e.level = #{eventLevel}
            </if>
            <if test="type != null">
                and t.type = #{type}
            </if>
            <if test="status != null and status.size() > 0">
                and t.status in
                <foreach collection="status" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="spaceId != null">
                and t.space_path like concat('%', #{spaceId}, '%')
            </if>
            <if test="reportUserName != null and reportUserName != ''">
                and u.show_name like concat('%', #{reportUserName}, '%')
            </if>
            <if test="beginDate != null and beginDate != ''">
                and t.create_time &gt;= #{beginDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and t.create_time &lt; date_add(#{endDate}, interval 1 day)
            </if>
            <if test="handleBeginDate != null and handleBeginDate != ''">
                and t.handle_time &gt;= #{handleBeginDate}
            </if>
            <if test="handleEndDate != null and handleEndDate != ''">
                and t.handle_time &lt; date_add(#{handleEndDate}, interval 1 day)
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="earlyWarningStatis" resultType="com.soft.webadmin.vo.daping.xswt.SafetyMonitor.EarlyWarningStatisVO">
        select event_id, count, (select `name` from cm_event where id = t.event_id) event_name
        from
        (select event_id, count(1) count from cm_early_warning
        where deleted_flag = 1 and create_time like concat(#{queryDate}, '%')
        group by event_id) t
        order by count desc limit 4
    </select>
    
</mapper>