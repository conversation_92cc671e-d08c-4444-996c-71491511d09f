import { request } from '@/utils/request';

// 工单列表
export const getWorkOrderPageAPI = (data) => {
  return request('get', '/check/order/getPage', data, 'F');
};

// 我的工单
export const getMyWorkOrderPageAPI = (data) => {
  return request('get', '/check/order/getMyPage', data, 'F');
};

// 工单详情
export const getWorkOrderDetailAPI = (data) => {
  return request('get', '/check/order/detail', data, 'F');
};

// 查询接收人员
export const getWorkUserListAPI = (query) => {
  return request('get', '/check/order/getWorkUserList', query, 'F');
};

// 派单
export const workDispatchAPI = (data) => {
  return request('post', '/check/order/dispatch', data);
};

// 关闭
export const workCloseAPI = (data) => {
  return request('post', '/check/order/close', data);
};

// 处理
export const workHandleAPI = (data) => {
  return request('post', '/check/order/handle', data);
};

// 运送工单处理
export const workHandleTAPI = (data) => {
  return request('post', '/transport/record/handle', data);
}

// 退回
export const workBackAPI = (data) => {
  return request('post', '/check/order/back', data);
};

// 挂单
export const workPendingAPI = (data) => {
  return request('post', '/check/order/pending', data);
};

// 取消挂单
export const workRestartAPI = (data) => {
  return request('post', '/check/order/restart', data, 'F');
};

// 评价
export const workEvaluateAPI = (data) => {
  return request('post', '/check/order/evaluate', data);
};

// 驳回
export const workRejectAPI = (data) => {
  return request('post', '/check/order/reject', data);
};

// 配品配件
export const accessoriesListAPI = (query) => {
  return request('get', '/sparePart/info/getDetailPage', query, 'F');
}

// 报价
export const quoteWorkAPI = (data) => {
  return request('post', '/check/order/quote', data);
}

// 保存草稿
export const workDraftSaveAPI = (data) => {
  return request('post', '/check/order/handleSave', data);
}

// 获取草稿
export const workDraftQueryAPI = (query) => {
  return request('get', '/check/order/handleView', query, 'F');
}

