<template>
  <dialog-common ref="dialog" title="编辑" @submit="submit" @onClose="onClose" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="100px" :model="form" :rules="state.rules" label-suffix=":">
      <el-form-item label="值" prop="keyValue">
        <el-input v-model="form.keyValue" placeholder="请输入值"/>
      </el-form-item>
      <el-form-item label="说明" prop="explainInit">
        <el-input type="textarea" v-model="form.explainInit" :maxlength="500" show-word-limit :autosize="{ minRows: 5}"
                placeholder="请输入说明" />
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {configUpdateAPI} from '@/api/settingSystem/config.js';

import {ElMessage} from 'element-plus'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
})

let {title} = toRefs(props)
const emit = defineEmits(['submit'])

let ruleFormRef = ref()
let dialog = ref()

const form = reactive({})
const state = reactive({
  rules: {
    keyValue: [{required: true, message: '请输入值', trigger: 'blur'},],
    explainInit: [{required: true, message: '请输入说明', trigger: 'blur'}],
  }
})

const onClose = () => {
  delete form.configKey
}

const open = () => {
  dialog.value.open()
}

// 提交字典
const submit = () => {
  configUpdateAPI({...form}).then(res => {
    if (res.success) {
      ElMessage.success('编辑成功')
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

defineExpose({
  form,
  open
})
</script>

<style lang='less' scoped></style>
