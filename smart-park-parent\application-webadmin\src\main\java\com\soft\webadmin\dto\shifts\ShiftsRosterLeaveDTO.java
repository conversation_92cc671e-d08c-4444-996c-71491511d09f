package com.soft.webadmin.dto.shifts;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * ShiftsRosterDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsRosterDTO对象")
@Data
public class ShiftsRosterLeaveDTO {

    @ApiModelProperty(value = "花名册Id")
    @NotNull(message = "数据验证失败，花名册Id不能为空！", groups = {UpdateGroup.class})
    private Long rosterId;

    @NotNull(message = "业务类型不能为空！")
    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private String businessType;

    @ApiModelProperty(value = "职位状态:1在职 0离职")
    private Integer postStatus;

    @NotNull(message = "离职日期不能为空！")
    @ApiModelProperty(value = "离职日期")
    private Date leaveJobDate;

    @NotNull(message = "离职原因不能为空！")
    @ApiModelProperty(value = "离职原因")
    private String leaveJobReason;
}
