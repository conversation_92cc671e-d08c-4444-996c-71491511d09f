package com.soft.webadmin.dao.hiddenDanger;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.hiddenDanger.HiddenDangerQueryDTO;
import com.soft.webadmin.model.hiddenDanger.HiddenDanger;
import com.soft.webadmin.vo.hiddenDanger.HiddenDangerVO;

import java.util.List;

/**
 * 隐患Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-12
 */
public interface HiddenDangerMapper extends BaseMapper<HiddenDanger> {

    List<HiddenDangerVO> queryList(HiddenDangerQueryDTO queryDTO);

}
