<template>
  <el-card class="box-card card-textBg">
    <template #header>
      <el-row justify="space-between" align="middle">
        <span style="font-weight: bold;">{{ title }}</span>
        <el-button type="primary" icon="Back" style="float: right;" @click="showPage">返回</el-button>
      </el-row>
    </template>

    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-position="top">
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">基本信息</div>
        </div>
        <div class="detail-area">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item label="计划名称" prop="planName">
                {{ form.planName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="巡检方式" prop="workGroupId">
                {{ form.planMode === 1 ? '人工巡检' : '智能巡检' }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="开始时间" prop="startTime">
                {{ form.startTime }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="执行频率" prop="scheduleType">
                  <span v-if="form.scheduleType === 'MONTH'">
                    每月{{ form.dateList }}
                  </span>
                <span v-else-if="form.scheduleType === 'WEEK'">
                    每{{ form.dateList }}
                  </span>
                <span v-else>
                    {{ form.dateList }}
                  </span>
              </el-form-item>
            </el-col>
            <el-col :span="5" v-if="form.planMode === 1">
              <el-form-item  label="工作班组" prop="workGroupId">
                {{ form.workGroupName }}
              </el-form-item>
            </el-col>
            <el-col :span="5" v-if="form.planMode === 1">
              <el-form-item label="处理时限" prop="handleLimitDuration">
                {{ form.handleLimitDuration }}小时
              </el-form-item>
            </el-col>
            <el-col :span="5" v-if="form.planMode === 1">
              <el-form-item label="派单时间" prop="advanceTime">
                提前{{ form.advanceTime }}小时
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="启停状态" prop="state">
                <el-tag class="ml-2" :type="form.state ? 'success' : 'danger'">{{
                    form.state ? '启用' : '停用'
                  }}
                </el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="创建人" prop="createUserName">
                {{ form.createUserName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="创建时间" prop="createTime">
                {{ form.createTime }}
              </el-form-item>
            </el-col>
            <el-col v-if="form.planMode === 2">
              <el-form-item label="巡检设备" prop="equipmentList">
                <el-tag v-for="tag in form.equipmentList" :key="tag.equipmentId" class="tag">
                  {{ tag.equipmentName }}
                </el-tag>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div v-if="form.planMode === 1" style="margin-top: 15px">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">巡检点</div>
        </div>
        <div class="detail-area">
          <el-table :data="form.checkPlanPointList">
            <el-table-column label="序号" type="index" width="60"/>
            <el-table-column v-for="(column, index) in state.columnList" :label="column.label" :prop="column.prop"
                             :key="index">
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>

const emit = defineEmits(['showPage'])

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  info: {
    type: Object,
    default: {}
  }
});
const {title, info} = toRefs(props);
const ruleFormRef = ref();
const dialog = ref();
const drawer = ref();
const form = reactive({
  scheduleType: 'MONTH',
  planType: 'MAINTENANCE',
  checkPlanPointList: [],
  dateList: [],
});

/** 校验派单时间 */
const checkAdvanceTime = (rule, value, callback) => {
  if (!value && isNaN(value)) {
    callback(new Error('不能为空'));
  } else if (isNaN(value)) {
    callback(new Error('请输入数字值'));
  } else if (value < 0) {
    callback(new Error('请输入不小于0的数字'));
  } else {
    callback();
  }
};

const state = reactive({
  workGroupList: [],
  templateList: [],
  columnList: [
    {prop: 'dataName', label: '设备'},
    {prop: 'dataSpace', label: '位置'},
    {prop: 'templateName', label: '检查模板'},
  ],
  rules: {},
  drawer: false,
});
const modal = ref();

onMounted(() => {
  Object.assign(form, props.info);
  // 处理时限，分钟转换成小时
  if (form.handleLimitDuration) {
    form.handleLimitDuration = form.handleLimitDuration / 60;
  }
  if (form.scheduleRule) {
    let scheduleRuleList = form.scheduleRule.split(',');
    let dateList = []
    scheduleRuleList.map((day) => {
      if (form.scheduleType === 'MONTH') {
        dateList.push(day + '号')
      } else if (form.scheduleType === 'WEEK') {
        dateList.push(formatWeek(day));
      } else {
        dateList.push(day);
      }
    })
    form.dateList = dateList.join('、');
  }
})

const formatWeek = (val) => {
  if (val === '1') {
    return '周一';
  } else if (val === '2') {
    return '周二';
  } else if (val === '3') {
    return '周三';
  } else if (val === '4') {
    return '周四';
  } else if (val === '5') {
    return '周五';
  } else if (val === '6') {
    return '周六';
  } else if (val === '7') {
    return '周日';
  }
};

const showPage = () => {
  emit('showPage', 0)
}
</script>

<style lang="less" scoped>
.tag {
  margin: 5px 5px 5px 0;
}
</style>
