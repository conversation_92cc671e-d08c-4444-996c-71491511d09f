package com.soft.webadmin.dto.face;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


@Data
public class FaceRepositorySaveDTO {

    @ApiModelProperty(value = "人脸id")
    @NotNull(message = "数据验证失败，人脸id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "人脸库类型，1黑名单；2白名单")
    private Integer listType;

    @ApiModelProperty(value = "姓名")
    private String username;

    @ApiModelProperty(value = "性别，1男；2女；3未知")
    private Integer sex;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "证书类型")
    private String certificateType;

    @ApiModelProperty(value = "证书号码")
    private String certificateNum;

    @ApiModelProperty(value = "人脸图片")
    private String faceUrl;

    @ApiModelProperty(value = "备注")
    private String remark;

    private String enterType;
}
