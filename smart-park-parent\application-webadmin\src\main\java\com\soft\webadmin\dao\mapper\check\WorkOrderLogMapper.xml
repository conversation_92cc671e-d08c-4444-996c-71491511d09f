<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.WorkOrderLogMapper">
    <resultMap type="com.soft.webadmin.model.check.WorkOrderLog" id="CheckOperateLogResult">
        <result property="id" column="id" />
        <result property="orderId" column="order_id" />
        <result property="operate" column="operate" />
        <result property="content" column="content" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectCheckOperateLogVo">
        select id, order_id, operate, content, deleted_flag, create_user_id, create_time, update_user_id, update_time from sp_work_order_log
    </sql>
    
</mapper>