import { request } from '@/utils/request';

// 分页查询
export const planPageAPI = (params) => {
    return request('get', '/contingency/emergency/getPage', params, 'F');
};


// 保存
export const planSaveAPI = (data) => {
    return request('post', '/contingency/emergency/saveOrUpdate', data);
};

// 删除
export const planDeteleAPI = (params) => {
    return request('post', '/contingency/emergency/delete', params, 'F');
};

// 详情
export const planDetailAPI = (params) => {
    return request('get', '/contingency/emergency/detail', params, 'F');
}