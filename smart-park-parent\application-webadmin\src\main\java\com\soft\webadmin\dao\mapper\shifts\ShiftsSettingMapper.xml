<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.shifts.ShiftsSettingMapper">
    <resultMap type="com.soft.webadmin.model.shifts.ShiftsSetting" id="ShiftsSettingResult">
        <result property="id" column="id" />
        <result property="shiftsName" column="shifts_name" />
        <result property="startTime" column="start_time" />
        <result property="endTime" column="end_time" />
        <result property="businessType" column="business_type" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectShiftsSettingVo">
        select id, shifts_name, start_time, end_time, business_type, deleted_flag, create_user_id, create_time, update_user_id, update_time from sp_shifts_setting
    </sql>
    
</mapper>