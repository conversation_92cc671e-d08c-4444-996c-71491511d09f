<template>
  <div class="card-textBg" style="height: 100%;">
    <el-card class="box-card">
      <template #header>
        <el-row justify="space-between" align="middle">
          <strong>详情</strong>
          <el-button type="primary" icon="Back" @click="onHandle">返回</el-button>
        </el-row>
      </template>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <div class="detail-area">
        <el-form label-position="top">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item label="设备名称">
                {{ detailData.equipmentName }}
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="设备编号">
                {{ detailData.equipmentCode }}
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="设备类型">
                {{ detailData.equipmentTypeName }}
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="设备型号">
                {{ detailData.equipmentModel }}
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="安装位置">
                {{ detailData.spaceFullName }}
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="管理部门">
                {{ detailData.deptName }}
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="负责人">
                {{ detailData.ownerName }}
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="生产厂商">
                {{ detailData.factory }}
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="启用时间">
                {{ detailData.activationDate }}
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="保修开始日期">
                {{ detailData.warrantyBeginDate }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="保修结束日期">
                {{ detailData.warrantyEndDate }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">生命周期</div>
      </div>

      <div class="detail-area">
        <equipmentCycle :data="detailData.lifeCycleVOList"></equipmentCycle>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import equipmentCycle from '@/views/operationManagement/workOrder/component/equipmentCycle.vue'

import {viewEquipAPI} from "@/api/operationManagement/equipManage.js";

const emit = defineEmits(['showPage'])
const activeName = ref('first')
// 设备详情
const detailData = ref({})

const state = reactive({
  equipmentId: '',
  cycleTypeOptions: {
    0: '报废',
    1: '新增设备',
    2: '设备故障',
    3: '设备修复',
    4: '设备保养',
  },
})

const loadPage = (equipmentId) => {
  state.equipmentId = equipmentId
  getDetail()
}

// 获取详情
const getDetail = () => {
  viewEquipAPI({equipmentId: state.equipmentId}).then(res => {
    detailData.value = res.data
  })
}

// 返回
const onHandle = () => {
  emit('showPage', 0)
}


defineExpose({
  loadPage
})
</script>
