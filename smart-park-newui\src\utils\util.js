import { JSEncrypt } from 'jsencrypt'

// 删除操作
export const calcPageNo = (total, pageNo = 1, pageSize = 10, delNum = 1) => {
  const restNum = total - pageSize * (pageNo - 1)
  let pageNoDiff = Math.floor((delNum - restNum) / pageSize) + 1
  pageNoDiff < 0 && (pageNoDiff = 0)
  pageNo = pageNo - pageNoDiff
  pageNo < 1 && (pageNo = 1)
  return pageNo
}

/**
 * 加密
 * @param {*} value 要加密的字符串
 */
const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCpC4QMnbTrQOFriJJCCFFWhlruBJThAEBfRk7pRx1jsAhyNVL3CqJb0tRvpnbCnJhrRAEPdgFHXv5A0RrvFp+5Cw7QoFH6O9rKB8+0H7+aVQeKITMUHf/XMXioymw6Iq4QfWd8RhdtM1KM6eGTy8aU7SO2s69Mc1LXefg/x3yw6wIDAQAB';
export function encrypt (value) {
  if (value == null || value === '') return null;
  let encrypt = new JSEncrypt();
  encrypt.setPublicKey(publicKey);
  return encodeURIComponent(encrypt.encrypt(value));
}

//字符串转数组 a,b,c
export function parseArrayByStr(str){
  if(str!=null){
    let array = []
    for(let item of str.split(",")){
      array.push(item)
    }
    return array;
  }else{
    return [];
  }
}


let imgsuffix = ['jpg', 'jpeg', 'gif', 'png']
let videosuffix = ['mp4', 'wmv', 'avi', 'mov']
/* 图片视频显示处理 */
export function pictureVideo(str){
	let imgVideoList = (str || '').split(',')

	return imgVideoList.map(item => {
		const picNamestr = item.substring(item.lastIndexOf(".") + 1)
		if(imgsuffix.includes(picNamestr)){ //图片
			return {
				type: 0,
				url: import.meta.env.VITE_BASE_URL+item,
			}
		}else if(videosuffix.includes(picNamestr)) { // 视频
			return {
				type: 1,
				url: import.meta.env.VITE_BASE_URL+item,
			}
		}
	}).filter(i => i)
}


// 递归路由
