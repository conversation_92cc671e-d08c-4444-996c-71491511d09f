package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.check.WorkGroupUserVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 工作班组成员对象 sp_work_group_user
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Data
@TableName(value = "sp_work_group_user")
public class WorkGroupUser {

    @TableId(value = "id")
    private Long id;

    /**
     * 班组id
     */
    private Long workGroupId;

    /**
     * 成员id
     */
    private Long userId;


    @Mapper
    public interface WorkGroupUserModelMapper extends BaseModelMapper<WorkGroupUserVO, WorkGroupUser> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        WorkGroupUser toModel(WorkGroupUserVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        WorkGroupUserVO fromModel(WorkGroupUser entity);
    }

    public static final WorkGroupUserModelMapper INSTANCE = Mappers.getMapper(WorkGroupUserModelMapper.class);
}
