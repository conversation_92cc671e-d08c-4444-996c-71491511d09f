package com.soft.webadmin.dto.sparePart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * SparePartStocktakingDetailDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartStocktakingDetailDTO对象")
@Data
public class SparePartStocktakingDetailDTO {

    // @ApiModelProperty(value = "主键id")
    // private Long id;

    @ApiModelProperty(value = "盘点id")
    private Long stocktakingId;

    @ApiModelProperty(value = "备件id")
    @NotNull(message = "备件id不能为空！")
    private Long sparePartId;

    @ApiModelProperty(value = "仓库id")
    private Long storehouseId;

    @ApiModelProperty(value = "库存数量")
    @NotNull(message = "库存数量不能为空！")
    @Min(value = 0, message = "库存数量不能小于0！")
    private Integer inventoryQuantity;

    @ApiModelProperty(value = "盘点数量")
    @NotNull(message = "盘点数量不能为空！")
    @Min(value = 0, message = "盘点数量不能小于0！")
    private Integer stocktakingQuantity;

    @ApiModelProperty(value = "盈亏数量")
    @NotNull(message = "盈亏数量不能为空！")
    private Integer resultQuantity;

    @ApiModelProperty(value = "盘点结果：1无盈亏、2盘盈、3盘亏")
    @NotNull(message = "盘点结果不能为空！")
    private Integer result;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "图片")
    private String imgs;

}
