import { request } from '@/utils/request';

// 分页查询
export const getCategoryAPI = (query) => {
    return request('get', '/sparePart/classify/getList', query, 'F');
};

// 树形查询
export const getCategoryTreeAPI = (query) => {
    return request('get', '/sparePart/classify/tree', query, 'F');
};

// 保存
export const saveCategoryAPI = (data) => {
    return request('post', '/sparePart/classify/saveOrUpdate', data);
};

// 删除
export const  deleteCategoryAPI = (query) => {
    return request('post', '/sparePart/classify/delete', query, 'F');
}
