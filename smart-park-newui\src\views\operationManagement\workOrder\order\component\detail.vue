<template>
  <el-card class="box-card dialogCommon commonTextarea">
    <template #header>
      <div class="card-header">
        <span style="font-weight: bold;">{{ title }}</span>
        <el-button type="primary" icon="Back" style="float: right;" @click="showPage">返回</el-button>
      </div>
    </template>
    <div class="conent">
      <el-form ref="ruleFormRef"  label-width="110px" label-position="top">
        <div>
          <el-row class="row-bg" justify="space-between">
            <div class="divFlex">
              <div class="divLeft"></div>
              <div class="divRight">工单信息</div>
            </div>
            <order-status :status="state.dataInfo.state"></order-status>
          </el-row>
          <div class="detail-area">
            <el-row>
              <el-col :span="5">
                <el-form-item label="工单编号" prop="orderNo">
                  {{ state.dataInfo.orderNo }}
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="工单类别" prop="orderType">
                  {{ state.orderTypeOptions[state.dataInfo.orderType] }}
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="开始时间" prop="startTime">
                  {{ state.dataInfo.checkRecordVO.startTime }}
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="完成时间" prop="finishTime">
                  {{ state.dataInfo.realityFinishTime }}
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="优先级" prop="priority">
                  <el-tag :type="state.dataInfo.priority === 1 ? 'primary' : (state.dataInfo.priority === 2 ? 'warning' : 'danger')">
                    {{ state.priorityOptions[state.dataInfo.priority] }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="所属计划" prop="planName">
                  {{ state.dataInfo.checkRecordVO.planName }}
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="处理时限" prop="createTime">
                  {{ state.dataInfo.handleLimitDuration/60 }}小时
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="执行人" prop="workUserName">
                  {{ state.dataInfo.workUserName }}
                </el-form-item>
              </el-col>
              <el-col :span="5"></el-col>
              <el-col :span="5"></el-col>
              <el-col :span="5"></el-col>
            </el-row>
          </div>
        </div>
        <div style="margin-top: 15px">
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">{{ state.pointTitle[state.dataInfo.orderType] }}</div>
          </div>
          <div class="detail-area">
            <el-table :data="state.dataInfo.checkRecordVO.pointVOList">
              <el-table-column label="序号" type="index" width="60" />
              <el-table-column v-for="(column, index) in state.columnList" :label="column.label" :prop="column.prop" :formatter="column.formatter" :key="index"/>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope">
                  <el-button link icon="Tickets" type="primary" @click="viewHandle(scope.row)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-form>
    </div>
    <point-item ref="modal"/>
  </el-card>
</template>

<script setup>
import { ElTag } from 'element-plus';
import pointItem from './modal/pointItem.vue';
import orderStatus from "@/views/operationManagement/workOrder/component/orderStatus.vue";
import {
  getWorkOrderDetailAPI
} from '@/api/operationManagement/workOrder.js';

import {pictureVideo} from '@/utils/util'

const emit = defineEmits(['showPage'])

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: ''
  }
});
const {title} = toRefs(props);
const ruleFormRef = ref();
const dialog = ref();

const state = reactive({
  dataInfo: {
    checkRecordVO:{}
  },
  columnList: [
    {prop: 'pointName', label: ''},
    {
      prop: 'dataSpace', label: '位置',
      formatter: (row, column, cellValue) => {
        return cellValue ? cellValue : '';
      }
    },
    {
      prop: 'state', label: '状态',
      formatter: (row, column, cellValue) => {
        if (cellValue === 0) {
          return h(ElTag, {type: "danger", size: "small"}, {default: () => '未完成'})
        } else if (cellValue === 1) {
          return h(ElTag, {type: "success", size: "small"}, {default: () => '已完成'})
        }
      }
    },
  ],
  priorityOptions: {
    1: '普通',
    2: '紧急',
    3: '特急',
  },
  orderTypeOptions: {
    'PATROL_INSPECTION': '巡检工单',
    'MAINTENANCE': '维保工单',
    'REPAIR': '维修工单',
    'CLEANING': '保洁工单',
    'CLEANING_TEMP': '临时保洁',
    'TRANSPORT_LOOP': '循环运送',
    'TRANSPORT_TEMP': '临时运送'
  },
  pointTitle:{
    "PATROL_INSPECTION" : "巡检点" ,
    "MAINTENANCE" : "维保点" ,
    "CLEANING" : "保洁点"
  }
});
const modal = ref();

onMounted(() => {
  state.id = props.id
  refreshInfo()
})

// 刷新工单信息
const refreshInfo = () => {
  getWorkOrderDetailAPI({id: state.id}).then((res) => {
    Object.assign(state.dataInfo, res.data);
    state.columnList[0].label = state.pointTitle[res.data.orderType]
  });
};

const showPage = () => {
  emit('showPage', 0)
}

// 查看检查项
const viewHandle = (row) => {
  modal.value.open();
  nextTick(() => {
    modal.value.form.itemList = row.itemVOList;
    modal.value.form.pointId = row.id;
    modal.value.form.remark = row.remark
    modal.value.form.fileList = pictureVideo(row.img)
  });
};
</script>

<style lang="less" scoped>

:deep(.el-form-item__label) {
  color: #73767a;
}

.tag {
  margin: 5px 5px 5px 0;
}
</style>
