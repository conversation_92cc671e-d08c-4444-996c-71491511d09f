<template>
  <dialog-common ref="dialog" title="工单处理" @submit="submit" @onClose="onclose" :formRef="ruleFormRef" :width="state.dialogWidth">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <div>
        <el-form-item label="过程描述" prop="describe">
          <el-input v-model="form.describe" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit placeholder="请输入过程描述"/>
        </el-form-item>
        <el-form-item label="上传附件">
          <el-upload
              v-model:file-list="state.fileList"
              list-type="picture-card"
              :on-preview="handleImgPreview"
              :http-request="httpRequest"
              :on-success="(response) => { fileSuccess(response, state.fileList) }"
              :limit="3">
            <el-icon>
              <Plus />
            </el-icon>
            <template #tip>
              <div class="el-upload__tip">
                支持格式：jpg、png ，单个文件不能超过5MB，最多支持3张
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </div>

    </el-form>
  </dialog-common>

  <el-dialog v-model="state.dialogVisible" id="imgDialog">
    <img w-full :src="state.dialogImageUrl" alt="Preview Image" style="width: 100%;" />
  </el-dialog>
</template>

<script setup>
import { workHandleTAPI } from '@/api/operationManagement/workOrder.js';
import { ElInput, ElMessage } from 'element-plus';
import { annexUpload } from '@/api/file.js';

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  dialogWidth: 1000,
  rules: {
    describe: [{required: true, message: '请输入过程描述', trigger: 'blur'},],
  },
  fileList: [],
  dialogVisible: false,
  dialogImageUrl: ''
});

// 覆盖Http
const httpRequest = (option) => {
  const formData = new FormData()
  formData.append('file', option.file)
  return annexUpload(formData)
}

// 查看图片
const handleImgPreview = (uploadFile) => {
  state.dialogImageUrl = uploadFile.url
  state.dialogVisible = true
}

// 上传图片
const fileSuccess = (response, fileList) => {
  fileList[fileList.length - 1].filePath = response.data.filePath
}


// 提交表单
const submit = () => {
  form.img = (state.fileList || []).map(img => img.filePath).join(',');
  workHandleTAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success('操作成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

const open = () => {
  dialog.value.open();
};

const onclose = () => {
  delete form.id
  state.fileList = []
}

defineExpose({
  form,
  open,
});
</script>
<style lang="less" scoped>
.treatmentPlan {
  margin-left: 30px;
  margin-bottom: 10px;
  width: 85%;
  height: 90px;
  background-color: #FAFAFA;
  border-radius: 5px;
  padding: 10px;
  line-height: 22px;
  color: #73767a;
}
.title {
  font-size: 14px;
  font-weight: bold;
}
</style>
