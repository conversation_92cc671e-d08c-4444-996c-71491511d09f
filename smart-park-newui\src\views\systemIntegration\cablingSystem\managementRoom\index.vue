<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form inline :model="state.queryForm" ref="queryFormRef">
        <el-form-item prop="spaceIds">
          <el-cascader
            clearable
            placeholder="所在位置"
            v-model="state.queryForm.spaceIds"
            :options="state.spaceOptions"
            :props="{
              checkStrictly: true,
              expandTrigger: 'blur',
              value: 'id',
              label: 'name',
            }"
          ></el-cascader>
        </el-form-item>
        <el-form-item prop="name">
          <el-input v-model="state.queryForm.name" placeholder="管理间名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="queryList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #operate>
      <div class="button">
        <el-button type="primary" icon="Plus" @click="onAdd">新建管理间</el-button>
        <el-popover v-model:visible="showPopover" placement="bottom" :width="180" trigger="click" @hide="hiddenPopover">
          <template #reference>
            <el-button type="primary" icon="Upload">导入</el-button>
          </template>
          <template #default>
            <el-upload
              ref="uploadRef"
             :http-request="onUploadFile"
            >
              <template #trigger>
                <el-button size="small" type="primary">上传数据</el-button>
              </template>
              <template #default>
                <el-button
                  type="primary"
                  size="small"
                  style="margin-left: 10px"
                  @click="downTemplate"
                >下载模板</el-button>
              </template>
              <template #tip>
                <div class="el-upload__tip" align="center">
                  只能上传一个xlsx文件
                </div>
              </template>
            </el-upload>
          </template>
        </el-popover>

        <el-button type="primary" icon="Download" @click="onExport">导出</el-button>
      </div>
    </template>

    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" tooltip-effect="dark">
        <el-table-column
          :key="index"
          :prop="item.prop"
          :label="item.label"
          show-overflow-tooltip
          v-for="(item, index) in state.tableHeader"
        >
          <template #default="scope">
            {{ scope.row[item.prop] ? scope.row[item.prop] : "/" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click.prevent="onEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.prevent="onDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="state.pageParam.pageNum"
        :page-sizes="state.pageSizes"
        :page-size="state.pageParam.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="state.pageParam.total"
      >
      </el-pagination>

      <room-dialog ref="roomDialogRef" :space-options="state.spaceOptions" @onClose="queryList"/>
    </template>
  </page-common>
</template>

<script setup>


import PageCommon from "@/components/basic/pageCommon.vue";
import {treeAPI} from "@/api/iotManagement/space.js";
import {Refresh, Search} from "@element-plus/icons-vue";
import {
  deleteManagementRoomAPI,
  listManagementRoomAPI
} from "@/api/systemIntegration/cablingSystem/managementRoom.js";
import RoomDialog from "@/views/systemIntegration/cablingSystem/managementRoom/component/roomDialog.vue";
import {ElMessage, ElMessageBox} from "element-plus";
import {exportFile, uploadFile} from "@/utils/down.js";

// 子组件
const roomDialogRef = ref()


// 气泡框组件
const showPopover = ref(false)

// 上传组件
const uploadRef = ref()


// 查询表单
let queryFormRef = ref()

const state = reactive({
  tableHeight: 100,
  // 查询表单
  queryForm: {
    spaceIds: null,
    name: null,
  },
  // 空间下拉选项
  spaceOptions: [],
  // 表格头
  tableHeader: [
    { label: "管理间名称", prop: "name" },
    { label: "所属位置", prop: "spaceFullName" },
    { label: "描述", prop: "remarks" },
    { label: "创建时间", prop: "createTime" },
  ],
  tableData: [],
  pageSizes: [10, 20, 30, 50],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})



const queryList = () => {
  let param = assembleQueryParam()
  listManagementRoomAPI(param).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList
      state.pageParam.total = res.data.totalCount
    }
  })
}


// 重置
const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 12
  queryList()
}


// 组装请求参数
const assembleQueryParam = () => {
  let param = {
    name: state.queryForm.name,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  if (state.queryForm.spaceIds) {
    param.spacePath = state.queryForm.spaceIds.join(',')
  }
  return param;
}

const handleSizeChange = (val) => {
  state.pageParam.pageSize = val
  queryList()
}

const handleCurrentChange = (val) => {
  state.pageParam.pageNum = val
  queryList()
}

/**
 * 查询空间下拉选项列表
 * @returns {Promise<unknown>}
 */
const querySpaces = () => {
  return new Promise((resolve, reject) => {
    nextTick(() => {
      let query = {
        deep: 4
      }
      treeAPI(query).then(res => {
        state.spaceOptions = res.data
        resolve()
      })
    })
  })
}


const onAdd = () => {
  roomDialogRef.value.open('新建管理间')
}


const onEdit = (val) => {
  roomDialogRef.value.open('新建管理间', val)
}

// 删除管理间
const onDelete = (val) => {
  ElMessageBox.confirm(
    '是否删除当前管理间?',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    let params = {
      id: val.id
    }
    deleteManagementRoomAPI(params).then(res => {
      if (res.success) {
        ElMessage.success('删除成功！')
      } else {
        ElMessage.error('删除失败！' + res.errorMessage)
      }
      queryList()
    });
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除！',
    })
  })
}


/**
 * 导出数据
 */
const onExport = async () => {
  let param = {
    template: false
  }
  if (state.queryForm.spaceIds) {
    param.spacePath = state.queryForm.spaceIds.join(',')
  }
  if (state.queryForm.name) {
    param.name = state.queryForm.name
  }
  await exportFile('/cablingSystem/management-room/export', param, '管理间数据.xlsx')
}

// 下载模板文件
const downTemplate = async () => {
  await exportFile('/cablingSystem/management-room/export', null, '管理间模板.xlsx')
}

// 上传文件
const onUploadFile = async (fileData) => {
  let { data: res} = await uploadFile('/cablingSystem/management-room/import', fileData.file);
  if (res.success) {
    ElMessage.success('上传成功！');

    // 2s 后关闭气泡框
    setTimeout(() => {
      showPopover.value = false
    }, 2000)
  } else {
    ElMessage.error("上传 " + res.errorMessage)
  }
  queryList()
}


// 隐藏气泡框事件
const hiddenPopover = () => {
  uploadRef.value.clearFiles()
}


onMounted(() => {
  querySpaces()
  queryList()
})
</script>


<style lang="less" scoped>

</style>
