import { request } from "@/utils/request";

// 分页查询
export const getPageAPI = (data) => {
    return request('get','/system/threeParty/robot/page',data, 'F')
}

// 创建机器人配置
export const addThreePartyRobotAPI = (data) => {
    return request('post','/system/threeParty/robot/add',data)
}

// 更新机器人配置
export const updateThreePartyRobotAPI = (data) => {
    return request('post','/system/threeParty/robot/update',data)
}

// 删除机器人配置
export const deleteThreePartyRobotAPI = (data) => {
    return request('post','/system/threeParty/robot/delete',data)
}

