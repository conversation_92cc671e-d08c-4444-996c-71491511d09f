package com.soft.webadmin.dao.check;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.admin.upms.vo.SysUserVo;
import com.soft.webadmin.model.check.WorkGroupUser;

import java.util.List;

/**
 * 工作班组成员Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public interface WorkGroupUserMapper extends BaseMapper<WorkGroupUser> {

    /**
     * 批量新增
     *
     * @param workGroupUsers
     */
    void insertBatch(List<WorkGroupUser> workGroupUsers);

    List<SysUserVo> queryList();
}
