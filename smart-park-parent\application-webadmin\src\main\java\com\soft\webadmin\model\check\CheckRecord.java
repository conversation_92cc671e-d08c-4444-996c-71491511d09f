package com.soft.webadmin.model.check;

import java.util.Date;
import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.*;
import com.soft.webadmin.vo.check.CheckRecordVO;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * 【请填写功能名称】对象 sp_check_record
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_check_record")
public class CheckRecord extends BaseModel {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 计划id */
    private Long planId;

    /** 计划名称 */
    private String planName;

    /** 工作班组id */
    private Long workGroupId;

    /** 检查人id */
    private Long checkUserId;

    /** 开始时间 */
    private Date startTime;

    /** 完成时间 */
    private Date finishTime;

    /** 删除标记(1: 正常 -1: 已删除) */
    @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;


    @Mapper
    public interface CheckRecordModelMapper extends BaseModelMapper<CheckRecordVO, CheckRecord> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        CheckRecord toModel(CheckRecordVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        CheckRecordVO fromModel(CheckRecord entity);
    }

    public static final CheckRecordModelMapper INSTANCE = Mappers.getMapper(CheckRecordModelMapper.class);
}
