package com.soft.webadmin.dto.sparePart;

import com.soft.webadmin.enums.BusinessTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * SparePartStorehouseDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartStorehouseDTO对象")
@Data
public class SparePartStorehouseDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房", required = true)
    private BusinessTypeEnums businessType = BusinessTypeEnums.OPERATIONS;

    @ApiModelProperty(value = "仓库名称")
    @NotBlank(message = "仓库名称不能为空！")
    private String storehouseName;

    // @ApiModelProperty(value = "仓库编号")
    // @NotBlank(message = "仓库编号能为空！")
    // private String storehouseNo;

    @ApiModelProperty(value = "容量")
    private Long capacity;

    @ApiModelProperty(value = "负责人id")
    private Long headUserId;

    @ApiModelProperty(value = "位置（空间点位）")
    @NotNull(message = "位置不能为空！")
    private Long spaceId;

    @ApiModelProperty(value = "状态（0锁定，1启用）", hidden = true)
    private Integer state = 1;

    @ApiModelProperty(value = "备注")
    private String remark;

}
