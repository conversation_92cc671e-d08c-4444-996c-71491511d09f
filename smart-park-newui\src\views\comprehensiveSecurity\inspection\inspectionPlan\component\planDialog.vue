<template>
  <el-dialog v-model="showDialog" :title="state.title" width="40%" :before-close="close" class="dialogCommon"
             align-center>
    <template #default>
      <el-steps :active="state.active" :process-status="'finish'" align-center>
        <el-step title="基本信息"></el-step>
        <el-step title="巡更路线"></el-step>
        <el-step v-if="state.isModify" title="保存"></el-step>
      </el-steps>
      <div v-show="state.active === 0" class="content">
        <el-form :model="state.dataForm" :rules="state.baseInfoFormRules" ref="baseInfoFormRef" label-width="160"
                 label-suffix=":" :disabled="!state.isModify">
          <el-form-item label="计划名称" prop="name">
            <el-input v-model="state.dataForm.name" placeholder="计划名称"/>
          </el-form-item>
          <el-form-item label="计划日期" prop="planDate">
            <el-date-picker
              v-model="state.dataForm.planDate"
              type="daterange"
              range-separator="-"
              start-placeholder="计划开始日期"
              end-placeholder="计划结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="巡更时间" prop="inspectionTime">
            <el-time-picker
              v-model="state.dataForm.inspectionTime"
              is-range
              clearable
              range-separator="-"
              start-placeholder="巡更开始时间"
              end-placeholder="巡更结束时间"
              format="HH:mm"
            />
          </el-form-item>
          <el-form-item label="巡更人员" prop="inspectionUserIds">
            <el-select
              v-model="state.dataForm.inspectionUserIds"
              multiple
              clearable
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="4"
              placeholder="巡更人员"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.userId"
                :label="user.showName"
                :value="user.userId"
              />
            </el-select>
          </el-form-item>
          <div class="workDayBox">
            <el-form-item label-width="0" prop="workDays">
              <el-checkbox-group
                v-model="state.dataForm.workDays">
                <el-checkbox v-for="workDay in workDayOptions" :key="workDay.value" :label="workDay.value">{{
                    workDay.label
                  }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div v-show="state.active === 1" class="lineTableBox">
        <el-form :model="state.dataForm" ref="lineFormRef" :rules="state.lineFormRules" :disabled="!state.isModify">
          <el-form-item prop="lineId">
            <el-input v-model="state.dataForm.lineId" v-show="false" />
            <el-table :data="state.lineData" tooltip-effect="light">
              <el-table-column v-for="item of state.lineTableHeader" :label="item.label" :prop="item.prop" align="center" show-overflow-tooltip="true">
              </el-table-column>
              <el-table-column label="操作" align="center">
                <template #default="scope">
                  <el-button link type="primary" @click="onSelectLineId(scope.row)" v-show="scope.row.id !== state.dataForm.lineId">选择</el-button>
                  <el-button link type="success" :icon="Check" v-show="scope.row.id === state.dataForm.lineId" :disabled="true">选择</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
      <div v-show="state.active === 2 && state.isModify" class="lineTableBox">
        <span>是否保存巡更计划？</span>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button v-show="state.active !== 0 && state.active <= 2" type="primary" @click="prev">上一步</el-button>
        <el-button v-show="state.active === 0 || (state.active === 1 && state.isModify)" type="primary" @click="next">下一步</el-button>
        <el-button v-show="state.active === 2 && state.isModify" type="primary" @click="onSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import {getPageAPI} from "@/api/settingSystem/user.js";
import {listInspectionLineAPI} from "@/api/comprehensiveSecurity/inspectionLine.js";
import {Check} from "@element-plus/icons-vue";
import {useDateFormat} from "@vueuse/core";
import {saveOrUpdateInspectionPlanAPI} from "@/api/comprehensiveSecurity/inspectionPlan.js";
import {ElMessage} from "element-plus";


// 声明父组件
const emits = defineEmits(['onClose'])

// dialog组件
const showDialog = ref(false)


// 表单对象
const baseInfoFormRef = ref()
const lineFormRef = ref()

// 用户列表
const userOptions = ref()

const state = reactive({
  title: '',
  isModify: true,
  active: 0,
  // 表单数据
  dataForm: {},
  // 基本信息表单规则
  baseInfoFormRules: {
    name: [
      { required: true, message: '计划名称不能为空', trigger: 'blur' }
    ],
    planDate: [
      { required: true, message: '计划时间不能为空', trigger: 'blur' }
    ],
    inspectionTime: [
      { required: true, message: '巡更时间不能为空', trigger: 'blur' }
    ],
    inspectionUserIds: [
      { required: true, message: '巡更人员不能为空', trigger: 'blur' }
    ],
    workDays: [
      { required: true, message: '巡更工作日不能为空', trigger: 'blur' }
    ]
  },
  // 巡更路线表单规则
  lineFormRules: {
    lineId: [
      { required: true, message: '巡更路线不能为空', trigger: 'blur' }
    ]
  },
  // 巡更路线列表
  lineData: [],
  // 巡更路线表格
  lineTableHeader: [
    { label: '路线名称', prop: 'name' },
    { label: '巡更点数量', prop: 'count' },
    { label: '巡更点位', prop: 'pointNames' }
  ]
})


// 工作日属性
const workDayOptions = [
  {label: '星期一', value: '1'},
  {label: '星期二', value: '2'},
  {label: '星期三', value: '3'},
  {label: '星期四', value: '4'},
  {label: '星期五', value: '5'},
  {label: '星期六', value: '6'},
  {label: '星期天', value: '7'}
]


// 查询用户列表
const queryUsers = () => {
  let data = {
    sysUserDtoFilter: {
      userStatus: 0
    }
  }
  getPageAPI(data).then(res => {
    if (res.success) {
      userOptions.value = res.data.dataList
    }
  })
}


// 查询巡更路线列表数据
const queryLineList = () => {
  listInspectionLineAPI({}).then(res => {
    if (res.success) {
      state.lineData = res.data.dataList
    }
  })
}


// 选择巡更路线
const onSelectLineId = (val) => {
  state.dataForm.lineId = val.id
}


// 保存巡更计划
const onSubmit = () => {
  let param = {
    id: state.dataForm.id,
    name: state.dataForm.name,
    lineId: state.dataForm.lineId,
    inspectionUserIds: state.dataForm.inspectionUserIds
  }
  if (state.dataForm.planDate) {
    param.planBeginDate = state.dataForm.planDate[0]
    param.planEndDate = state.dataForm.planDate[1]
  }
  if (state.dataForm.inspectionTime) {
    param.inspectionBeginTime = useDateFormat(state.dataForm.inspectionTime[0], 'HH:mm').value
    param.inspectionEndTime = useDateFormat(state.dataForm.inspectionTime[1], 'HH:mm').value
  }
  if (state.dataForm.workDays) {
    // 排序，因为显示的时候需要顺序显示
    state.dataForm.workDays.sort()
    param.planCron = state.dataForm.workDays.join(',')
  }
  saveOrUpdateInspectionPlanAPI(param).then(res => {
    if (res.success) {
      ElMessage.success('保存成功！');
      close()
    } else {
      ElMessage.error('保存失败，' + res.errorMessage)
    }
  })
}


// 下一步
const next = () => {
  if (state.active === 0) {
    baseInfoFormRef.value.validate((valid, fields) => {
      if (valid) {
        if (state.active++ > 2)
          state.active = 0
      }
    })
  } else if (state.active === 1) {
    lineFormRef.value.validate((valid, fields) => {
      if (valid) {
        if (state.active++ > 2)
          state.active = 0
      }
    })
  } else {
    if (state.active++ > 2)
      state.active = 0
  }
}

// 上一步
const prev = () => {
  if (state.active > 0) state.active--
}

const open = (title, val) => {
  showDialog.value = true
  nextTick(() => {
    state.title = title
    if (state.title.startsWith('查看')) {
      state.isModify = false
    }
    if (val) {
      let data = JSON.parse(JSON.stringify(val));
      state.dataForm.id = data.id
      state.dataForm.name = data.name
      state.dataForm.planDate = [data.planBeginDate, data.planEndDate]
      let nowDate = new Date();
      let inspectionBeginTime = new Date(nowDate.getFullYear() + '-' + nowDate.getMonth() + '-' + nowDate.getDay() + ' ' + data.inspectionBeginTime)
      let inspectionEndTime = new Date(nowDate.getFullYear() + '-' + nowDate.getMonth() + '-' + nowDate.getDay() + ' ' + data.inspectionEndTime)
      state.dataForm.inspectionTime = [inspectionBeginTime, inspectionEndTime]
      state.dataForm.inspectionUserIds = data.inspectionUserIds.split(',')
      state.dataForm.workDays = data.planCron.split(',')
      state.dataForm.lineId = data.lineId
    }

    queryUsers()
    queryLineList()
  })
}

const close = () => {
  showDialog.value = false
  state.active = 0
  state.isModify = true
  // state.dataForm = {}

  baseInfoFormRef.value.resetFields()
  lineFormRef.value.resetFields()

  baseInfoFormRef.value.clearValidate()
  lineFormRef.value.clearValidate()
  emits('onClose')
}

// 向外暴漏的对象
defineExpose({
  open
})
</script>

<style scoped lang="less">
.content {
  margin-top: 10px;

  .el-input {
    width: 80%;
  }

  .el-select, .el-cascader {
    width: 80%;
  }

  .el-input-number {
    width: 80%;

    .el-input {
      width: 100%;
    }
  }

  .el-textarea {
    width: 80%;
  }

  :deep(.el-date-editor) {
    width: 80%;
    flex-grow: 0;
  }
}


.workDayBox {
  border: #88c4fc solid 1px;
  border-radius: 5px;
  margin-top: 20px;
  margin-left: 88px;
  margin-right: 15%;
  padding-left: 40px;
  padding-top: 20px;
  position: relative;
}

.workDayBox::after {
  content: '巡更工作日';
  position: absolute;
  left: 20px;
  top: -10px;
  width: 90px;
  text-align: center;
  font-size: 14px;
  background-color: white;
}

.lineTableBox {
  margin-left: 88px;
  margin-top: 10px;
  margin-right: 15%;
}
</style>
