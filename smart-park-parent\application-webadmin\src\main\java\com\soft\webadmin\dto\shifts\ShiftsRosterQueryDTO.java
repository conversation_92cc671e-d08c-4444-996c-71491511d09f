package com.soft.webadmin.dto.shifts;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * ShiftsRosterDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsRosterDTO对象")
@Data
public class ShiftsRosterQueryDTO extends MyPageParam {

    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private String businessType;

    @ApiModelProperty(value = "用户显示名称")
    private String showName;

    @ApiModelProperty(value = "用户所在部门Id")
    private Long deptId;

    @ApiModelProperty(value = "职位状态:1在职 0离职")
    private Integer postStatus;

    @ApiModelProperty(value = "用户所在岗位Id")
    private Long postId;

    private List<Long> rosterIds;
}
