<template>
  <dialog-common ref="dialog" title="添加排班" @submit="submit" :formRef="ruleFormRef" :width="450">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-form-item label="班次" prop="shiftsSettingId">
        <el-select v-model="form.shiftsSettingId" placeholder="请选择班次" clearable filterable>
          <el-option v-for="item in state.shiftsSettingOption" :label="`${item.shiftsName} ${item.startTime}-${item.endTime}` " :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="人员" prop="rosterId">
        <el-select v-model="form.rosterId" placeholder="请选择人员" clearable filterable>
          <el-option v-for="item in state.rosterOption" :label="item.showName + (item.deptName ? ' - ' + item.deptName : '')" :value="item.rosterId"/>
        </el-select>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {ElMessage} from "element-plus";

import { settingPageAPI } from '@/api/operationManagement/workforceSetting.js'
import { rosterPageAPI } from '@/api/operationManagement/roster.js'

import { workManageAddAPI } from '@/api/operationManagement/workforceManage.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  attendanceDate: {
    type: String,
    default: ''
  },
  liabilityArea: {
    type: String,
    default: ''
  },
})

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  shiftsSettingOption: [],  // 班次
  rosterOption: [], // 人员
  rules: {
    shiftsSettingId: [{required: true, message: '请选择班次'}],
    rosterId: [{required: true, message: '请选择人员'}]
  },
});

onMounted(() => {
  getshiftsSettingOption()
  getrosterOption()
})

// 获取班次
const getshiftsSettingOption = () => {
  settingPageAPI({
    businessType:'OPERATIONS'
  }).then(res => {
    state.shiftsSettingOption = res.data.dataList
  })
}

// 获取人员
const getrosterOption = () => {
  rosterPageAPI({
    businessType:'OPERATIONS',
    postStatus: 1
  }).then(res => {
    state.rosterOption = res.data.dataList
  })
}

// 提交表单
const submit = () => {
  workManageAddAPI({
    attendanceDate: props.attendanceDate,
    businessType: 'OPERATIONS',
    liabilityArea: props.liabilityArea,
    ...form,
  }).then(res => {
    if (res.success) {
      ElMessage.success(form.id ? '保存成功' : '新增成功');
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = () => {
  dialog.value.open();
}

defineExpose({
  form,
  open,
});
</script>
