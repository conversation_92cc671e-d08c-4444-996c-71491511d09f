package com.soft.webadmin.controller.hiddenDanger;

import cn.hutool.core.collection.CollUtil;
import com.soft.common.core.annotation.MyRequestBody;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.object.TokenData;
import com.soft.webadmin.dto.hiddenDanger.*;
import com.soft.webadmin.enums.HiddenDangerStatusEnums;
import com.soft.webadmin.model.hiddenDanger.HiddenDanger;
import com.soft.webadmin.service.hiddenDanger.HiddenDangerService;
import com.soft.webadmin.vo.hiddenDanger.HiddenDangerRectifyVO;
import com.soft.webadmin.vo.hiddenDanger.HiddenDangerVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 隐患控制器类
 * 
 * <AUTHOR>
 * @date 2024-07-12
 */
@Api(tags = "隐患管理")
@RestController
@RequestMapping("/hiddenDanger")
public class HiddenDangerController {

    @Autowired
    private HiddenDangerService hiddenDangerService;

    @ApiOperation(value = "上报")
    @PostMapping("/save")
    public ResponseResult<Void> save(@MyRequestBody HiddenDangerDTO dangerDTO,
                                     @MyRequestBody HiddenDangerRectifyDTO rectifyDTO) {
        return hiddenDangerService.save(dangerDTO, rectifyDTO);
    }

    @ApiOperation(value = "委派")
    @PostMapping("/appoint")
    public ResponseResult<Void> appoint(@RequestBody @Validated HiddenDangerAppointDTO appointDTO) {
        return hiddenDangerService.appoint(appointDTO);
    }

    @ApiOperation(value = "处理--处理人")
    @PostMapping("/handle")
    public ResponseResult<Void> handle(@RequestBody @Validated HiddenDangerRectifyDTO rectifyDTO) {
        HiddenDanger oriHiddenDanger = hiddenDangerService.getById(rectifyDTO.getHiddenDangerId());
        if (oriHiddenDanger == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, "隐患信息不存在！");
        }
        if (oriHiddenDanger.getStatus() != HiddenDangerStatusEnums.HANDING.getValue()) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, "隐患当前的状态不支持处理！");
        }
        if (!Objects.equals(oriHiddenDanger.getHandleUserId(), TokenData.takeFromRequest().getUserId())) {
            // 当前用户非处理人
            return ResponseResult.error(ErrorCodeEnum.NO_ACCESS_PERMISSION);
        }
        return hiddenDangerService.handle(rectifyDTO);
    }

    @ApiOperation(value = "处理--安全检查员")
    @PostMapping("/handle2")
    public ResponseResult<Void> handle2(@RequestBody @Validated HiddenDangerRectifyDTO rectifyDTO) {
        HiddenDanger oriHiddenDanger = hiddenDangerService.getById(rectifyDTO.getHiddenDangerId());
        if (oriHiddenDanger == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, "隐患信息不存在！");
        }
        if (oriHiddenDanger.getStatus() != HiddenDangerStatusEnums.NO_INSPECTION.getValue()) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, "隐患当前的状态不支持处理！");
        }
        boolean b = hiddenDangerService.checkUserPost();
        if (!b) {
            return ResponseResult.error(ErrorCodeEnum.NO_ACCESS_PERMISSION);
        }
        rectifyDTO.setStatus(HiddenDangerStatusEnums.FINISH.getValue());
        rectifyDTO.setInspectionUserId(TokenData.takeFromRequest().getUserId());
        return hiddenDangerService.handle(rectifyDTO);
    }

    @ApiOperation(value = "结束")
    @PostMapping("/end")
    public ResponseResult<Void> end(@RequestBody @Validated HiddenDangerEndDTO endDTO) {
        return hiddenDangerService.end(endDTO);
    }

    @ApiOperation(value = "退回")
    @PostMapping("/refund")
    public ResponseResult<Void> refund(@RequestBody @Validated HiddenDangerReturnDTO returnDTO) {
        return hiddenDangerService.refund(returnDTO);
    }

    @ApiOperation(value = "审核")
    @PostMapping("/examine")
    public ResponseResult<Void> examine(@RequestBody @Validated HiddenDangerExamineDTO examineDTO) {
        return hiddenDangerService.examine(examineDTO);
    }

    @ApiOperation(value = "列表查询，移动端")
    @GetMapping("/getAppPage")
    public ResponseResult<MyPageData<HiddenDangerVO>> getAppPage(HiddenDangerQueryDTO queryDTO) {
        return ResponseResult.success(hiddenDangerService.getAppPage(queryDTO));
    }

    @ApiOperation(value = "列表查询，PC端")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<HiddenDangerVO>> getPage(HiddenDangerQueryDTO queryDTO) {
        return ResponseResult.success(hiddenDangerService.getPage(queryDTO));
    }

    @ApiOperation(value = "查询历次整改记录")
    @GetMapping("/rectify/list")
    public ResponseResult<List<HiddenDangerRectifyVO>> getRectifyList(@RequestParam Long id) {
        return ResponseResult.success(hiddenDangerService.getRectifyList(id));
    }

    @ApiOperation(value = "查询最后一次整改记录")
    @GetMapping("/rectify/last")
    public ResponseResult<HiddenDangerRectifyVO> getLastRectify(@RequestParam Long id) {
        List<HiddenDangerRectifyVO> rectifyList = hiddenDangerService.getRectifyList(id);
        if (CollUtil.isNotEmpty(rectifyList)) {
            return ResponseResult.success(rectifyList.get(0));
        } else {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
    }

    @ApiOperation(value = "我上报的隐患记录")
    @GetMapping("/getMyPage")
    public ResponseResult<MyPageData<HiddenDangerVO>> getMyPage() {
        HiddenDangerQueryDTO queryDTO = new HiddenDangerQueryDTO();
        queryDTO.setCreateUserId(TokenData.takeFromRequest().getUserId());
        return ResponseResult.success(hiddenDangerService.getPage(queryDTO));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public ResponseResult<HiddenDangerVO> detail(@RequestParam Long id) {
        HiddenDangerVO hiddenDangerVO = hiddenDangerService.detail(id);
        if (hiddenDangerVO == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success(hiddenDangerVO);
    }

}
