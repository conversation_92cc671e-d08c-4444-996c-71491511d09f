<template>
  <page-common>
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="name">
          <el-input v-model="formInline.name" placeholder="配置名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" :icon="Plus" @click="addHandle">新建告警配置</el-button>
    </template>
    <template #table>
      <div style="height: calc(100% - 50px); overflow: auto">
        <el-row v-for="item in state.tableData" :key="item.id">
          <el-col>
            <el-card shadow="hover">
              <el-descriptions :column="8" direction="vertical">
                <el-descriptions-item width="160">
                  <template #label>
                    <div class="cell-item">
                      <el-icon>
                        <Clock />
                      </el-icon>
                      &nbsp;创建时间
                    </div>
                  </template>
                  {{ item.createTime }}
                </el-descriptions-item>
                <el-descriptions-item width="150">
                  <template #label>
                    <div class="cell-item">
                      <el-icon>
                        <User />
                      </el-icon>
                      &nbsp;创建人
                    </div>
                  </template>
                  {{ item.createUser }}
                </el-descriptions-item>
                <el-descriptions-item label="配置名称" width="180">{{ item.name }}</el-descriptions-item>
                <el-descriptions-item label="常开时间" width="160">
                  <span style="color: #f56c6c">{{ item.ruleDesc }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="状态" width="120">
                  <el-tag :type="item.status == 0 ? 'danger' : 'success'">{{ item.status == 0 ? '停用' : '启用' }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="紧要程度" width="120">{{ item.level }}</el-descriptions-item>
                <el-descriptions-item label="关联设备">
                  <template v-for="(rel, index) in item.relationEquipmentList" :key="index">
                    {{ rel.equipmentName }}
                    <span v-if="index != item.relationEquipmentList.length - 1">、</span>
                  </template>
                </el-descriptions-item>
                <el-descriptions-item label="" width="180">
                  <div style="margin-left: 20px">
                    <el-tooltip content="编辑" placement="top" effect="light">
                      <el-button type="primary" :icon="Edit" size="small" circle @click="editHandle(item)" />
                    </el-tooltip>
                    <el-tooltip content="删除" placement="top" effect="light">
                      <el-button type="danger" :icon="Delete" size="small" circle @click="deleteHandle(item)" />
                    </el-tooltip>
                    <el-tooltip content="查看详情" placement="top" effect="light">
                      <el-button type="success" :icon="Document" size="small" circle @click="viewHandle(item)" />
                    </el-tooltip>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
      <!-- 设备详情 -->
      <device-drawer ref="drawer" :drawer="state.equipmentDrawer" :deviceId="state.equipmentId" @cancelClick="state.equipmentDrawer = false" />
      <!-- 告警配置详情 -->
      <warning-setting-info ref="infoModel" :drawer="state.drawer" @cancelClick="cancelClick" />
      <!-- 新建、编辑告警配置Dialog -->
      <edit-warning-setting ref="editModel" :title="state.title" @submit="getList" />
    </template>
  </page-common>
</template>

<script setup>
import { getPageAPI, deleteWarningSettingAPI } from '@/api/iotManagement/warningSetting.js';
import { Edit, Delete, Plus, View, Search, Refresh, Document } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import editWarningSetting from './editWarningSetting.vue';
import warningSettingInfo from './warningSettingInfo.vue';

const drawer = ref();
const infoModel = ref();
const editModel = ref();
const formInlineRef = ref();
const formInline = reactive({
  name: '',
});
const state = reactive({
  tableData: [],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  equipmentId: undefined,
  equipmentDrawer: false,
  drawer: false,
  title: '',
});

onMounted(() => {
  getList();
});

const getList = () => {
  let params = {
    subType: 'ENTRANCE_GUARD',
    name: formInline.name,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };
  getPageAPI(params).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
    state.tableData.map((item) => {
      if (item.ruleJson) {
        const json = JSON.parse(item.ruleJson);
        item.rule = json.rule;
        item.ruleValue = json.value;
        item.ruleDesc = json.rule + json.value + '分钟';
      }
    });
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

/** 查看设备详情 */
const viewEquipment = (equipmentId) => {
  state.drawer = true;
  state.equipmentId = equipmentId;
  drawer.value.state.deviceId = equipmentId + ''
  drawer.value.loadEquipmentInfo(equipmentId);
};

/** 新建告警配置 */
const addHandle = () => {
  state.title = '新建告警配置';
  editModel.value.form.id = undefined;
  editModel.value.form.level = '重要';
  editModel.value.form.rule = '大于';
  editModel.value.form.ruleValue = 5;
  editModel.value.open();
};

/** 编辑告警规则 */
const editHandle = (row) => {
  state.title = '编辑告警规则';
  editModel.value.open();
  nextTick(() => {
    Object.assign(editModel.value.form, { ...row });
    editModel.value.form.equipmentList = JSON.parse(JSON.stringify(row.relationEquipmentList));
  });
};

/** 查看详情 */
const viewHandle = (row) => {
  nextTick(() => {
    infoModel.value.data = JSON.parse(JSON.stringify(row));
    state.drawer = true;
  });
};

/** 关闭抽屉 */
const cancelClick = () => {
  state.drawer = false;
};

/** 删除 */
const deleteHandle = (row) => {
  if (row.status === 1) {
    ElMessageBox.alert('启用状态的告警配置不可以删除！', '提醒', {
      type: 'warning',
    });
  } else {
    ElMessageBox.confirm('是否删除当前告警配置?', '提醒', {
      type: 'warning',
    }).then(() => {
      deleteWarningSettingAPI({ id: row.id }).then((res) => {
        if (res.success) {
          ElMessage.success('删除成功');
          getList();
        } else {
          ElMessage.error(res.errorMessage);
        }
      });
    });
  }
};
</script>

<style scoped lang="less">
.el-row {
  margin-bottom: 20px;
}
:deep(.el-card__body) {
  padding: 18px 15px 8px 15px;
}
:deep(.el-descriptions__label.el-descriptions__cell:not(.is-bordered-label).is-vertical-label) {
  padding-bottom: 10px;
  color: #909399;
  font-weight: bold;
}
.cell-item {
  display: flex;
  align-items: center;
}
.el-link {
  margin-right: 10px;
}
</style>
