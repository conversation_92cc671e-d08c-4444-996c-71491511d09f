<template>
  <dialog-common :showButton="false" title="导入数据" ref="dialog2" @onClose="onClose" :title="state.title" :width="900" class="dialogTextarea">
    <el-card class="box-card">
      <!-- <template #header>
        <div class="card-header">
          <span style="font-weight: bold;">导入数据</span>
          <el-button type="primary" icon="Back" style="float: right;" @click="showPage2">返回</el-button>
        </div>
      </template> -->
      <el-steps :active="state.active" align-center>
        <el-step title="上传文件"/>
        <el-step title="导入数据"/>
        <el-step title="导入完成"/>
      </el-steps>
      <div class="conent">
        <div v-show="state.active === 0" v-loading="state.loading" element-loading-text="正在导入...">
          <el-card style="margin: 15px 30px;">
            <h4>填写导入设备的信息</h4>
            <span
                class="introduce">请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除，单次导入的数据不超过1000条。</span>
            <div>
              <el-button type="primary" link @click="downTem">下载模板</el-button>
            </div>
          </el-card>
          <el-card style="margin: 15px 30px;">
            <h4>选择数据重复时的处理方式</h4>
            <div>
              <el-select v-model="uploadType" placeholder="Select" style="width:300px">
                <el-option label="覆盖导入" :value="false"/>
                <el-option label="不覆盖导入" :value="true"/>
              </el-select>
            </div>
          </el-card>
          <el-card style="margin: 15px 30px;">
            <h4>上传填好的设备信息表</h4>
            <span class="introduce">文件后缀名必须为xls或xlsx(即Excel格式),文件大小不得大于10M</span>
            <el-upload :limit="1" action="#" accept=".xls,.xlsx" v-model:file-list="state.fileList" :auto-upload="false"
                       ref="upload">
              <el-button type="primary" icon="Upload">上传文件</el-button>
            </el-upload>
          </el-card>
        </div>
        <div v-show="state.active === 1">
          <span>存在差异的企业列表</span>
          <el-card style="margin: 15px 30px;">
            <h4>正常数量条数</h4>
            <div>
              <el-button type="primary" link>{{ getNormalCount() }}</el-button>
            </div>
          </el-card>
          <el-card style="margin: 15px 30px;">
            <h4>异常数量条数</h4>
            <div>
              <el-button type="primary" link>{{
                  uploadData.errorMessages ? uploadData.errorMessages.length : 0
                }}
              </el-button>
            </div>
          </el-card>
          <!-- <el-table :data="state.tableData" :height="state.tableHeight">
          <el-table-column label="序号" type="index" width="100" />
          <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
            :align="item.align" :formatter="item.formatter" />
        </el-table> -->
          <!-- <div class="norrer">
          <div class="phone">
          </div>
        </div>
        <div class="err">异常数量条数</div> -->
        </div>
        <div v-show="state.active === 2" class="successPage">
          <el-icon size="50" color="#67c23a">
            <SuccessFilled/>
          </el-icon>
          <!-- <h3 style="margin: 10px 0;">数据导入完成</h3> -->
          <span class="introduce">正常数量条数: {{ getNormalCount() }} 条</span>
          <span class="introduce">异常数量条数: {{
              uploadData.errorMessages ? uploadData.errorMessages.length : 0
            }} 条</span>
        </div>
      </div>
      <div style="text-align: center;" v-show="state.active === 0">
        <el-button type="primary" size="large" icon="Right" @click="stepHandleFirst">下一步</el-button>
      </div>
      <div style="text-align: center;" v-show="state.active === 1">
        <el-button type="primary" size="large" @click="leadHandle(uploadType)">下一步</el-button>
        <el-button size="large" icon="Back" @click="state.active -= 1">返回重新上传</el-button>
      </div>
      <div v-show="state.active === 1">
        <div v-if="uploadData.errorMessages">
          <el-card style="margin: 15px 30px;background-color: #f5f5f5;color: #ff6a00;">
            <h4>异常提示</h4>
            <div style="margin-top:10px" v-for="errorMessage in uploadData.errorMessages" :key="errorMessage.rowNo">
              <p>第{{ errorMessage.rowNo }}行： {{ errorMessage.errorMessages }}</p>
            </div>
          </el-card>
        </div>
        <div v-else>
          <el-empty description="暂无数据"/>
        </div>
      </div>
      <div style="text-align: center;" v-show="state.active === 2">
        <el-button type="primary" size="large" icon="Check" @click="handleSuccess">完成</el-button>
      </div>
    </el-card>
  </dialog-common>
</template>


<script setup>
import {ElMessage} from 'element-plus'
import {exportFile} from '@/utils/down.js'

import {
  importEquipmentCheckAPI,
  importEquipmentDataAPI
} from "@/api/iotManagement/equipManage"  //这个是导入的接口


const props = defineProps({queryForm: {}})

const { queryForm } = toRefs(props)


const emit = defineEmits(['showPage2', 'submit'])

// 上传类型：覆盖导入、不覆盖导入
const uploadType = ref(false)

// 导入的数据
const uploadData = ref({
  insertEquipments: [],
  updateEquipments: []
})

// dialog组件
const dialog2 = ref();

const state = reactive({
  active: 0,
  loading: false,
  fileList: [],
  tableHeight: 'calc(100vh - 400px)',
  tableHeader: [
    {
      prop: 'enterpriseName',
      label: '企业名称',
    },
    {
      prop: 'enterpriseCreditCode',
      label: '统一社会信用代码'
    },
    {
      prop: 'corporationName',
      label: '法定代表人'
    },
    {
      prop: 'corporationPhone',
      label: '法定代表人手机号码'
    },
    {
      prop: 'corporationIdCardNo',
      label: '法定代表人身份证号码'
    }
  ],
})

// 上传成功
const handleSuccess = () => {
  dialog2.value.close()
}

const onClose = () => {
  state.active = 0
  state.fileList = []
  emit('submit')
}

// const showPage2 = () => {
//   dialog2.value.close()
//   emit('showPage2', 0)
// }

// 下载模板
const downTem = () => {
  exportFile('/equipment/export/template', queryForm.value, '设备导入模版.xlsx')
}

const stepHandleFirst = () => {
  // 步骤一
  if (!state.fileList.length) {
    return ElMessage.error('请上传文件')
  }
  const formData = new FormData()
  formData.append('file', state.fileList[0].raw)
  state.loading = true

  // 上传文件
  importEquipmentCheckAPI(formData).then(res => {
    if (res.success) {
      state.active += 1
      uploadData.value = res.data
    } else {
      ElMessage.error(res.errorMessage)
    }
  }).finally(() => {
    state.loading = false
  })
}

// 覆盖导入
const leadHandle = (bool) => {
  if (bool && uploadData.value.updateEquipments) {
    delete uploadData.value.updateEquipments
  }

  importEquipmentDataAPI(uploadData.value).then(res => {
    if (res.success) {
      state.active += 1
      ElMessage.success('数据导入成功');
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}
const open = () => {
  dialog2.value.open();
  state.active = 0
  state.loading = false
  state.fileList = []
  state.tableHeight = 'calc(100vh - 400px)'
  state.tableData = []
  state.tableHeader = [
    {
      prop: 'enterpriseName',
      label: '企业名称',
    },
    {
      prop: 'enterpriseCreditCode',
      label: '统一社会信用代码'
    },
    {
      prop: 'corporationName',
      label: '法定代表人'
    },
    {
      prop: 'corporationPhone',
      label: '法定代表人手机号码'
    },
    {
      prop: 'corporationIdCardNo',
      label: '法定代表人身份证号码'
    }
  ]
};

// 正常的数量
const getNormalCount = () => {
  let insertCount = uploadData.value.insertEquipments ? uploadData.value.insertEquipments.length : 0;
  let updateCount = uploadData.value.updateEquipments ? uploadData.value.updateEquipments.length : 0;
  return insertCount + updateCount;
}

defineExpose({
  open,
});

</script>

<style scoped lang="less">
.box-card {
  height: 100%;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: none;
  display: flex;
  flex-direction: column;

  :deep(.el-card__header) {
    background-color: rgb(249 249 249);
  }

  :deep(.el-card__body) {
    flex: 1;
    overflow: hidden;
  }
}

.conent {
  height: calc(100% - 100px);
  overflow: auto;
}

.introduce {
  color: #7b7b7b;
  font-size: 14px;
}

.successPage {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
