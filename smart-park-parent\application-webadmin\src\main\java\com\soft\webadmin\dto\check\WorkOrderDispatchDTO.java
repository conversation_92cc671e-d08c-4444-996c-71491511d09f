package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * WorkOrderDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@ApiModel("WorkOrderDispatchDTO对象")
@Data
public class WorkOrderDispatchDTO {

    @ApiModelProperty(value = "工单id")
    @NotNull(message = "工单id不能为空！")
    private Long id;

    @ApiModelProperty(value = "执行人")
    @NotNull(message = "执行人不能为空！")
    private Long workUserId;

    @ApiModelProperty(value = "执行人")
    private String workUserName;

    @ApiModelProperty(value = "备注信息")
    private String remark;

}
