package com.soft.webadmin.dao.check;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.check.WorkOrderQueryDTO;
import com.soft.webadmin.model.check.WorkOrder;
import com.soft.webadmin.vo.check.WorkOrderAppVO;
import com.soft.webadmin.vo.check.WorkOrderVO;

import java.util.List;

/**
 * 工单Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
public interface WorkOrderMapper extends BaseMapper<WorkOrder> {

    List<WorkOrderVO> queryList(WorkOrderQueryDTO queryDTO);

    List<WorkOrderAppVO> queryAppList(WorkOrderQueryDTO queryDTO);

}
