<template>
  <div class="page">
    <div class="tree">
      <el-input v-model="filterText" placeholder="请输入搜索关键词" />
      <el-tree ref="treeRef" :data="state.spaceOptions" :props="{ label: 'name' }" :filter-node-method="filterNode"
               highlight-current accordion style="margin-top: 10px;" @node-click="handleNodeClick" default-expand-all>
      </el-tree>
    </div>
    <div class="container">
      <el-button type="primary" @click="handleEquip" class="btn-equip">
        <el-icon><CaretLeft/></el-icon>
        设备列表
      </el-button>
      <div class="exhibits">
        <vue3-draggable-resizable v-model:x="state.x" v-model:y="state.y" :w="200" :h="200" :handles="[]" @mousewheel.prevent="mouseWheel">
          <div id="container" :style="styleDraw">
            <template v-for="(item, index) in state.componentData" :key="item.id">
              <el-tooltip effect="dark" :popper-class="classPrompt(item)" :content="titlePrompt(item)" placement="top-start" raw-content>
                <component :is="item.component" class="component" :element="item" :style="getStyle(item.style)"
                           type="exhibits" @mousewheel.stop/>
              </el-tooltip>
            </template>
          </div>
        </vue3-draggable-resizable>
      </div>
    </div>
  </div>
</template>

<script setup>
import { v4 as uuidv4 } from 'uuid'
import Vue3DraggableResizable from 'vue3-draggable-resizable'
import { ElMessage } from 'element-plus'

import axios from 'axios'
import { Client } from '@stomp/stompjs'
import JSONbig from 'json-bigint';

import { getStyle } from '@/utils/webtopo/style.js'
import {changeComponentsSizeWithScale, changeStyleWithScale, getOriginStyle} from '@/utils/webtopo/math.js'

import { topoTabGetAPI } from '@/api/settingSystem/topoCenter.js'
import { equipListAPI, equipAlarmAPI } from '@/api/settingSystem/topoConnect.js'
import {treeAPI, treeGroupAPI} from '@/api/iotManagement/space.js'
import {CaretLeft, CaretRight} from "@element-plus/icons-vue";

const route = useRoute()

let stompClient //连接实例
const JSONbigString = new JSONbig({ storeAsString: true });

const props = defineProps({
  spaceId:{
    type: String,
    default: ''
  }
})

const emit = defineEmits(['showPage'])

const reload = inject('reload')

const treeRef = ref()
const filterText = ref('')

const state = reactive({
  spaceId: '',
  spaceOptions: [],
  x:0,
  y:0,
  canvasStyle: {},
  componentData: [],
  idList: [] //当前集合id
})

onMounted(() => {
  getSpaceTree()
})

onUnmounted(() => {
  stompDestroy()
})

onBeforeRouteUpdate((to, from) => {
  setTimeout(() => {
    reload();
  });
})

watch(filterText, (val) => {
  treeRef.value.filter(val)
})

// 设备位置
const getSpaceTree = () => {
  treeGroupAPI({ deep: 3, menuId: route.params.id }).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
};

// 节点选择
const handleNodeClick = (node) => {
  if(!node.children){
    state.spaceId = node.id
    getData()
  }
}

// 过滤方法
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value)
}

// 画布样式
const styleDraw = computed(() => {
  return {
    ...getCanvasStyle(state.canvasStyle),
    width: state.canvasStyle.width && changeStyleWithScale(state.canvasStyle.width, state.canvasStyle.scale) + 'px',
    height: state.canvasStyle.height && changeStyleWithScale(state.canvasStyle.height, state.canvasStyle.scale) + 'px',
  }
})

// 是否显示文本框
const classPrompt = computed(() => {
  return function (info) {
    if (info && info.params) {
      let { classInfo } = info.params
      return (classInfo.checkList || []).includes("topoPrompt") ? '' : 'tooltipVisible'
    } else {
      return 'tooltipVisible'
    }
  }
})

//  提示文字
const titlePrompt = computed(() => {
  return function (info) {
    if (info && info.params) {
      let { classInfo } = info.params
      let current = (classInfo.classList || []).find(item => item.classType == 'topoPrompt') || {}
      return current.conent
    }
  }
})

// 放大或缩小
const mouseWheel = (e) => {
  let scale = state.canvasStyle.scale

  if(scale + (e.deltaY * -0.01) > 10 && scale + (e.deltaY * -0.01) < 200){
    state.canvasStyle.scale =  changeComponentsSizeWithScale(state.canvasStyle , state.componentData , scale + (e.deltaY * -0.01), scale )
    state.x = state.x + parseFloat(changeStyleWithScale(getOriginStyle(e.offsetX, scale),e.deltaY * 0.01).toFixed(2))
    state.y = state.y + parseFloat(changeStyleWithScale(getOriginStyle(e.offsetY, scale),e.deltaY * 0.01).toFixed(2))
  }
}

const getCanvasStyle = (style) => {
  return getStyle(style, ['width', 'height', 'scale'])
}

// 预览重置
const preReset = (canvas) => {
  if(canvas instanceof Object){
    const exhibits = document.querySelector('.exhibits').getBoundingClientRect()
    canvas.width = changeStyleWithScale(canvas.width, canvas.scale)
    canvas.height = changeStyleWithScale(canvas.height, canvas.scale)

    if(canvas.width < exhibits.width){
      state.x = (exhibits.width -  canvas.width) / 2
    }else {
      state.x = -(canvas.width - exhibits.width) / 2
    }

    if(canvas.height < exhibits.height){
      state.y = (exhibits.height - canvas.height) / 2
    }else {
      state.y = -(canvas.height - exhibits.height) / 2
    }
  }
}

// 获取当前组态数据
const getData = () => {
  topoTabGetAPI({ menuId:route.params.id, spaceId: state.spaceId }).then(res => {
    if(res.data && res.data.length){
      res.data =  res.data[0]
    }else {
      return ElMessage.warning('请配置组态页面')
    }

    let content = res.data.content && JSON.parse(res.data.content)
    state.canvasStyle = content.canvasStyle
    state.componentData = content.componentData
    preReset(JSON.parse(JSON.stringify(state.canvasStyle)))

    // 查询设备信息
    let objEquip = {}
    state.idList = state.componentData.map(item => {
      item.id = uuidv4()
      if (item.params) {
        objEquip[item.params.equipInfo.equipmentId] = {
          alarmInfo: []
        }
        return item.params.equipInfo.equipmentId
      }
    }).filter(i => i)

    if(!state.idList.length) return false

    axios.all([equipListAPI({ idList: state.idList }), equipAlarmAPI({ equipmentIdList: state.idList, status: 0 })]).then(axios.spread((responseE, responseA) => {
      // 设备信息
      responseE.data.forEach(item => {
        objEquip[item.equipmentId].equipInfo = item
      });

      // 报警信息
      responseA.data.dataList.forEach(item => {
        objEquip[item.equipmentId] && objEquip[item.equipmentId].alarmInfo.push(item)
      })

      state.componentData.forEach(item => {
        if(!item.params) return false

        let { equipmentId } = item.params.equipInfo

        if(!objEquip[equipmentId].equipInfo && equipmentId){
          item.params.equipInfo.delete = 1
        }

        Object.assign(item.params, JSON.parse(JSON.stringify(objEquip[equipmentId])))
      })

      if (stompClient){
        stompDestroy()
      }
      stompConnect()
    }))
  })
}

// stomp连接
const stompConnect = () => {
  stompClient = new Client({
    brokerURL: import.meta.env.VITE_STOMP_URL + '/stomp',
    connectHeaders: {
      Authorization: localStorage.getItem('Authorization')
    },
    debug: function (str) {
      // console.log(str);
    },
    reconnectDelay: 5000,
    heartbeatIncoming: 4000,
    heartbeatOutgoing: 4000,
  });

  stompClient.onConnect = function (frame) {
    console.log('stomp 连接成功')

    // 主题:/topic/equipment 设备推送
    stompClient.subscribe('/topic/equipment', equipStomp);

    // 主题:/topic/equipment 报警
    stompClient.subscribe('/topic/equipment/warning', alarmStomp);
  };

  stompClient.onStompError = function (frame) {
    console.log('Broker reported error: ' + frame.headers['message']);
    console.log('Additional details: ' + frame.body);
  };

  stompClient.activate();
}

// 实例销毁
const stompDestroy = () => {
  if (stompClient)
    stompClient.deactivate()
}

// 设备消息推送
const equipStomp = (message) => {
  const payload = JSONbigString.parse(message.body);
  console.log(payload)

  if (state.idList.indexOf(payload.equipmentId) == -1) {
    return false
  }
  state.componentData.forEach(item => {
    let { equipmentId } = item.params.equipInfo
    if (equipmentId == payload.equipmentId) {
      item.params.equipInfo = { ...payload }
    }
  })
}

// 报警推送
const alarmStomp = (message) => {
  const payload = JSONbigString.parse(message.body);
  console.log(payload)
  if (state.idList.indexOf(payload.equipmentId) == -1) {
    return false
  }

  state.componentData.forEach(item => {  //设备状态
    let { equipmentId } = item.params.equipInfo
    if (equipmentId == payload.equipmentId) {
      item.params.equipInfo.status = payload.status
    }
  })

  equipAlarmAPI({ equipmentIdList: state.idList, status: 0 }).then(res => {
    let objAlarm = {}
    state.idList.forEach(item => objAlarm[item] = [])

    res.data.dataList.forEach(item => {
      objAlarm[item.equipmentId] && objAlarm[item.equipmentId].push(item)
    })

    state.componentData.forEach(item => {
      let { equipmentId } = item.params.equipInfo
      item.params.alarmInfo = objAlarm[equipmentId]
    })
  })
}

// 设备列表展示
const handleEquip = () => {
  if(state.idList && state.idList.length){
    emit('showPage',1,state.idList)
  }else {
    ElMessage.warning('暂无设备')
  }
}
</script>

<style lang='less' scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;

  .tree {
    width: 250px;
    height: 100%;
    margin-right: 20px;
    flex-shrink: 0;
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
    overflow: auto;
  }

  .container{
    flex: 1;
    background: #FFFFFF;
    border-radius: 10px;
    overflow: hidden;
    padding: 18px;
    position: relative;
  }
}

.btn-equip{
  position: absolute;
  right: 18px;
  top: 18px;
  z-index: 999;
}

.exhibits {
  width: 100%;
  height: 100%;
  background-image: linear-gradient(45deg, #ffffff, #F2F6FC);
  position: relative;
}

#container {
  position: relative;
  background-repeat: no-repeat;
  background-position: center;

  .component {
    position: absolute;
  }
}

.vdr-container.active {
  border-color: transparent;
}
</style>
