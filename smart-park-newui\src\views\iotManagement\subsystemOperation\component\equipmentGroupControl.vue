<template>
  <page-common v-model="state.tableHeight" :query-bool="false">
    <template #operate>
      <el-button type="primary" icon="Plus" @click="onAdd">新增分组</el-button>
    </template>

    <template #table>
      <div class="conent-control" v-if="state.tableData">
        <el-form :model="state.tableData" ref="formRef" label-width="70px" label-suffix=":">
          <el-space wrap :size="50">
            <el-card v-for="item in state.tableData" :key="item" style="width: 350px;">
              <template #header>
                <div class="card-header">
                  <div class="divFlex">
                    <div class="divLeft"></div>
                    <div class="divRight">{{ item.groupName }}</div>
                    <el-button-group style="float: right">
                      <el-button type="primary" size="small" text :icon="Edit" @click="onEdit(item)">
                        编辑
                      </el-button>
                      <el-button type="primary" size="small" text :icon="Delete" @click="onDelete(item)">
                        删除
                      </el-button>
                    </el-button-group>
                  </div>
                </div>
              </template>
              <template #default>
                <div v-for="element in item.groupControllerElements">
                  <el-form-item :label="element.name" label-width="80">
                    <el-select v-if="element.operatorType === 'SELECT'" v-model="element.attributeValue"
                      style="flex: 1;margin-right: 10px">
                      <el-option v-for="(value, key) in JSON.parse(element.operatorValues)" :key="key"
                        :label="value.name" :value="value.value" />
                    </el-select>

                    <el-switch v-else-if="element.operatorType === 'SWITCH'" v-model="element.attributeValue"
                      :active-text="element.operatorValues[0].name" :active-value="element.operatorValues[0].value"
                      :inactive-text="element.operatorValues[1].name"
                      :inactive-value="element.operatorValues[1].value" />
                    <el-input-number v-else v-model="element.attributeValue" :precision="2" :min="0" :step="0.1"
                      style="flex: 1;margin-right: 10px" />

                    <!--                    <span style="color: #6E7074">{{ element.unit }}</span>-->
                  </el-form-item>
                </div>
              </template>
              <template #footer>
                <div style="text-align: center;">
                  <el-button type="primary" @click="onControl(item)">一键组控</el-button>
                </div>
              </template>
            </el-card>
          </el-space>
        </el-form>
      </div>
      <div v-else>
        <el-empty description="暂无数据" />
      </div>

      <equipment-group-control-dialog ref="groupControlDialogRef" :group-control-type="groupControlType"
        @onClose="queryGroupControls" />
    </template>
  </page-common>
</template>

<script setup>
import {
  controlEquipmentGroupControlAPI,
  deleteEquipmentGroupControlAPI,
  listEquipmentGroupControlAPI
} from "@/api/iotManagement/groupControl.js";
import { Delete, Edit } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import EquipmentGroupControlDialog from "./equipmentGroupControlDialog.vue";

const props = defineProps({
  groupControlType: {
    type: Object,
    default: {
      name: '',
      type: ''
    }
  }
})

const { groupControlType } = toRefs(props)


const groupControlDialogRef = ref()

const formRef = ref()

const state = reactive({
  tableHeight: 100,
  tableData: []
})


const onAdd = () => {
  groupControlDialogRef.value.open('新增分组')
}

const onEdit = (val) => {
  groupControlDialogRef.value.open('新增分组', JSON.parse(JSON.stringify(val)))
}

const onDelete = (val) => {
  ElMessageBox.confirm(
    '是否删除设备组控！',
    '提醒',
    {
      type: "warning"
    }
  ).then(() => {
    deleteEquipmentGroupControlAPI({ groupControlId: val.id }).then(res => {
      if (res.success) {
        ElMessage.success('删除成功！')
        queryGroupControls()
      } else {
        ElMessage.error("删除失败！")
      }
    })
  })
}

const onControl = (val) => {
  let param = {
    groupId: val.id
  }
  let equipmentGroupControlAttributes = []
  if (val.groupControllerElements) {
    val.groupControllerElements.forEach(element => {
      equipmentGroupControlAttributes.push({
        groupControllerElement: element,
        attributeValue: element.attributeValue
      })
    })
  }
  param.equipmentGroupControlAttributes = equipmentGroupControlAttributes
  controlEquipmentGroupControlAPI(param).then(res => {
    if (res.success) {
      ElMessage.success('组控成功！')
    } else {
      ElMessage.error('组控失败，' + res.errorMessage)
    }
  })
}


const queryGroupControls = () => {
  listEquipmentGroupControlAPI({ groupControlType: groupControlType.value.type }).then(res => {
    if (res.success) {
      state.tableData = res.data
    }
  })
}

const init = () => {
  queryGroupControls()
}

defineExpose({
  init
})
</script>

<style scoped lang="less">
:deep(.el-card__header) {
  background: #E6EBF1;
  padding: 10px 5px 0 5px;
}


.divFlex {
  margin-bottom: 10px;
}

.divRight {
  margin-left: 8px;
  font-size: 16px;
  display: inline-block;
}

.divLeft {
  width: 7px;
  height: 20px;
  background-color: #3f9eff;
  display: inline-block;
  vertical-align: top;
}

.conent-control {
  height: 100%;
  overflow: auto;
}
</style>
