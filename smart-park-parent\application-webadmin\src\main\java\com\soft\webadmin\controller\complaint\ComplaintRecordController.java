package com.soft.webadmin.controller.complaint;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.complaint.ComplaintRecordCreateDTO;
import com.soft.webadmin.dto.complaint.ComplaintRecordHandleDTO;
import com.soft.webadmin.dto.complaint.ComplaintRecordQueryDTO;
import com.soft.webadmin.service.complaint.ComplaintRecordService;
import com.soft.webadmin.vo.complaint.ComplaintRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 投诉建议记录控制器类
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@Api(tags = "投诉建议接口管理")
@RestController
@RequestMapping("/complaint/record")
public class ComplaintRecordController {

    @Resource
    private ComplaintRecordService complaintRecordService;


    @ApiOperation("列表查询")
    @PostMapping("/list")
    public ResponseResult<MyPageData<ComplaintRecordVO>> list(@RequestBody ComplaintRecordQueryDTO complaintRecordQueryDTO) {
        MyPageData<ComplaintRecordVO> pageData = complaintRecordService.list(complaintRecordQueryDTO);
        return ResponseResult.success(pageData);
    }


    @ApiOperation("创建投诉")
    @PostMapping("/create")
    public ResponseResult<Void> create(@RequestBody ComplaintRecordCreateDTO complaintRecordCreateDTO) {
        complaintRecordService.create(complaintRecordCreateDTO);
        return ResponseResult.success();
    }


    @ApiOperation("查看（进行中）")
    @GetMapping("/view/{id}")
    public ResponseResult<ComplaintRecordVO> view(@PathVariable Long id) {
        ComplaintRecordVO complaintRecordVO = complaintRecordService.view(id);
        return ResponseResult.success(complaintRecordVO);
    }


    @ApiOperation("处理投诉")
    @PostMapping("/handle")
    public ResponseResult<Void> handle(@RequestBody ComplaintRecordHandleDTO complaintRecordHandleDTO) {
        complaintRecordService.handle(complaintRecordHandleDTO);
        return ResponseResult.success();
    }
}
