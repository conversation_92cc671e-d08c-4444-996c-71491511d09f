@font-face {
  font-family: DINREGULAR;
  src: url('@/assets/font/PingFang-SC.ttf');
}

*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

html,body,#app{
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/*主题色*/
.main-color {
  color: #1bbf80;
}

.white-color {
  color: #ffffff;
}

.black-color {
  color: #333333;
}

.normal-color {
  color: #666666;
}

.gray-color {
  color: #999999;
}

.light-color {
  color: FCCCCCC;
}

.bule-color {
  color: #316CF6;
}

.warn-color{
  color: red;
}

//一行省略
.textellipsis{
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

// 隐藏文本提示
.tooltipVisible{
  display: none;
}

// 弹框
.dialogCommon {
  .el-dialog__header {
    margin-right: 0px;
  }

  .el-dialog__body {
    max-height: 57vh;
    overflow: auto;
  }

  .el-form:not(.el-form--inline){
    .el-input,
    .el-date-editor,
    .el-select,
    .el-textarea {
      width: 85%;
    }

    .el-input-number, .el-cascader {
      width: 85%;

      .el-input {
        width: 100%;
      }
    }

    .el-range-editor.el-input__wrapper{
      flex-grow: initial;
    }
  }
}


.dialogTextarea {
  .el-textarea {
    width: 93.5%!important;
  }
}

.commonTextarea {
  .el-textarea {
    width: 96%!important;
  }
}

// 上传
.disUpload{
  .el-upload--picture-card{
    display: none;
  }
}

.upload-noTip{
  .el-upload-list__item.is-success:focus:not(:hover){
    display: none !important;
  }
}

// vue3-cron-plus-container组件
.vue3-cron-plus-container{
  .el-row{
    .el-radio {
      height: auto;
      min-height: 32px;
    }
    .el-tag.el-tag--info{
      margin-left: 0;
    }
  }
}

//视频组件
.dialogVideo .el-dialog__body {
  height: 60vh;
  position: relative;
}
#mui-player{
  .dplayer-live-badge{
    display: none;
  }
}

// 详情文字区域
.detail-area{
  margin: 15px 0 20px 0px;
}

// 详情标题
.divFlex {
  margin-bottom: 10px;
}

.divRight {
  margin-left: 8px;
  font-size: 16px;
  display: inline-block;
}

.divLeft {
  width: 7px;
  height: 20px;
  background-color: #3f9eff;
  display: inline-block;
  vertical-align: top;
}

// 生命周期
.life-circle {
  .top {
    display: flex;
    justify-content: space-between;
    .right{
      font-size: 16px;
    }
  }

  .item{
    margin-top: 5px;
    &:first-child{
      margin-top: 27px;
    }

    .item-inline-content{
      color: #2B2B2B;
    }

    .item-content{
      margin-top: 5px;
      color: #2B2B2B;
    }
  }
}

// 卡片高度
.box-card {
  height: 100%;

  .el-card__body {
    height: calc(100% - 69px);
    overflow: auto;
  }
}

// 卡片文字背景
.card-textBg{
  .el-form-item:not(.item__content-noBg){
    .el-form-item__content {
      background: #F5F7FA;
      border-radius: 4px;
      border: 1px solid #EBEEEF;
      padding: 0 10px;
      min-height: 32px;
      word-break: break-all;
    }
  }
}

//状态点
.status-circle{
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
  margin-bottom: 1px;
}
/* 定义滚动条高宽及背景
 高宽分别对应横竖滚动条的尺寸 */
 ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* 定义滚动条轨道
内阴影+圆角 */
::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 定义滑块
内阴影+圆角 */
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #ddd;
}
