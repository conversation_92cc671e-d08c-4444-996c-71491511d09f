package com.soft.webadmin.dto.contingency;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * EarlyWarningDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EarlyWarningDTO对象")
@Data
public class EarlyWarningSummarizeDTO {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空！")
    private Long id;

    @ApiModelProperty(value = "事件概述/演练总结")
    @NotBlank(message = "事件概述/演练总结不能为空")
    private String summary;

    @ApiModelProperty(value = "原因分析")
    private String reason;

    @ApiModelProperty(value = "改进措施")
    private String improve;

    @ApiModelProperty(value = "附件")
    private String annex;

}
