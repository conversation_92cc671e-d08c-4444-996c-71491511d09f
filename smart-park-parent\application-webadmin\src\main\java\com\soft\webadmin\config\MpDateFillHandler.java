package com.soft.webadmin.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.ContextUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * mybatis plus 自动填充
 * TODO 注意：修改操作的自动填充只有是通过 updateById() 才会生效，而通过 Wrapper 查询条件的更新不会生效；
 */
@Component
public class MpDateFillHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        boolean createTime = metaObject.hasSetter("createTime");
        if (createTime && metaObject.getValue("createTime") == null) {
            setFieldValByName("createTime", new Date(), metaObject);
        }

        boolean updateTime = metaObject.hasSetter("updateTime");
        if (updateTime && metaObject.getValue("updateTime") == null) {
            setFieldValByName("updateTime", new Date(), metaObject);
        }

        // 判断是在 HTTP 请求，才能从请求中获取认证信息
        if (ContextUtil.hasRequestContext()) {
            TokenData tokenData = TokenData.takeFromRequest();
            boolean createUserId = metaObject.hasSetter("createUserId");
            if (createUserId && metaObject.getValue("createUserId") == null) {
                if (tokenData != null) {
                    Long userId = tokenData.getUserId();
                    setFieldValByName("createUserId", userId, metaObject);
                }
            }
            boolean updateUserId = metaObject.hasSetter("updateUserId");
            if (updateUserId && metaObject.getValue("updateUserId") == null) {
                if (tokenData != null) {
                    Long userId = tokenData.getUserId();
                    setFieldValByName("updateUserId", userId, metaObject);
                }
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        boolean hasUpdateTime = metaObject.hasSetter("updateTime");
        if (hasUpdateTime && metaObject.getValue("updateTime") == null) {
            setFieldValByName("updateTime", new Date(), metaObject);
        }

        boolean hasUpdateUserId = metaObject.hasSetter("updateUserId");
        if (ContextUtil.hasRequestContext() && hasUpdateUserId && metaObject.getValue("updateUserId") == null) {
            TokenData tokenData = TokenData.takeFromRequest();
            if (tokenData != null) {
                Long userId = tokenData.getUserId();
                setFieldValByName("updateUserId", userId, metaObject);
            }
        }
    }
}
