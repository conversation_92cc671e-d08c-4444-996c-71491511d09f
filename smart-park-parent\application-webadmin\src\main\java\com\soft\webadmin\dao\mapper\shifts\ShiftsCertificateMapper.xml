<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.shifts.ShiftsCertificateMapper">
    <resultMap type="com.soft.webadmin.model.shifts.ShiftsCertificate" id="ShiftsCertificateResult">
        <result property="rosterId" column="roster_id" />
        <result property="certificateName" column="certificate_name" />
        <result property="certificateStartTime" column="certificate_start_time" />
        <result property="certificateEndTime" column="certificate_end_time" />
        <result property="img" column="img" />
    </resultMap>

    <sql id="selectShiftsCertificateVo">
        select roster_id, certificate_name, certificate_start_time, certificate_end_time, img from sp_shifts_certificate
    </sql>
    
</mapper>