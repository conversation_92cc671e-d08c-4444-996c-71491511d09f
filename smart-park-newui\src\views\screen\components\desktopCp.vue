<template>
  <div class="desktop">
    <template v-for="menu in pageList" :key="menu.menuId">
      <div class="ico" @dblclick="openWindow(menu)" @click="openWindowClick(menu)">
        <img :src="iconPath(menu.icon)">
        <span>{{ menu.menuName }}</span>
      </div>
    </template>
  </div>
</template>

<script setup>
import { useWindowStore } from '@/store/modules/window.js'

import {menuGetAPI} from '@/api/settingSystem/menu.js'

const props = defineProps({
  dblclick: {
    type: Boolean,
    default: true
  }
})

const windowStore = useWindowStore()
const { windowArray } = storeToRefs(windowStore)

const pageList = ref([])

onMounted(() => {
  getMenu()
})

// 图标地址
const iconPath = (path) => {
  return new URL(`/src/assets/window/${path}.png`, import.meta.url).href
}

// 获取菜单
const getMenu = () => {
  menuGetAPI('pc').then(res => {
    pageList.value = res.data
  })
}

// 打开window
const openWindow = (menu) => {
  if(!props.dblclick)return

  iconClick(menu)
}

const openWindowClick = (menu) => {
  if(props.dblclick) return

  iconClick(menu)
}

// 图标处理
const iconClick = (menu) => {
  let path

  if(!menu.children){
    window.open(menu.path, 'foo', 'noopener=yes,noreferrer=yes')
    return
  }
  function deep(arrMenu) {
    return arrMenu.some(item => {
      if(item.path && item.isHidden){
        path = '/' + item.path
        return true
      } else if (item.children) {
        return deep(item.children)
      }
    })
  }

  deep(menu.children)

  let windowTask = windowArray.value.find(item => item.url === window.location.origin  +  path)

  if(windowTask){
    windowStore.cancelMin(windowTask.id);
    windowStore.goTop(windowTask.id);
  }else {
    windowStore.push({
      id: new Date().getTime(),
      title: menu.menuName,
      url: window.location.origin  + path,
      icon: menu.icon,
      isSize: true
    })
  }
}
</script>

<style scoped lang="less">
.desktop{
  display: grid;
  grid-auto-flow: column ;
  grid-template-columns: repeat(auto-fill,80px);
  grid-template-rows: repeat(auto-fill,80px);
  .ico{
    font-size: 14px;
    color: #ffffff;
    line-height: 12px;
    padding: 5px 5px 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    word-break: break-all;
    text-align: center;
    img{
      width: 44px;
      height: 44px;
      margin-bottom: 7px;
    }
  }
  .ico:hover{
    background-color: rgba(255,255,255,0.1);
  }
}
</style>
