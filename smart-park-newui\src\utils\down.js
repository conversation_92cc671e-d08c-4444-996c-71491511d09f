import axios from "axios";
import {ElMessage} from "element-plus";

export const parseUrl = (path, params) => {
  let url = import.meta.env.VITE_BASE_URL+ path
  if (!params || params.constructor !== Object) {
    return url
  }
  let str = ''
  Object.keys(params).forEach(key => {
    str += `&${key}=${encodeURIComponent(params[key])}`
  })
  return `${url}?${str.slice(1)}`
}


// 获取文件路径
const getFilepath = (path, param) => {
  let headers = {
    Authorization: localStorage.getItem('Authorization'),
    projectId: localStorage.getItem('projectId')
  }
  let url = parseUrl(path, param);
  return axios
    .get(url, { responseType: "arraybuffer" , headers })
    .then((res) => {
      let blob = new Blob([res.data], { type: res.headers.contentType });
      return window.URL.createObjectURL(blob);
    })
    .catch((err) => {
      ElMessage.error(err.message);
    });
}

//下载文件
export const exportFile = async (url, param, fileName) => {
  // 获取文件流的下载路径
  let filepath = await getFilepath(url, param);

  // 下载
  let a = document.createElement("a");
  document.body.appendChild(a);
  a.href = filepath;
  a.download = fileName;
  a.click();
  window.URL.revokeObjectURL(filepath);
  a.remove()
}

//上传文件
export const uploadFile = (path, file) => {
  let headers = {
    Authorization: localStorage.getItem('Authorization'),
    projectId: localStorage.getItem('projectId')
  }
  let formData = new FormData();
  formData.append("file", file);
  let url = parseUrl(path);
  return axios.post(url, formData, { headers });
}

//上传文件
export const uploadFile2 = (path, file, params) => {
  let headers = {
    Authorization: localStorage.getItem('Authorization'),
    projectId: localStorage.getItem('projectId')
  }
  let formData = new FormData();
  formData.append("file", file);
  params.map((item) => {
    formData.append(item.key, item.value);
  })
  let url = parseUrl(path);
  return axios.post(url, formData, { headers });
}

//下载文件
export const exportFileMessage = async (file, fileName) => {
  let blob = new Blob([file.data], { type: file.headers.contentType });
  let filepath = window.URL.createObjectURL(blob);

  // 下载
  let a = document.createElement("a");
  a.href = filepath;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(filepath);
  a.remove()
}
