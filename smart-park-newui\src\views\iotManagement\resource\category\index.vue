<template>
  <page-common v-model="state.tableHeight" :queryBool="false">
    <template #operate>
      <el-button type="primary" :icon="Plus" @click="addHandle" v-permsBtn="'menu:add'">新建分类</el-button>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableData" row-key="id">
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" />
        <el-table-column align="center" label="操作" width="230">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <modal-page ref="modal" :title="state.title" :tableData="state.tableData"
        @submit="getList"></modal-page>
    </template>
  </page-common>
</template>

<script setup>
import modalPage from './component/modal.vue'

import {
  Plus
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { equipemtListAPI, equipmentDelAPI } from '@/api/iotManagement/category.js'

let modal = ref()

const state = reactive({
  title: '',
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'name',
      label: '分类名称'
    },
    {
      prop: 'subType',
      label: '子系统分类'
    }
  ]
})

onMounted(() => {
  getList()
})

// 获取设备分类
const getList = () => {
  equipemtListAPI().then(res => {
    console.log(res.data)
    state.tableData = res.data
  })
}

// 新建设备分类
const addHandle = () => {
  state.title = '新建分类'
  modal.value.form.id = ''
  modal.value.open()
}

// 编辑设备分类
const editHandle = (info) => {
  if (!Number(info.parentId)) {
    info.parentId = ''
  }

  state.title = '编辑分类'
  modal.value.open()

  nextTick(() => {
    Object.assign(modal.value.form, info)
  })
}

// 删除设备分类
const deleteHandle = (info) => {
  ElMessageBox.confirm(
    '是否删除当前设备分类?',
    '提醒',
    {
      type: "warning"
    }
  ).then(() => {
    equipmentDelAPI({ id: info.id }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}


</script>

<style lang='less' scoped></style>
