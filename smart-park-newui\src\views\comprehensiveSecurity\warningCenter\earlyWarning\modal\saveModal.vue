<template>
  <dialog-common ref="dialog" title="事件上报" @submit="submit" :width="900" :formRef="ruleFormRef"
                 class="dialogTextarea">
    <el-form ref="ruleFormRef" label-width="110px" :model="form" :rules="state.rules" label-suffix=":">
      <el-row>
        <el-col :span="12">
          <el-form-item label="事件分类" prop="eventId">
            <el-select v-model="form.eventId" filterable clearable placeholder="请选择事件类型">
              <el-option v-for="item in eventOptions" :label="item.name" :value="item.id"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位置" prop="spaceId">
            <el-cascader
                v-model="form.spaceId"
                :options="spaceOptions"
                :props="optionsProps"
                clearable
                placeholder="请选择位置"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="详细位置" prop="location">
            <el-input v-model="form.location" maxlength="11" clearable placeholder="请输入详细位置"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报人" prop="reportUserId">
            <el-select v-model="form.reportUserId" filterable clearable placeholder="请选择报修人(可直接搜索)"
                       @change="reportUserChangeHandle">
              <el-option v-for="item in state.userList" :label="item.showName + (item.deptName ? ' - ' + item.deptName : '')" :value="item.userId"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="reportUserPhone">
            <el-input v-model.number="form.reportUserPhone" maxlength="11" clearable placeholder="请输入联系电话"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="事件描述" prop="description">
        <el-input v-model="form.description" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit
                  placeholder="请输入事件描述"/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { ElMessage } from 'element-plus'

import { listUsersAPI } from '@/api/settingSystem/user.js'
import { warningSaveAPI } from '@/api/comprehensiveSecurity/earlyWarning.js'

// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'id',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};

const props = defineProps({
  eventOptions: {
    type: Array,
    default: () => []
  },
  spaceOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['submit'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({})

const state = reactive({
  rules: {
    eventId: [{required: true, message: '请选择事件类型'}],
    spaceId: [{required: true, message: '请选择位置'}],
    description: [{required: true, message: '请输入事件描述'}]
  },
  tableData: [],
  menuType: []
})

onMounted(() => {
  getUserList()
})


// 查询系统用户
const getUserList = (deptId) => {
  listUsersAPI({deptId: deptId}).then((res) => {
    state.userList = res.data.dataList;
  });
};

// 选择人员
const reportUserChangeHandle = (val) => {
  let index = state.userList.findIndex(e => e.userId === val);
  form.reportUserPhone = state.userList[index]?.phone;
};

const open = () => {
  dialog.value.open()
}

// 提交
const submit = () => {
  warningSaveAPI({...form}).then((res) => {
    if (res.success) {
      ElMessage.success('保存成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

defineExpose({
  form,
  open
})
</script>

<style lang='less' scoped></style>
