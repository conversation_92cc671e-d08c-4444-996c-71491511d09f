import { request } from "@/utils/request";

export const maintenanceListApi = (data) => {
  return request('get', '/cablingSystem/equipment/list', data, 'F');
}

export const saveOrUpdateAPI = (data) => {
  return request('post', '/cablingSystem/equipment/saveOrUpdate', data);
}

export const listApi = (data) => {
  return request('get', '/cablingSystem/equipment/list', data, 'F');
}

export const deleteAPI = (data) => {
  return request('post', '/cablingSystem/equipment/delete', data, 'F');
}

export const listByEquipmentIdApi = (data) => {
  return request('get', '/cablingSystem/equipment-port/listByEquipmentId', data, 'F');
}