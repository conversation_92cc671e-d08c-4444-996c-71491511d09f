<template>
  <el-row>
    <el-col :span="isShowEquip ?  17 : 24">
      <el-card class="box-card card-textBg">
        <template #header>
          <el-row align="middle" justify="space-between">
            <span style="font-weight: bold;">{{ title }}</span>
            <div>
              <el-button v-show="!isShowEquip" icon="Back" style="float: right;margin-left: 12px;" type="primary"
                         @click="showPage">返回
              </el-button>
              <el-button
                  v-if="from === 1 && state.dataInfo.state === 3 && state.dataInfo.workUserId === state.loginUserId && state.dataInfo.orderType == 'REPAIR'"
                  icon="RefreshLeft" style="float: right;" type="primary" @click="backHandle">退回
              </el-button>
              <el-button
                  v-if="from === 1 && state.dataInfo.state === 3 && state.dataInfo.workUserId === state.loginUserId"
                  icon="Check" style="float: right;" type="primary" @click="finishHandle">处理
              </el-button>
              <el-button
                  v-if="from === 0 && (state.dataInfo.state === 1 )"
                  icon="CloseBold" style="float: right;margin-left: 12px;" type="" @click="closeHandle">关闭
              </el-button>
              <el-button v-if="from === 0 && (state.dataInfo.state === 1)"
                         icon="Check" style="float: right;" type="primary" @click="dispatchHandle">派单
              </el-button>
            </div>
          </el-row>
        </template>
        <div class="conent">
          <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-position="top" label-width="110px">
            <div>
              <el-row justify="space-between">
                <div class="divFlex">
                  <div class="divLeft"></div>
                  <div class="divRight">工单信息</div>
                </div>
                <div>
                  <order-status :status="state.dataInfo.state"></order-status>
                  <el-link v-show="from === 1 && state.dataInfo.state === 3 && state.dataInfo.workUserId === state.loginUserId" style="margin-left: 10px" type="primary"
                           @click="handleView">处理预案
                  </el-link>
                </div>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="isShowEquip ? 6 : 5">
                  <el-form-item label="工单编号">
                    {{ state.dataInfo.orderNo }}
                  </el-form-item>
                </el-col>
                <el-col :span="isShowEquip ? 6 : 5">
                  <el-form-item label="工单类别">
                    {{ state.orderTypeOptions[state.dataInfo.orderType] }}
                  </el-form-item>
                </el-col>
                <el-col v-if="isShowEquip" :span="isShowEquip ? 6 : 5">
                  <el-form-item label="故障设备">
                    <div v-if="state.dataInfo.businessTable === 'sp_check_repair_log'">
                      {{ state.dataInfo.checkRepairLogVO.equipmentName }}
                    </div>
                    <div v-if="state.dataInfo.businessTable === 'sp_equipment_warning'">
                      {{ state.dataInfo.equipmentWarningVO.equipmentName }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="isShowEquip ? 6 : 5">
                  <el-form-item label="位置">
                    <div v-if="state.dataInfo.businessTable === 'sp_check_repair_log'">
                      {{ state.dataInfo.checkRepairLogVO.spaceFullName }}
                    </div>
                    <div v-if="state.dataInfo.businessTable === 'sp_equipment_warning'">
                      {{ state.dataInfo.equipmentWarningVO.equipmentSpaceFullName }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="isShowEquip ? 6 : 5">
                  <el-form-item label="优先级">
                    <el-tag :type="state.dataInfo.priority === 1 ? 'primary' : (state.dataInfo.priority === 2 ? 'warning' : 'danger')">
                      {{ state.priorityOptions[state.dataInfo.priority] }}
                    </el-tag>
                  </el-form-item>
                </el-col>
                <el-col v-if="isShowEquip" :span="isShowEquip ? 6 : 5">
                  <el-form-item label="预计完成时间">
                    {{ state.dataInfo.predictRepairTime }}
                  </el-form-item>
                </el-col>
                <el-col :span="isShowEquip ? 6 : 5">
                  <el-form-item label="完成时间" prop="realityFinishTime">
                    {{ state.dataInfo.realityFinishTime }}
                  </el-form-item>
                </el-col>
                <el-col v-if="state.dataInfo.businessTable === 'sp_check_repair_log'" :span="isShowEquip ? 6 : 5">
                  <el-form-item label="报修科室">
                    {{ state.dataInfo.checkRepairLogVO.reportDeptName }}
                  </el-form-item>
                </el-col>
                <el-col v-if="state.dataInfo.businessTable === 'sp_check_repair_log'" :span="isShowEquip ? 6 : 5">
                  <el-form-item label="报修人">
                    {{ state.dataInfo.checkRepairLogVO.reportUserName }}
                  </el-form-item>
                </el-col>
                <el-col v-if="state.dataInfo.businessTable === 'sp_check_repair_log'" :span="isShowEquip ? 6 : 5">
                  <el-form-item label="联系电话">
                    {{ state.dataInfo.checkRepairLogVO.reportUserPhone }}
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="问题描述">
                    <div v-if="state.dataInfo.businessTable === 'sp_check_repair_log'">
                      {{ state.dataInfo.checkRepairLogVO.content }}
                    </div>
                    <div v-if="state.dataInfo.businessTable === 'sp_equipment_warning'">
                      {{ state.dataInfo.equipmentWarningVO.content }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col v-if="state.dataInfo.businessTable === 'sp_check_repair_log'" :span="24">
                  <el-form-item class="item__content-noBg" label="附件">
                    <img-video :list="state.fileList"></img-video>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item v-if="state.dataInfo.score" label="评分"  class="item__content-noBg">
                    <el-rate v-model="state.dataInfo.score" disabled/>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item v-if="state.dataInfo.score" label="评价">
                    {{ resultCommon.content }}
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="divFlex" v-show="state.dataInfo.state === 5">
                <div class="divLeft"></div>
                <div class="divRight">处理信息</div>
              </div>
              <el-row v-show="state.dataInfo.state === 5">
                <el-col :span="24">
                  <el-form-item label="过程描述">
                    {{ resultWork.describe }}
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item class="item__content-noBg" label="附件">
                    <img-video :list="pictureVideo(resultWork.img)"></img-video>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item class="item__content-noBg">
                    <el-table :data="state.dataInfo.workOrderSparePartVOList">
                      <el-table-column v-for="(item, index) in state.quoteHeader" :key="index" :align="item.align"
                                       :formatter="item.formatter"
                                       :label="item.label" :prop="item.prop" :width="item.width"/>
                    </el-table>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注">
                    {{ resultWork.remarks }}
                  </el-form-item>
                </el-col>
              </el-row>
              <div>
                <div class="divFlex">
                  <div class="divLeft"></div>
                  <div class="divRight">工单日志</div>
                </div>
                <orderLog :data="state.dataInfo.workOrderLogVOList" class="detail-area"></orderLog>
              </div>
            </div>
          </el-form>
        </div>
      </el-card>
    </el-col>
    <el-col v-show="isShowEquip" :span="7">
      <el-card class="box-card" style="margin-left: 15px;">
        <template #header>
          <div class="card-header">
            <span style="font-weight: bold;">设备生命周期</span>
            <el-button icon="Back" style="float: right;" type="primary" @click="showPage">返回</el-button>
          </div>
        </template>
        <equipmentCycle :data="state.dataInfo.equipmentLifeCycleVOList"></equipmentCycle>
      </el-card>
    </el-col>

    <!--派单-->
    <dispatch ref="dispatchDialog" @submit="refreshInfo"/>
    <!--退回-->
    <back ref="backDialog" @submit="refreshInfo"/>
    <!--处理-->
    <handle ref="handleDialog" @submit="refreshInfo"/>
    <!--评价-->
    <evaluate ref="evaluateDialog" @submit="refreshInfo"/>
    <!-- 报价 -->
    <quoted ref="quotedDialog" :laborCost="state.laborCost" :orderId="state.orderId" @submit="refreshInfo"></quoted>
    <!-- 历史报价 -->
    <history ref="historyRef" :list="state.quoteList"></history>
    <!--    关闭-->
    <close ref="closeRef" @submit="refreshInfo"></close>
  </el-row>
</template>

<script setup>
import {getWorkOrderDetailAPI, workDraftQueryAPI,} from '@/api/operationManagement/workOrder.js';

import {ElMessageBox, ElTag} from 'element-plus';
import dispatch from './modal/dispatch.vue';
import back from './modal/back.vue';
import handle from './modal/handle.vue';
import evaluate from './modal/evaluate.vue';
import quoted from './modal/quoted.vue';
import history from './modal/history.vue';
import close from './modal/close.vue'

import orderStatus from "@/views/operationManagement/workOrder/component/orderStatus.vue";
import orderLog from '@/views/operationManagement/workOrder/component/orderLog.vue'
import equipmentCycle from '@/views/operationManagement/workOrder/component/equipmentCycle.vue'

import {pictureVideo} from '@/utils/util';

const emit = defineEmits(['showPage'])

const props = defineProps({
  from: {
    type: Number,
    default: -1,
  },
  title: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: ''
  }
});
const {from, title} = toRefs(props);
const ruleFormRef = ref();
const dispatchDialog = ref();
const handleDialog = ref();
const evaluateDialog = ref();
const backDialog = ref();
const quotedDialog = ref()
const historyRef = ref()
const closeRef = ref()
const form = reactive({});

const state = reactive({
  id: undefined,  // 工单id
  priorityOptions: {
    1: '普通',
    2: '紧急',
    3: '特急',
  },
  businessTypeOptions: {
    OPERATIONS: '运维',
    CLEANING: '保洁',
    TRANSPORT: '运送',
    PROPERTY: '房产',
    STOREHOUSE: '库房'
  },
  orderTypeOptions: {
    'PATROL_INSPECTION': '巡检工单',
    'MAINTENANCE': '维保工单',
    'REPAIR': '维修工单',
    'CLEANING': '保洁工单',
    'CLEANING_TEMP': '临时保洁',
    'TRANSPORT_LOOP': '循环运送',
    'TRANSPORT_TEMP': '临时运送'
  },
  dataInfo: {
    checkRepairLogVO: {},
    equipmentWarningVO: {},
    equipmentLifeCycleVOList: [],
    workOrderLogVOList: [],
    workOrderSparePartVOList:[]
  },
  baseUrl: import.meta.env.VITE_BASE_URL,
  fileList: [],
  loginUserId: undefined,
  title: '',
  quoteHeader: [
    {
      prop: 'sparePartName',
      label: '备件名称',
    },
    {
      prop: 'classifyName',
      label: '备件分类'
    },
    {
      prop: 'receiveQuantity',
      label: '领用数量'
    },
  ],
  quoteList: [],   // 报价记录
  laborCost: 0,  // 人工费
  orderId: '',
  total: {} // 总计人工
});

onMounted(() => {

  // 当前登录用户信息
  let userInfo = JSON.parse(localStorage.getItem('userInfo'));
  state.loginUserId = userInfo.userId;

  state.id = props.id;
  refreshInfo()
})

// 是否显示设备周期
const isShowEquip = computed(() => {
  return state.dataInfo.equipmentLifeCycleVOList && state.dataInfo.equipmentLifeCycleVOList.length
})

// 完成结果内容
const resultWork = computed(() => {
  let result = state.dataInfo.workOrderLogVOList.find(item => item.operate == 11) || {content: '{}'}
  return JSON.parse(result.content)
})

// 评价
const resultCommon = computed(() => {
  let result = state.dataInfo.workOrderLogVOList.find(item => item.operate == 12) || {content: '{}'}
  return JSON.parse(result.content)
})

const showPage = () => {
  emit('showPage', 0)
}

// 刷新工单信息
const refreshInfo = () => {
  getWorkOrderDetailAPI({id: state.id}).then((res) => {
    Object.assign(state.dataInfo, res.data);
    if (res.data.checkRepairLogVO) {
      state.fileList = pictureVideo(res.data.checkRepairLogVO.img);
    }
  });
};

// 查看预案
const handleView = () => {
  ElMessageBox.alert(state.dataInfo.treatmentPlan, '处理预案')
}

// 派单
const dispatchHandle = () => {
  dispatchDialog.value.open();
  nextTick(() => {
    dispatchDialog.value.form.id = state.id;
  });
}

// 处理
const finishHandle = () => {
  handleDialog.value.open();
  nextTick(() => {
    handleDialog.value.form.id = state.id;

    // 获取草稿
    workDraftQueryAPI({id: state.id}).then(res => {
      if (res.data && res.data.content) {
        res.data.content = JSON.parse(res.data.content);
        handleDialog.value.form.describe = res.data.content.describe;
        handleDialog.value.state.fileList = res.data.content.img ? res.data.content.img.split(',').map(item => {
          return {
            url: import.meta.env.VITE_BASE_URL + item,
            filePath: item
          };
        }) : [];
        handleDialog.value.form.remarks	 = res.data.content.remarks;
      }

      if (res.data.workOrderSparePartVOList && res.data.workOrderSparePartVOList.length) { // 配品配件
        handleDialog.value.form.use = 1
        handleDialog.value.form.sparePartList = res.data.workOrderSparePartVOList
      }
    });
  });
}

// 退回
const backHandle = () => {
  backDialog.value.open();
  nextTick(() => {
    backDialog.value.form.id = state.id;
  });
}


// 关闭
const closeHandle = () => {
  closeRef.value.open()
  nextTick(() => {
    closeRef.value.form.id = state.id
  })
}

</script>

<style lang="less" scoped>
.el-row, .el-col {
  height: 100%;
}

:deep(.el-form-item__label) {
  color: #73767a;
}

.tag {
  margin: 5px 5px 5px 0;
}

:deep(.child-table .el-table__header th.el-table__cell) {
  padding: 8px 0;
  background-color: #FFFFFF !important;
  color: #7e7e7e;
}

:deep(.child-table .el-table__body .el-table__cell) {
  padding: 5px 0;
}
</style>
