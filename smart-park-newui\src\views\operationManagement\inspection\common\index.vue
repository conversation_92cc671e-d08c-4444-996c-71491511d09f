<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="人工巡检" name="first">
      <page-artificial ref="artificial"></page-artificial>
    </el-tab-pane>
    <el-tab-pane label="智能巡检" name="second" v-if="props.planType == 'PATROL_INSPECTION'">
      <page-smart ref="smart"></page-smart>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import pageArtificial from './component/pageArtificial.vue'
import pageSmart from './component/pageSmart.vue'

const props = defineProps({
  planType: {
    type: String,
    default: ""
  }
})

let init = true
const artificial = ref()
const smart = ref()

const activeName = ref('first')

onMounted(() => {
  artificial.value.getTree(props.planType)
})

const handleClick = (tab, event) => {
  if (tab.props.name == 'second' && init) {
    init = false
    smart.value.getTree(props.planType)
  }
}
</script>

<style lang='less' scoped>
.el-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);

    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
