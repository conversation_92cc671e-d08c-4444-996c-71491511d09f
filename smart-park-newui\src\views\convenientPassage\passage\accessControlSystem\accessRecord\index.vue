<template>
  <tree-page-common v-model="state.tableHeight" :leftBool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
        <el-form-item prop="name">
          <el-input v-model="formInline.name" placeholder="人员姓名" />
        </el-form-item>
        <el-form-item prop="equipmentName">
          <el-input v-model="formInline.equipmentName" placeholder="设备名称" />
        </el-form-item>
        <el-form-item prop="recordType">
          <el-select v-model="formInline.recordType" placeholder="人员类型">
            <el-option v-for="item in state.options1" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="evenId">
          <el-input v-model="formInline.evenId" placeholder="通行方式" />
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="state.date" type="daterange" value-format="YYYY-MM-DD" start-placeholder="开始时间"
            end-placeholder="结束时间" :clearable="false" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" :width="item.width">
          <template #default="scope">
            <div v-if="item.prop === 'inoutPhoto'">
              <img @click="preview(scope.row)" :src="state.baseUrl + scope.row.inoutPhoto" style="width: 30px;height: 30px;" alt="">
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />

      <el-dialog v-model="dialogVisible" title="预览">
        <img w-full :src="imgUrl" alt="抓拍照片" style="width: 100%;" />
      </el-dialog>
    </template>
  </tree-page-common>
</template>

<script setup>
import {
  Search, Refresh
} from '@element-plus/icons-vue'
import { inOutList } from "@/api/iotManagement/door.js";
import { dayjs, ElTag } from "element-plus";

const route = useRoute()
const dialogVisible = ref(false)
const imgUrl = ref('')

let formInlineRef = ref()
const formInline = reactive({})

const state = reactive({
  baseUrl: import.meta.env.VITE_BASE_URL,
  date: [],
  options1: [{
    value: '1',
    label: '访客',
  }, {
    value: '0',
    label: '住户',
  }],
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'visitorName',
      label: '姓名'
    },
    {
      prop: 'recordType',
      label: '人员类型',
      formatter: (row, column, cellValue) => {
        if (cellValue == 0) {
          return h(ElTag, { type: "success" }, { default: () => "住户" })
        } else if (cellValue == 1) {
          return h(ElTag, { type: "success" }, { default: () => "访客" })
        }
      }
    },
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'evenId',
      label: '通行方式'
    },
    {
      prop: 'openTime',
      label: '通行时间',
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    },
    {
      prop: 'inoutPhoto',
      label: '抓拍照片'
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

const preview = (row) => {
  imgUrl.value = state.baseUrl + row.inoutPhoto
  dialogVisible.value = true
}

//分页
const getList = () => {
  let query = {
    ...route.query,
    ...formInline,
    ...state.pagetion,
    startDate: state.date[0],
    endDate: state.date[1]
  }

  inOutList(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

//重置方法
const onReset = () => {
  formInlineRef.value.resetFields()
  state.date = []
  onSubmit()
}

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped></style>
