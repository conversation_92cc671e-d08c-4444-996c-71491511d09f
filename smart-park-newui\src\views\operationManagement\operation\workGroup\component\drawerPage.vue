<template>
  <el-drawer :modelValue="drawer" :before-close="cancelClick">
    <template #header>
      <h4>工作班组信息</h4>
    </template>
    <template #default>
      <el-form ref="ruleFormRef" :model="form" label-width="100px" label-suffix=":">
        <el-row>
          <el-col>
            <el-form-item label="班组名称" prop="name">
              {{ form.name }}
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="负责人" prop="leaderName">
              {{ form.leaderName }}
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="成员" prop="workGroupUserList">
              <template v-for="(item, index) in form.workGroupUserList" :key="index">
                <span>
                  {{ item.userName }}
                </span>
                <span v-if="index != form.workGroupUserList.length - 1">、</span>
              </template>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="备注" prop="remark">
              {{ form.remark }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
const props = defineProps({
  drawer: {
    type: Boolean,
    default: false,
  },
});
let { drawer } = toRefs(props);
const emit = defineEmits(['cancelClick']);
const form = ref({});

const cancelClick = () => {
  emit('cancelClick');
};

defineExpose({
  form,
});
</script>
