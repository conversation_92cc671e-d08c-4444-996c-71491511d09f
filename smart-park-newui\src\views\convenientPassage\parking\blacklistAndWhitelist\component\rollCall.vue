<template>
  <page-common v-model="state.tableHeight" :leftBool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
        <el-form-item prop="licensePlateNumber">
          <el-input v-model="formInline.licensePlateNumber" placeholder="车牌号" />
        </el-form-item>
        <el-form-item prop="person">
          <el-input v-model="formInline.person" placeholder="联系人" />
        </el-form-item>
        <el-form-item prop="type">
          <el-select v-model="formInline.type" placeholder="车辆类型">
            <el-option v-for="item in state.options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="time">
          <el-date-picker v-model="formInline.time" type="datetimerange" range-separator="-" start-placeholder="创建时间开始"
            end-placeholder="创建时间结束" value-format="YYYY-MM-DD HH:mm" format="YYYY-MM-DD HH:mm" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addRoll">{{ panel == 1 ? '新增黑名单' : '新增白名单' }}</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" :width="item.width" />
        <el-table-column align="center" label="控制" width="300">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link icon="Refresh" type="primary" @click="synchronization(scope.row)">同步</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteMessage(scope.row.id)">移出{{ panel == 1 ? '黑' :
              '白'}}名单</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <modal-page ref="modal" @submit="getList" :title="state.title" />
    </template>
  </page-common>
</template>

<script setup>
import dayjs from 'dayjs'
import modalPage from './modalPage.vue';
import { rollList, deleteAPI,syncData } from "@/api/iotManagement/parking.js";
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue'
import { calcPageNo } from '@/utils/util.js'
const props = defineProps({
  panel: {
    type: Number,
    default: 1
  },
});

console.log(props.panel)

let formInlineRef = ref()

const modal = ref();

const formInline = reactive({})

const state = reactive({
  title: '',
  options: [{
    value: '1',
    label: '小型车',
  }, {
    value: '2',
    label: '中型车',
  }, {
    value: '3',
    label: '大型车',
  }],
  syncStatusOptions:{
    0: '未同步',
    1: '已同步'
  },
  syncStatusColors:{
    0: '#E6A23C',
    1: '#67C23A'
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'licensePlateNumber',
      label: '车牌号'
    },
    {
      prop: 'color',
      label: '车辆颜色'
    },
    {
      prop: 'type',
      label: '车辆类型',
      formatter: (row, column, cellValue) => {
        if (cellValue == 1) {
          return '小型车'
        } else if (cellValue == 2) {
          return '中型车'
        } else if (cellValue == 3) {
          return '大型车'
        }
      }
    },
    {
      prop: 'person',
      label: '联系人'
    },
    {
      prop: 'phone',
      label: '联系方式'
    },
    {
      prop: 'cause',
      label: '备注'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      formatter: (row, column, cellValue) => {
        return cellValue ? dayjs(cellValue).format("YYYY-MM-DD HH:mm") : ''
      }
    },
    {
      prop: 'syncStatus',
      label: '同步状态',
      formatter: (row, column, cellValue) => {
        let color = state.syncStatusColors[cellValue];
        return h("div", [
          h("span", {
            class: "status-circle",
            style: "background-color: " + color,
          }),
          state.syncStatusOptions[cellValue],
        ]);
      },
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

const editHandle = (row) => {
  if (props.panel == 1) {
    state.title = '编辑黑名单';
  } else {
    state.title = '编辑白名单';
  }
  modal.value.open();
  nextTick(() => {
    if (row.equipmentIds) {
      row.equipmentList = row.equipmentIds.split(",")
    }
    Object.assign(modal.value.form, { ...row });
  });
}

const addRoll = () => {
  if (props.panel == 1) {
    state.title = '新增黑名单';
  } else {
    state.title = '新增白名单';
  }
  modal.value.form.panel = props.panel
  modal.value.form.id = undefined
  modal.value.open();
}

// 同步
const synchronization = (row) => {
  ElMessageBox.confirm('是否同步当前车辆?', '提醒', {
    type: 'warning',
  }).then(() => {
    let query = {
      id: row.id
    }
    syncData(query).then(res => {
      if (res.success) {
        ElMessage.success('同步成功');
        getList()
      } else {
        ElMessage.error(res.errorMessage);
      }
    })
  })
}

const deleteMessage = (id) => {
  ElMessageBox.confirm('是否移出当前名单?', '提醒', {
    type: 'warning',
  }).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize)

    deleteAPI({ id: id }).then(res => {
      if (res.success) {
        ElMessage.success('移出成功');
        getList()
      } else {
        ElMessage.error(res.errorMessage);
      }
    })
  });
}

//分页
const getList = () => {
  let query = {
    panel: props.panel,
    licensePlateNumber: formInline.licensePlateNumber,
    person: formInline.person,
    type: formInline.type,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize
  }

  if (formInline.time) {
    query.createStartDate = formInline.time[0]
    query.createEndDate = formInline.time[1]
  }

  rollList(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

//重置方法
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

defineExpose({
  getList,
  state
})
</script>

<style lang='less' scoped></style>
