package com.soft.webadmin.dao.sparePart;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.sparePart.SparePartQuantityChangeQueryDTO;
import com.soft.webadmin.model.sparePart.SparePartQuantityChange;
import com.soft.webadmin.vo.sparePart.SparePartInoutRecordVO;
import com.soft.webadmin.vo.sparePart.SparePartQuantityChangeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备品备件库存数量变更Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface SparePartQuantityChangeMapper extends BaseMapper<SparePartQuantityChange> {

    /**
     * 数据查询：物资管理-库存变化记录
     * @param queryDTO
     * @return
     */
    List<SparePartQuantityChangeVO> getList(SparePartQuantityChangeQueryDTO queryDTO);

    /**
     * 数据查询：出入库管理-出入库明细
     * @param inoutId
     * @return
     */
    List<SparePartInoutRecordVO> getListByInoutId(@Param("inoutId") Long inoutId);

}
