package com.soft.webadmin.dto.contingency;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * GroupMemberDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("GroupMemberDTO对象")
@Data
public class GroupMemberDTO {

    @ApiModelProperty(value = "成员用户id")
    @NotNull(message = "成员不能为空！")
    private Long userId;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空！")
    private String phone;

    @ApiModelProperty(value = "手机短号")
    private String shortPhone;

    @ApiModelProperty(value = "组内职务：1组长、2组员")
    @NotNull(message = "组内职务不能为空！")
    private Integer duties;

}
