{"title": "基础控件", "name": "base", "list": [{"component": "VText", "label": "文字", "propValue": "编辑文字", "icon": "icon-wen<PERSON><PERSON><PERSON>", "type": "text", "params": {"equipInfo": {}, "classInfo": {"classList": []}}, "style": {"width": 200, "height": 28, "lineHeight": 28, "zIndex": 0, "fontSize": 16, "fontWeight": 400, "letterSpacing": 0, "textAlign": "left", "color": "#000", "backgroundColor": "", "borderRadius": 0}}, {"component": "VInsertImg", "label": "插入图片", "icon": "icon-charu<PERSON><PERSON>", "type": "insert", "style": {"width": 50, "height": 50, "borderRadius": 0, "opacity": 1, "zIndex": 0, "transform": 0}}, {"component": "VLine", "label": "直线", "icon": "icon-zhixian", "type": "svg", "points": [{"x": 10, "y": 10, "node": 1}, {"x": 150, "y": 10, "node": 2}, {"x": 290, "y": 10, "node": 1}], "style": {"lineWidth": 6, "lineColor": "#29b6f2"}}, {"component": "<PERSON><PERSON><PERSON><PERSON>", "label": "管线", "image": "base/管线.png", "type": "svg", "points": [{"x": 10, "y": 10, "node": 1}, {"x": 150, "y": 10, "node": 2}, {"x": 290, "y": 10, "node": 1}], "style": {"lineWidth": 6, "strokeDasharray": 20, "lineColor": "#29b6f2", "lineBgColor": "#ecf5ff", "rateOfFlow": 1000, "zIndex": 0}}]}