<template>
  <div style="height: 100%;overflow: hidden;">
    <transition name="el-zoom-in-center">
      <listTable v-show="0 === pageIndex" ref="table" @showPage="showPage"></listTable>
    </transition>
    <transition name="el-zoom-in-center">
      <detail v-show="1 === pageIndex" ref="detailRef" @showPage="showPage"></detail>
    </transition>
  </div>
</template>

<script setup>
import listTable from './component/tablePage.vue'
import detail from '@/components/deviceDetail/index.vue'

const pageIndex = ref(0)

// 设备列表
const table = ref()

// 设备详情
const detailRef = ref()

const showPage = (index, equipmentId) => {
  pageIndex.value = index;
  if (index == 0) {
    table.value.getEquipList()
  } else {
    detailRef.value.equipmentId = equipmentId
    detailRef.value.init();
  }
}
</script>
