package com.soft.webadmin.controller.contingency;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.contingency.GroupDTO;
import com.soft.webadmin.dto.contingency.GroupQueryDTO;
import com.soft.webadmin.service.contingency.GroupService;
import com.soft.webadmin.vo.contingency.GroupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 应急小组控制器类
 * 
 * <AUTHOR>
 * @date 2024-04-17
 */
@Api(tags = "应急小组")
@RestController
@RequestMapping("/contingency/group")
public class GroupController {

    @Autowired
    private GroupService groupService;

    @ApiOperation(value = "查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<GroupVO>> getPage(GroupQueryDTO queryDTO) {
        return ResponseResult.success(groupService.list(queryDTO));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public ResponseResult<GroupVO> detail(@RequestParam Long id) {
        return ResponseResult.success(groupService.detail(id));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@Validated @RequestBody GroupDTO saveDTO) {
        groupService.saveOrUpdate(saveDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        groupService.delete(id);
        return ResponseResult.success();
    }

}
