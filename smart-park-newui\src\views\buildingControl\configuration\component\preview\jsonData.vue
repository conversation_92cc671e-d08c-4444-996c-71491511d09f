<template>
  <el-drawer v-model="state.drawer" title="josn" direction="rtl">
    <JsonViewer :value="state.data" :expand-depth="5" :expanded="true" copyable boxed sort/>
  </el-drawer>
</template>

<script setup>
import {webtopoStore} from '@/store/modules/webtopo.js'
import {JsonViewer} from "vue3-json-viewer"
import "vue3-json-viewer/dist/index.css";


const webtopo = webtopoStore()
let { componentData } = storeToRefs(webtopo)

const state = reactive({
  drawer: false,
  data:[]
})


const open = () => {
  state.data = [...componentData.value]
  state.drawer = true
}



defineExpose({
  open
})
</script>

<style lang='less' scoped></style>