package com.soft.webadmin.dto.shifts;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * ShiftsRosterDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsRosterDTO对象")
@Data
public class ShiftsSettingQueryDTO extends MyPageParam {

    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private String businessType;

    @ApiModelProperty(value = "班次名称")
    private String shiftsName;
}
