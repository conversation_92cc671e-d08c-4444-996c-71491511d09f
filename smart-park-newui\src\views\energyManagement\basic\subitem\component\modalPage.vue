<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef" :width="450">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="120px" label-suffix=":">
      <el-row>
        <el-col>
          <el-form-item label="设备类型" prop="equipmentType">
            <el-select v-model="form.equipmentType" placeholder="请选择设备类型" @change="loadTreeData" :disabled="true">
              <el-option v-for="item in state.equipmentTypeOptions" :key="item.value" :value="item.value"
                :label="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="上级分类" prop="parentId">
            <el-cascader ref="cascader" v-model="form.parentId" :options="state.treeList" :props="optionsProps" clearable
              placeholder="请选择上级分类" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="分类名称" prop="energyName">
            <el-input v-model="form.energyName" placeholder="请输入分类名称" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="关联设备" prop="equipmentList"  v-if="title != '新增分类'">
            <el-select v-model="form.equipmentList" multiple placeholder="请选择关联设备" filterable>
              <el-option v-for="item in state.equipmentList" :key="item.equipmentId" :label="item.equipmentName" :value="item.equipmentId" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { getSubitemTreeAPI, saveSubitemAPI, deleteSubitemAPI } from '@/api/energyManagement/subitem.js';
import { equipmentListAPI } from '@/api/iotManagement/equipManage.js';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
const { title } = toRefs(props);
const ruleFormRef = ref();
const dialog = ref();
const cascader = ref();
const form = reactive({});
const state = reactive({
  equipmentList:[],
  treeList: [],
  equipmentTypeOptions: [
    { label: '水表', value: '水表' },
    { label: '电表', value: '电表' },
    { label: '气表', value: '气表' },
  ],
  rules: {
    equipmentType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
    energyName: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }],
    parentId: [{ required: true, message: '上级分类不能为空', trigger: 'blur' }],
  },
});
const emit = defineEmits(['submit']);

const optionsProps = {
  checkStrictly: true,
  label: 'energyName',
  value: 'id',
  expandTrigger: 'hover',
};

const open = () => {
  dialog.value.open();
  loadTreeData();
};

const loadTreeData = () => {
  getSubitemTreeAPI({ equipmentType: form.equipmentType }).then((res) => {
    state.treeList = res.data;
  });
  equipmentListAPI({equipmentType:form.equipmentType,energyTypeId:form.id}).then(res=>{
    state.equipmentList = res.data
  })
};

/** 保存 */
const submit = () => {
  if (cascader.value.getCheckedNodes().length > 0) {
    const node = cascader.value.getCheckedNodes()[0].data;
    form.parentId = node.id;
  }
  if(form.equipmentList){
    form.equipmentIds = form.equipmentList.join(",")
  }
  saveSubitemAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success(title.value + '成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

defineExpose({
  form,
  open,
});
</script>
