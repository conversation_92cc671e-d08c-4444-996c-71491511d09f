package com.soft.webadmin.controller.face;


import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.face.FaceSearchCaptureDTO;
import com.soft.webadmin.service.face.FaceSearchService;
import com.soft.webadmin.vo.face.FaceSearchCaptureVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "人脸搜索接口管理")
@RestController
@RequestMapping("/face/search")
public class FaceSearchController {

    @Resource
    private FaceSearchService faceSearchService;


    @ApiOperation("抓拍记录")
    @PostMapping("/capture")
    public ResponseResult<MyPageData<FaceSearchCaptureVO>> capture(@RequestBody FaceSearchCaptureDTO faceSearchCaptureDTO) {
        MyPageData<FaceSearchCaptureVO> pageData = faceSearchService.capture(faceSearchCaptureDTO);
        return ResponseResult.success(pageData);
    }

}
