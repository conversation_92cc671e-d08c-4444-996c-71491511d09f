<template>
  <page-common :operateBool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="dictName">
          <el-tree-select v-model="formInline.equipmentId" :data="state.data" filterable :render-after-expand="false"
            node-key="id" placeholder="选择设备通道" clearable />
        </el-form-item>
        <el-form-item prop="date">
          <el-date-picker v-model="formInline.date" type="datetimerange" range-separator="-" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD hh:mm" format="YYYY-MM-DD hh:mm" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleNodeClick">查询</el-button>
          <!-- <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button> -->
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <div class="video">
        <div style="width: 100%;height: 100%;" ref="videoMain" v-if="showVideo">
        </div>
        <el-empty description="视频播放失败" v-else>
          <el-button type="primary" @click="reconnection">点击重连</el-button>
        </el-empty>
      </div>
    </template>
  </page-common>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import DPlayer from 'dplayer';
import { perviewURLs, equipTree } from '@/api/iotManagement/realtime.js'

let url
let dp;
let videoMain = ref()
const showVideo = ref(true)

let formInlineRef = ref()
const formInline = reactive({
  date:[]
})

const state = reactive({
  data: []
})

onMounted(() => {
  getMonitor()
})

onBeforeUnmount(() => {
  videoDestroy()
})

// 获取监控树形
const getMonitor = () => {
  equipTree({
    subSystemEnums: 'MONITOR'
  }).then(res => {
    state.data = treeTransfer(res.data)
  })
}

// 树形转化
const treeTransfer = (list) => {
  return list.map(item => {
    if (item.children) {
      item.children = treeTransfer(item.children)
    }
    return {
      ...item,
      label: item.name,
      disabled: item.type == 1 && !item.children
    }
  });
}

const handleNodeClick = () => {
  if(!formInline.equipmentId){
    return ElMessage.warning("请选择设备通道！");
  }

  if(!formInline.date.length){
    return ElMessage.warning("请选择时间段！");
  }

  let query = {
    equipmentId: formInline.equipmentId,
    startTime : formInline.date[0],
    endTime : formInline.date[1]
  }

  perviewURLs(query).then(res => {
    handleChoose(res.data)
    // handleChoose('https://api.dogecloud.com/player/get.mp4?vcode=5ac682e6f8231991&userId=17&ext=.mp4')
  })
}

const handleChoose = (vidoeurl) => {
  url = vidoeurl
  if (dp) {
    handleChange()
  } else {
    if (showVideo.value) {
      initVideo()
    } else {
      reconnection()
    }
  }
}

// 初始化摄像头
const initVideo = () => {
  dp = new DPlayer({
    container: videoMain.value,
    live: true,
    autoplay: true,
    preventClickToggle: true,
    screenshot: true,
    hotkey: true,
    mutex: false,
    preload: 'auto',
    volume: 0,
    video: {
      url,
      type: 'customFlv',
    },
  });

  dp.on('play', function () {
    let flvPlayer = dp.video
    if (flvPlayer.buffered.length) {
      let end = flvPlayer.buffered.end(0);//获取当前buffered值
      let diff = end - flvPlayer.currentTime;//获取buffered与currentTime的差值
      if (diff >= 2.5) {//如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定
        dp.seek(flvPlayer.buffered.end(0));
      }
    }
  });

  dp.on('error', function () {
    ElMessage({
      message: '播放失败',
      type: 'error',
    })
    videoDestroy()
    showVideo.value = false
  })
}

// 重新连接  
const reconnection = () => {
  showVideo.value = true
  nextTick(() => {
    initVideo()
  })
}

// 切换摄像头
const handleChange = () => {

  nextTick(() => {
    dp.switchVideo({ url })
  })
}

// 视频销毁
const videoDestroy = () => {
  if (dp) {
    dp.destroy()
    dp = null
  }
}
</script>

<style lang='less' scoped>
.video {
  // border: solid red 1px;
  height: 100%;
  background: #FFFFFF;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  background-image: url('@/assets/img/videobg.png');
  background-size: 100% 100%;
}
</style>