<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.shifts.ShiftsRosterMapper">
    <resultMap type="com.soft.webadmin.model.shifts.ShiftsRoster" id="ShiftsRosterResult">
        <result property="rosterId" column="roster_id" />
        <result property="businessType" column="business_type" />
        <result property="showName" column="show_name" />
        <result property="sex" column="sex" />
        <result property="age" column="age" />
        <result property="phone" column="phone" />
        <result property="deptId" column="dept_id" />
        <result property="postStatus" column="post_status" />
        <result property="cardNo" column="card_no" />
        <result property="joinJobDate" column="join_job_date" />
        <result property="leaveJobDate" column="leave_job_date" />
        <result property="leaveJobReason" column="leave_job_reason" />
        <result property="annexPath" column="annex_path" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createTime" column="create_time" />
        <result property="createUserId" column="create_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="updateUserId" column="update_user_id" />
    </resultMap>

    <sql id="selectShiftsRosterVo">
        select roster_id, business_type, show_name, sex, age, phone, dept_id, post_status, card_no, join_job_date, leave_job_date, leave_job_reason,
               annex_path, deleted_flag, create_time, create_user_id, update_time, update_user_id from sp_shifts_roster
    </sql>


    <select id="queryList" resultType="com.soft.webadmin.vo.shifts.ShiftsRosterVO">
        select csu.roster_id, csd.dept_name, csu.show_name,  csu.sex, csu.phone, csu.age,
        csu.join_job_date,  csu.post_status,csu.card_no,
        group_concat(distinct csp.post_name order by csp.post_id) as post_names
        from sp_shifts_roster csu
        left join common_sys_dept csd on csu.dept_id = csd.dept_id and csd.deleted_flag = 1
        left join sp_shifts_roster_post csup on csu.roster_id = csup.roster_id
        left join common_sys_post csp on csup.post_id = csp.post_id and csp.deleted_flag = 1
        <where>
            csu.deleted_flag = 1
            <if test="showName != null and showName != ''">
                and csu.show_name like concat('%', #{showName}, '%')
            </if>
            <if test="businessType != null and businessType != ''">
                and csu.business_type = #{businessType}
            </if>
            <if test="deptId != null">
                and csu.dept_id = #{deptId}
            </if>
            <if test="postId != null">
                and csp.post_id = #{postId}
            </if>
            <if test="postStatus != null">
                and csu.post_status = #{postStatus}
            </if>
            <if test="rosterIds != null">
                AND csu.roster_id IN
                <foreach collection="rosterIds" item="rostId" separator="," open="(" close=")">
                    #{rostId}
                </foreach>
            </if>
        </where>
        group by csu.roster_id
        order by  csu.post_status desc, csu.join_job_date  desc
    </select>

    <insert id="batchInsert" parameterType="list">
        insert into sp_shifts_roster
        (
         roster_id,
         business_type,
         show_name,
         sex,
         age,
         phone,
         dept_id,
         post_status,
         card_no,
         join_job_date,
         leave_job_date,
         leave_job_reason,
         deleted_flag,
         create_time,
         create_user_id,
         update_time,
         update_user_id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.rosterId},
            #{item.businessType},
            #{item.showName},
            #{item.sex},
            #{item.age},
            #{item.phone},
            #{item.deptId},
            #{item.postStatus},
            #{item.cardNo},
            #{item.joinJobDate},
            #{item.leaveJobDate},
            #{item.leaveJobReason},
            #{item.deletedFlag},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateTime},
            #{item.updateUserId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        show_name = VALUES(show_name),
        sex = VALUES(sex),
        age = VALUES(age),
        phone = VALUES(phone),
        dept_id = VALUES(dept_id),
        card_no = VALUES(card_no),
        join_job_date = VALUES(join_job_date),
        update_user_id = VALUES(update_user_id),
        update_time = VALUES(update_time),
        deleted_flag = VALUES(deleted_flag)
    </insert>

    <select id="getRoster" resultMap="ShiftsRosterResult">
        select csu.roster_id, csu.show_name
        from sp_shifts_roster csu
        left join common_sys_dept csd on csu.dept_id = csd.dept_id and csd.deleted_flag = 1
        <where>
            csu.deleted_flag = 1
            and csu.show_name =  #{showName}
            and csu.business_type = #{businessType}
            and csd.dept_name = #{deptName}
        </where>
    </select>

</mapper>