import { request } from '@/utils/request';

// 分页查询
export const getCheckPlanPageAPI = (data) => {
  return request('get', '/check/plan/getPage', data, 'F');
};

// 启停用
export const updateStateAPI = (data) => {
  return request('post', '/check/plan/updateState', data, 'F');
};

// 保存
export const saveCheckPlanAPI = (data) => {
  return request('post', '/check/plan/save', data);
};

// 删除
export const deleteCheckPlanAPI = (data) => {
  return request('post', '/check/plan/delete', data, 'F');
};

// 查询计划下的点位
export const getCheckPlanPointListAPI = (data) => {
  return request('get', '/check/plan/point/list', data, 'F');
};

// 查询智能巡检设备
export const getEquipmentListAPI = (data) => {
  return request('get', '/check/plan/equipment/list', data, 'F');
};
