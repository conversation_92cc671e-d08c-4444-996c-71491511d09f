<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.WorkOrderSparePartMapper">
    <resultMap type="com.soft.webadmin.model.check.WorkOrderSparePart" id="WorkQuoteRelationResult">
        <result property="id" column="id" />
        <result property="orderId" column="order_id" />
        <result property="sparePartId" column="spare_part_id" />
        <result property="sparePartName" column="spare_part_name" />
        <result property="classifyName" column="classify_name" />
        <result property="receiveQuantity" column="receive_quantity" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectWorkQuoteRelationVo">
        select id, order_id, spare_part_id, spare_part_name, spare_part_classify_name, receive_quantity, create_user_id, create_time, update_user_id, update_time from sp_work_order_spare_part
    </sql>
    
</mapper>