<template>
  <dialog-common ref="dialog" title="员工详情" :width="900" :showButton="false"
                 class="dialogTextarea card-textBg" >
    <el-form ref="ruleFormRef" label-position="top" label-suffix=":">
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <el-row :gutter="40" style="margin: 0">
        <el-col :span="12">
          <el-form-item label="姓名">
            {{ form.showName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别">
            {{ props.sexOptions[form.sex] }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年龄">
            {{ form.age }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号">
            {{ form.phone }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证号">
            {{ form.cardNo }}
          </el-form-item>
        </el-col>
      </el-row>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">入职信息</div>
      </div>
      <el-row :gutter="40" style="margin: 0">
        <el-col :span="12">
          <el-form-item label="部门" >
            {{ form.sysDeptVO?.deptName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位">
            {{ (form.sysDeptPosts || []).map(item => item.postShowName).toString() }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入职日期" >
            {{ dayjs(form.joinJobDate).format('YYYY-MM-DD') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任区域" >
            <el-tag type="info" v-for="(item,index) in form.rosterLiabilityVOS" style="margin: 3px">
              {{ item.fullName }}
            </el-tag>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-for="(item,index) in form.certificateVOS" :gutter="40" style="margin: 0">
        <el-col :span="12">
          <el-form-item label="操作证名称" >
            {{ item.certificateName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期">
            {{ dayjs(item.certificateStartTime).format('YYYY-MM-DD') }} - {{ dayjs(item.certificateEndTime).format('YYYY-MM-DD')  }}
          </el-form-item>
        </el-col>
      </el-row>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">材料附件</div>
      </div>
      <img-video :list="pictureVideo(form.annexPath)"></img-video>
      <div v-show="form.postStatus == 0">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">离职原因</div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="离职日期" prop="leaveJobDate">
              {{ dayjs(form.leaveJobDate).format('YYYY-MM-DD')}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="离职原因" prop="leaveJobReason">
              {{ form.leaveJobReason }}
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </dialog-common>
</template>

<script setup>
import dayjs from "dayjs";
import {pictureVideo} from "@/utils/util.js";

const props = defineProps({
  sexOptions:{
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['submit']);

const ruleFormRef = ref();
const dialog = ref();

const form = reactive({});
const open = () => {
  dialog.value.open();
}

defineExpose({
  form,
  open
});
</script>

<style scoped lang="less">
</style>
