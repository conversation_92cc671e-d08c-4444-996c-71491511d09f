<template>
  <page-common v-model="state.tableHeight" :operate-bool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="userName">
          <el-input v-model="formInline.userName" placeholder="姓名" />
        </el-form-item>
        <el-form-item prop="phone">
          <el-input v-model="formInline.phone" placeholder="手机号" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableData" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" :witdh="item.witdh" />
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize" :total="state.pageParam.total"
        @size-change="sizeChange" @current-change="currentChange" />
    </template>
  </page-common>
</template>

<script setup>
import { getOneCardPageAPI } from "@/api/iotManagement/oneCard.js";
import { Refresh, Search } from "@element-plus/icons-vue";
import { dayjs, ElTag } from "element-plus";
import { reactive, ref } from "vue";

const formInlineRef = ref();
const formInline = reactive({
  userName: "",
  phone: []
});
const state = reactive({
  title: "",
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'cardNo',
      label: '卡号'
    },
    {
      prop: 'cardType',
      label: '卡类型'
    },
    {
      prop: 'userName',
      label: '姓名'
    },
    {
      prop: 'phone',
      label: '手机号'
    },
    {
      prop: 'cardStatus',
      label: '状态',
      formatter: (row, column, cellValue) => {
        if (cellValue === 'NORMAL') {
          return '正常'
        } else if (cellValue === 'STOP') {
          return '停用'
        }
        return '挂失'
      }
    },
    {
      prop: 'deptName',
      label: '部门'
    },
    {
      prop: 'startDate',
      label: '生效日期',
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      },
      witdh: 160
    },
    {
      prop: 'endDate',
      label: '失效日期',
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      },
      witdh: 160
    }
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  }
});

onMounted(() => {
  getList();
});

/**
 * 查询列表数据
 */
const getList = () => {
  let query = {
    userName: formInline.userName,
    phone: formInline.phone,
    ...state.pageParam,
  };
  getOneCardPageAPI(query).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

/**
 * 提交查询
 */
const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

/**
 * 重置查询
 */
const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

/**
 * 分页查询（页码）
 * @param pageNum
 */
const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

/**
 * 分页查询（条数）
 * @param pageSize
 */
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

</script>
