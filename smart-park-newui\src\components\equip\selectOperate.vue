<template>
  <el-dialog v-model="dialog" title="选择设备" @submit="submit" @close="dialogClose" class="dialogCommon" :width="1050">
    <el-form :inline="true" ref="formInlineRef" :model="formInline">
      <el-form-item class="item__content-noBg" prop="equipmentWord">
        <el-input v-model="formInline.equipmentWord" placeholder="设备编号或名称" />
      </el-form-item>
      <el-form-item class="item__content-noBg" prop="equipmentTypeId">
        <el-tree-select v-model="formInline.equipmentTypeId" :data="equipmentTypeIdsOptions"
          :render-after-expand="false" ref="tree" clearable check-strictly node-key="id" :props="{ label: 'name' }"
          placeholder="设备类型" />
      </el-form-item>
      <el-form-item class="item__content-noBg" prop="spaceId">
        <el-cascader v-model="formInline.spaceId" :options="state.spaceOptions" :props="optionsProps" clearable
          placeholder="设备安装位置" />
      </el-form-item>
      <el-form-item class="item__content-noBg">
        <el-tooltip content="查询" placement="top" effect="light">
          <el-button type="primary" icon="Search" @click="onSubmit" circle />
        </el-tooltip>
        <el-tooltip content="重置" placement="top" effect="light">
          <el-button type="primary" icon="Refresh" @click="onReset" circle />
        </el-tooltip>
      </el-form-item>
    </el-form>
    <el-table show-overflow-tooltip :data="state.tableData" :height="state.tableHeight" ref="multipleTableRef" @select="select"
      @selection-change="selection" @row-click="rowClick" @select-all="selectAll">
      <el-table-column type="selection" width="55" v-if="multiple" />
      <el-table-column prop="equipmentCode" show-overflow-tooltip label="设备编号" />
      <el-table-column prop="equipmentName" show-overflow-tooltip label="设备名称" />
      <el-table-column prop="equipmentTypeName" show-overflow-tooltip label="设备类型" />
      <el-table-column prop="spaceFullName" show-overflow-tooltip label="安装位置" />
    </el-table>
    <el-pagination background layout="total, prev, pager, next" :current-page="state.pageParam.pageNum"
      :page-size="state.pageParam.pageSize" :total="state.pageParam.total" @size-change="sizeChange"
      @current-change="currentChange" />
    <div v-if="multiple">
      <h4 style="margin: 10px 0">已选择的设备：</h4>
      <el-tag v-for="tag in selectedList" :key="tag.equipmentId" class="tag" closable @close="handleClose(tag)">
        {{ tag.equipmentName }}
      </el-tag>
      <el-button class="button-new-tag tag" size="small" @click="clearSelected"> x 清除所有 </el-button>
    </div>
    <template #footer v-if="multiple">
      <span class="dialog-footer">
        <el-button @click="dialogClose">关闭</el-button>
        <el-button type="primary" @click="submit">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { getEquipListAPI } from '@/api/operationManagement/equipManage.js';
import { treeAPI } from '@/api/iotManagement/space.js'

import { equipTypTreeAPI } from '@/api/operationManagement/equipType.js'

import { ElTag } from 'element-plus'

const props = defineProps({
  multiple: {
    type: Boolean,
    default: true
  },
  category:{
    type: String,
    default: ''
  }
})

let { multiple } = toRefs(props)

const emit = defineEmits(['submit', 'rowClick']);
const multipleTableRef = ref();
const dialog = ref();
const ruleFormRef = ref();
const formInlineRef = ref();
const tree = ref()
let selectedList = ref([]);

// 设备类型（子系统类型）
const equipmentTypeIdsOptions = ref([])

const formInline = reactive({});

// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'id',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};
const state = reactive({
  spaceOptions: [],
  tableData: [],
  tableHeight: 323,
  pageParam: {
    pageNum: 1,
    pageSize: 5,
    total: 0,
  },
  title: '',
  drawer: false,
  equipmentId: undefined,
});

const open = () => {
  dialog.value = true;
  getSpaceTree();
  getEquipGroup()
  getList();
};

// 获取设备类别列表
const getEquipGroup = async () => {
  const res = await equipTypTreeAPI({category: props.category})
  equipmentTypeIdsOptions.value = res.data
}


const getSpaceTree = () => {
  treeAPI({ deep: 3 }).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
};

const getList = () => {
  let params = {
    category: props.category,
    excludeStatus: 5,
    ...formInline,
    ...state.pageParam
  };
  getEquipListAPI(params).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
    // 已选中的设备在表格内设置选中状态
    nextTick(() => {
      if (Array.isArray(selectedList.value) && selectedList.value.length > 0) {
        selectedList.value.map((e) => {
          const index = state.tableData.findIndex((item) => item.equipmentId === e.equipmentId);
          if (index !== -1) {
            multipleTableRef.value.toggleRowSelection(state.tableData[index], true);
          }
        });
      }
    });
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 5,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

/** 判断是否为取消选择 */
const select = (e, v) => {
  let selected = e.length && e.indexOf(v) !== -1;
  if (!selected) {
    selectedList.value.splice(
      selectedList.value.findIndex((item) => item.equipmentId === v.equipmentId),
      1
    );
  }
};

/** 处理选中设备 */
const selection = (val) => {
  val.map((item) => {
    selectedList.value.push(item);
  });
  let obj = {};
  selectedList.value = selectedList.value.reduce((cur, next) => {
    obj[next.equipmentId] ? '' : (obj[next.equipmentId] = true && cur.push(next));
    return cur;
  }, []);
};

const selectAll = (selection) => {
  if (!selection.length) {
    state.tableData.forEach(equipment => {
      selectedList.value.splice(selectedList.value.findIndex(selected => selected.equipmentId === equipment.equipmentId), 1)
    })
  }
}

/** 清除已选的设备 */
const clearSelected = () => {
  multipleTableRef.value.clearSelection();
  selectedList.value = [];
};

/** 删除已选的设备tag */
const handleClose = (tag) => {
  selectedList.value.splice(
    selectedList.value.findIndex((item) => item.equipmentId === tag.equipmentId),
    1
  );
};

/* 单行点击 */
const rowClick = (row) => {
  if (!multiple.value) {
    dialogClose()
    emit('rowClick', row)
  }
}

/** 确认 */
const submit = () => {
  dialog.value = false;
  emit('submit', selectedList);
};

/** 关闭 */
const dialogClose = () => {
  dialog.value = false;
  if (multiple.value) { // 多选刷新
    state.pageParam = {
      pageNum: 1,
      pageSize: 5,
      total: 0,
    };
    formInlineRef.value.resetFields();
  }
};

defineExpose({
  selectedList,
  open,
});
</script>

<style lang="less" scoped>
.dialogCommon {
  .dialog__header {
    background-color: #f5f7fa;
    margin-right: 0;
  }
}

.el-table {
  :deep(.el-table__header th.el-table__cell) {
    background-color: #fff !important;
    color: #939395;
  }
}

.tag {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
