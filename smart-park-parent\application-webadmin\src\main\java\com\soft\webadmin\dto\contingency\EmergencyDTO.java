package com.soft.webadmin.dto.contingency;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * EmergencyDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EmergencyDTO对象")
@Data
public class EmergencyDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "预案名称")
    @NotBlank(message = "预案名称不能为空！")
    private String name;

    @ApiModelProperty(value = "应急事件id")
    @NotNull(message = "应急事件不能为空！")
    private Long eventId;

    @ApiModelProperty(value = "预案附件")
    private String annex;

    @ApiModelProperty(value = "处理节点")
    @NotEmpty(message = "处理节点不能为空！")
    @Valid
    private List<EmergencyNodeDTO> nodeDTOList;

}
