package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.*;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import com.soft.webadmin.vo.check.WorkGroupVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 工作班组对象 sp_work_group
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_work_group")
public class WorkGroup extends BaseModel {

    @TableId(value = "id")
    private Long id;

    /**
     * 班组名称
     */
    private String name;

    /**
     * 负责人id
     */
    private Long leaderId;

    /**
     * 成员数量
     */
    private Integer memberCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识，1正常；-1已删除
     */
    @TableLogic(value = "1", delval = "-1")
    private Integer deleteFlag;


    @Mapper
    public interface WorkGroupModelMapper extends BaseModelMapper<WorkGroupVO, WorkGroup> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        WorkGroup toModel(WorkGroupVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        WorkGroupVO fromModel(WorkGroup entity);
    }

    public static final WorkGroupModelMapper INSTANCE = Mappers.getMapper(WorkGroupModelMapper.class);
}
