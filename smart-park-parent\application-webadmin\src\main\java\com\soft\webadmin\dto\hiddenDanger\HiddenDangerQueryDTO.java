package com.soft.webadmin.dto.hiddenDanger;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * HiddenDangerDTO对象
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@ApiModel("HiddenDangerDTO对象")
@Data
public class HiddenDangerQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "隐患类型(1消防隐患、2安全隐患)")
    private Integer type;

    @ApiModelProperty(value = "隐患等级(1一般隐患、2严重隐患、3重大隐患)")
    private Integer level;

    @ApiModelProperty(value = "隐患位置（空间id）")
    private Long spaceId;

    @ApiModelProperty(value = "状态(1未查验、2处理中、3未审核、4已完成)")
    private Integer status;

    @ApiModelProperty(value = "上报人")
    private String createUserName;

    @ApiModelProperty(value = "开始时间")
    private String beginDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "上报人", hidden = true)
    private Long createUserId;

    @ApiModelProperty(value = "处理人", hidden = true)
    private Long handleUserId;

}
