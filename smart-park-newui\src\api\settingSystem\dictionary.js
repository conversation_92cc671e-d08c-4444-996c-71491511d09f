import { request } from "@/utils/request";

// 查询字典
export const dictionListAPI = (data) => {
  return request('post','/admin/upms/globalDict/list',data)
}

// 字典添加
export const dictionAddAPI = (data) => {
  return request('post','/admin/upms/globalDict/add',data)
}

// 字典编辑
export const dictionEditAPI = (data) => {
  return request('post','/admin/upms/globalDict/update',data)
}

// 字典删除
export const dictionDelAPI = (data) => {
  return request('post','/admin/upms/globalDict/delete',data)
}

// 查询字典项
export const dictionListItemAPI = (query) => {
  return request('get','/admin/upms/globalDict/listAll',query,'F')
}

// 字典项添加
export const dictionAddItemAPI = (data) => {
  return request('post','/admin/upms/globalDict/addItem',data)
}

// 删除字典项
export const dictionDelItemAPI = (data) => {
  return request('post','/admin/upms/globalDict/deleteItem',data)
}

