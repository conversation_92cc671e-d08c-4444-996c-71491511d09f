package com.soft.webadmin.dto.hiddenDanger;

import com.soft.webadmin.enums.HiddenDangerStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * HiddenDangerRectifyDTO对象
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@ApiModel("HiddenDangerRectifyDTO对象")
@Data
public class HiddenDangerRectifyDTO {

    @ApiModelProperty(value = "隐患id")
    private Long hiddenDangerId;

    @ApiModelProperty(value = "处理记录")
    @NotBlank(message = "处理记录不能为空！")
    private String description;

    @ApiModelProperty(value = "处理时长，小时")
    @NotNull(message = "处理时长不能为空！")
    private BigDecimal handleDuration;

    @ApiModelProperty(value = "整改图片")
    private String rectifyImgs;

    @ApiModelProperty(value = "操作（1处理、2退回、3结束）", hidden = true)
    private Integer operate = 1;

    @ApiModelProperty(value = "隐患状态", hidden = true)
    private Integer status = HiddenDangerStatusEnums.NO_EXAMINE.getValue();

    @ApiModelProperty(value = "查验人", hidden = true)
    private Long inspectionUserId;

}
