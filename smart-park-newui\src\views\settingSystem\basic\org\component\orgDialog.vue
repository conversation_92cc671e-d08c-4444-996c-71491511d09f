<template>
  <el-dialog v-model="showDialogRef" :title="state.title" :width="540" :before-close="close" align-center class="dialogCommon">
    <template #default>
      <div class="content">
        <el-form ref="dataFormRef" :model="state.dataForm" :rules="state.rules" label-suffix=":" label-width="90">
          <el-form-item label="组件名称" prop="name">
            <el-input v-model="state.dataForm.name" placeholder="请输入组件名称"></el-input>
          </el-form-item>
          <el-form-item label="组织类型" prop="type">
            <el-select clearable v-model="state.dataForm.type" placeholder="请选择组织类型">
              <el-option label="企业" value="企业"></el-option>
              <el-option label="商家" value="商家"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="state.dataForm.address" placeholder="请输入详细地址"></el-input>
          </el-form-item>
          <el-form-item label="关联区域" prop="spaces">
            <el-cascader v-model="state.dataForm.spaces" :options="state.spaceOptions" :props="state.spaceOptionsProps"
                         collapse-tags collapse-tags-tooltip clearable placeholder="请选择关联区域" />
          </el-form-item>
          <el-form-item label="通行权限" prop="authorizedEquipments">
            <el-select v-model="state.dataForm.authorizedEquipments" collapse-tags collapse-tags-tooltip multiple :max-collapse-tags="2" clearable placeholder="请选择通行权限">
              <el-option
                v-for="item in state.equipmentOptions"
                :key="item.equipmentId"
                :label="item.equipmentName"
                :value="item.equipmentId"
              />
            </el-select>
<!--            <el-cascader v-model="state.dataForm.authorizedEquipments" :options="state.equipmentOptions" :props="state.equipmentOptionsProps"-->
<!--                         collapse-tags collapse-tags-tooltip clearable placeholder="请选择通行权限"/>-->
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="state.dataForm.remark" placeholder="请输入备注"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="onSave">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script setup>

// 父组件
import {treeAPI} from "@/api/iotManagement/space.js";
import {listEquipmentsByGroupAPI} from "@/api/iotManagement/equipManage.js";
import {saveOrUpdateOrgAPI} from "@/api/settingSystem/org.js";
import {ElMessage} from "element-plus";

const emits = defineEmits(['onClose'])

// 弹出引用对象，控制是否显示
const showDialogRef = ref(false)

// 表单对象
const dataFormRef = ref()

const state = reactive({
  title: '',
  dataForm: {
    id: '',
    name: '',
    type: '',
    address: '',
    spaces: [],
    authorizedEquipments: [],
    remark: ''
  },
  // 位置树
  spaceOptions: [],
  spaceOptionsProps: {
    expandTrigger: 'hover',
    checkStrictly: false,
    multiple: true,
    label: "name",
    value: "id"
  },
  // 权限集合
  equipmentOptions: [],
  equipmentOptionsProps: {
    expandTrigger: 'hover',
    multiple: true,
    label: "equipmentName",
    value: "equipmentId"
  },
  rules: {
    name: [
      { required: true, message: "请输入组织名称", trigger: "change" },
      { min: 2, max: 40, message: '长度在 2 到 40 个字符', trigger: 'blur' }
    ],
    type: [
      { required: true, message: '请选择组织类型', trigger: 'change' }
    ],
    address: [
      { required: true, message: "请输入详细地址", trigger: "change" },
      { min: 2, max: 40, message: '长度在 2 到 40 个字符', trigger: 'blur' }
    ],
    spaces: [
      { required: true, message: '请选择关联区域', trigger: 'change' }
    ],
    authorizedEquipments: [
      { required: true, message: '请选择通行权限', trigger: 'change' }
    ],
  }
})


const onSave = () => {
  dataFormRef.value.validate((valid, fields) => {
    if (valid) {
      let param = {
        name: state.dataForm.name,
        type: state.dataForm.type,
        address: state.dataForm.address,
        authorizedEquipments: state.dataForm.authorizedEquipments.join(','),
        remark: state.dataForm.remark
      }
      // 主键 id
      if (state.dataForm.id) {
        param.id =  state.dataForm.id
      }
      // 区域处理
      let spacePaths = []
      for (let space of state.dataForm.spaces) {
        spacePaths.push(space.join('/'))
      }
      param.spaces = spacePaths.join(',')
      saveOrUpdateOrgAPI(param).then(res => {
        if (res.success) {
          ElMessage({
            type: 'success',
            message: '保存成功'
          })
          close()
        }
      })
    }
  })
}

// 打开
const open = (title, val) => {
  state.title = title
  showDialogRef.value = true
  nextTick(() => {
    if (val){
      state.dataForm = {
        id: val.id,
        name: val.name,
        type: val.type,
        address: val.address,
        remark: val.remark
      }
      if (val.spaces){
        let spacePaths = val.spaces.split(",")
        let spaces = []
        for (let spacePath of spacePaths) {
          spaces.push(spacePath.split('/'))
        }
        state.dataForm.spaces = spaces
      }
      if (val.authorizedEquipments) {
        state.dataForm.authorizedEquipments = val.authorizedEquipments.split(',')
      }
      console.log(state.dataForm.authorizedEquipments)
    }
    querySpaces()
    queryEquipments()
  })
}

/**
 * 查询空间下拉选项列表
 * @returns {Promise<unknown>}
 */
const querySpaces = () => {
  return new Promise((resolve, reject) => {
    nextTick(() => {
      let query = {
        deep: 4
      }
      treeAPI(query).then(res => {
        state.spaceOptions = res.data
        resolve()
      })
    })
  })
}

/**
 * 查询通行权限设备
 * @returns {Promise<unknown>}
 */
const queryEquipments = () => {
  return new Promise((resolve, reject) => {
    nextTick(() => {
      // groupId 004 字典表 通行权限设备
      listEquipmentsByGroupAPI({ groupId: '004' }).then(res => {
        state.equipmentOptions = res.data
        resolve()
      })
    })
  })
}

// 关闭
const close = () => {
  showDialogRef.value = false
  dataFormRef.value.resetFields()
  state.dataForm = {}
  emits('onClose')
}

defineExpose({
  open
})
</script>

<style scoped lang="less">
.content {
  .el-input {
    width: 95%;
  }

  .el-select, .el-cascader {
    width: 95%;
  }

  .el-input-number {
    width: 95%;
    .el-input {
      width: 100%;
    }
  }

  .el-textarea {
    width: 95%;
  }

  :deep(.el-date-editor) {
    width: 95%;
    flex-grow: 0;
  }

  :deep(.el-cascader) {
    width: 95%;
  }
}
</style>
