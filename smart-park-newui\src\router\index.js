
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { createRouter, createWebHistory } from 'vue-router'
import pinia from '@/store'
import { menuStore } from '@/store/modules/menu.js'

let menu

const routes = [
  {
    path: "/",
    redirect: '/screen',
    name: 'index',
    component: () => import('@/Layout/index.vue'),
    children:[
      {
        path:'buildingControl/configurationExhibits/:id',  // 楼宇
        component: () => import('@/views/buildingControl/configurationExhibits/index.vue'),
      },
      {
        path:'buildingControl/configurationExhibits1/:id', // 单个组态
        component: () => import('@/views/buildingControl/configurationExhibits1/index.vue'),
      }
    ]
  },
  {
    path: "/screen",
    component: () => import('@/views/screen/index.vue'),
    meta: {
      title: '首页'
    }
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录页'
    }
  },
  // 编辑组态
  {
    path:'/buildingControl/configuration',
    component: () => import('@/views/buildingControl/configuration/index.vue'),
    meta: {
      title: '编辑组态'
    }
  },
  // 预览组态
  {
    path:'/webtopoDraw',
    component: () => import('@/views/buildingControl/configuration/component/preview/draw.vue'),
    meta: {
      title: '预览组态'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router

router.beforeEach((to, from, next) => {
  if(!menu) menu = menuStore()
  NProgress.start()
  if (to.path == '/login' || localStorage.getItem('Authorization')) { //token是否存在
    if (to.path == '/login') {
      menu.$reset()
      localStorage.clear()
      next()
    } else {
      if (!menu.menuList.length) {
        menu.getMenu(to.fullPath).then(res => {
          if(res.length){ //判断是否菜单
            next({...to})
          }else {
            next()
          }
        })
      } else {
        next()
      }
    }
  } else {
    next('/login')
  }
})

router.afterEach((to, from) => {
  NProgress.done()
})
