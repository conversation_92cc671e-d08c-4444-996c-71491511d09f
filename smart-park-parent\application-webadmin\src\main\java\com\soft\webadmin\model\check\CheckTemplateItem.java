package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.check.CheckTemplateItemVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 检查模板项目对象 sp_check_template_item
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Data
@TableName(value = "sp_check_template_item")
public class CheckTemplateItem {

    @TableId(value = "id")
    private Long id;

    /**
     * 检查模板id
     */
    private Long templateId;

    /**
     * 检查项名称
     */
    private String itemName;

    /**
     * 检查项内容
     */
    private String itemContent;


    /**
     * 检查项类型：1选项，2数值，3选项&数值
     */
    private Integer itemType;


    @Mapper
    public interface CheckTemplateItemModelMapper extends BaseModelMapper<CheckTemplateItemVO, CheckTemplateItem> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        CheckTemplateItem toModel(CheckTemplateItemVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        CheckTemplateItemVO fromModel(CheckTemplateItem entity);
    }

    public static final CheckTemplateItemModelMapper INSTANCE = Mappers.getMapper(CheckTemplateItemModelMapper.class);
}
