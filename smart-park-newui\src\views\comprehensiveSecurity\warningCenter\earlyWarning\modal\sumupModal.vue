<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" @onClose="onClose" :width="900" :formRef="ruleFormRef"
                 class="dialogTextarea">
    <el-form ref="ruleFormRef" label-width="110px" :model="form" :rules="state.rules" label-suffix=":" >
<!--      真实事件-->
      <div v-show="type == 1">
        <el-form-item label="事件概述" prop="summary" :rules="{required: true, message: '请输入事件概述'}">
          <el-input v-model="form.summary" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit
                    placeholder="请输入事件概述"/>
        </el-form-item>
        <el-form-item label="原因分析" prop="reason">
          <el-input v-model="form.reason" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit
                    placeholder="请输入原因分析"/>
        </el-form-item>
        <el-form-item label="改进措施" prop="improve">
          <el-input v-model="form.improve" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit
                    placeholder="请输入改进措施"/>
        </el-form-item>
        <el-form-item label="上传附件">
          <el-upload
              drag
              v-model:file-list="form.annexList"
              :action="state.action"
              :headers="{ Authorization: state.Authorization }"
              :on-success="fileSuccess"
              :on-preview="hanldePreview"
              :limit="3"
              multiple
              accept="image/*,.pdf"
              style="width: 93.5%"
          >
            <el-icon class="el-icon--upload">
              <upload-filled/>
            </el-icon>
            <div class="el-upload__text">
              <em>《上传附件》</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持格式：jpg、png、pdf ，单个文件不能超过5MB，最多支持3份
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </div>
<!--      应急演练-->
      <div v-show="type == 2">
        <el-form-item label="演练总结" prop="summary" :rules="{required: true, message: '请输入演练总结'}">
          <el-input v-model="form.summary" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit
                    placeholder="请输入演练总结"/>
        </el-form-item>
      </div>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { ElMessage } from 'element-plus'

import { warningSumupAPI } from '@/api/comprehensiveSecurity/earlyWarning.js'

const props = defineProps({
  title:{
    type: String,
    default: ''
  },
  type: {
    type: Number,
    default: -1
  }
})

const emit = defineEmits(['submit'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({})

const state = reactive({
  action: import.meta.env.VITE_BASE_URL + '/core/file/upload',
  Authorization: localStorage.getItem('Authorization'),
  rules: {
    eventId: [{required: true, message: '请选择事件类型'}],
  },
})

// 上传图片
const fileSuccess = (response, file) => {
  file.filePath = response.data.filePath
  file.suffix = response.data.suffix
}

// 预览
const hanldePreview = (res) => {
  window.open(import.meta.env.VITE_BASE_URL + res.filePath)
}


const open = () => {
  dialog.value.open()
}

// 关闭
const onClose = () => {
  form.id = ''
  form.annexList = []
}

// 提交
const submit = () => {

  let subForm = JSON.parse(JSON.stringify(form))

  if(props.type == 1){
    // 数据处理
    subForm.annex = JSON.stringify(subForm.annexList.map(item => {
      return {
        fileName: item.name,
        filePath: item.filePath,
        suffix: item.suffix
      }
    }))
  }

  warningSumupAPI(subForm).then((res) => {
    if (res.success) {
      ElMessage.success('保存成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

defineExpose({
  form,
  open
})
</script>

<style lang='less' scoped></style>
