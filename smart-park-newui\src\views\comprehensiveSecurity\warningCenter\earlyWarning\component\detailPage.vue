<template>
  <el-row :gutter="15">
    <el-col :span="6">
      <div class="space-basic">
        <el-card class="box-card">
          <template #header>
            <el-row justify="space-between">
              <strong>基本信息</strong>
              <el-button type="primary" icon="Select" @click="handleDeal" v-if="data.status == 0">
                处理
              </el-button>
            </el-row>
          </template>
          <div style="position: relative" class="conent">
            <div class="absolute-image">
              <img :src="imgTransfer(data.eventVO.img)" alt="" v-show="data.eventVO.img">
              <br>
              <strong>{{ data.eventVO.name }}</strong>
            </div>
            <div style="margin-bottom: 15px">
              <strong style="margin-right: 10px">{{ data.no }}</strong>
              <el-tag :type="state.levelTypeOptions[data.eventVO.level]">{{
                  state.levelOptions[data.eventVO.level]
                }}
              </el-tag>
            </div>
            <el-form label-position="top">
              <el-form-item label="处理状态" >
                <span class="status-circle" :style="{backgroundColor: state.statusColorOptions[data.status]}"></span>{{
                  state.statusOptions[data.status]
                }}
              </el-form-item>
              <el-form-item label="位置" >
                {{ data.spaceFullName }}
              </el-form-item>
              <el-form-item label="详细位置" >
                {{ data.location }}
              </el-form-item>
              <el-form-item label="上报人" >
                {{ data.reportUserName }}
              </el-form-item>
              <el-form-item label="联系电话" >
                {{ data.reportUserPhone }}
              </el-form-item>
              <el-form-item label="上报时间" >
                {{ data.createTime }}
              </el-form-item>
              <el-form-item label="事件描述" >
                {{ data.description }}
              </el-form-item>
            </el-form>
          </div>
        </el-card>
        <el-card class="box-card" >
          <template #header>
            <strong>视频监控</strong>
          </template>
          <div class="video-layout">
            <div v-for="(item,index) in state.videoList" class="video-frames" @click="viewVideo(item)">
              <span>{{ item.equipmentName }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </el-col>
    <el-col :span="12">
      <el-card class="box-card box-card-main">
        <template #header>
          <el-row justify="space-between">
            <strong>应急预案</strong>
            <div v-if="data.status == 2" style="float: right">
              <template v-for="(value,key) in state.sumupOptions">
                <el-button type="primary" icon="Tickets" v-if="data.type == key" @click="handleSumup(Number(key))">
                  {{ value }}
                </el-button>
              </template>
              <el-button type="primary" icon="Download" @click="handleDown">导出</el-button>
            </div>
          </el-row>
        </template>
        <el-steps :active="state.active" style="width: 80%;margin: 0 auto" align-center>
          <el-step @click="handleLink(index + 1)" v-for="(item,index) in data.nodeList"
                   :key="index">
            <template #title>
              <el-tooltip
                  :content="item.nodeName"
                  placement="bottom"
              >
                {{ ellipsisSymbol(item.nodeName)  }}
              </el-tooltip>
            </template>
          </el-step>
        </el-steps>
        <div class="scroll-main" @scroll="scroll">
          <div
              v-for="(item,index) in data.nodeList"
              :key="index"
              :ref="el => containerRef[index + 1] = el"
              style="margin-bottom: 30px;"
          >
            <el-card class="card-border">
              <template #header>
                <div class="card-header">
                  节点{{ index + 1 }}：{{ item.nodeName }}
                </div>
              </template>
              <div>
                <div class="node-title">
                  <el-icon style="vertical-align: text-top">
                    <StarFilled/>
                  </el-icon>
                  任务
                </div>
                <el-table :data="item.tAPIList">
                  <el-table-column prop="tAPIName" label="任务描述">
                  </el-table-column>
                  <el-table-column prop="groupName" label="应急小组">
                  </el-table-column>
                </el-table>
              </div>
            </el-card>
          </div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <el-row justify="space-between">
            <strong>{{ data.status == 0 ? '应急成员' : '处理记录' }}</strong>
            <el-button type="primary" icon="Back" @click="showPage">返回</el-button>
          </el-row>
        </template>
        <!--        应急成员-->
        <div class="conent" v-show="data.status == 0">
          <div v-for="(item,index) in data.groupList">
            <strong>{{ item.name }}</strong>
            <el-table :data="item.memberVOList" style="margin: 15px 0 5px">
              <el-table-column prop="duties" label="职务" width="60">
                <template #default="{row,$index}">
                  {{ state.dutiesOptions[row.duties] }}
                </template>
              </el-table-column>
              <el-table-column prop="name" label="姓名">
              </el-table-column>
              <el-table-column prop="phone" label="手机号">
              </el-table-column>
              <el-table-column prop="shortPhone" label="短号" width="70">
              </el-table-column>
            </el-table>
            <div class="duty-detail">
              职责：{{ item.duty }}
            </div>
          </div>
        </div>
        <!--        处理记录-->
        <div class="conent" v-show="data.status == 1 || data.status == 2">
          <el-form label-suffix=":">
            <el-form-item label="处理结果" >
              {{ state.statusOptions[data.status] }}
            </el-form-item>
            <div v-show="data.type == 1">
              <el-form-item label="事件概述" >
                <el-link type="primary" @click="handleView('事件概述',data.summary)">{{ data.summary }}</el-link>
              </el-form-item>
              <el-form-item label="原因分析" >
                <el-link type="primary" @click="handleView('原因分析',data.reason)">{{ data.reason }}</el-link>
              </el-form-item>
              <el-form-item label="改进措施" >
                <el-link type="primary" @click="handleView('改进措施',data.improve)">{{ data.improve }}</el-link>
              </el-form-item>
            </div>
            <div v-show="data.type == 2">
              <el-form-item label="演练总结" >
                <el-link type="primary" @click="handleView('演练总结',data.summary)">{{ data.summary }}</el-link>
              </el-form-item>
            </div>

            <el-form-item label="操作人" >
              {{ data.summaryUserName }}
            </el-form-item>
            <el-form-item label="操作时间" >
              {{ data.summaryTime }}
            </el-form-item>
            <el-form-item label="附件" v-show="data.annexList && data.annexList.length" >
              <el-link type="primary" v-for="(item,index) in data.annexList" :key="index" style="margin-right: 10px"
                       @click="handlePreView(item.filePath)"> {{ item.name }}
              </el-link>
            </el-form-item>
          </el-form>
          <div>
            <el-input
                v-model="state.content"
                autosize
                type="textarea"
                placeholder="评论"
                class="common-textarea"
            />
            <el-button type="primary" @click="handleComment">发送</el-button>
          </div>

          <div v-for="(item,index) in state.contentList" class="common-item">
            <div class="user">
              <el-avatar :size="50"> {{ item.commentUserName }}</el-avatar>
              <div class="name">
                <text>{{ item.commentUserName }}</text>
                <br>
                <text>{{ item.commentTime }}</text>
              </div>
              <el-button link type="danger" icon="Delete" @click="commentDetele(item)"
                         v-if="state.userInfo.isAdmin || state.userInfo.userId == item.commentUserId">删除
              </el-button>
            </div>
            <p class="comment-conent">{{ item.content }}</p>
          </div>
        </div>
      </el-card>
    </el-col>
    <dealModal ref="deal" @submit="getDetail"></dealModal>
    <sumupModal ref="sumup" @submit="getDetail" :title="state.title" :type="state.type"></sumupModal>
    <el-drawer
        v-model="state.drawer"
        :title="state.drawerTitle"
    >
      <p class="conent-paragraph">{{ state.conent }}</p>
    </el-drawer>
    <video-modal title="视频监控" ref="video"></video-modal>
  </el-row>
</template>

<script setup>
import dealModal from '../modal/dealModal.vue'
import sumupModal from '../modal/sumupModal.vue'

import {warningDetailAPI, warningVideoAPI} from '@/api/comprehensiveSecurity/earlyWarning.js'
import {commentDeteleAPI, commentPageAPI, commentSaveAPI} from '@/api/comprehensiveSecurity/comment.js'

import {exportFile} from '@/utils/down.js'
import {ElMessage, ElMessageBox} from "element-plus";

const emit = defineEmits(['showPage'])

const deal = ref()
const sumup = ref()
const video = ref()

const data = ref({
  eventVO: {}
})

const load = ref(false)
const containerRef = ref([])
const listPage = ref([])

const state = reactive({
  id: '',
  userInfo: JSON.parse(localStorage.getItem('userInfo')) || {},
  active: 1,
  title: '',
  type: -1,
  drawer: false,
  drawerTitle: '',
  conent: '',
  content: '',
  contentList: [],
  videoList:[],
  levelOptions: {
    1: '普通',
    2: '重要',
    3: '严重'
  },
  levelTypeOptions: {
    1: "primary",
    2: "warning",
    3: 'danger'
  },
  statusOptions: {
    0: '待处理',
    1: '误报',
    2: '已解决'
  },
  statusColorOptions: {
    0: '#F59A23',
    1: '#F59A23',
    2: '#03BF16'
  },
  dutiesOptions: {
    1: '组长',
    2: '组员'
  },
  sumupOptions: {
    1: '事件总结',
    2: '演练总结'
  }
})

// 省略符号
const ellipsisSymbol = (nodeName) => {
  return nodeName.length > 4 ? nodeName.slice(0,3) + '...' : nodeName
}

// 图片转化
const imgTransfer = (param) => {
  return import.meta.env.VITE_BASE_URL + param
}

const loadPage = (id) => {
  state.id = id
  getDetail()
  getComment()
  getVideo()
}

// 获取详情
const getDetail = () => {
  warningDetailAPI({id: state.id}).then(res => {
    res.data.annexList = JSON.parse(res.data.annex || '[]').map(item => {
      return {
        name: item.fileName,
        url: item.filePath,
        filePath: item.filePath
      }
    })

    Object.assign(data.value, res.data)
    setTimeout(() => {
      initDom()
    }, 500)
  })
}

// 获取评论
const getComment = () => {
  commentPageAPI({warningId: state.id}).then(res => {
    state.contentList = res.data
  })
}

// 获取监控摄像头
const getVideo = () => {
  warningVideoAPI({warningId: state.id}).then(res => {
    console.log(res)
    state.videoList = res.data
  })
}

// 评论
const handleComment = () => {
  commentSaveAPI({content: state.content, warningId: state.id}).then(res => {
    if (res.success) {
      state.content = ''
      ElMessage.success('提交成功');
      getComment()
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

// 删除评论
const commentDetele = ({id}) => {
  ElMessageBox.confirm(
      '是否删除当前评论?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    commentDeteleAPI({id}).then(res => {
      if (res.success) {
        getComment()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 计算dom的值
const initDom = () => {
  let tabHeight = 0;
  for (let i in containerRef.value) {
    listPage.value[i] = {}
    listPage.value[i].top = tabHeight;
    tabHeight = tabHeight + containerRef.value[i].getBoundingClientRect().height + 30;
    listPage.value[i].bottom = tabHeight;
  }
}

// 跳转
const handleLink = (index) => {
  load.value = true
  state.active = index
  containerRef.value[index].scrollIntoView({block: "start", behavior: "smooth"})
  setTimeout(() => {
    load.value = false
  }, 500)
}

const scroll = (event) => {
  if (load.value) {
    return false
  }
  for (let i in containerRef.value) {
    if (event.target.scrollTop >= listPage.value[i].top && event.target.scrollTop < listPage.value[i].bottom) {
      state.active = Number(i)
      return false
    }
  }
}

// 查看视频
const viewVideo = (info) => {
  video.value.open(info.url, info)
}

// 处理
const handleDeal = () => {
  deal.value.open()
  nextTick(() => {
    deal.value.form.id = state.id
  })
}

// 总结
const handleSumup = (type) => {
  state.title = state.sumupOptions[type]
  state.type = type
  sumup.value.open()
  nextTick(() => {
    sumup.value.form.id = state.id
    sumup.value.form.summary = data.value.summary

    if (state.type == 1) {
      sumup.value.form.reason = data.value.reason
      sumup.value.form.improve = data.value.improve

      sumup.value.form.annexList = JSON.parse(JSON.stringify(data.value.annexList))
    }
  })
}

// 查看
const handlePreView = (filePath) => {
  window.open(import.meta.env.VITE_BASE_URL + filePath)
}

// 查看详情
const handleView = (title, conent) => {
  state.drawer = true
  state.drawerTitle = title
  state.conent = conent
}


// 导出
const handleDown = () => {
  exportFile('/contingency/warning/exportWord', {id: state.id}, `${data.value.no}.docx`)
}

const showPage = () => {
  data.value = {
    eventVO: {}
  }
  emit('showPage', 0)
}

defineExpose({
  loadPage
})
</script>

<style scoped lang="less">
.el-row, .el-col {
  height: 100%;
}

.space-basic{
  height: 100%;
  display: flex;
  flex-direction: column;
  .box-card:nth-child(1){
    height: calc(65% - 8px);
    margin-bottom: 8px;
  }
  .box-card:nth-child(2){
    height: 35%;
    .video-layout{
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      .video-frames{
        height: 100px;
        color: #b3b5bb;
        padding: 5px 0 0 5px;
        background-image: url("@/assets/img/videobg.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }
  }
}

.absolute-image {
  position: absolute;
  right: 10px;
  top: 0px;
  text-align: center;

  img {
    width: 50px;
    height: 50px;
    margin-bottom: 5px;
  }
}

.scroll-main {
  height: calc(100% - 67px);
  overflow: auto;
  margin-top: 10px;
  font-size: 14px;
  padding-right: 10px;

  .node-title {
    color: #EA9518;
    margin-bottom: 10px;
  }
}

:deep(.el-step) {
  cursor: pointer;
}

.card-border{
  border-radius: 0;
  :deep(.el-card__header){
    background: #3f9eff;
    font-size: 14px;
    color: #FFFFFF;
    .el-button--primary.is-link,.el-button--danger.is-link{
      color: #fff;
    }
  }
}

.el-link {
  overflow: hidden;

  :deep(.el-link__inner) {
    display: inline-block;
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 隐藏溢出的内容 */
    text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
  }
}

.duty-detail {
  font-size: 12px;
  color: #838383;
  line-height: 24px;
  margin-bottom: 20px;
}

.common-textarea {
  width: calc(100% - 70px);
  margin-right: 10px;
}

.common-item {
  margin: 20px 0;
  font-size: 14px;

  .user {
    display: flex;
    align-items: flex-end;

    .name {
      line-height: 22px;
      color: #7F7F7F;
      flex: 1;
      margin: 0 10px;
    }
  }

  .comment-conent {
    padding-left: 60px;
    margin-top: 10px;
    word-break: break-all;
  }
}

.conent-paragraph {
  text-indent: 20px;
  font-weight: 400;
  font-size: 14px;
  color: #4D5353;
  line-height: 30px;
}

</style>
