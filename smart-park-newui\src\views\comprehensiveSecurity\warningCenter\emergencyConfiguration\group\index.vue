<template>
  <div class="group">
    <div class="left">
      <template v-for="(item,index) in state.eventList">
        <div class="block-tag" :class="{active: index == state.currentIndex }" :title="item.name"
             @click="handleEvent(index)">
          {{ item.name }}
        </div>
      </template>
    </div>
    <div class="right">
      <page-common v-model="state.tableHeight">
        <template #query>
          <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
            <el-form-item prop="name">
              <el-input v-model="formInline.name" placeholder="小组名称"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
              <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </template>
        <template #operate>
          <el-button type="primary" icon="Plus" @click="addHandle">新建应急小组</el-button>
        </template>
        <template #table>
          <el-table :height="state.tableHeight" :data="state.tableData" row-key="id" show-overflow-tooltip>
            <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                             :label="item.label"
                             :align="item.align" :formatter="item.formatter" :width="item.width"/>
            <el-table-column align="center" label="操作"  width="240">
              <template #default="scope">
                <el-button link type="primary" icon="Tickets" @click="editHandle(scope.row,true)">详情</el-button>
                <el-button link type="primary" icon="Edit" @click="editHandle(scope.row,false)">编辑</el-button>
                <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
              background
              :page-sizes="[10, 20, 30, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="state.pagetion.pageNum"
              :page-size="state.pagetion.pageSize"
              :total="state.pagetion.total"
              @size-change="sizeChange"
              @current-change="currentChange"
          />
          <saveModal ref="save" :title="state.title" :isRead="state.isRead"
                     @submit="getList"></saveModal>
        </template>
      </page-common>
    </div>
  </div>
</template>

<script setup>
import {ElMessageBox, ElMessage} from "element-plus";

import saveModal from './component/saveModal.vue'

import {eventsPageAPI} from '@/api/comprehensiveSecurity/events.js'
import {groupPageAPI, groupDeteleAPI, groupDetailAPI} from '@/api/comprehensiveSecurity/group.js'

import {calcPageNo} from "@/utils/util.js";

let save = ref()

const formInlineRef = ref();
const formInline = reactive({});

const state = reactive({
  eventList: [],
  currentIndex: 0,
  title: '',
  isRead: false,
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'name',
      label: '小组名称'
    },
    {
      prop: 'duty',
      label: '职责'
    },
    {
      prop: 'leaderName',
      label: '组长'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  }
})

onMounted(() => {
  getEvent()
})

const getEvent = () => {
  eventsPageAPI().then(res => {
    state.eventList = res.data.dataList
    getList()
  })
}

const handleEvent = (index) => {
  state.currentIndex = index
  getList()
}

// 获取设备应急小组
const getList = () => {
  let query = {
    eventId: state.eventList[state.currentIndex]?.id,
    ...formInline,
    ...state.pagetion
  };
  groupPageAPI(query).then(res => {
    state.tableData = res.data.dataList;
    state.pagetion.total = res.data.totalCount;
  })
}
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};


// 新建设备应急小组
const addHandle = () => {
  state.isRead = false
  state.title = '新建应急小组'
  save.value.open()

  save.value.form.eventId = state.eventList[state.currentIndex]?.id
}

// 编辑设备应急小组
const editHandle = ({id}, isRead) => {
  state.isRead = isRead
  state.title = isRead ? '应急小组' : '编辑应急小组'

  groupDetailAPI({id}).then(res => {
    if (res.success) {
      save.value.open()
      nextTick(() => {
        save.value.form.eventId = state.eventList[state.currentIndex]?.id
        res.data.memberDTOList = res.data.memberVOList
        Object.assign(save.value.form, res.data)
      })
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

// 删除设备应急小组
const deleteHandle = ({id}) => {
  ElMessageBox.confirm(
      '是否删除当前设备应急小组?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, 1);
    groupDeteleAPI({id}).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

</script>

<style scoped lang="less">
.group {
  display: flex;
  height: 100%;

  .left {
    width: 200px;
    margin-right: 20px;
    flex-shrink: 0;
    background: #FFFFFF;
    border-radius: 10px;
    overflow: auto;
    padding: 18px;

    .block-tag {
      width: 100%;
      text-align: center;
      padding: 5px;
      margin-bottom: 7px;
      border-radius: 4px;
      border: 1px solid #EAEDED;
      cursor: pointer;
      font-size: 14px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .block-tag:hover {
      color: #3f9eff;
      border-color: #E0F8F9;
      background: #eaf5ff;
    }

    .active {
      color: #3f9eff;
      border-color: #E0F8F9;
      background: #eaf5ff;
    }
  }

  .right {
    flex: 1;
    overflow: hidden;
  }
}
</style>
