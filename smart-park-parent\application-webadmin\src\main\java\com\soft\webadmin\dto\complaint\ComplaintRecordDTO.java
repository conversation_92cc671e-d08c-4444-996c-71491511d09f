package com.soft.webadmin.dto.complaint;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * ComplaintRecordDTO对象
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@ApiModel("ComplaintRecordDTO对象")
@Data
public class ComplaintRecordDTO {

    @ApiModelProperty(value = "${column.columnComment}")
    @NotNull(message = "数据验证失败，${column.columnComment}不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "类型：1投诉；2建议；")
    private Integer type;

    @ApiModelProperty(value = "问题描述")
    private String content;

    @ApiModelProperty(value = "上报人id")
    private Long reportUserId;

    @ApiModelProperty(value = "上报人姓名")
    private String reportUserName;

    @ApiModelProperty(value = "联系方式")
    private String reportUserPhone;

    @ApiModelProperty(value = "附件id列表")
    private String annexIds;

    @ApiModelProperty(value = "处理状态：1待查看；2进行中；3已处理；")
    private Integer handleStatus;

    @ApiModelProperty(value = "处理人id")
    private Long handleUserId;

    @ApiModelProperty(value = "处理人姓名")
    private String handleUserName;

    @ApiModelProperty(value = "处理时间")
    private Date handleTime;

    @ApiModelProperty(value = "处理结果")
    private String handleResult;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建者id")
    private Long createUserId;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新者id")
    private Long updateUserId;

}
