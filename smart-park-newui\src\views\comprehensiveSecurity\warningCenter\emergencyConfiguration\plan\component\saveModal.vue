<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" @onClose="onClose" :formRef="ruleFormRef" :width="900">
    <el-form ref="ruleFormRef" label-width="0" :model="form" :rules="state.rules" label-suffix=":">
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="预案名称" prop="name" label-width="110px">
            <el-input v-model="form.name" placeholder="请输入预案名称"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件类型" prop="eventId" label-width="110px">
            <el-select v-model="form.eventId" filterable clearable placeholder="请选择事件类型">
              <el-option v-for="item in props.eventOptions" :label="item.name" :value="item.id"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">处理流程</div>
      </div>
      <el-space direction="vertical" size="large" :fill="true" style="margin-bottom: 16px;width: 100%;">
        <el-card v-for="(item,index) in form.nodeDTOList" class="card-border">
          <template #header>
            <el-row justify="space-between">
              <span>节点{{ index + 1 }}</span>
              <div>
                <el-button link type="primary" icon="CirclePlus" @click="handleAdd(index)">添加</el-button>
                <el-button link type="danger" icon="Delete" @click="handleDetele(index)">删除</el-button>
              </div>
            </el-row>
          </template>
          <el-row>
            <el-col :span="12">
              <el-form-item label="节点名称" :prop="'nodeDTOList.' + index + '.nodeName'" label-width="84"
                            :rules="{required: true, message: '请输入节点名称'}" class="item__content-noBg">
                <el-input v-model="item.nodeName" placeholder="请输入节点名称"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row justify="space-between" style="margin-bottom: 10px">
            <span style="color: #EA9518"><el-icon><StarFilled /></el-icon>任务</span>
            <el-button link type="primary" icon="CirclePlus" @click="handleAddItem(item)" style="float: right">添加</el-button>
          </el-row>
          <el-table :data="item.taskDTOList">
            <el-table-column type="index" width="80" label="序号">
              <template #default="{row,$index}">
                <div class="table-btn">
                  {{ $index + 1 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="" label="任务描述">
              <template #default="{row,$index}">
                <el-form-item :prop="'nodeDTOList.' + index + '.taskDTOList.' + $index + '.taskName'"
                              :rules="{required: true, message: '请输入任务描述'}" class="item__content-noBg">
                  <el-input v-model="row.taskName" placeholder="请输入任务描述" autosize :maxlength="500"  type="textarea"/>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="" label="应急小组" width="240">
              <template #default="{row,$index}">
                <el-form-item :prop="'nodeDTOList.' + index + '.taskDTOList.' + $index + '.groupId'" class="item__content-noBg">
                  <el-select v-model="row.groupId" filterable clearable placeholder="请选择应急小组">
                    <el-option v-for="item in state.groupOptions" :label="item.name" :value="item.id"/>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column align="center" label="操作" width="80">
              <template #default="{row,$index}">
                <div class="table-btn">
                  <el-button link type="danger" icon="Delete" @click="handleDeleteItem(item,$index)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-space>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">预案附件</div>
      </div>
      <el-upload
          drag
          v-model:file-list="form.annexList"
          :action="state.action"
          :headers="{ Authorization: state.Authorization }"
          :on-success="fileSuccess"
          :on-preview="hanldePreview"
          :limit="3"
          :on-exceed="() => ElMessage.warning('最多可支持上传三份文件')"
          multiple
          accept="image/*,.pdf"
          style="margin: 20px 0"
      >
        <el-icon class="el-icon--upload">
          <upload-filled/>
        </el-icon>
        <div class="el-upload__text">
          <em>《预案附件》</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持格式：jpg、png、pdf ，单个文件不能超过5MB，最多支持3份
          </div>
        </template>
      </el-upload>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {ElMessage} from 'element-plus'
import { StarFilled } from '@element-plus/icons-vue'

import {planSaveAPI} from '@/api/comprehensiveSecurity/plan.js'
import {groupPageAPI} from '@/api/comprehensiveSecurity/group.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  eventOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['submit'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({
  nodeDTOList: [{taskDTOList: [{}]}],
  annexList: []
})

const state = reactive({
  rules: {
    name: [{required: true, message: '请输入预案名称', trigger: 'blur'},],
    eventId: [{required: true, message: '请选择事件类型', trigger: 'blur'}]
  },
  action: import.meta.env.VITE_BASE_URL + '/core/file/upload',
  Authorization: localStorage.getItem('Authorization'),
})

onMounted(() => {
  getGroupPage()
})

// 获取小组
const getGroupPage = () => {
  groupPageAPI().then(res => {
    state.groupOptions = res.data.dataList
  })
}

// 添加
const handleAdd = (index) => {
  form.nodeDTOList.splice(index + 1, 0, {taskDTOList: [{}]})
}

// 删除
const handleDetele = (index) => {
  if (form.nodeDTOList.length == 1) {
    ElMessage.warning('请误删除当前节点')
    return false
  }
  form.nodeDTOList.splice(index, 1)
}

// 添加任务
const handleAddItem = (item) => {
  item.taskDTOList.push({})
}

// 删除任务
const handleDeleteItem = (item, index) => {
  item.taskDTOList.splice(index, 1)
}

// 上传图片
const fileSuccess = (response, file) => {
  file.filePath = response.data.filePath
  file.suffix = response.data.suffix
}

// 预览
const hanldePreview = (res) => {
  window.open(import.meta.env.VITE_BASE_URL + res.filePath)
}


const open = () => {
  dialog.value.open()
}

// 关闭
const onClose = () => {
  form.id = ''
  form.nodeDTOList = [{taskDTOList: []}]
  form.annexList = []
}

// 提交
const submit = () => {
  let subForm = JSON.parse(JSON.stringify(form))

  // 数据处理
  subForm.annex = JSON.stringify(subForm.annexList.map(item => {
    return {
      fileName: item.name,
      filePath: item.filePath,
      suffix: item.suffix
    }
  }))

  planSaveAPI(subForm).then((res) => {
    if (res.success) {
      ElMessage.success('保存成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}


defineExpose({
  form,
  open
})
</script>

<style lang="less" scoped>
.card-border{
  border-radius: 0;
  :deep(.el-card__header){
    background: #3f9eff;
    font-size: 14px;
    color: #FFFFFF;
    .el-button--primary.is-link,.el-button--danger.is-link{
      color: #fff;
    }
  }
}
</style>
