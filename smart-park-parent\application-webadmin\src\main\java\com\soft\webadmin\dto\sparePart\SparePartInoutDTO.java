package com.soft.webadmin.dto.sparePart;

import com.soft.webadmin.enums.BusinessTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * SparePartInoutDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@ApiModel("SparePartInoutDTO对象")
@Accessors(chain = true)
@Data
public class SparePartInoutDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private BusinessTypeEnums businessType = BusinessTypeEnums.OPERATIONS;

    @ApiModelProperty(value = "业务类型：1入库、2出库")
    @NotNull(message = "业务类型不能为空！")
    private Integer operateType;

    @ApiModelProperty(value = "出入库单号")
    @NotBlank(message = "出入库单号不能为空！")
    private String inoutNo;

    @ApiModelProperty(value = "出入库日期")
    @NotNull(message = "出入库日期不能为空！")
    private Date inoutDate;

    @ApiModelProperty(value = "出入库类型：1原始入库、2盘盈入库、3剩余备件归还、6备件领用、7盘亏出库")
    @NotNull(message = "出入库类型不能为空！")
    private Integer type = 1;

    @ApiModelProperty(value = "申请人")
    @NotNull(message = "申请人不能为空！")
    private Long applyUserId;

    // @ApiModelProperty(value = "审核状态（1待入库/待出库、2已同意）", hidden = true)
    // private Integer examineState = 1;

    // @ApiModelProperty(value = "关联工单单号", hidden = true)
    // private String workOrderNo;

    // @ApiModelProperty(value = "关联出库单号", hidden = true)
    // private String outNo;

    @ApiModelProperty(value = "关联盘点单号", hidden = true)
    private String stocktakingNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "出入库明细")
    @NotEmpty(message = "出入库明细不能为空！")
    @Valid
    private List<SparePartQuantityChangeDTO> quantityChangeList;

}
