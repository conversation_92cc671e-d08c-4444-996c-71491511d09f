package com.soft.webadmin.controller.equipment;

import cn.hutool.core.lang.tree.Tree;
import com.soft.common.core.object.ResponseResult;
import com.soft.sub.dto.equipment.EquipmentTypeDTO;
import com.soft.sub.dto.equipment.EquipmentTypeQueryDTO;
import com.soft.sub.vo.equipment.EquipmentTypeVO;
import com.soft.webadmin.service.equipment.EquipmentOmTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName EquipmentOmController
 * @description:
 * @date 2024年03月25日
 */
@Api(tags = "设备类型管理")
@RestController
@RequestMapping("/om/equipment/type")
public class EquipmentOmTypeController {

    @Autowired
    private EquipmentOmTypeService equipmentOmTypeService;

    @ApiOperation(value = "数据查询")
    @GetMapping("/list")
    public ResponseResult<List<EquipmentTypeVO>> list(EquipmentTypeQueryDTO queryDTO) {
        return ResponseResult.success(equipmentOmTypeService.list(queryDTO));
    }

    @ApiOperation(value = "树形查询")
    @GetMapping("/tree")
    public ResponseResult<List<Tree<String>>> tree(EquipmentTypeQueryDTO queryDTO) {
        return ResponseResult.success(equipmentOmTypeService.tree(queryDTO));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@Valid @RequestBody EquipmentTypeDTO saveDTO) {
        saveDTO.setCategory("OM");
        return equipmentOmTypeService.saveOrUpdate(saveDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        return equipmentOmTypeService.delete(id);
    }

}
