<template>
  <div class="page">
    <div class="tree">
      <el-form ref="formInlineRef" :model="state">
        <el-row>
          <el-col>
            <el-form-item prop="planName">
              <el-input v-model="state.planName" placeholder="请输入计划名称"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item prop="date">
              <el-date-picker v-model="state.date" end-placeholder="结束时间" format="MM-DD" range-separator="-"
                              start-placeholder="开始时间" style="width: 100%;" type="daterange" value-format="YYYY-MM-DD"
                              @change="changeDate"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-button icon="Search" type="primary" @click="onSubmit">查询</el-button>
            <el-button icon="Refresh" type="primary" @click="onReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-tree ref="treeRef" :data="state.treeData" accordion class="filter-tree" highlight-current node-key="key"
               style="margin-top: 10px;"
               @node-click="handleNodeClick"/>
    </div>
    <div class="record card-textBg">
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <el-form label-position="top">
        <el-row :gutter="40">
          <el-col :span="6">
            <el-form-item label="开始时间">
              <span v-if="state.info.startTime">{{ dayjs(state.info.startTime).format('YYYY-MM-DD HH:mm') }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="完成时间">
              <span v-if="state.info.finishTime">{{ dayjs(state.info.finishTime).format('YYYY-MM-DD HH:mm') }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="执行人">
              {{ state.info.checkUserName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="覆盖率">
              {{ coverage }}%
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">{{ state.planType == 'PATROL_INSPECTION' ? '巡检点位' : '维保设备' }}</div>
      </div>
      <div class="equipList detail-area">
        <template v-for="(item,index) in state.list" :key="item.id">
          <div class="single" @click="handleView(item)">
            <img alt="" src="@/assets/img/equipment.png">
            <div class="basic">
              <div class="basic-top">
                <span class="label">{{ item.pointName }}</span>
                <div class="state">
                  {{ state.stateObj[item.state]?.text }}
                </div>
              </div>
              <div class="basic-bottom">
                <div v-show="item.pointType == 'EQUIPMENT'">
                  <div>设备编号：<span>{{ item.dataNo }}</span></div>
                  <div>安装位置：<span>{{ item.dataSpace }}</span></div>
                </div>
                <div v-show="item.pointType == 'SPACE'">
                  <div>空间编号：<span>{{ item.dataNo }}</span></div>
                  <div>空间位置：<span>{{ item.dataSpace }}</span></div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
      <dialogArtificial ref="dialog"></dialogArtificial>
    </div>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'

import dialogArtificial from './dialogArtificial.vue';

import {checkRecordAPI, pointInspectionAPI} from '@/api/operationManagement/checkRecord.js'

import {pictureVideo} from '@/utils/util'

const formInlineRef = ref()
const treeRef = ref()
const dialog = ref()

const firstDay = dayjs().startOf('month').format('YYYY-MM-DD');
const today = dayjs().format("YYYY-MM-DD");

const state = reactive({
  planType: '',
  date: [firstDay, today],
  treeData: [],
  info: {},
  list: [], //点位集合
  stateObj: {
    0: {
      text: '未完成',
    },
    1: {
      text: '已完成',
    }
  }
})

const onSubmit = () => {
  getTree(state.planType);
};

const onReset = () => {
  formInlineRef.value.resetFields();
  state.date = [firstDay, today]
  onSubmit();
};

// 覆盖率
const coverage = computed(() => {
  let percentage = state.list.filter(item => item.state == 1).length / state.list.length
  return isNaN(percentage) ? 0 : (percentage * 100).toFixed(2)
})

// 获取数据
const getTree = (planType) => {
  state.planType = planType
  checkRecordAPI({
    planName: state.planName,
    beginDate: state.date[0],
    endDate: state.date[1],
    planMode: 1,
    planType
  }).then(res => {
    let defaultId
    state.treeData = Object.keys(res.data).map((item, index) => {
      return {
        label: item,
        key: index,
        children: (res.data[item] || []).map(i => {
          if (!Object.keys(state.info).length) { // 初始化数据
            defaultId = i.id
            state.info = i
            pointInspection(i.id)
          }
          return {
            label: i.createDate,
            value: i.id,
            key: i.id,
            info: i
          }
        })
      }
    })
    nextTick(() => {  // 默认节点
      treeRef.value.setCurrentKey(defaultId)
    })
  })
}

// 选择节点
const handleNodeClick = (node) => {
  if (node.value) {
    state.info = node.info
    pointInspection(node.value)
  }
}

// 设备/检测点
const pointInspection = (recordId) => {
  pointInspectionAPI({recordId}).then(res => {
    state.list = res.data
  })
}

// 时间变化
const changeDate = () => {
  state.date = state.date || []
  getTree(state.planType)
}

// 查看点位详情
const handleView = (item) => {
  item.imgList = pictureVideo(item.img)
  dialog.value.open(item)
}

defineExpose({
  getTree
})
</script>

<style lang='less' scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;

  .tree {
    width: 300px;
    height: 100%;
    margin-right: 20px;
    flex-shrink: 0;
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
    overflow: auto;
  }

  .record {
    flex: 1;
    background: #FFFFFF;
    border-radius: 10px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 18px;

    :deep .el-card__body {
      padding: 0;
    }
  }
}

.equipList {
  display: flex;

  .single {
    width: 25%;
    margin: 20px 0;
    padding: 10px;
    box-shadow: 0px 3px 7px 0px rgba(69, 100, 94, 0.35);
    display: flex;
    cursor: pointer;
    position: relative;
    border-radius: 10px;
    margin-right: 16px;

    img {
      width: 60px;
      height: 60px;
    }

    .basic {
      color: #999999;
      display: flex;
      flex-direction: column;
      line-height: 30px;
      flex: 1;
      overflow: hidden;
      word-break: break-all;
      margin: 0 10px;
      font-size: 14px;

      .basic-top {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;

        .label {
          color: #333333;
        }
      }

      .basic-bottom {
        span {
          color: #4D5353;
        }
      }
    }

    .state {
      width: 62px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 2px;
      background-color: #3f9eff;
      color: #fff;
    }
  }

  .single:nth-child(4n+4) {
    margin-right: 0;
  }
}
</style>
