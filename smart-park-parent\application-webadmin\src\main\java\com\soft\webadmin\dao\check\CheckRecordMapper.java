package com.soft.webadmin.dao.check;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.check.CheckRecordQueryDTO;
import com.soft.webadmin.model.check.CheckRecord;
import com.soft.webadmin.vo.check.CheckRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-12
 */
public interface CheckRecordMapper extends BaseMapper<CheckRecord> {

    List<CheckRecordVO> queryRecordTree(CheckRecordQueryDTO queryDTO);

    CheckRecordVO detail(@Param("id") Long id);

}
