<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="planName">
          <el-input v-model="formInline.planName" placeholder="计划名称" />
        </el-form-item>
        <el-form-item prop="state">
          <el-select v-model="formInline.state" placeholder="启停状态" clearable>
            <el-option value="1" label="启用" />
            <el-option value="0" label="停用" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新建计划</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="tableHeader in state.tableHeader" :label="tableHeader.label" :prop="tableHeader.prop" :width="tableHeader.width">
          <template #default="{ row }">
            <div v-if="tableHeader.prop === 'state'">
              <el-switch v-model="row.state" @change="enableHandle(row)"/>
            </div>
            <div v-if="tableHeader.prop === 'advanceTime'">
              <span v-if="row.advanceTime">
                {{ row.advanceTime }} 小时
              </span>

            </div>
            <div v-if="tableHeader.prop === 'handleLimitDuration'">
              {{ row.handleLimitDuration / 60 }} 小时
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="240">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
            <el-button link icon="Tickets" type="primary" @click="viewHandle(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pageParam.pageNum"
          :page-size="state.pageParam.pageSize"
          :total="state.pageParam.total"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
    </template>
  </page-common>
</template>

<script setup>
import {
  deleteCheckPlanAPI,
  getCheckPlanPageAPI,
  getCheckPlanPointListAPI,
  updateStateAPI
} from '@/api/operationManagement/checkPlan.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { calcPageNo } from '@/utils/util.js';
const emit = defineEmits(['showPage'])

const modal = ref();
const drawer = ref();
const formInlineRef = ref();
const formInline = reactive({});
const state = reactive({
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    { label: '计划名称', prop: 'planName' },
    { label: '开始时间', prop: 'startTime' },
    { label: '提前派单', prop: 'advanceTime' },
    { label: '处理时限', prop: 'handleLimitDuration' },
    { label: '下次维保日期', prop: 'maintenanceTimeNext' },
    { label: '启停状态', prop: 'state' , width: 80},
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  title: '',
  drawer: false,
});

onMounted(() => {
  getList();
});

const getList = () => {
  let params = {
    planType: 'MAINTENANCE',
    planName: formInline.planName,
    state: formInline.state,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };
  getCheckPlanPageAPI(params).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

// 启停用
const enableHandle = (row) => {
  let form = {
    id: row.id,
    state: row.state
  }
  updateStateAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success('计划已' + (row.state ? '启用' : '停用'));
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

/** 新建 */
const addHandle = (row) => {
  row.advanceTime = 0;
  row.checkPlanPointList = [];
  emit('showPage', 1, '新建计划', row);
};

/** 编辑 */
const editHandle = (row) => {
  let data = JSON.parse(JSON.stringify(row || {}));
  nextTick(() => {
    getCheckPlanPointListAPI({planId: row.id}).then((res) => {
      data.checkPlanPointList = res.data;
      emit('showPage', 1, '编辑计划', data);
    });
  });
};

/** 删除 */
const deleteHandle = (row) => {
  if (row.state === 1) {
    ElMessageBox.alert('启用状态的维保计划不可以删除！', '提醒', {
      type: 'warning',
    });
  } else {
    ElMessageBox.confirm('是否删除当前维保计划?', '提醒', {
      type: 'warning',
    }).then(() => {
      state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize);

      deleteCheckPlanAPI({ id: row.id }).then((res) => {
        if (res.success) {
          ElMessage.success('删除成功');
          getList();
        } else {
          ElMessage.error(res.errorMessage);
        }
      });
    });
  }
};

/** 查看详情 */
const viewHandle = (row) => {
  let data = JSON.parse(JSON.stringify(row || {}));
  nextTick(() => {
    getCheckPlanPointListAPI({planId: row.id}).then((res) => {
      data.checkPlanPointList = res.data;
      emit('showPage', 2, '计划详情', data);
    });
  });
};

defineExpose({
  getList
})
</script>
