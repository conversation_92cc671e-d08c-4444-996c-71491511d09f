package com.soft.webadmin.controller.sparePart;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.sparePart.SparePartInoutDTO;
import com.soft.webadmin.dto.sparePart.SparePartInoutQueryDTO;
import com.soft.webadmin.enums.BusinessTypeEnums;
import com.soft.webadmin.service.sparePart.SparePartInoutService;
import com.soft.webadmin.vo.sparePart.SparePartInoutVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 备品备件出入库控制器类
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@Api(tags = "出入库管理")
@RestController
@RequestMapping("/sparePart/inout")
public class SparePartInoutController {
    @Autowired
    private SparePartInoutService sparePartInoutService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<SparePartInoutVO>> getPage(SparePartInoutQueryDTO queryDTO) {
        return ResponseResult.success(sparePartInoutService.getPage(queryDTO));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public ResponseResult<SparePartInoutVO> detail(Long id) {
        return ResponseResult.success(sparePartInoutService.detail(id));
    }

    @ApiOperation(value = "新增出入库")
    @PostMapping("/create")
    public ResponseResult<Void> create(@Validated @RequestBody SparePartInoutDTO saveDTO) {
        return sparePartInoutService.create(saveDTO);
    }

    // @ApiOperation(value = "出入库审批")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "id", value = "出入库id", required = true),
    //         @ApiImplicitParam(name = "examineState", value = "审批状态：2同意", required = true)
    // })
    // @PostMapping("/examine")
    // public ResponseResult<Void> examine(@RequestParam Long id, @RequestParam Integer examineState) {
    //     return sparePartInoutService.examine(id, examineState);
    // }

    @ApiOperation("下载模板")
    @GetMapping("/exportTemplate")
    public void exportTemplate(BusinessTypeEnums businessType) {
        sparePartInoutService.exportTemplate(businessType);
    }

    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public ResponseResult<Map<String, Integer>> importExcel(@RequestParam MultipartFile file,
                                                            @RequestParam BusinessTypeEnums businessType) {
        return sparePartInoutService.importExcel(file, businessType);
    }

}
