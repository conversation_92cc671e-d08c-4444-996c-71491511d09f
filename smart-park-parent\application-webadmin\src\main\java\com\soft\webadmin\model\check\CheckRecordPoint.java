package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.check.CheckRecordPointVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 检查记录点位对象 sp_check_record_point
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@Data
@TableName(value = "sp_check_record_point")
public class CheckRecordPoint {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 检查记录id */
    private Long recordId;

    /** 检查点名称 */
    private String pointName;

    /** 检查点类型（EQUIPMENT设备，SPACE空间） */
    private String pointType;

    /** 设备id/空间id */
    private Long dataId;

    /** 状态（0未完成，1已完成） */
    private Integer state;

    /** 备注 */
    private String remark;

    /** 附件图片 */
    private String img;


    @Mapper
    public interface CheckRecordPointModelMapper extends BaseModelMapper<CheckRecordPointVO, CheckRecordPoint> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        CheckRecordPoint toModel(CheckRecordPointVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        CheckRecordPointVO fromModel(CheckRecordPoint entity);
    }

    public static final CheckRecordPointModelMapper INSTANCE = Mappers.getMapper(CheckRecordPointModelMapper.class);
}
