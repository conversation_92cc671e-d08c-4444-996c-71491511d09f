<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form inline :model="state.queryForm" ref="queryFormRef">
        <el-form-item prop="orgName">
          <el-input v-model="state.queryForm.orgName" placeholder="组织名称"></el-input>
        </el-form-item>
        <el-form-item prop="createUsername">
          <el-input v-model="state.queryForm.createUsername" placeholder="创建人姓名"></el-input>
        </el-form-item>
        <el-form-item prop="orgType">
          <el-select clearable v-model="state.queryForm.orgType" placeholder="组织类型">
            <el-option label="企业" value="企业"></el-option>
            <el-option label="商家" value="商家"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <el-select clearable v-model="state.queryForm.status" placeholder="请选择组织状态">
            <el-option label="启用" value="true"></el-option>
            <el-option label="禁用" value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="queryList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="onAdd">新建组织</el-button>
      <el-button type="primary" icon="Download" @click="onExport">导出</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" tooltip-effect="light">
        <el-table-column
          :key="index"
          :prop="item.prop"
          :label="item.label"
          show-overflow-tooltip
          v-for="(item, index) in state.tableHeader"
        >
          <template #default="scope">
            <span v-if="item.prop === 'orgUserIds'">
              {{ scope.row[item.prop].length }}
            </span>
            <span v-else-if="item.prop === 'spaces'">
              {{ scope.row[item.prop] ? scope.row[item.prop].split(',').length : 0 }}
            </span>
            <span v-else-if="item.prop === 'status'">
              <el-tag type="success" v-if="scope.row[item.prop]">启用</el-tag>
              <el-tag type="danger" v-else>禁用</el-tag>
            </span>
            <span v-else>
              {{ scope.row[item.prop] ? scope.row[item.prop] : "/" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" align="center">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Document"
              @click.prevent="onDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              link
              v-if="scope.row.status === true"
              :disabled="state.isLock"
              type="danger"
              @click.prevent="onUpdateStatus(scope.row, false)"
            >
              <el-icon><Lock /></el-icon>
              禁用
            </el-button>
            <el-button
              link
              v-else-if="scope.row.status === false"
              :disabled="state.isLock"
              type="success"
              @click.prevent="onUpdateStatus(scope.row, true)"
            >
              <el-icon><Unlock /></el-icon>
              启用
            </el-button>

            <el-button
              link
              type="primary"
              icon="Edit"
              @click.prevent="onEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.prevent="onDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="state.pageParam.pageNum"
        :page-sizes="state.pageParam.pageSizes"
        :page-size="state.pageParam.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="state.pageParam.total"
      >
      </el-pagination>

      <org-dialog ref="orgDialogRef" @onClose="queryList" />

      <org-detail ref="orgDetailRef" />
    </template>
  </page-common>
</template>


<script setup>
import {exportFile} from "@/utils/down.js"
import {deleteOrgAPI, listOrgAPI, updateOrgStatusAPI} from "@/api/settingSystem/org.js"
import {ElMessage, ElMessageBox} from "element-plus"
import PageCommon from "@/components/basic/pageCommon.vue"
import {Refresh, Search} from "@element-plus/icons-vue"
import OrgDialog from "@/views/settingSystem/basic/org/component/orgDialog.vue"
import OrgDetail from "@/views/settingSystem/basic/org/component/orgDetail.vue"



let orgDialogRef = ref()

let orgDetailRef = ref()

let queryFormRef = ref()

const state = reactive({
  tableHeight: 100,
  //表格数据
  tableData: [],
  // 查询条件
  queryForm: {
    // 姓名
    orgName: null,
    // 创建人姓名
    createUsername: null,
    // 组织类型
    orgType: null,
    // 组织状态
    status: null
  },
  isLock: false,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
    pageSizes: [10, 20, 30, 50]
  },
  tableHeader: [
    { label: '组织名称', prop: 'name' },
    { label: '组织编号', prop: 'code' },
    { label: '组织类型', prop: 'type' },
    { label: '组织人数', prop: 'orgUserIds' },
    { label: '关联区域', prop: 'spaces' },
    { label: '创建人姓名', prop: 'createUsername' },
    { label: '创建时间', prop: 'createTime' },
    { label: '组织状态', prop: 'status' }
  ]
})


//获取页面表格
const queryList = async () => {
  let param = assembleQueryParam()
  param.pageNum = state.pageParam.pageNum
  param.pageSize = state.pageParam.pageSize
  await listOrgAPI(param).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList
      state.pageParam.total = res.data.totalCount
    }
  })
}

// 重置
const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  queryList()
}


const assembleQueryParam = () => {
  let param = {}
  if (state.queryForm.orgName) {
    param.orgName = state.queryForm.orgName
  }
  if (state.queryForm.createUsername) {
    param.createUsername = state.queryForm.createUsername
  }
  if (state.queryForm.orgType) {
    param.orgType = state.queryForm.orgType
  }
  if (state.queryForm.status) {
    param.status = state.queryForm.status
  }
  return param
}

//导出方法
const onExport = async () => {
  await exportFile('/passage/org/export', assembleQueryParam(), '组织列表.xlsx')
}


// 启用、禁用方法
const onUpdateStatus = async (val, status) => {
  state.isLock = true
  await updateOrgStatusAPI({id: val.id, status: status}).then(res => {
    if (res.success) {
      ElMessage({
        type: "success",
        message: status ? "已启用！" : "已禁用！"
      })
      queryList()
    }
  })
  setTimeout(() => {
    state.isLock = false
  }, 500)
}


//分页器选择展示条数
const handleSizeChange = (val) => {
  state.pageParam.pageSize = val
  queryList()
}

//分页器点击页面数字
const handleCurrentChange = (val) => {
  state.pageParam.pageNum = val;
  queryList()
}


const onAdd = () => {
  orgDialogRef.value.open('新建组织')
}
const onEdit = (val) => {
  orgDialogRef.value.open('编辑组织', val)
}
const onDetail = (val) => {
  orgDetailRef.value.open('组织详情', val)
}


//删除
const onDelete = (val) => {
  ElMessageBox.confirm(
    "是否删除当前组织?", "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    deleteOrgAPI({id: val.id}).then(res => {
      if (res.success) {
        ElMessage.success('删除成功！')
        queryList()
      }
    })
  }).catch(() => {
    ElMessage.info('取消删除！')
  });
}

onMounted(() => {
  nextTick(() => {
    queryList()
  })
})
</script>


<style scoped lang="less">

</style>
