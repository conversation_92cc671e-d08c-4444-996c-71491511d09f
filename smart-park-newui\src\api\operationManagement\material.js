import { request } from '@/utils/request';

// 分页查询
export const getMaterialAPI = (query) => {
    return request('get', '/sparePart/info/getPage', query, 'F');
};

// 库存预警设置
export const setMaterialAPI = (data) => {
    return request('post', '/sparePart/info/setUpWarning', data);
}

// 备件详情
export const detailMaterialAPI = (query) => {
    return request('get', '/sparePart/info/detail', query, 'F');
}

// 库存变化记录
export const changeMaterialAPI = (query) => {
    return request('get', '/sparePart/info/getChangePage', query, 'F');
}
