<template>
  <div class="imgVideo">
    <template v-for="(item, index) in props.list">
      <video :src="item.url" controls v-if="item.type"></video>
      <el-image :src="item.url" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
        :preview-src-list="[item.url]" :initial-index="4" fit="contain" v-else/>
    </template>
  </div>
</template>

<script setup>
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
})
</script>

<style lang='less' scoped>
.imgVideo {
  margin-top: 5px;
  .el-image,video{
    background-color: #F5F7FA;
    border: solid #EBF3F3 1px;
    width: 140px;
    margin-right: 10px;
    height: 100px;
  }
}
</style>