package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * SpCheckTemplateItemDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@ApiModel("CheckTemplateItemDTO对象")
@Data
public class CheckTemplateItemDTO {

    @ApiModelProperty(value = "${column.columnComment}")
    @NotNull(message = "数据验证失败，${column.columnComment}不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "检查模板id")
    private Long templateId;

    @ApiModelProperty(value = "检查项名称")
    private String itemName;

    @ApiModelProperty(value = "检查项内容")
    private String itemContent;

    @ApiModelProperty(value = "检查项类型：1选项；2数值；3选项数值")
    private Integer itemType;

}
