<template>
  <div style="height: 100%;">
    <el-card class="box-card card-textBg">
      <template #header>
        <el-row justify="space-between" align="middle">
          <strong>备件详情</strong>
          <div>
            <el-button type="primary" icon="Back" @click="showPage">返回</el-button>
          </div>
        </el-row>
      </template>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>

      <div class="detail-area">
        <el-form  label-position="top">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item label="备件名称">
                {{ data.sparePartName}}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="备件编号">
                {{ data.sparePartNo }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="备件分类">
                {{ data.classifyName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="规格型号">
                {{ data.model }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="单位">
                {{ data.unit }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="单价">
                {{ data.unitPrice }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="库存数量">
                {{ data.inventoryQuantity }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="更新人">
                {{ data.updateUserName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="更新时间">
                {{ data.updateTime }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        </div>
        <div style="display: flex;justify-content: center;">
          <el-radio-group v-model="state.radio" style="margin: 20px;">
            <el-radio-button value="spread">
              库存分布
            </el-radio-button>
            <el-radio-button value="variety">
              库存变化
            </el-radio-button>
          </el-radio-group>
        </div>
        <!--库存分布-->
        <div v-show="state.radio == 'spread'">
          <el-table :data="data.detailVOList">
            <el-table-column v-for="(item, index) in state.tableHeaderS" :key="index" :prop="item.prop" :label="item.label"
                             :align="item.align" :formatter="item.formatter" />
          </el-table>
        </div>
        <!--库存变化-->
        <div v-show="state.radio == 'variety'">
          <el-form :inline="true" ref="formInlineRefV" :model="formInlineV" style="float: right">
            <el-form-item prop="operateType" class="item__content-noBg">
              <el-select v-model="formInlineV.operateType" placeholder="业务类型">
                <el-option v-for="(value, key) in state.operateTypeOptions" :key="key" :label="value" :value="key" />
              </el-select>
            </el-form-item>
            <el-form-item prop="type" class="item__content-noBg">
              <el-select v-model="formInlineV.type" placeholder="出入库类型">
                <el-option v-for="(value, key) in state.typeOptions" :key="key" :label="value" :value="key" />
              </el-select>
            </el-form-item>
            <el-form-item class="item__content-noBg" style="margin-right: 0">
              <el-button type="primary" icon="Search" @click="onSubmitVariety">查询</el-button>
              <el-button type="primary" icon="Refresh" @click="onResetVariety">重置</el-button>
            </el-form-item>
          </el-form>
          <el-table :data="state.tableDataV">
            <el-table-column v-for="(item, index) in state.tableHeaderV" :key="index" :prop="item.prop" :label="item.label"
                             :align="item.align" :formatter="item.formatter" />
          </el-table>
          <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                         :current-page="state.pagetionV.pageNum" :page-size="state.pagetionV.pageSize"
                         :total="state.pagetionV.total"
                         @size-change="sizeChangeV" @current-change="currentChangeV"/>
        </div>
    </el-card>
  </div>
</template>

<script setup>
import {detailMaterialAPI, changeMaterialAPI} from '@/api/operationManagement/material.js'

const emit = defineEmits(['showPage'])

const data = ref({})
const formInlineRefV = ref()

const formInlineV = reactive({})
const state = reactive({
  id:'',
  radio: 'spread',
  tableHeightS: 'calc(100vh - 680px)',
  tableHeaderS: [
    {
      prop: 'storehouseName',
      label: '仓库名称'
    },
    // {
    //   prop: 'storehouseNo',
    //   label: '仓库编号'
    // },
    {
      prop: 'inventoryQuantity',
      label: '当前库存'
    },
    // {
    //   prop: 'tobeIntoQuantity',
    //   label: '待入库'
    // },
    // {
    //   prop: 'tobeOutQuantity',
    //   label: '待出库'
    // },
  ],
  operateTypeOptions:{ // 业务类型
    1:'入库',
    2:'出库'
  },
  typeOptions:{ // 出入库类型
    1:'原始入库',
    2:'盘盈入库',
    3:'剩余备件归还',
    6:'备件领用',
    7:'盘亏出库'
  },
  tableDataV: [], // 库存变化
  tableHeaderV: [
    {
      prop: 'inoutNo',
      label: '入库/出库单号'
    },
    {
      prop: 'operateType',
      label: '业务类型',
      formatter: (row, column, cellValue) =>  {
        return state.operateTypeOptions[cellValue]
      }
    },
    {
      prop: 'type',
      label: '出入库类型',
      formatter: (row, column, cellValue) =>  {
        return state.typeOptions[cellValue]
      }
    },
    {
      prop: 'createUserName',
      label: '入库/出库人'
    },
    // {
    //   prop: 'storehouseName',
    //   label: '仓库'
    // },
    {
      prop: 'changeQuantity',
      label: '数量',
      formatter: (row, column, cellValue) =>  {
        if(row.operateType == 1){
          return '+' + cellValue
        }else if(row.operateType == 2){
          return '-' + cellValue
        }
      }
    },
    {
      prop: 'inventoryQuantity',
      label: '库存'
    },
    {
      prop: 'createTime',
      label: '时间'
    },
  ],
  pagetionV: {  // 库存页签
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

// 设置id
const setId = (id) => {
  console.log(id)
  state.id = id
  getDetail()
  getVariety()
}

// 获取详情
const getDetail = () => {
  detailMaterialAPI({id:state.id}).then(res => {
    data.value = res.data
  })
}

// 获取库房变化
const getVariety = () => {
  let query = {
    ...formInlineV,
    sparePartId: state.id,
    pageNum: state.pagetionV.pageNum,
    pageSize: state.pagetionV.pageSize,
  }

  changeMaterialAPI(query).then(res => {
    state.tableDataV = res.data.dataList
    state.pagetionV.total = res.data.totalCount * 1
  })
}

// 库存变化查询
const onSubmitVariety = () => {
  state.pagetionV = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getVariety()
}

// 库存变化重置
const onResetVariety = () => {
  formInlineRefV.value.resetFields()
  onSubmitVariety()
}

// 库存页数改变
const currentChangeV = (pageNum) => {
  state.pagetionV.pageNum = pageNum
  getVariety()
}

// 库存页容量改变
const sizeChangeV = (pageSize) => {
  state.pagetionV.pageSize = pageSize
  getVariety()
}


// 返回
const showPage = () => {
  state.radio = 'spread'
  emit('showPage', 0)
}

defineExpose({
  setId
})
</script>
