<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.WorkGroupMapper">
    <resultMap type="com.soft.webadmin.model.check.WorkGroup" id="SpWorkGroupResult">
        <result property="id" column="id" />
        <result property="name" column="name" />
        <result property="leaderId" column="leader_id" />
        <result property="memberCount" column="member_count" />
        <result property="remark" column="remark" />
        <result property="createTime" column="create_time" />
        <result property="createUserId" column="create_user_id" />
        <result property="updateTime" column="update_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="deleteFlag" column="delete_flag" />
    </resultMap>

    <sql id="selectSpWorkGroupVo">
        select id, name, leader_id, member_count, remark, create_time, create_user_id, update_time, update_user_id, delete_flag from sp_work_group
    </sql>

</mapper>