package com.soft.webadmin.dto.contingency;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * EarlyWarningCommentDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EarlyWarningCommentDTO对象")
@Data
public class EarlyWarningCommentDTO {

    // @ApiModelProperty(value = "主键id")
    // private Long id;

    @ApiModelProperty(value = "预警记录id")
    @NotNull(message = "预警记录不能为空！")
    private Long warningId;

    // @ApiModelProperty(value = "父级id")
    // private Long parentId;

    @ApiModelProperty(value = "评论内容")
    @NotBlank(message = "评论内容不能为空！")
    private String content;

    @ApiModelProperty(value = "图片")
    private String imgs;

}
