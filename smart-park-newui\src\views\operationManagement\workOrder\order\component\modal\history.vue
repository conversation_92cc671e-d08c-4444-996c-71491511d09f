<template>
  <dialog-common ref="dialog" title="历史报价" :width="1100">
    <div class="conent">
      <quoteTable v-for="(item, index) in props.list" :data="item" style="margin-bottom: 10px;"></quoteTable>
    </div>
  </dialog-common>
</template>

<script setup>
import quoteTable from '@/views/operationManagement/workOrder/component/quoteTable.vue'

const props = defineProps({
  list:{
    type:Array,
    defalut:() => []
  }
})

const dialog = ref()

const open = () => {
  dialog.value.open();
}

defineExpose({
  open
});
</script>

<style scoped lang="less">
.conent{
  max-height: 60vh;
  overflow: auto;
}
</style>
