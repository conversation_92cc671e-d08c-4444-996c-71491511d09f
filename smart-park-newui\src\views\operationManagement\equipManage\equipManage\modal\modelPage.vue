<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" @onClose="onClose"  :formRef="ruleFormRef" :width="900"
    class="dialogTextarea">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":">
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">基本信息</div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备编号" prop="equipmentCode">
              <el-input v-model="form.equipmentCode" :disabled="Boolean(form.equipmentId && form.equipmentCode)" placeholder="请输入设备编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="equipmentName">
              <el-input v-model="form.equipmentName" :disabled="Boolean(form.equipmentId && form.equipmentName)" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="equipmentTypeId">
              <el-tree-select v-model="form.equipmentTypeId" :data="props.equipmentTypeOptions"
                              :render-after-expand="false"  clearable
                              node-key="id" :props="{label: 'name'}" placeholder="请选择设备类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备型号" prop="equipmentModel">
              <el-input v-model="form.equipmentModel" placeholder="请输入设备型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="生产厂商" prop="factory">
              <el-input v-model="form.factory" placeholder="请输入生产厂商" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保修开始日期" prop="warrantyBeginDate">
              <el-date-picker v-model="form.warrantyBeginDate" type="date" placeholder="请选择保修开始日期" :disabled-date="disabledMaxDate"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="保修结束日期" prop="warrantyEndDate">
              <el-date-picker v-model="form.warrantyEndDate" type="date" placeholder="请选择保修结束日期" :disabled-date="disabledMinDate"/>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">管理信息</div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="安装位置" prop="spaceId">
              <el-cascader v-model="form.spaceId" :options="state.spaceOptions" :props="state.spaceProps"
                clearable placeholder="请选择安装位置" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用时间" prop="activationDate">
              <el-date-picker v-model="form.activationDate" type="date" placeholder="请选择启用日期" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="管理部门" prop="deptId">
              <el-cascader v-model="form.deptId" :clearable="true" placeholder="请选择管理部门"
                :loading="state.deptInfo.impl.loading" :props="{
                  value: 'deptId',
                  label: 'deptName',
                  checkStrictly: true,
                  expandTrigger: 'hover',
                }" :options="state.deptOptions" @change="deptValueChange">
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="owner">
              <el-select v-model="form.owner" placeholder="请选择负责人" clearable>
                <el-option v-for="item in state.userOptions" :key="item.userId" :label="item.showName"
                           :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </dialog-common>
</template>

<script setup>
import dayjs from 'dayjs'
import {ElMessage} from "element-plus";

import { treeAPI } from '@/api/iotManagement/space.js';
import { getDeptListAPI, getPageAPI } from '@/api/settingSystem/user.js';
import {DropdownWidget} from '@/utils/widget.js';
import { equipSaveAPI } from '@/api/operationManagement/equipManage.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  equipmentTypeOptions: {
    type: Array,
    default: []
  },
})

// 父组件定义事件
const emit = defineEmits(['submit']);

const dialog = ref();
const ruleFormRef = ref();
const form = reactive({});

const state = reactive({
  title: '',
  deptOptions: [],
  userOptions: [],
  spaceOptions: [],
  deptInfo: {
    impl: new DropdownWidget(loadDeptDropdownList, true, 'deptId'),
    value: [],
  },
  // 位置级联选择配置
  spaceProps: {
    emitPath: false,
    checkStrictly: true,
    label: 'name',
    value: 'id',
    expandTrigger: 'hover',
  },
  rules: {
    // equipmentCode: [{ required: true, message: '设备编号不能为空'}],
    equipmentName: [{ required: true, message: '设备名称不能为空'}],
    spaceId: [{ required: true, message: '安装位置不能为空', }],
  },
});

onMounted(() => {
  state.deptInfo.impl.onVisibleChange(true).then((res) => {
    state.deptOptions = res;
  });
  loadSpaceOptions();
})


const open = () => {
  dialog.value.open();
};


// 禁选时间
const disabledMaxDate = (time) => {
  return form.warrantyEndDate ?  time.getTime() > dayjs(form.warrantyEndDate).valueOf() : false
}

const disabledMinDate = (time) => {
  return form.warrantyBeginDate ? time.getTime() < dayjs(form.warrantyBeginDate).valueOf() : false
}

/** 查询部门list */
function loadDeptDropdownList() {
  return new Promise((resolve, reject) => {
    getDeptListAPI({})
      .then((res) => {
        resolve(res.data.dataList);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

/** 部门下拉框改变事件 */
const deptValueChange = (value) => {
  form.deptId = Array.isArray(value) ? value[value.length - 1] : undefined;
  loadUserList();
};

/** 查询用户 */
const loadUserList = () => {
  if (form.deptId) {
    const sysUserDtoFilter = {
      deptId: form.deptId,
    };
    form.owner = undefined;
    getPageAPI({ sysUserDtoFilter }).then((res) => {
      if (res.success) {
        state.userOptions = res.data.dataList;
      }
    });
  }
};

// 查询位置列表
const loadSpaceOptions = () => {
  /** 查询位置 */
  treeAPI({ deep: 4 }).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
}

// 关闭dialog
const onClose = () => {
  delete form.equipmentId
}

/** 保存 */
const submit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      equipSaveAPI({...form}).then(res => {
        if (res.success) {
          ElMessage.success(form.equipmentId ? '保存成功' : '新增成功');
          dialog.value.close()
          emit('submit')
        } else {
          ElMessage.error(res.errorMessage);
        }
      })
    }
  })
};

defineExpose({
  form,
  open
});
</script>

<style lang="less" scoped>
.tag {
  margin: 5px 5px 5px 0;
}

.input-number-left {
  :deep(.el-input__inner) {
    text-align: left;
  }
}
</style>
