package com.soft.webadmin.model.contingency;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.contingency.EarlyWarningCommentVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * 预警记录评论对象 cm_early_warning_comment
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Data
@TableName(value = "cm_early_warning_comment")
public class EarlyWarningComment {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 预警记录id */
    private Long warningId;

    /** 父级id */
    private Long parentId;

    /** 评论内容 */
    private String content;

    /** 图片 */
    private String imgs;

    /** 评论人id */
    private Long commentUserId;

    /** 评论人名字 */
    private String commentUserName;

    /** 评论时间 */
    private Date commentTime;

    /** 删除标记(1: 正常 -1: 已删除) */
    @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;


    @Mapper
    public interface EarlyWarningCommentModelMapper extends BaseModelMapper<EarlyWarningCommentVO, EarlyWarningComment> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        EarlyWarningComment toModel(EarlyWarningCommentVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        EarlyWarningCommentVO fromModel(EarlyWarningComment entity);
    }

    public static final EarlyWarningCommentModelMapper INSTANCE = Mappers.getMapper(EarlyWarningCommentModelMapper.class);
}
