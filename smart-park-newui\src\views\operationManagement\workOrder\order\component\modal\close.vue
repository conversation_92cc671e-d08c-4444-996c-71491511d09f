<template>
  <dialog-common ref="dialog" title="关闭" @submit="submit" :formRef="ruleFormRef" :width="650">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-form-item label="关闭原因" prop="reason">
        <el-input v-model="form.reason" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit placeholder="请输入关闭原因"/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { workCloseAPI } from '@/api/operationManagement/workOrder.js';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  rules: {
    reason: [{ required: true, message: '请输入原因', trigger: 'blur' }],
  },
});

// 提交表单
const submit = () => {
  workCloseAPI({...form}).then((res) => {
      if (res.success) {
        ElMessage.success('操作成功');
        dialog.value.close();
        emit('submit');
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
}
const open = () => {
  dialog.value.open();
};

defineExpose({
  form,
  open,
});
</script>
