<template>
  <el-dialog v-model="showDialogRef" :title="state.title" width="60%" :before-close="close" class="dialogCommon"
             align-center>
    <template #default>
      <div class="content">
        <el-form :model="state.dataForm" ref="dataFormRef" :rules="state.rules" label-width="55" label-suffix=":">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="box-card">
                <template #header>
                  <div class="card-header">
                    <span>信息面板</span>
                  </div>
                </template>
                <el-form-item label="位置" prop="oneSpaceId">
                  <el-cascader v-model="state.dataForm.oneSpaceId" :options="state.spaceOptions"
                               :props="state.spaceProps" @change="onChangeQueryEquipments(1,1, state.dataForm.oneSpaceId)" placeholder="设备位置" clearable/>
                </el-form-item>
                <el-form-item label="设备" prop="oneEquipmentId">
                  <el-select v-model="state.dataForm.oneEquipmentId"
                             @change="onChangeQueryEquipmentPorts(1, state.dataForm.oneEquipmentId)"
                             clearable placeholder="设备">
                    <el-option
                      v-for="item in state.oneEquipmentOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="端口" prop="onePortId">
                  <el-select v-model="state.dataForm.onePortId" clearable placeholder="端口">
                    <el-option
                      v-for="item in state.onePortOptions"
                      :key="item.id"
                      :label="item.code"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <!-- 占位，保持样式一致 -->
                  <el-input style="visibility:hidden"/>
                </el-form-item>
              </el-card>
            </el-col>

            <el-col :span="8">
              <el-card class="box-card">
                <template #header>
                  <div class="card-header">
                    <span>配线架</span>
                  </div>
                </template>
                <el-form-item label="位置" prop="twoSpaceId">
                  <el-cascader v-model="state.dataForm.twoSpaceId" :options="state.spaceOptions"
                               :props="state.spaceProps" @change="onChangeQueryEquipments(2, 4, state.dataForm.twoSpaceId)" placeholder="设备位置"/>
                </el-form-item>
                <el-form-item label="机柜" prop="twoCabinetId">
                  <el-select v-model="state.dataForm.twoCabinetId" @change="onChangeQueryEquipments(2, 2, state.dataForm.twoCabinetId)" clearable placeholder="机柜">
                    <el-option
                      v-for="item in state.twoCabinetOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="设备" prop="twoEquipmentId">
                  <el-select v-model="state.dataForm.twoEquipmentId"
                             @change="onChangeQueryEquipmentPorts(2, state.dataForm.twoEquipmentId)"
                             clearable
                             placeholder="设备">
                    <el-option
                      v-for="item in state.twoEquipmentOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="端口" prop="twoPortId">
                  <el-select v-model="state.dataForm.twoPortId"
                             placeholder="端口">
                    <el-option
                      v-for="item in state.twoPortOptions"
                      :key="item.id"
                      :label="item.code"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-card>
            </el-col>

            <el-col :span="8">
              <el-card class="box-card">
                <template #header>
                  <div class="card-header">
                    <span>交换机</span>
                  </div>
                </template>
                <el-form-item label="位置" prop="threeSpaceId">
                  <el-cascader v-model="state.dataForm.threeSpaceId" :options="state.spaceOptions"
                               :props="state.spaceProps"
                               @change="onChangeQueryEquipments(3, 4, state.dataForm.threeSpaceId)"
                               placeholder="设备位置"/>
                </el-form-item>
                <el-form-item label="机柜" prop="threeCabinetId">
                  <el-select v-model="state.dataForm.threeCabinetId"
                             @change="onChangeQueryEquipments(3, 3, state.dataForm.threeCabinetId)"
                             clearable placeholder="机柜">
                    <el-option
                      v-for="item in state.threeCabinetOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="设备" prop="threeEquipmentId">
                  <el-select v-model="state.dataForm.threeEquipmentId"
                             @change="onChangeQueryEquipmentPorts(3, state.dataForm.threeEquipmentId)"
                             clearable
                             placeholder="设备">
                    <el-option
                      v-for="item in state.threeEquipmentOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="端口" prop="threePortId">
                  <el-select v-model="state.dataForm.threePortId" placeholder="端口">
                    <el-option
                      v-for="item in state.threePortOptions"
                      :key="item.id"
                      :label="item.code"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-card>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="onSave">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script setup>

import {treeAPI} from "@/api/iotManagement/space.js";
import {listByEquipmentIdAPI, maintenanceListAPI} from "@/api/buildingControl/maintenance.js";
import {saveOrUpdateCabLinkAPI} from "@/api/buildingControl/cablingLink.js";
import {ElMessage} from "element-plus";

/**
 * 父组件引用
 */
const emits = defineEmits('onClose')

/**
 * 弹窗组件引用
 *
 * @type {*}
 */
const showDialogRef = ref(false)

// 表单
const dataFormRef = ref()

const state = reactive({
  title: '',
  dataForm: {},
  // 空间下拉选项
  spaceOptions: [],

  // 信息面板设备下拉选项
  oneEquipmentOptions: [],
  onePortOptions: [],

  // 机柜下拉选项
  twoCabinetOptions: [],
  // 配线架设备下拉选项
  twoEquipmentOptions: [],
  // 配线架端口下拉选项
  twoPortOptions: [],

  // 机柜下拉选项
  threeCabinetOptions: [],
  // 交换机下拉选项
  threeEquipmentOptions: [],
  // 交换机端口下拉选项
  threePortOptions: [],

  // 空间下拉选择配置
  spaceProps: {
    checkStrictly: true,
    label: 'name',
    value: 'id'
  },
  rules: {
    oneSpaceId: [
      { required: true, message: '位置不能为空！', trigger: 'blur'}
    ],
    oneEquipmentId: [
      { required: true, message: '设备不能为空！', trigger: 'blur'}
    ],
    onePortId: [
      { required: true, message: '端口不能为空！', trigger: 'blur'}
    ],
    twoSpaceId: [
      { required: true, message: '位置不能为空！', trigger: 'blur'}
    ],
    twoCabinetId: [
      { required: true, message: '机柜不能为空！', trigger: 'blur'}
    ],
    twoEquipmentId: [
      { required: true, message: '设备不能为空！', trigger: 'blur'}
    ],
    twoPortId: [
      { required: true, message: '端口不能为空！', trigger: 'blur'}
    ],
    threeSpaceId: [
      { required: true, message: '位置不能为空！', trigger: 'blur'}
    ],
    threeCabinetId: [
      { required: true, message: '机柜不能为空！', trigger: 'blur'}
    ],
    threeEquipmentId: [
      { required: true, message: '设备不能为空！', trigger: 'blur'}
    ],
    threePortId: [
      { required: true, message: '端口不能为空！', trigger: 'blur'}
    ]
  }
})


/**
 * 打开 dialog 弹框
 *
 * @param title
 * @param val
 */
const open = (title, val) => {
  state.title = title
  if (val) {
    let data = JSON.parse(JSON.stringify(val));
    // 线路连接 id
    state.dataForm.id = data.id

    // 信息面板
    if (data.firstSpacePath) {
      state.dataForm.oneSpaceId = data.firstSpacePath.split('/')
    }
    state.dataForm.oneEquipmentId = data.firstEquipmentId
    state.dataForm.onePortId = data.firstPortId

    // 查询信息面板选项
    onChangeQueryEquipments(1, 1, state.dataForm.oneSpaceId)
    // 查询信息面板端口选项
    onChangeQueryEquipmentPorts(1, state.dataForm.oneEquipmentId)


    // 配线架
    if (data.secondSpacePath) {
      state.dataForm.twoSpaceId = data.secondSpacePath.split('/')
    }
    state.dataForm.twoCabinetId = data.secondCabinetId
    state.dataForm.twoEquipmentId = data.secondEquipmentId
    state.dataForm.twoPortId = data.secondPortId

    // 查询配线架 机柜选项列表
    onChangeQueryEquipments(2, 4, state.dataForm.twoSpaceId)
    // 查询 配线架选项列表
    onChangeQueryEquipments(2, 2, state.dataForm.twoCabinetId)
    // 查询 配线架的端口选项列表
    onChangeQueryEquipmentPorts(2, state.dataForm.twoEquipmentId)


    // 交换机
    if (data.thirdSpacePath) {
      state.dataForm.threeSpaceId = data.thirdSpacePath.split('/')
    }
    state.dataForm.threeCabinetId = data.thirdCabinetId
    state.dataForm.threeEquipmentId = data.thirdEquipmentId
    state.dataForm.threePortId = data.thirdPortId

    // 查询交换机 机柜选项列表
    onChangeQueryEquipments(3, 4, state.dataForm.threeSpaceId)
    // 查询交换机 选项列表
    onChangeQueryEquipments(3, 3, state.dataForm.threeCabinetId)
    // 查询交换机端口选项列表
    onChangeQueryEquipmentPorts(3, state.dataForm.threeEquipmentId)
  }
  showDialogRef.value = true

  // 查询位置信息
  querySpaceOptions();
}

/**
 * 关闭弹窗
 */
const close = () => {
  // 表单
  dataFormRef.value.clearValidate()
  state.dataForm = {}
  // 空间初始化
  state.spaceOptions = []
  // 信息面板下拉选项初始化
  state.oneEquipmentOptions = []
  state.onePortOptions = []
  // 配线架下拉选项初始化
  state.twoCabinetOptions = []
  state.twoEquipmentOptions = []
  state.twoPortOptions = []
  // 交换机下拉选项初始化
  state.threeCabinetOptions = []
  state.threeEquipmentOptions = []
  state.threePortOptions = []
  showDialogRef.value = false
  emits('onClose')
}

/**
 * 查询空间位置
 */
const querySpaceOptions = () => {
  let query = {
    deep: 4
  }
  treeAPI(query).then(res => {
    if (res.success) {
      state.spaceOptions = res.data
    }
  })
}

/**
 * 改变下拉选择，触发查询对应设备信息
 */
const onChangeQueryEquipments = (equipmentType, classify, val) => {
  if (val) {
    let param;
    // 设备类型为 1，查询信息面板设备
    if (equipmentType === 1) {
      param = {
        spacePath: val.join('/'),
        classify: classify
      }
    } else if (equipmentType === 2 || equipmentType ===3) { // 设备类型为 2或 3，查询机柜或者配线架、交换机设备
      if (classify === 4) {
        param = {
          spacePath: val.join('/'),
          classify: classify
        }
      } else if (classify === 2 || classify ===3) {
        param = {
          carbineId: val,
          classify: classify
        }
      }
    }

    // 查询设备列表
    maintenanceListAPI(param).then(res => {
      // 信息面板
      if (equipmentType === 1) {
        state.oneEquipmentOptions = res.data.dataList
      } else if (equipmentType === 2) {
        if (classify === 4) {     // 机柜
          state.twoCabinetOptions = res.data.dataList
        } else if (classify === 2) {   // 配线架
          state.twoEquipmentOptions = res.data.dataList
        }
      } else if (equipmentType === 3) {
        if (classify === 4) {    // 机柜
          state.threeCabinetOptions = res.data.dataList
        } else if (classify === 3) {   // 交换机
          state.threeEquipmentOptions = res.data.dataList
        }
      }
    })
  }
}


/**
 * 改变设备选择，触发查询设备端口列表
 */
const onChangeQueryEquipmentPorts = (equipmentType, equipmentId) => {
  if (equipmentId) {
    let params = {
      equipmentId: equipmentId
    }
    listByEquipmentIdAPI(params).then(res => {
      if (res.success) {
        // 查询信息面板设备端口列表
        if (equipmentType === 1) {
          state.onePortOptions = res.data
        } else if (equipmentType === 2) {
          state.twoPortOptions = res.data
        } else if (equipmentType === 3) {
          state.threePortOptions = res.data
        }
      }
    })
  }
}


/**
 * 保存
 */
const onSave = () => {
  dataFormRef.value.validate((valid, fields) => {
    if (valid) {
      let params = {
        id: state.dataForm.id,
        firstEquipmentId: state.dataForm.oneEquipmentId,
        firstPortId: state.dataForm.onePortId,
        secondEquipmentId: state.dataForm.twoEquipmentId,
        secondPortId: state.dataForm.twoPortId,
        thirdEquipmentId: state.dataForm.threeEquipmentId,
        thirdPortId: state.dataForm.threePortId
      }
      saveOrUpdateCabLinkAPI(params).then(res => {
        if (res.success) {
          ElMessage.success('保存成功！')
          close()
        } else {
          ElMessage.error('保存失败！' + res.errorMessage)
        }
      })
    }
  })
}



defineExpose({
  open
})
</script>


<style scoped lang="less">

.content {
  .el-input {
    width: 95%;
  }

  .el-select, .el-cascader {
    width: 95%;
  }

  .el-input-number {
    width: 95%;
    .el-input {
      width: 100%;
    }
  }

  .el-textarea {
    width: 95%;
  }

  :deep(.el-date-editor) {
    width: 95%;
    flex-grow: 0;
  }

  :deep(.el-cascader) {
    width: 95%;
  }
}
</style>
