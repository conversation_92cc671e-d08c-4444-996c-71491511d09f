import {request} from "@/utils/request.js";


export const pageListAPI = (data) => {
    return request('get', '/core/space/pageList', data, 'F');
}

// 树形查询
export const treeAPI = (query) => {
    return request('get', '/core/space/tree', query, 'F')
}

// 树形查询
export const treeGroupAPI = (query = {}) => {
    query = {
        displayCode: true,
        ...query,
    };
    return request("get", "/core/space/treeGroup", query, "F");
};

export const deleteAPI = (data) => {
    return request('post', '/core/space/delete', data, 'F')
}

export const addAPI = (data) => {
    return request('post', '/core/space/add', data)
}

export const updateAPI = (data) => {
    return request('post', '/core/space/update', data)
}

// 空间二维码
export const qrCodeAPI = (data) => {
    return request('get', '/core/space/qrcode/' + data)
}

