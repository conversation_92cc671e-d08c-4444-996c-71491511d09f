<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.CheckRecordPointItemMapper">
    <resultMap type="com.soft.webadmin.model.check.CheckRecordPointItem" id="CheckRecordPointItemResult">
        <result property="id" column="id" />
        <result property="pointId" column="point_id" />
        <result property="itemName" column="item_name" />
        <result property="itemContent" column="item_content" />
        <result property="itemResult" column="item_result" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="selectCheckRecordPointItemVo">
        select id, point_id, item_name, item_content, item_result, remark from sp_check_record_point_item
    </sql>
    
</mapper>