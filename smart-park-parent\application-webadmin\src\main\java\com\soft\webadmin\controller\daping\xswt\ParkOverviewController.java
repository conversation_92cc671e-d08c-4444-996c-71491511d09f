package com.soft.webadmin.controller.daping.xswt;

import com.soft.common.core.object.ResponseResult;
import com.soft.sub.dto.equipment.EquipmentWarningPageQueryDTO;
import com.soft.sub.vo.equipment.EquipmentStatByTypeVO;
import com.soft.sub.vo.equipment.EquipmentWarningVO;
import com.soft.webadmin.service.daping.xswt.ParkOverviewService;
import com.soft.webadmin.vo.daping.xswt.ParkOverview.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @ClassName ParkOverviewController
 * @description:
 * @date 2024年08月12日
 */
@Api(tags = "园区总览")
@RestController
@RequestMapping("/daping/xswt/parkOverview")
public class ParkOverviewController {

    @Autowired
    private ParkOverviewService parkOverviewService;

    @ApiOperation("空间统计")
    @GetMapping("/spaceStatis")
    public ResponseResult<List<SpaceStatisVO>> spaceStatis() {
        return ResponseResult.success(parkOverviewService.spaceStatis());
    }

    @ApiOperation("告警信息")
    @GetMapping("/equipmentWarning")
    public ResponseResult<List<EquipmentWarningVO>> equipmentWarning() {
        return ResponseResult.success(parkOverviewService.equipmentWarning());
    }

    @ApiOperation("设备信息")
    @GetMapping("/equipmentInfo")
    public ResponseResult<EquipmentInfoVO> equipmentInfo() {
        return ResponseResult.success(parkOverviewService.equipmentInfo());
    }

    @ApiOperation("流量信息：车流量")
    @GetMapping("/carInOutCount")
    public ResponseResult<CarInOutCountVO> carInOutCount() {
        return ResponseResult.success(parkOverviewService.carInOutCount());
    }

    @ApiOperation("流量信息：客流")
    @GetMapping("/passengerFlow")
    public ResponseResult<PassengerFlowVO> passengerFlow() throws InterruptedException {
        PassengerFlowVO passengerFlowVO = parkOverviewService.passengerFlow();
        return ResponseResult.success(passengerFlowVO);
    }

    @ApiOperation("实时视频")
    @GetMapping("/equipmentMonitor")
    public ResponseResult<List<EquipmentMonitorVO>> equipmentMonitor() {
        return ResponseResult.success(parkOverviewService.equipmentMonitor());
    }

    @ApiOperation("设备统计")
    @GetMapping("/statEquipmentCountByType")
    public ResponseResult<List<EquipmentStatByTypeVO>> statEquipmentCountByType() {
        return ResponseResult.success(parkOverviewService.statEquipmentCountByType());
    }

    @ApiOperation("按楼幢统计设备数量")
    @GetMapping("/statEquipmentCountByBuilding")
    public ResponseResult<Map<String, Object>> statEquipmentCountByBuilding(@RequestParam Long equipmentTypeId) {
        return ResponseResult.success(parkOverviewService.statEquipmentCountByBuilding(equipmentTypeId));
    }

}
