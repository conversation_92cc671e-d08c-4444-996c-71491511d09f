package com.soft.webadmin.dto.sparePart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * SparePartQuantityChangeDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@ApiModel("SparePartQuantityChangeDTO对象")
@Accessors(chain = true)
@Data
public class SparePartQuantityChangeDTO {

    @ApiModelProperty(value = "仓库id")
    @NotNull(message = "仓库id不能为空！")
    private Long storehouseId;

    @ApiModelProperty(value = "备件id")
    private Long sparePartId;

    @ApiModelProperty(value = "备件名称")
    private String sparePartName;

    @ApiModelProperty(value = "分类id")
    private Long classifyId;

    @ApiModelProperty(value = "规格型号")
    private String model;

    // @ApiModelProperty(value = "条码")
    // private String barCode;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "出入库数量")
    @NotNull(message = "出入库数量不能为空！")
    private Integer changeQuantity;

}
