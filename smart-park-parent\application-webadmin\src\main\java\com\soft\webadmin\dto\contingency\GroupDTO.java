package com.soft.webadmin.dto.contingency;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * GroupDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("GroupDTO对象")
@Data
public class GroupDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "应急事件id")
    @NotNull(message = "所属应急事件不能为空！")
    private Long eventId;

    @ApiModelProperty(value = "小组名称")
    @NotBlank(message = "小组名称不能为空！")
    private String name;

    @ApiModelProperty(value = "职责")
    @NotBlank(message = "职责不能为空！")
    private String duty;

    @ApiModelProperty(value = "应急成员")
    @NotEmpty(message = "应急成员不能为空！")
    @Valid
    private List<GroupMemberDTO> memberDTOList;

}
