<template>
  <dialog-common ref="dialog" title="派单" @submit="submit" :formRef="ruleFormRef" :width="650">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-form-item label="执行人" prop="workUserId">
        <el-select v-model="form.workUserId" placeholder="请选择执行人" filterable clearable>
          <el-option v-for="item in state.workUserList" :value="item.userId" :label="item.showName + (item.deptName ? ' - ' + item.deptName : '')"/>
        </el-select>
      </el-form-item>
      <el-form-item label="备注信息" prop="remark">
        <el-input v-model="form.remark" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit placeholder="请输入备注信息"/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { getWorkUserListAPI, workDispatchAPI } from '@/api/operationManagement/workOrder.js';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  rules: {
    workUserId: [{required: true, message: '请选择接收人员', trigger: 'change'},],
  },
  workUserList: [],
});

// 提交表单
const submit = () => {
  let user = state.workUserList.find(
      (item) => item.userId === form.workUserId
  );
  form.workUserName = user.showName;
  workDispatchAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success('操作成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

const open = (businessType) => {
  dialog.value.open();
  setTimeout(() => {
    getWorkUserListAPI({businessType}).then((res) => {
      state.workUserList = res.data;
    });
  }, 200);
}

defineExpose({
  form,
  open,
});
</script>
