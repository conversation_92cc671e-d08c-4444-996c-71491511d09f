package com.soft.webadmin.dto.sparePart;

import com.soft.common.core.object.MyPageParam;
import com.soft.webadmin.enums.BusinessTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SparePartInoutDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@ApiModel("SparePartInoutDTO对象")
@Data
public class SparePartInoutQueryDTO extends MyPageParam {

    // @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房", required = true)
    // private BusinessTypeEnums businessType = BusinessTypeEnums.OPERATIONS;

    @ApiModelProperty(value = "业务类型：1入库、2出库", required = true)
    private Integer operateType;

    // @ApiModelProperty(value = "审核状态（1待入库/待出库、2已同意）", required = true)
    // private Integer examineState;

    @ApiModelProperty(value = "出入库单号")
    private String inoutNo;

    @ApiModelProperty(value = "出入库类型：1原始入库、2盘盈入库、3剩余备件归还、6备件领用、7盘亏出库")
    private Integer type;

    // @ApiModelProperty(value = "关联工单单号/关联出库单号/关联盘点单号")
    // private String relNo;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建开始日期")
    private String beginDate;

    @ApiModelProperty(value = "创建结束日期")
    private String endDate;

    @ApiModelProperty(value = "备件名称")
    private String sparePartName;

    @ApiModelProperty(value = "关联盘点单号", hidden = true)
    private String stocktakingNo;

}
