<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.WorkOrderMapper">
    <resultMap type="com.soft.webadmin.model.check.WorkOrder" id="WorkOrderResult">
        <result property="id" column="id" />
        <result property="orderNo" column="order_no" />
        <result property="orderType" column="order_type" />
        <result property="priority" column="priority" />
        <result property="businessId" column="business_id" />
        <result property="businessTable" column="business_table" />
        <result property="state" column="state" />
        <result property="workUserId" column="work_user_id" />
        <result property="workTime" column="work_time" />
        <result property="responseTime" column="response_time" />
        <result property="realityFinishTime" column="reality_finish_time" />
        <result property="workDuration" column="work_duration" />
        <result property="handleLimitDuration" column="handle_limit_duration" />
        <result property="treatmentPlan" column="treatment_plan" />
        <result property="score" column="score" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectWorkOrderVo">
        t.id, t.order_no, t.order_type, t.priority, t.business_id, t.business_table, t.state, t.work_user_id, t.work_time,
        t.response_time, t.reality_finish_time, t.work_duration, t.handle_limit_duration, t.treatment_plan, t.score,
        t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <sql id="filter">
        <where>
            and t.deleted_flag = 1
            <if test="orderNo != null and orderNo != ''">
                and t.order_no like concat('%', #{orderNo},'%')
            </if>
            <if test="orderType != null">
                and t.order_type = #{orderType}
            </if>
            <if test="priority != null">
                and t.priority = #{priority}
            </if>
            <if test="state != null">
                and t.state = #{state}
            </if>
            <if test="beginDate != null and beginDate != ''">
                and date_format(t.create_time, '%Y-%m-%d') &gt;= date_format(#{beginDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and date_format(t.create_time, '%Y-%m-%d') &lt;= date_format(#{endDate}, '%Y-%m-%d')
            </if>
            <if test="workUserName != null and workUserName != ''">
                and u.show_name like concat('%', #{workUserName},'%')
            </if>
            <if test="workUserId != null">
                and t.work_user_id = #{workUserId}
            </if>
            <if test="isReport != null and isReport">
                and t.order_type = 'REPAIR' and t.business_table != 'sp_equipment_warning'
            </if>
        </where>
    </sql>

    <select id="queryList" resultType="com.soft.webadmin.vo.check.WorkOrderVO">
        select <include refid="selectWorkOrderVo" />, u.show_name work_user_name
        from sp_work_order t
        left join common_sys_user u on t.work_user_id = u.user_id
        <include refid="filter" />
        order by t.create_time desc
    </select>

    <select id="queryAppList" resultType="com.soft.webadmin.vo.check.WorkOrderAppVO">
        select t.id, order_no, order_type, t.state, t.priority, business_id, business_table, handle_limit_duration,
        t.create_time, r.plan_name, r.start_time,
        (case when t.business_table = 'sp_check_repair_log' then l.content
        when t.business_table = 'sp_equipment_warning' then e.content end) fault_content,
        (case when t.business_table = 'sp_check_repair_log' then l.space_full_name
        when t.business_table = 'sp_equipment_warning' then e.equipment_space_full_name end) space_full_name
        from sp_work_order t
        left join sp_check_repair_log l on t.business_id = l.id and t.business_table = 'sp_check_repair_log'
        left join sp_equipment_warning e on t.business_id = e.id and t.business_table = 'sp_equipment_warning'
        left join sp_check_record r on t.business_id = r.id and t.business_table = 'sp_check_record'
        <include refid="filter" />
        order by t.create_time desc
    </select>
    
</mapper>