<template>
  <dialog-common ref="dialog" title="处理" @submit="submit" :formRef="ruleFormRef" :width="650">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-form-item label="处理人" prop="handleUserId">
        <el-select-v2 v-model="form.handleUserId" placeholder="请选择处理人" filterable clearable :options="state.userOptions">
        </el-select-v2>
      </el-form-item>
      <el-form-item label="过程描述" prop="desc">
        <el-input v-model="form.desc" :rows="5" type="textarea" :maxlength="500" show-word-limit placeholder="请输入过程描述" />
      </el-form-item>
      <el-form-item label="上传附件" prop="fileList">
        <el-upload v-model:file-list="form.fileList" list-type="picture-card" :on-preview="handleImgPreview"
          :http-request="httpRequest" :on-success="(response) => { fileSuccess(response, form.fileList) }" :limit="3">
          <el-icon>
            <Plus />
          </el-icon>
          <template #tip>
            <div class="el-upload__tip">
              支持格式：jpg、png ，单个文件不能超过2MB，最多支持上传3张图片。
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
  </dialog-common>
  <el-dialog v-model="state.dialogVisible" id="imgDialog">
    <img w-full :src="state.dialogImageUrl" alt="Preview Image" style="width: 100%;" />
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus';

import {
  getPageAPI
} from '@/api/iotManagement/person.js';

import { subsystemEliminateAPI } from '@/api/iotManagement/InvasionAlarm.js'

import { annexUpload } from '@/api/file.js';
import { onMounted } from 'vue';

const props = defineProps({
  subSystemId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['submit']);

const ruleFormRef = ref();
const dialog = ref();
const form = reactive({
  fileList: []
});

const state = reactive({
  userOptions: [],
  rules: {
    handleUserId: [{ required: true, message: '请选择处理人' },],
    desc: [{ required: true, message: '请输入过程描述' }]
  },
  fileList: [],
  dialogVisible: false,
  dialogImageUrl: '',
});

onMounted(() => {
  getPerson()
})

// 获取人员
const getPerson = () => {
  getPageAPI({}).then(res => {
    state.userOptions = (res.data.dataList || []).map(item => {
      return {
        label: item.name,
        value: item.id
      }
    })
  })
}

// 覆盖Http
const httpRequest = (option) => {
  const formData = new FormData()
  formData.append('file', option.file)
  return annexUpload(formData)
}

// 查看图片
const handleImgPreview = (uploadFile) => {
  state.dialogImageUrl = uploadFile.url
  state.dialogVisible = true
}

// 上传图片
const fileSuccess = (response, fileList) => {
  fileList[fileList.length - 1].filePath = response.data.filePath
}

// 提交表单
const submit = () => {
  form.annex = (form.fileList || []).map(img => img.filePath).join(',');


  let subForm = {
    subSystemId: props.subSystemId,
    ...form,
  }

  subsystemEliminateAPI(subForm).then(res => {
    if (res.success) {
      ElMessage.success('提交成功')
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

const open = () => {
  dialog.value.open();
}

defineExpose({
  form,
  open,
});
</script>
