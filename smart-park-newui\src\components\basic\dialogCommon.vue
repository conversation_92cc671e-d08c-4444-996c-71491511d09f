<template>
  <el-dialog v-model="dialogVisible" :title="title" :width="width" :close-on-click-modal="false" :before-close="close"
    class="dialogCommon" :append-to-body="true">
    <div class="conent">
      <slot></slot>
    </div>
    <template #footer>
      <span class="dialog-footer" v-if="showButton">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="submitDeb">
          确定
        </el-button>
        <slot name="footerBtn"></slot>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import _ from 'lodash'

const props = defineProps({
  title: { //标题
    type: String,
    default: ''
  },
  width: { // 宽度
    type: Number,
    default: 450
  },
  showButton: { // 按钮是否显示
    type: Boolean,
    default: true
  },
  formRef: { // 表单ref
    type: Object,
    default: () => { return {} }
  }
})

const emit = defineEmits(['submit', 'onClose','didMounted'])

let { title, width, formRef } = toRefs(props)

let dialogVisible = ref(false)

onMounted(() => {
  emit('didMounted')
})

const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
  if(formRef.value instanceof Object && Object.keys(formRef.value).length){
    formRef.value.clearValidate()
    formRef.value.resetFields()
  }
  emit('onClose')
}

const submitDeb = _.debounce(() => { submit()},200)

const submit = () => {
  if(formRef.value instanceof Object && Object.keys(formRef.value).length){
    formRef.value.validate((valid, fields) => {
    if (valid) {
      emit('submit')
    } else {
      console.log('error submit!', fields)
    }
  })
  }else{
    emit('submit')
  }
}

defineExpose({
  open,
  close
})
</script>

<style lang='less'>
</style>
