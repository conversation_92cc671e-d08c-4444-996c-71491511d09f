package com.soft.webadmin.controller.check;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.sub.dto.equipment.EquipmentWarningPageQueryDTO;
import com.soft.sub.vo.equipment.EquipmentWarningVO;
import com.soft.webadmin.dto.check.AlarmCenterHandleDTO;
import com.soft.webadmin.service.check.AlarmCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName AlarmCenterController
 * @description: 告警中心控制器
 * @date 2024年01月30日
 */
@Api(tags = "告警中心")
@RestController
@RequestMapping("/check/alarmCenter")
public class AlarmCenterController {

    @Autowired
    private AlarmCenterService alarmCenterService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<EquipmentWarningVO>> list(EquipmentWarningPageQueryDTO queryDTO) {
        return ResponseResult.success(alarmCenterService.getPage(queryDTO));
    }

    @ApiOperation(value = "告警处理")
    @PostMapping("/handle")
    public ResponseResult<Void> handle(@Validated @RequestBody AlarmCenterHandleDTO handleDTO) {
        return alarmCenterService.handle(handleDTO);
    }

}
