<template>
  <div class="thumb">
    <el-icon color="#2166D8" :size="24" @click="isCollapse = !isCollapse" class="isCollapse">
      <Expand v-if="isCollapse" />
      <Fold v-else />
    </el-icon>
    <div class="thumb-tabs">
      <el-icon color="#ccc" style="margin-left:7px;" v-show="state.showArrow" @click="arrowOperate('left')">
        <ArrowLeftBold />
      </el-icon>
      <div class="thumb-conent" :style="{width: state.showArrow ? 'calc(100% - 53px)' : '100%' }">
        <div class="thumb-conent-scroll">
          <template v-for="(item, index) in tabsList" :key="index">
            <router-link :to="item.path" class="router-link" :class="{ active: item.path == route.fullPath }">
              {{ item.title }}
              <el-icon @click.prevent.stop="removeTags(item)">
                <Close />
              </el-icon>
            </router-link>
          </template>
        </div>
      </div>
      <el-icon color="#ccc" style="margin: 7px;" v-show="state.showArrow" @click="arrowOperate('right')">
        <ArrowRightBold />
      </el-icon>
    </div>
  </div>
</template>

<script setup>
import { menuStore } from '@/store/modules/menu.js'
import _ from 'lodash'

let menu = menuStore()
const { isCollapse, tabsList } = storeToRefs(menu)

const router = useRouter()
const route = useRoute()

const state = reactive({
  showArrow: false,
  moveDistance: 0
})

onMounted(() => {
  window.addEventListener('resize', resizeTagDeb)
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeTagDeb)
})

const addTags = (path,title) => {
  if (tabsList.value.findIndex(item => item.path == path) == -1) {
    if (tabsList.value.length > 12) {
      tabsList.value.splice(0, 1)
    }
    tabsList.value.push({
      path,
      title
    })
    nextTick(() => {
      resizeTags()
    })
  }
}

const removeTags = (params) => {
  let currentIndex = tabsList.value.findIndex(item => item.path == params.path)
  if (currentIndex != -1) {
    tabsList.value.splice(currentIndex, 1)
    if (route.fullPath == params.path && tabsList.value.length) {
      if(currentIndex == tabsList.value.length){
        router.push(tabsList.value[currentIndex - 1].path)
      }else{
        router.push(tabsList.value[currentIndex].path)
      }
    }
  }
  nextTick(() => {
    resizeTags()
  })
}

const resizeTags = () => {
  let conent = document.querySelector('.thumb-conent')
  let scroll = document.querySelector('.thumb-conent-scroll')
  state.showArrow = scroll.scrollWidth > conent.offsetWidth
  nextTick(() => {
    let style = window.getComputedStyle(scroll);
    let matrix = new WebKitCSSMatrix(style.webkitTransform);
    if(scroll.scrollWidth > conent.offsetWidth){
      state.moveDistance = scroll.scrollWidth - conent.offsetWidth
      if(matrix.m41 < 0){  // 向右拉拽.菜单隐藏切换.删除(transfer为负) || 添加新tag(transfer为负)
        scroll.style.transform = `translateX(-${state.moveDistance}px)`
      }
    }else{ // 超出 -> 包含 translateX恢复
      scroll.style.transform = 'translateX(0px)'
    }
  })
}

const resizeTagDeb = _.debounce(() => { resizeTags()},100)

const arrowOperate = (arrow) => {
  let scroll = document.querySelector('.thumb-conent-scroll')
  let style = window.getComputedStyle(scroll);
  let matrix = new WebKitCSSMatrix(style.webkitTransform);
  switch (arrow) {
    case 'left':
      if(matrix.m41 < 0)
        scroll.style.transform = `translateX(0px)`
      break;
    case 'right':
      if(matrix.m41 == 0)
        scroll.style.transform = `translateX(-${state.moveDistance}px)`
      break;

    default:
      break;
  }
}

defineExpose({
  addTags
})
</script>

<style lang='less' scoped>
.thumb {
  width: 100%;
  height: 44px;
  line-height: 44px;
  display: flex;
  border-top: 1px solid #dcdfe6;

  .isCollapse {
    width: 40px;
    height: 100%;

    &:hover {
      background-color: #f7f6f6;
    }
  }

  &-tabs {
    width: calc(100% - 40px);
    height: 100%;
    display: flex;
    align-items: center;
  }

  &-conent {
    overflow: hidden;
    white-space: nowrap;
    &-scroll{
      transition: 0.3s all  cubic-bezier(0.42,0,0.58,1);
    }
  }
}

.router-link {
  margin-left: 7px;
  text-decoration: none;
  color: #333333;
  border: 1px solid #dcdfe6;
  padding: 3px 14px;
  font-size: 14px;
  border-radius: 4px;

  .el-icon {
    vertical-align: middle;
    border-radius: 10px;
  }

  .el-icon:hover {
    background-color: #eee;
  }
}

.active {
  color: #107ff1;
  background-color: #e2f0fa;
  border: none;
}

.active::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 3px;
  background-color: #107ff1;
  display: inline-block;
  vertical-align: middle;
  transform: translateX(-5px);
}</style>
