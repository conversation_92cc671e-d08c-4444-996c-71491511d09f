<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="nameOrNo">
          <el-input v-model="formInline.nameOrNo" placeholder="仓库名称"/>
        </el-form-item>
        <el-form-item prop="headUserId">
          <el-select v-model="formInline.headUserId" filterable clearable placeholder="负责人">
            <el-option v-for="item in state.headUserIdOptions" :label="item.showName + (item.deptName ? ' - ' + item.deptName : '')" :value="item.userId"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="state">
          <el-select v-model="formInline.state" filterable clearable placeholder="状态">
            <el-option v-for="(value, key) in state.stateOptions" :key="key" :label="value" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新增仓库</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="160">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize"
                     :total="state.pagetion.total"
                     @size-change="sizeChange" @current-change="currentChange"/>
      <modalPage ref="modal" :title="state.title" :headUserIdOptions="state.headUserIdOptions"
                 @submit="getList"></modalPage>
    </template>
  </page-common>
</template>

<script setup>
import {ElMessage, ElMessageBox, ElSwitch} from 'element-plus'

import modalPage from './component/modalPage.vue'

import {listUsersAPI} from "@/api/settingSystem/user.js";
import {getStorePageAPI, updateStoreStateAPI, deteleStoreAPI} from '@/api/operationManagement/storeManagement.js'

import {calcPageNo} from '@/utils/util.js'

let formInlineRef = ref()
let modal = ref()

const formInline = reactive({})

const state = reactive({
  title: '',
  headUserIdOptions: [], //负责人
  stateOptions: {
    0: '锁定',
    1: '启用'
  }, //状态
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    // {
    //   prop: 'storehouseNo',
    //   label: '仓库编号'
    // },
    {
      prop: 'storehouseName',
      label: '仓库名称'
    },
    {
      prop: 'spaceFullName',
      label: '位置'
    },
    {
      prop: 'capacity',
      label: '容量（m³）'
    },
    {
      prop: 'headUserName',
      label: '负责人'
    },
    {
      prop: 'remark',
      label: '备注'
    },
    {
      prop: 'state',
      label: '状态',
      width: 80,
      formatter: (row, column, cellValue) => {
        return h(ElSwitch, {
          modelValue: cellValue,
          activeText: '启用',
          activeValue: 1,
          inactiveText: '锁定',
          inactiveValue: 0,
          inlinePrompt: true,
          onChange: (value) => {
            changeSwitch(value,row.id,row.state)
          }
        })
      }
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
  getHandUser()
})

// 获取系统用户
const getHandUser = () => {
  listUsersAPI({}).then((res) => {
    state.headUserIdOptions = res.data.dataList;
  });
}

// 获取仓库
const getList = () => {
  let query = {
    ...formInline,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
    businessType: 'OPERATIONS'
  }
  getStorePageAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 删除仓库
const deleteHandle = ({id}) => {
  ElMessageBox.confirm(
      '是否删除当前仓库?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize)
    deteleStoreAPI({id}).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 更新仓库状态
const changeSwitch = (value,id,state) => {
  if(!state && state != 0){
    return false
  }
  ElMessageBox.confirm(
      value ? '是否启用当前仓库？' :  '是否锁定当前仓库?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    updateStoreStateAPI({id,state:value}).then(res => {
      if (res.success) {
        getList()
        ElMessage.success(value? '启用成功' : '锁定成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })

}

// 新增仓库
const addHandle = () => {
  state.title = '新增仓库'
  modal.value.open()
}

// 编辑仓库
const editHandle = (info) => {
  state.title = '编辑仓库'
  modal.value.open()
  nextTick(() => {
    Object.assign(modal.value.form, {...info})
  })
}
</script>

<style lang='less' scoped></style>
