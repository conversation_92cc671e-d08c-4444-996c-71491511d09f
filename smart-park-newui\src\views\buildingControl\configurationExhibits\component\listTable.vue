<template>
  <page-common v-model="state.tableHeight" :operateBool="false">
    <template #query>
      <el-row justify="space-between">
        <el-form :inline="true" ref="formInlineRef" :model="formInline">
          <el-form-item prop="equipmentWord">
            <el-input v-model="formInline.equipmentWord" placeholder="设备名称/编号" />
          </el-form-item>
          <el-form-item prop="runStatus">
            <el-select v-model="formInline.runStatus" placeholder="在线状态">
              <el-option v-for="(value, key) in state.runStatusOptions" :key="key" :label="value" :value="key" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
            <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-button type="primary" @click="handleExhibition" class="btn-equip">
          <el-icon><CaretRight/></el-icon>
          组态展示
        </el-button>
      </el-row>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" />
        <el-table-column align="center" label="操作" width="290">
          <template #default="scope">
            <el-button link type="primary" icon="VideoCamera" @click.prevent="onPreview(scope.row)" v-if="scope.row.subType == 'MONITOR'">
              预览
            </el-button>
            <el-button link type="primary" icon="Setting" @click.prevent="control(scope.row)" v-else>
              控制
            </el-button>
            <el-button link type="primary" icon="Tickets" @click.prevent="onDetail(scope.row.equipmentId)">
              详情
            </el-button>
          </template>
        </el-table-column>

      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
                     @size-change="sizeChange" @current-change="currentChange" />
      <!-- 控制 -->
      <equip-control ref="equip" title="设备控制" />

      <!-- 视频预览 -->
      <video-modal title="视频预览" ref="videoRef"></video-modal>
    </template>
  </page-common>
</template>

<script setup>
import { ElTag, ElMessage } from 'element-plus'

import {getEquipListAPI} from "@/api/iotManagement/equipManage.js";
import { perviewURLs } from "@/api/iotManagement/realtime.js";
import {CaretLeft, CaretRight} from "@element-plus/icons-vue";


const emit = defineEmits(['showPage'])

import useSubTypeOptions from '@/hooks/option/useSubTypeOptions.js'

// 设备类别（子系统类型）
const { subTypeDictionaries } = useSubTypeOptions()

// 类型
const typeOptions = {
  BA: 'BA',
  ENTRANCE_GUARD: '门禁',
  FIRE_FIGHTING: '消防',
  INFORMATION: '信息发布',
  LADDER_CONTROL: '梯控',
  LIGHTING: '照明',
  MONITOR: '监控',
  PARKING: '停车',
  ENERGY: '能耗',
  CANTEEN: '餐厨',
  INTRUSION_ALARM: '入侵报警',
  ONE_CARD: '一卡通'
}

const drawer = ref()
const formInlineRef = ref()
const equip = ref()
const formInline = reactive({})


const videoRef = ref()

const state = reactive({
  runStatusOptions:{
    0: '离线',
    1: '正常'
  },
  runStatusColors:{
    0:'#F56C6C',
    1:'#67C23A'
  },
  equipmentStatusOptions:{
    0:'故障',
    1:'正常',
    5:'已报废'
  },
  equipmentStatusColors:{
    0:'#F56C6C',
    1:'#67C23A',
    5:'#73767a'
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'equipmentCode',
      label: '设备编号'
    },
    {
      prop: 'equipmentName',
      label: '设备名称',
    },
    {
      prop: 'subType',
      label: '设备类别',
      // formatter: (row, column, cellValue) => {
      //   return h(ElTag, { type: 'info' }, { default: () => typeOptions[cellValue] })
      // }
      formatter: (row, column, cellValue) => {
        return h(ElTag, { }, { default: () => toRaw(subTypeDictionaries)[cellValue] })
      }
    },
    {
      prop: 'equipmentType',
      label: '设备型号'
    },
    {
      prop: 'spaceFullName',
      label: '安装位置',
    },
    {
      prop: 'runStatus',
      label: '在线状态',
      formatter: (row, column, cellValue) => {
        return h("div", [
          h("span", {
            class: "status-circle",
            style: "background-color: " + state.runStatusColors[cellValue],
          }),
          state.runStatusOptions[cellValue],
        ]);
      }
    },
    {
      prop: 'equipmentStatus',
      label: '设备状态',
      formatter: (row, column, cellValue) => {
        return h("div", [
          h("span", {
            class: "status-circle",
            style: "background-color: " + state.equipmentStatusColors[cellValue],
          }),
          state.equipmentStatusOptions[cellValue],
        ]);
      }
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

const open = (idList) => {
  state.idList = idList
  getList()
}

// 获取设备
const getList = () => {
  let query = {
    ...formInline,
    equipmentIds: state.idList,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
  }
  getEquipListAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}


//控制
const control = (info) => {
  equip.value.open(info)
}


/**
 * 视频预览
 * @param val
 */
const onPreview = (val) => {
  perviewURLs({ equipmentId: val.equipmentId }).then(res => {
    if (res.success) {
      videoRef.value.open(res.data,val)
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

/** 查看详情 */
const onDetail = (equipmentId) => {
  emit('showPage', 2, equipmentId);
};

// 返回组态
const handleExhibition = () => {
  emit('showPage', 0);
}

defineExpose({
  open
})
</script>
