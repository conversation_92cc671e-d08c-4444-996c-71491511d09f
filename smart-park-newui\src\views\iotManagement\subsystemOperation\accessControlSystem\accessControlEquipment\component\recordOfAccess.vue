<template>
  <tree-page-common v-model="state.tableHeight" :leftBool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
        <el-form-item prop="equipmentName">
          <el-input v-model="formInline.equipmentName" placeholder="设备名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" />
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />
    </template>
  </tree-page-common>
</template>

<script setup>
import {
  Delete, Plus, Search, Refresh
} from '@element-plus/icons-vue'
import { inOutList } from "@/api/iotManagement/door.js";
import { ElMessage, ElMessageBox, dayjs, ElTag } from "element-plus";

let formInlineRef = ref()
const formInline = reactive({})

const state = reactive({
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'visitorName',
      label: '姓名'
    },
    {
      prop: 'type',
      label: '人员类型',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { type: "success" }, { default: () => "员工" })
      }
    },
    {
      prop: 'equipmentNo',
      label: '设备编号'
    },
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'evenId',
      label: '通行类型',
      formatter: (row, column, cellValue) => {
        if (cellValue == 0) {
          return h(ElTag, { type: "success" }, { default: () => "进" })
        } else if (cellValue == 1) {
          return h(ElTag, { type: "danger" }, { default: () => "出" })
        }
      }
    },
    {
      prop: 'openTime',
      label: '通行时间',
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

//分页
const getList = () => {
  let query = {
    equipmentName: formInline.equipmentName,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize
  }
  inOutList(query).then(res => {
    res.data.dataList.forEach(e => {
      if (e.recordType) {
        if (e.recordType == 1) {
          e.type = '住户'
          e.name = e.ownerName
        } else if (e.recordType == 2) {
          e.type = '访客'
          e.name = e.visitorName
        }
      }
    })
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

//重置方法
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped></style>
