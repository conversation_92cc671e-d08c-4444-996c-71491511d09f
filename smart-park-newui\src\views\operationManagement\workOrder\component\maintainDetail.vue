<template>
  <dialog-common ref="dialog" :width="1200" title="详情" :show-button="false">
    <el-card class="box-card card-textBg" style="border: none">
      <el-form label-position="top" label-width="110px">
        <div>
          <el-row class="row-bg" justify="space-between">
            <div class="divFlex">
              <div class="divLeft"></div>
              <div class="divRight">工单信息</div>
            </div>
            <order-status :status="state.dataInfo.state"></order-status>
          </el-row>

            <el-row :gutter="40">
              <el-col :span="6">
                <el-form-item label="工单编号">
                  {{ state.dataInfo.orderNo }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="工单类别">
                  {{ state.orderTypeOptions[state.dataInfo.orderType] }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="故障设备">
                  <div v-if="state.dataInfo.businessTable === 'sp_check_repair_log'">
                    {{ state.dataInfo.checkRepairLogVO.equipmentName }}
                  </div>
                  <div v-if="state.dataInfo.businessTable === 'sp_equipment_warning'">
                    {{ state.dataInfo.equipmentWarningVO.equipmentName }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="位置">
                  <div v-if="state.dataInfo.businessTable === 'sp_check_repair_log'">
                    {{ state.dataInfo.checkRepairLogVO.spaceFullName }}
                  </div>
                  <div v-if="state.dataInfo.businessTable === 'sp_equipment_warning'">
                    {{ state.dataInfo.equipmentWarningVO.equipmentSpaceFullName }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="优先级">
                  <el-tag
                      :type="state.dataInfo.priority === 1 ? 'primary' : (state.dataInfo.priority === 2 ? 'warning' : 'danger')">
                    {{ state.priorityOptions[state.dataInfo.priority] }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="预计完成时间">
                  {{ state.dataInfo.predictRepairTime }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="完成时间" prop="realityFinishTime">
                  {{ state.dataInfo.realityFinishTime }}
                </el-form-item>
              </el-col>
              <el-col v-if="state.dataInfo.businessTable === 'sp_check_repair_log'" :span="6">
                <el-form-item label="报修科室">
                  {{ state.dataInfo.checkRepairLogVO.reportDeptName }}
                </el-form-item>
              </el-col>
              <el-col v-if="state.dataInfo.businessTable === 'sp_check_repair_log'" :span="6">
                <el-form-item label="报修人">
                  {{ state.dataInfo.checkRepairLogVO.reportUserName }}
                </el-form-item>
              </el-col>
              <el-col v-if="state.dataInfo.businessTable === 'sp_check_repair_log'" :span="6">
                <el-form-item label="联系电话">
                  {{ state.dataInfo.checkRepairLogVO.reportUserPhone }}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="问题描述">
                  <div v-if="state.dataInfo.businessTable === 'sp_check_repair_log'">
                    {{ state.dataInfo.checkRepairLogVO.content }}
                  </div>
                  <div v-if="state.dataInfo.businessTable === 'sp_equipment_warning'">
                    {{ state.dataInfo.equipmentWarningVO.content }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col v-if="state.dataInfo.businessTable === 'sp_check_repair_log'" :span="24">
                <el-form-item label="附件" class="item__content-noBg">
                  <img-video :list="state.fileList"></img-video>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item v-if="state.dataInfo.score" label="评分"  class="item__content-noBg">
                  <el-rate v-model="state.dataInfo.score" disabled/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item v-if="state.dataInfo.score" label="评价">
                  {{ resultCommon.content }}
                </el-form-item>
              </el-col>
            </el-row>
          <div class="divFlex" v-show="state.dataInfo.state === 5">
            <div class="divLeft"></div>
            <div class="divRight">处理信息</div>
          </div>
          <el-row v-show="state.dataInfo.state === 5">
            <el-col :span="24">
              <el-form-item label="过程描述">
                {{ resultWork.describe }}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item class="item__content-noBg" label="附件">
                <img-video :list="pictureVideo(resultWork.img)"></img-video>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item class="item__content-noBg">
                <el-table :data="state.dataInfo.workOrderSparePartVOList">
                  <el-table-column v-for="(item, index) in state.quoteHeader" :key="index" :align="item.align"
                                   :formatter="item.formatter"
                                   :label="item.label" :prop="item.prop" :width="item.width"/>
                </el-table>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注">
                {{ resultWork.remarks }}
              </el-form-item>
            </el-col>
          </el-row>
          </div>
      </el-form>
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">工单日志</div>
      </div>
      <orderLog :data="state.dataInfo.workOrderLogVOList" class="detail-area"></orderLog>
    </el-card>
  </dialog-common>
</template>

<script setup>
import {getWorkOrderDetailAPI} from '@/api/operationManagement/workOrder.js';

import orderLog from './orderLog.vue'
import orderStatus from './orderStatus.vue'

import {pictureVideo} from '@/utils/util';
import {ElTag} from "element-plus";

const dialog = ref()

const state = reactive({
  id: undefined,  // 工单id
  priorityOptions: {
    1: '普通',
    2: '紧急',
    3: '特急',
  },
  businessTypeOptions: {
    OPERATIONS: '运维',
    CLEANING: '保洁',
    TRANSPORT: '运送',
    PROPERTY: '房产',
    STOREHOUSE: '库房'
  },
  orderTypeOptions: {
    'PATROL_INSPECTION': '巡检工单',
    'MAINTENANCE': '维保工单',
    'REPAIR': '维修工单',
    'CLEANING': '保洁工单',
    'CLEANING_TEMP': '临时保洁',
    'TRANSPORT_LOOP': '循环运送',
    'TRANSPORT_TEMP': '临时运送'
  },
  dataInfo: {
    checkRepairLogVO: {},
    equipmentWarningVO: {},
    equipmentLifeCycleVOList: [],
    workOrderLogVOList: []
  },
  baseUrl: import.meta.env.VITE_BASE_URL,
  fileList: [],
  loginUserId: undefined,
  title: '',
  quoteHeader: [
    {
      prop: 'sparePartName',
      label: '备件名称',
    },
    {
      prop: 'classifyName',
      label: '备件分类'
    },
    {
      prop: 'receiveQuantity',
      label: '领用数量'
    },
  ],
  quoteList: [],   // 报价记录
  laborCost: 0,  // 人工费
  total: {} // 总计人工
});

// 完成结果内容
const resultWork = computed(() => {
  let result = state.dataInfo.workOrderLogVOList.find(item => item.operate == 11) || {content: '{}'}
  return JSON.parse(result.content)
})

// 评价
const resultCommon = computed(() => {
  let result = state.dataInfo.workOrderLogVOList.find(item => item.operate == 12) || {content: '{}'}
  return JSON.parse(result.content)
})

const open = (id) => {
  state.id = id
  refreshInfo()
}

// 刷新工单信息
const refreshInfo = () => {
  state.dataInfo.workOrderSparePartVOList = []
  getWorkOrderDetailAPI({id: state.id}).then((res) => {
    console.log(res.data)
    Object.assign(state.dataInfo, res.data);
    if (res.data.checkRepairLogVO) {
      state.fileList = pictureVideo(res.data.checkRepairLogVO.img);
    }
    dialog.value.open()
  });
};


defineExpose({
  open
})
</script>

<style lang="less" scoped>
.tag {
  margin: 5px 5px 5px 0;
}


:deep(.child-table .el-table__header th.el-table__cell) {
  padding: 8px 0;
  background-color: #FFFFFF !important;
  color: #7e7e7e;
}

:deep(.child-table .el-table__body .el-table__cell) {
  padding: 5px 0;
}
</style>
