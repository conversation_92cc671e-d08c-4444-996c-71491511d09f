<template>
  <el-drawer :modelValue="drawer" direction="rtl" :before-close="cancelClick" :size="type ? 750 : '50%'">
    <template #header>
      <h4>{{ type ? '设备信息' : '详情' }}</h4>
    </template>
    <template #default>
      <el-tabs v-model="activeName" :class="{ 'tabsClass': type }" @tab-click="handleClick">
        <el-tab-pane label="设备详情" name="first">
          <div>
            <div class="divFlex">
              <div class="divLeft"></div>
              <div class="divRight">基本信息</div>
            </div>
            <el-form label-width="100px" :model="state.tableData" label-suffix=":" style="margin-top: 20px;">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="设备编号" prop="equipmentNo">
                    <span @click="copyHandle('设备编号', state.tableData.equipmentNo)">{{ state.tableData.equipmentNo
                    }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备名称" prop="equipmentName">
                    <span @click="copyHandle('设备名称', state.tableData.equipmentName)">{{ state.tableData.equipmentName
                    }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="设备型号" prop="equipmentModel">
                    {{ state.tableData.equipmentModel }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="生产厂家" prop="factory">
                    {{ state.tableData.factory }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="保修期" prop="serviceName">
                    {{ state.tableData.warrantyBeginDate }} - {{ state.tableData.warrantyEndDate }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div>
            <div class="divFlex">
              <div class="divLeft"></div>
              <div class="divRight">管理信息</div>
            </div>
            <el-form label-width="100px" :model="state.tableData" label-suffix=":" style="margin-top: 20px;">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="安装位置" prop="spaceFullName">
                    {{ state.tableData.spaceFullName }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="启用时间" prop="activationDate">
                    {{ state.tableData.activationDate }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="设备分组" prop="equipmentGroupName">
                    {{ state.tableData.equipmentGroupName }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="关联设备" prop="relationEquipmentName">
                    {{ state.tableData.relationEquipmentName }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="管理部门" prop="deptName">
                    {{ state.tableData.deptName }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="负责人" prop="ownerName">
                    {{ state.tableData.ownerName }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="三维坐标" prop="coordinate">
                    {{ state.tableData.coordinate }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div v-if="state.attributeList.length > 0">
            <div class="divFlex">
              <div class="divLeft"></div>
              <div class="divRight">运行信息</div>
            </div>
            <el-form label-width="100px" :model="state.tableData" label-suffix=":" style="margin-top: 20px;">
              <el-row v-if="state.tableData.subType != null && state.tableData.subType !== 'MONITOR'">
                <template v-for="attribute in state.attributeList" :key="attribute.id">
                  <el-col :span="12">
                    <el-form-item :label="attribute.attributeName ? attribute.attributeName : attribute.attributeKey"
                      v-if="attribute.attributeValueEnum">
                      <template v-for="(val, key, i) in attribute.valueJson" :key="i">
                        <span v-if="attribute.attributeValue === key">{{ val }}</span>
                      </template>
                    </el-form-item>
                    <el-form-item :label="attribute.attributeName ? attribute.attributeName : attribute.attributeKey"
                      v-else>
                      {{ attribute.attributeValue }}
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
              <el-row v-else>
                <template v-for="attribute in state.attributeList" :key="attribute.id">
                  <el-col :span="24">
                    <el-form-item :label="attribute.attributeName" v-if="attribute.attributeKey === 'channelCode'">
                      {{ attribute.attributeValue }}
                    </el-form-item>
                    <el-form-item :label="attribute.attributeName" v-if="attribute.attributeKey === 'config'">
                      {{ attribute.attributeValueEnum }}
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </el-form>
          </div>
          <div>
            <div class="divFlex">
              <div class="divLeft"></div>
              <div class="divRight">其他</div>
            </div>
            <el-form label-width="100px" :model="state.tableData" label-suffix=":" style="margin-top: 20px;">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    {{ state.tableData.remark }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="进出记录" name="second" v-if="recordOfAccess">
          <tree-page-common v-model="state.tableHeight" :leftBool="false" style="height: calc(100vh - 170px);">
            <template #query>
              <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
                <el-form-item prop="time">
                  <el-date-picker v-model="formInline.time" type="datetimerange" range-separator="到"
                    start-placeholder="开始时间" end-placeholder="结束时间" @change="change" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
                  <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
              </el-form>
            </template>
            <template #table>
              <el-table :data="state.pagetTableData" :height="state.tableHeight">
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                  :label="item.label" :align="item.align" :formatter="item.formatter" />
              </el-table>
              <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
                @size-change="sizeChange" @current-change="currentChange" />
            </template>
          </tree-page-common>
        </el-tab-pane>
        <el-tab-pane label="维保记录" name="third">维保记录</el-tab-pane>
        <el-tab-pane label="维修记录" name="fourth">维修记录</el-tab-pane>
        <el-tab-pane label="操作记录" name="five">操作记录</el-tab-pane>
      </el-tabs>
    </template>
  </el-drawer>
</template>

<script setup>

import { inOutList } from "@/api/iotManagement/door.js";

import { Search, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, ElTag,dayjs } from 'element-plus';
import { events } from '@/utils/bus.js'
import { nextTick } from "vue";
const props = defineProps({
  drawer: {
    type: Boolean,
    default: false
  },
  type: {
    type: Boolean,
    default: true
  },
  recordOfAccess: {
    type: Boolean,
    default: true
  },
})

let { drawer, type } = toRefs(props)

const emit = defineEmits(['cancelClick'])
const activeName = ref('first')
let formInlineRef = ref()
const formInline = reactive({})
const state = reactive({
  deviceId:'',
  tableHeight: 100,
  tableData: [],
  attributeList: [],
  pagetTableData: [],
  tableHeader: [
  {
      prop: 'visitorName',
      label: '姓名'
    },
    {
      prop: 'type',
      label: '人员类型',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { type: "success" }, { default: () => "员工" })
      }
    },
    {
      prop: 'equipmentNo',
      label: '设备编号'
    },
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'evenId',
      label: '通行类型',
      formatter: (row, column, cellValue) => {
        if (cellValue == 0) {
          return h(ElTag, { type: "success" }, { default: () => "进" })
        } else if (cellValue == 1) {
          return h(ElTag, { type: "danger" }, { default: () => "出" })
        }
      }
    },
    {
      prop: 'openTime',
      label: '通行时间',
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

//设备详情
const loadEquipmentInfo = () => {
  if (activeName.value == 'first') {
    nextTick(() => {
      let query = {
        equipmentId: state.deviceId
      }
      viewEquipAPI(query).then(res => {
        if (res.success) {
          state.tableData = res.data
        } else {
          state.tableData = {}
          return;
        }
        var e = res.data
        //设备分组
        if (e.equipmentGroupList) {
          const equipmentGroupName = [];
          e.equipmentGroupList.map((i) => {
            equipmentGroupName.push(i.itemName);
          });
          e.equipmentGroupName = equipmentGroupName.join('、');
        }
        //关联设备
        if (e.equipmentRelationList) {
          const relationEquipmentName = [];
          e.equipmentRelationList.map((i) => {
            relationEquipmentName.push(i.relatedEquipmentName);
          });
          e.relationEquipmentName = relationEquipmentName.join('、');
        }
        //设备属性
        if (e.equipmentAttributeList) {
          state.attributeList = e.equipmentAttributeList;
          state.attributeList.map((attribute) => {
            if (attribute.attributeValueEnum) {
              const json = JSON.parse(attribute.attributeValueEnum);
              attribute.valueJson = json;
            }
          });
        }
      })
    });
  } else if (activeName.value == 'second') {
    state.pagetion.pageNum = 1
    getList();
  }

}

//进出记录
const getList = () => {
  let query = {
    equipmentId: state.deviceId,
    equipmentName: formInline.equipmentName,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
    startDate: state.startDate,
    endDate: state.endDate
  }
  inOutList(query).then(res => {
    res.data.dataList.forEach(e => {
      if (e.recordType) {
        if (e.recordType == 1) {
          e.type = '住户'
          e.name = e.ownerName
        } else if (e.recordType == 2) {
          e.type = '访客'
          e.name = e.visitorName
        }
      }
    })
    state.pagetTableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

const cancelClick = () => {
  emit('cancelClick')
}

//切换
const handleClick = (tab, event) => {
  if (tab.props.label == '设备详情') {
    loadEquipmentInfo();
  } else if (tab.props.label == '进出记录') {
    getList();
    nextTick(() => {

      events.emit('tabClick')
    })
  }
}

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

//重置方法
const onReset = () => {
  state.startDate = ''
  state.endDate = ''
  formInlineRef.value.resetFields()
  onSubmit()
}
defineExpose({
  loadEquipmentInfo,
  state
})

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

const change = (value) => {
  state.startDate = value[0]
  state.endDate = value[1]
}

/** 复制 */
const copyHandle = (title, val) => {
  var data = val;
  var oInput = document.createElement('input');
  oInput.value = data;
  document.body.appendChild(oInput);
  oInput.select();
  document.execCommand('Copy');
  oInput.className = 'oInput';
  oInput.style.display = 'none';
  ElMessage.success(title + '复制成功');
};
</script>

<style lang='less' scoped>
.divFlex {
  display: flex;
  align-items: center;
}

.divRight {
  margin-left: 10px;
}

.divLeft {
  width: 7px;
  height: 20px;
  background-color: #3f9eff;
}

.tabsClass {
  ::v-deep .el-tabs__header {
    display: none;
  }
}</style>
