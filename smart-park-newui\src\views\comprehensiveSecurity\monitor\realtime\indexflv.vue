<template>
  <div class="page">
    <div class="tree">
      <el-input v-model="filterText" placeholder="请输入搜索关键词" />
      <el-tree ref="treeRef" :data="state.data" :props="{ label: 'name' }" :filter-node-method="filterNode"
        highlight-current accordion style="margin-top: 10px;" @node-click="handleNodeClick">
        <template #default="{ node, data }">
          <el-icon v-show="data.type == 2" style="margin-right: 5px;" color="#409EFF">
            <VideoCamera />
          </el-icon>
          {{ data.name }}
        </template>
      </el-tree>
    </div>
    <div class="video">
      <el-radio-group v-model="state.radio" class="radio-group">
        <el-radio-button label="1">
          <img src="@/assets/img/oneIcon.png" alt="" srcset="">
        </el-radio-button>
        <el-radio-button label="4">
          <img src="@/assets/img/fourIcon.png" alt="" srcset="">
        </el-radio-button>
      </el-radio-group>

      <!-- 一路 -->
      <div class="one-way" v-show="state.radio == 1">
        <div style="width: 100%;height: 100%;" ref="videoMain" v-if="showVideo">
        </div>
        <el-empty description="视频播放失败" v-else>
          <el-button type="primary" @click="reconnection">点击重连</el-button>
        </el-empty>
      </div>

      <!-- 四路 -->
      <div class="grid" v-show="state.radio == 4">
        <div class="four-ways" :class="{ 'active': index == state.currentIndex }" v-for="(item, index) in urlList"
          @click="handleClick(index)">
          <div style="width: 100%;height: 100%;" :ref="el => videoMainList[index] = el" v-if="showVideoList[index]">
          </div>
          <el-empty description="视频播放失败" v-else>
            <el-button type="primary" @click="reconnectionFour(index)">点击重连</el-button>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import DPlayer from 'dplayer';
import flvjs from 'flv.js'

import { perviewURLs, equipTree } from '@/api/iotManagement/realtime.js'
import { equipContorlAPI } from '@/api/settingSystem/topoConnect.js'

// 一路
let url
let dp;
let flvPlayer
let videoMain = ref()
const showVideo = ref(true)

// 四路
let urlList = new Array(4);
let dpList = [];
let flvPlayerList = [];
let videoMainList = ref([])
let showVideoList = ref(new Array(4).fill(true))


const filterText = ref('')
const treeRef = ref()

const state = reactive({
  radio: 1,
  data: [],
  currentIndex: 0
})

onMounted(() => {
  getMonitor()
})

onBeforeUnmount(() => {
  videoDestroy()
  showVideoList.value.forEach((item,index) => {
    videoDestroyFour(index)
  });
})

// 获取监控树形
const getMonitor = () => {
  equipTree({
    subSystemEnums: 'MONITOR'
  }).then(res => {
    state.data = res.data || []
  })
}

// 点击视频
const handleClick = (index) => {
  state.currentIndex = index
}

// 选择节点
const handleNodeClick = (node) => {
  if (node.type == 2) { // type 1空间，2设备
    perviewURLs({ equipmentId: node.id }).then(res => {
      handleChoose(res.data)
    })
  }
}

// 视频选择
const handleChoose = (vidoeurl) => {
  if (state.radio == 1) {  // 一路
    url = vidoeurl
    if (dp) {
      handleChange()
    } else {
      if(showVideo.value){
        initVideo()
      }else{
        reconnection()
      }
    }
  } else if (state.radio == 4) { //四路
    urlList[state.currentIndex] = vidoeurl
    if (dpList[state.currentIndex]) {
      handleChangeFour(state.currentIndex)
    } else { // 初次加载
      if(showVideoList.value[state.currentIndex]){
        initVideoFour(state.currentIndex)
      }else{
        reconnectionFour(state.currentIndex)
      }
    }
  }
}

// 初始化摄像头
const initVideo = () => {
  dp = new DPlayer({
    container: videoMain.value,
    live: true,
    autoplay: true,
    preventClickToggle: true,
    screenshot: true,
    hotkey: true,
    mutex: false,
    preload: 'auto',
    volume: 0,
    video: {
      url,
      type: 'customFlv',
      customType: {
        customFlv: function (video, player) {
          flvPlayer = flvjs.createPlayer({
            type: 'flv',
            url: video.src,
            enableStashBuffer: false,
            isLive: true,
          });
          flvPlayer.attachMediaElement(video);
          flvPlayer.load();
        },
      },
    },
  });

  dp.on('play', function () {
    if (flvPlayer.buffered.length) {
      let end = flvPlayer.buffered.end(0);//获取当前buffered值
      let diff = end - flvPlayer.currentTime;//获取buffered与currentTime的差值
      if (diff >= 2.5) {//如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定
        dp.seek(flvPlayer.buffered.end(0));
      }
    }
  });
  flvPlayer.on(flvjs.Events.ERROR, function (errorType, errorDetail, errorInfo) {
    ElMessage({
      message: errorInfo?.msg,
      type: 'error',
    })
    videoDestroy()
    showVideo.value = false
  });
}

// 四路初始化
const initVideoFour = (index) => {
  dpList[index] = new DPlayer({
    container: videoMainList.value[index],
    live: true,
    autoplay: true,
    preventClickToggle: true,
    screenshot: true,
    hotkey: true,
    mutex: false,
    preload: 'auto',
    volume: 0,
    video: {
      url: urlList[index],
      type: 'customFlv',
      customType: {
        customFlv: function (video, player) {
          flvPlayerList[index] = flvjs.createPlayer({
            type: 'flv',
            url: urlList[index],
            enableStashBuffer: false,
            isLive: true,
          });
          flvPlayerList[index].attachMediaElement(video);
          flvPlayerList[index].load();
        },
      },
    },
  });

  dpList[index].on('play', function () {
    if (flvPlayerList[index].buffered.length) {
      let end = flvPlayerList[index].buffered.end(0);//获取当前buffered值
      let diff = end - flvPlayerList[index].currentTime;//获取buffered与currentTime的差值
      if (diff >= 2.5) {//如果差值大于等于0.5 手动跳帧 这里可根据自身需求来定
        dpList[index].seek(flvPlayerList[index].buffered.end(0));
      }
    }
  });
  flvPlayerList[index].on(flvjs.Events.ERROR, function (errorType, errorDetail, errorInfo) {
    ElMessage({
      message: errorInfo?.msg,
      type: 'error',
    })
    videoDestroyFour(index)
    showVideoList.value[index] = false
  });
}

// 视频销毁
const videoDestroy = () => {
  if (flvPlayer && dp) {
    flvPlayer.pause();
    flvPlayer.unload();
    flvPlayer.detachMediaElement();
    flvPlayer.destroy();
    flvPlayer = null;
    dp.destroy()
    dp = null
  }
}

// 视频销毁四路
const videoDestroyFour = (index) => {
  if (flvPlayerList[index] && dpList[index]) {
    flvPlayerList[index].pause();
    flvPlayerList[index].unload();
    flvPlayerList[index].detachMediaElement();
    flvPlayerList[index].destroy();
    flvPlayerList[index] = null;
    dpList[index].destroy()
    dpList[index] = null
  }
}

// 重新连接
const reconnection = () => {
  showVideo.value = true
  nextTick(() => {
    initVideo()
  })
}

// 重新连接四路
const reconnectionFour = (index) => {
  showVideoList.value[index] = true
  nextTick(() => {
    initVideoFour(index)
  })
}

// 切换摄像头
const handleChange = () => {
  flvPlayer.unload();
  flvPlayer.detachMediaElement();
  flvPlayer.destroy();

  nextTick(() => {
    flvPlayer = flvjs.createPlayer({
      type: 'flv',
      url,
      enableStashBuffer: false,
      isLive: true,
    });
    flvPlayer.attachMediaElement(dp.video);
    flvPlayer.load();
    flvPlayer.play()
    flvPlayer.on(flvjs.Events.ERROR, function (errorType, errorDetail, errorInfo) {
      ElMessage({
        message: errorInfo?.msg,
        type: 'error',
      })
      videoDestroy()
      showVideo.value = false
    });
  })
}

// 切换摄像头四路
const handleChangeFour = (index) => {
  flvPlayerList[index].unload();
  flvPlayerList[index].detachMediaElement();
  flvPlayerList[index].destroy();

  nextTick(() => {
    flvPlayerList[index] = flvjs.createPlayer({
      type: 'flv',
      url: urlList[index],
      enableStashBuffer: false,
      isLive: true,
    });
    flvPlayerList[index].attachMediaElement(dpList[index].video);
    flvPlayerList[index].load();
    flvPlayerList[index].play()
    flvPlayerList[index].on(flvjs.Events.ERROR, function (errorType, errorDetail, errorInfo) {
      ElMessage({
        message: errorInfo?.msg,
        type: 'error',
      })
      videoDestroyFour(index)
      showVideoList.value[index] = false
    });
  })
}

// 过滤方法
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value)
}

watch(filterText, (val) => {
  treeRef.value.filter(val)
})
</script>

<style lang='less' scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;

  .tree {
    width: 15%;
    height: 100%;
    margin-right: 20px;
    flex-shrink: 0;
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
    overflow: auto;
  }

  .video {
    flex: 1;
    background: #FFFFFF;
    border-radius: 10px;
    overflow: hidden;
    padding: 18px;

    .radio-group {
      margin-bottom: 10px;

      :deep(.el-radio-button__inner) {
        padding: 6px;
      }

      img {
        width: 26px;
      }
    }

    .one-way {
      height: calc(100% - 65px);
      cursor: pointer;
      border-radius: 5px;
      background-image: url('@/assets/img/videobg.png');
      background-size: 100% 100%;
    }

    .grid {
      height: calc(100% - 65px);
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      grid-gap: 10px;

      .four-ways {
        cursor: pointer;
        border-radius: 5px;
        background-image: url('@/assets/img/videobg.png');
        background-size: 100% 100%;
        overflow: hidden;
      }

      .active {
        border: 3px solid #409EFF;
      }
    }
  }
}
</style>
