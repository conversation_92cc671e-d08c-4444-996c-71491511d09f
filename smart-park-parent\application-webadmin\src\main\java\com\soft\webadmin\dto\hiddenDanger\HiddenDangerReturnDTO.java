package com.soft.webadmin.dto.hiddenDanger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * HiddenDangerRectifyDTO对象
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@ApiModel("HiddenDangerRectifyDTO对象")
@Data
public class HiddenDangerReturnDTO {

    @ApiModelProperty(value = "隐患id")
    @NotNull(message = "隐患id不能为空！")
    private Long hiddenDangerId;

    @ApiModelProperty(value = "退回原因")
    @NotBlank(message = "退回原因不能为空！")
    private String returnReason;

    @ApiModelProperty(value = "上传照片")
    private String rectifyImgs;

    @ApiModelProperty(value = "操作（1处理、2退回、3结束）", hidden = true)
    private Integer operate = 2;

}
