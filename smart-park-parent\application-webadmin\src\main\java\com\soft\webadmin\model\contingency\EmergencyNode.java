package com.soft.webadmin.model.contingency;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.contingency.EmergencyNodeVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 应急预案节点对象 cm_emergency_node
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Data
@TableName(value = "cm_emergency_node")
public class EmergencyNode {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 预案id */
    private Long emergencyId;

    /** 节点名称 */
    private String nodeName;


    @Mapper
    public interface EmergencyNodeModelMapper extends BaseModelMapper<EmergencyNodeVO, EmergencyNode> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        EmergencyNode toModel(EmergencyNodeVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        EmergencyNodeVO fromModel(EmergencyNode entity);
    }

    public static final EmergencyNodeModelMapper INSTANCE = Mappers.getMapper(EmergencyNodeModelMapper.class);
}
