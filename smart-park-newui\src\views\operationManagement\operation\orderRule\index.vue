<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="queryFormRef" :model="queryForm">
        <el-form-item prop="ruleName">
          <el-input v-model="queryForm.ruleName" placeholder="规则名称"/>
        </el-form-item>
        <el-form-item prop="equipmentTypeId">
          <el-tree-select
              v-model="queryForm.equipmentTypeId"
              :data="state.equipmentTypeTree"
              :render-after-expand="false"
              check-strictly
              node-key="id"
              :props="{label: 'name'}"
              placeholder="设备类型"
              clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onQuery">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #operate>
      <el-button type="primary" icon="Plus" @click="onAdd">新建规则</el-button>
    </template>

    <template #table>
      <el-table :data="state.tableData" :show-overflow-tooltip="true" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="item of state.tableHeader"
                         :key="item.value" :label="item.label"
                         :prop="item.prop" :width="item.width"
                         :formatter="item.formatter"/>
        <el-table-column label="操作" align="center" width="160">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="onEdit(scope.row)">编辑</el-button>
            <el-button link icon="Delete" type="danger" :disabled="scope.row.subType === 'COMMON'" @click="onDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pageParam.pageNum"
          :page-size="state.pageParam.pageSize"
          :total="state.pageParam.total"
          @size-change="sizeChange"
          @current-change="currentChange"
      />

      <modal-page ref="modalPageRef" :equipmentTypeTree="state.equipmentTypeTree" @onClose="onQuery" />
    </template>
  </page-common>
</template>

<script setup>
import {ElMessage, ElMessageBox, ElTag} from "element-plus";
import {deleteOrderRules, listOrderRules} from "@/api/operationManagement/workOrderRule.js";
import ModalPage from "./component/modalPage.vue";
import { equipTypTreeAPI } from '@/api/operationManagement/equipType.js';
import { calcPageNo } from '@/utils/util.js';


let modalPageRef = ref()

const queryForm = ref({
  ruleName: ''
});
const queryFormRef = ref()

const state = reactive({
  equipmentTypeTree: [],
  tableData: [],
  tableHeader: [
    {
      label: '规则名称',
      prop: 'ruleName',
    },
    {
      label: '设备类型',
      prop: 'equipmentTypeName',
    },
    {
      label: '优先级',
      prop: 'priority',
      formatter: (row, column, cellValue) => {
        let type = cellValue === 1 ? 'primary' : cellValue === 2 ? 'warning' :  'danger'
        return h(ElTag, { type
            }, {
              default: () => {
                if (cellValue === 1) {
                  return '普通'
                } else if (cellValue === 2) {
                  return '紧急'
                } else if (cellValue === 3) {
                  return '特急'
                }
              }
            }
        )
      }
    },
    // {
    //   label: '预计维修时长（小时）',
    //   prop: 'predictRepairDuration',
    //   formatter: (row, column, cellValue) => {
    //     if (cellValue) return cellValue / 60;
    //   },
    // },
    {
      label: '处理时限',
      prop: 'handleLimitDuration',
      formatter: (row, column, cellValue) => {
        if (cellValue) return cellValue / 60 + ' 小时';
      }
    },
    {
      label: '派单方式',
      prop: 'dispatchType',
      formatter: (row, column, cellValue) => {
        if (cellValue === 0) {
          return '自动派单'
        } else if (cellValue === 1) {
          return '手动派单'
        }
      }
    },
    {
      label: '工作组',
      prop: 'workGroupNames',
    }
  ],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  }
});

const onQuery = () => {
  let param = queryForm.value
  param.pageNum = state.pageParam.pageNum
  param.pageSize = state.pageParam.pageSize
  listOrderRules(param).then(res => {
    if (res.success) {
      state.pageParam.total = res.data.totalCount
      state.tableData = res.data.dataList
    }
  })
}

const onReset = () => {
  queryFormRef.value.resetFields()
  queryFormRef.value.clearValidate()
  onQuery()
}

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  onQuery()
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  onQuery()
};


// 新增
const onAdd = () => {
  modalPageRef.value.open('新建规则');
  modalPageRef.value.form.id = undefined;
  modalPageRef.value.form.equipmentTypeId = undefined;
};

// 编辑
const onEdit = (row) => {
  row.equipmentTypeId = row.equipmentTypeId &&  row.equipmentTypeId.toString()
  modalPageRef.value.open('编辑规则');
  nextTick(() => {
    Object.assign(modalPageRef.value.form, { ...row });
    modalPageRef.value.form.workGroupIds = row.workGroupIds ? row.workGroupIds.split(',') : [];
    // 分钟转换成小时
    if (row.predictRepairDuration) modalPageRef.value.form.predictRepairDuration = row.predictRepairDuration / 60;
    if (row.handleLimitDuration) modalPageRef.value.form.handleLimitDuration = row.handleLimitDuration / 60;
  });
};

// 删除
const onDelete = (row) => {
  ElMessageBox.confirm('是否删除当前规则?',
      '提醒',
      {
        type: 'warning',
      }
  ).then(() => {
    state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize);

    const params = {
      id: row.id,
    };
    deleteOrderRules(params).then((res) => {
      if (res.success) {
        ElMessage.success('删除成功');
        onQuery();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
}

const getEquipmentTypeTree = async () => {
  const res = await equipTypTreeAPI();
  state.equipmentTypeTree = res.data;
};

onMounted(() => {
  getEquipmentTypeTree();
  onQuery();
});
</script>
