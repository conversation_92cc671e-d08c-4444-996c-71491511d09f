<style lang="less" scoped>
/deep/ .el-card__body {
  .flag {
    position: relative;
    flex: 1;
    width: 100%;
    // left: 50%;
    // top: 50%;
    // transform: translate(-50%, -50%);
    // width: 8.42rem;
    height: 100%;
    .top {
      position: absolute;
      left: 50%;
      top: 0;
      transform: translate(-50%, 0);
      width: 51.42rem;
      height: 29.65rem;
    }
    .bottom {
      position: absolute;
      left: 50%;
      top: 100%;
      transform: translate(-50%, -100%);
      width: 8.42rem;
      height: 100%;
    }
  }
}
</style>

<template>
  <el-card style="height: 100%;">
    <div class="flag">
      <img
        :class="isTop ? 'top' : 'bottom'"
        src="@/assets/img/flag/bottom.png"
      />
    </div>
  </el-card>
</template>

<script>
export default {
  data() {
    return {
      isTop: true,
      // topUrl: new URL("@/assets/img/flag/top.png"),
      // bottomUrl: new URL("@/assets/img/flag/bottom.png"),
    };
  },
  methods: {
    async refresh() {
      let { data: res } = await this.axios.post("/flag/system/status");
      if (res.resultObject.search("FAIL") != -1)
        return this.message.error("刷新失败");
      this.isTop = res.resultObject.search("TOP") != -1;
    },
  },
};
</script>
