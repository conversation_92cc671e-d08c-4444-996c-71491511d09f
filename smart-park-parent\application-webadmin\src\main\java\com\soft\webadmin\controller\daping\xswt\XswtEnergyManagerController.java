package com.soft.webadmin.controller.daping.xswt;


import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.service.daping.xswt.XswtEnergyManagerService;
import com.soft.webadmin.vo.daping.xswt.energyManager.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Api(tags = "大屏：智慧能耗")
@RestController
@RequestMapping("/daping/energyManager")
public class XswtEnergyManagerController {

    @Resource
    private XswtEnergyManagerService xswtEnergyManagerService;

    @ApiOperation("用电详情")
    @GetMapping("/usage-details")
    public ResponseResult<XswtEnergyUsageDetailsVO> usageDetails() {
        XswtEnergyUsageDetailsVO xswtEnergyUsageDetailsVO = xswtEnergyManagerService.usageDetails();
        return ResponseResult.success(xswtEnergyUsageDetailsVO);
    }


    @ApiOperation("能耗概况")
    @GetMapping("/energy-overview")
    public ResponseResult<XswtEnergyOverviewVO> energyOverview() {
        XswtEnergyOverviewVO xswtEnergyOverviewVO = xswtEnergyManagerService.energyOverview();
        return ResponseResult.success(xswtEnergyOverviewVO);
    }


    @ApiOperation("区域能耗情况")
    @GetMapping("/area-usage-info")
    public ResponseResult<XswtEnergyAreaUsageInfoVO> areaUsageInfo() {
        XswtEnergyAreaUsageInfoVO xswtEnergyAreaUsageInfoVO = xswtEnergyManagerService.areaUsageInfo();
        return ResponseResult.success(xswtEnergyAreaUsageInfoVO);
    }

    @ApiOperation("区域能耗排行")
    @GetMapping("/area-usage-rank")
    public ResponseResult<List<XswtEnergyAreaUsageRankVO>> areaUsageRank(String dateType) {
        List<XswtEnergyAreaUsageRankVO> xswtEnergyAreaUsageRankVOS = xswtEnergyManagerService.areaUsageRank(dateType);
        return ResponseResult.success(xswtEnergyAreaUsageRankVOS);
    }


    @ApiOperation("能耗趋势")
    @GetMapping("/usage-trend")
    public ResponseResult<XswtEnergyUsageTrendVO> usageTrend(String dateType) {
        XswtEnergyUsageTrendVO xswtEnergyUsageTrendVO = xswtEnergyManagerService.usageTrend(dateType);
        return ResponseResult.success(xswtEnergyUsageTrendVO);
    }


    @ApiOperation("设备能耗排行")
    @GetMapping("/equipment-usage-rank")
    public ResponseResult<List<XswtEnergyEquipmentUsageRankVO>> equipmentUsageRank(String dateType) {
        List<XswtEnergyEquipmentUsageRankVO> xswtEnergyEquipmentUsageRankVOS = xswtEnergyManagerService.equipmentUsageRank(dateType);
        return ResponseResult.success(xswtEnergyEquipmentUsageRankVOS);
    }


    @ApiOperation("区域能耗费用")
    @GetMapping("/area-usage-cost")
    public ResponseResult<XswtEnergyAreaUsageCostVO> areaUsageCost(String dateType) {
        XswtEnergyAreaUsageCostVO xswtEnergyAreaUsageCostVO = xswtEnergyManagerService.areaUsageCost(dateType);
        return ResponseResult.success(xswtEnergyAreaUsageCostVO);
    }


    @ApiOperation("节能完成目标情况")
    @GetMapping("/saving-energy-complete-info")
    public ResponseResult<XswtEnergySavingEnergyCompleteInfoVO> savingEnergyCompleteInfo(String dateType) {
        XswtEnergySavingEnergyCompleteInfoVO xswtEnergySavingEnergyCompleteInfoVO = xswtEnergyManagerService.savingEnergyCompleteInfo(dateType);
        return ResponseResult.success(xswtEnergySavingEnergyCompleteInfoVO);
    }

}
