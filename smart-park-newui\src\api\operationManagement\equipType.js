import { request } from '@/utils/request.js';

// 分页查询
export const equipTypePageAPI = (query) => {
    return request('get', '/om/equipment/type/list', query, 'F');
};

// 树形查询
export const equipTypTreeAPI = (query) => {
    return request('get', '/om/equipment/type/tree', query, 'F');
};

// 保存
export const equipTypSaveAPI = (data) => {
    return request('post', '/om/equipment/type/saveOrUpdate', data);
};

// 删除
export const  equipTypDeleteAPI = (query) => {
    return request('post', '/om/equipment/type/delete', query, 'F');
}
