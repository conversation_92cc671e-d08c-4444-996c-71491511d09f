<template>
    <page-common v-model="state.tableHeight">
        <template #query>
            <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
                <el-form-item prop="queryWord">
                    <el-input placeholder="访客姓名/车牌号" v-model="formInline.queryWord" />
                </el-form-item>
                <el-form-item prop="visitStatus">
                    <el-select v-model="formInline.visitStatus" clearable placeholder="到访状态">
                        <el-option v-for="(val, key) in state.options" :value="key"
                            :label="val" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-date-picker v-model="state.date" type="daterange" value-format="YYYY-MM-DD"
                        start-placeholder="预计到访时间" end-placeholder="预计到访时间" :clearable="false" />
                </el-form-item>
                <el-form-item>
                    <el-date-picker v-model="state.date2" type="daterange" value-format="YYYY-MM-DD"
                        start-placeholder="实际到访时间" end-placeholder="实际到访时间" :clearable="false" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
                    <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
            </el-form>
        </template>

        <template #table>
            <el-table ref="table" show-overflow-tooltip :data="state.tableData" :height="state.tableHeight">
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                    :label="item.label" :formatter="item.formatter" :width="item.width" />
            </el-table>

            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize"
                :total="state.pagetion.total" @size-change="sizeChange" @current-change="currentChange" />
        </template>
    </page-common>
</template>

<script setup>
import { visitorRecord } from '@/api/convenientPassage/check.js'

import dayjs from 'dayjs'

const route = useRoute()
const table = ref()

const formInlineRef = ref();
const formInline = reactive({});

const state = reactive({
    date: [],
    date2: [],
    tableHeight: 100,
    tableData: [],
    options:{
        1:'未到访',
        2:'已到访'
    },
    colors:{
        1:'#409EFF',
        2:'#67C23A'
    },
    tableHeader: [{
        prop: 'visitorName',
        label: '访客姓名'
    },
    {
        prop: 'visitorIdCard',
        label: '身份证号'
    },
    {
        prop: 'visitorPhone',
        label: '手机号'
    }, {
        prop: 'licenceNumber',
        label: '车牌号'
    }, {
        prop: 'planVisitTime',
        label: '预计到访时间',
        formatter: (row, column, cellValue) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm') : ''
        }
    }, {
        prop: 'realVisitTime',
        label: '实际到访时间',
        formatter: (row, column, cellValue) => {
            return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm') : ''
        }
    }, {
        prop: 'visitReason',
        label: '到访事由'
    },
    {
        prop: 'visitStatus',
        label: '到访状态',
        formatter: (row, column, cellValue) => {
            let color = state.colors[cellValue];
            return h('div', [h('span', {
                class: 'status-circle',
                style: 'background-color: ' + color
            }), state.options[cellValue]]);
        }
    }],
    pagetion: {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
})

onMounted(() => {
    getList();
})

// 获取房间列表
const getList = () => {
    const query = {
        ...route.query,
        ...formInline,
        ...state.pagetion,
        planVisitDateStart: state.date[0],
        planVisitDateEnd: state.date[1],
        realVisitDateStart: state.date2[0],
        realVisitDateEnd: state.date2[1],
    }

    visitorRecord(query).then(res => {
        state.tableData = res.data.dataList
        state.pagetion.total = res.data.totalCount;
    })
}

const onSubmit = () => {
    state.pagetion = {
        pageNum: 1,
        pageSize: 10,
        total: 0
    };
    getList();
}

const onReset = () => {
    formInlineRef.value.resetFields();
    state.date = [];
    state.date2 = []
    onSubmit();
};

// 分页
const currentChange = (pageNum) => {
    state.pagetion.pageNum = pageNum;
    getList();
};

const sizeChange = (pageSize) => {
    state.pagetion.pageSize = pageSize;
    getList();
};

defineExpose({
    getList
})
</script>

<style lang="less" scoped>
:deep .status-circle {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}
</style>
