import { request } from '@/utils/request.js';


// 分页查询
export const listEquipmentGroupControlAPI = (data) => {
    return request('get', '/equipment/group-control/list', data, 'F');
};

// 新增或修改
export const saveOrUpdateEquipmentGroupControlAPI = (data) => {
    return request('post', '/equipment/group-control/saveOrUpdate', data);
};


// 新增或修改
export const deleteEquipmentGroupControlAPI = (data) => {
    return request('get', '/equipment/group-control/delete', data, 'F');
};

// 组控
export const controlEquipmentGroupControlAPI = (data) => {
    return request('post', '/equipment/group-control/control', data);
};
