<template>
  <div class="page">
    <div class="tree">
      <el-date-picker v-model="state.date" type="daterange" range-separator="-" start-placeholder="开始时间"
        end-placeholder="结束时间" style="width: 100%;" format="MM-DD" value-format="YYYY-MM-DD" @change="changeDate" />
      <el-tree ref="treeRef" class="filter-tree" :data="state.treeData" node-key="key" highlight-current accordion
        style="margin-top: 10px;" @node-click="handleNodeClick" />
    </div>
    <div class="record">
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">巡检记录</div>
      </div>
      <div class="conent">
        <div class="basic">
          <div class="top">
            <span class="label">{{ state.info.planName }}</span>
            <div class="time">
              <span>开始时间： {{ state.info.startTime }}</span>
              &nbsp;&nbsp;
              <span>完成时间： {{ state.info.finishTime }}</span>
            </div>
          </div>
          <div class="bottom">
            <div>
              <div class="total">
                <span>巡检设备数量</span>
                <br>
                <span class="num">{{ state.total }}</span>
              </div>
              <div class="error">
                <span>设备异常数量</span>
                <br>
                <span class="num">{{ state.error }}</span>
              </div>
            </div>
            <el-progress :percentage="state.percentage" :stroke-width="20" :show-text="false" />
            <div class="percentage">
              {{ state.current }} / {{ state.total }}
            </div>
          </div>
        </div>
        <el-table :data="state.list" ref="tableRef">
          <el-table-column type="index" width="50" />
          <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
            :align="item.align" :formatter="item.formatter" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'

import { checkRecordAPI, pointSmartAPI } from '@/api/operationManagement/checkRecord.js'

let time //计时器
const treeRef = ref()
const tableRef = ref()

const state = reactive({
  planType: '',
  date: [dayjs().subtract(70, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  treeData: [],
  info: {},
  list: [],
  total: 0,
  error: 0,
  current: 0,
  percentage: 0,
  statusObj: {
    1: {
      text: '正常',
      color: '#3f9eff'
    },
    2: {
      text: '告警',
      color: '#F70A36'
    },
    3: {
      text: '离线',
      color: '#70788D'
    },
    5: {
      text: '报废',
      color: '#70788D'
    }
  },
  tableHeader: [
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'equipmentCode',
      label: '设备编号'
    },
    {
      prop: 'equipmentSpace',
      label: '安装位置'
    },
    {
      prop: 'reportTime',
      label: '上报时间'
    },
    {
      prop: 'state',
      label: '设备状态',
      formatter: (row, column, cellValue) => {
        return h('span', { style: { color: state.statusObj[cellValue].color } }, state.statusObj[cellValue].text)
      }
    }
  ]
})

onBeforeUnmount(() => {
  clearInterval(time)
})

// 获取数据
const getTree = (planType) => {

  state.planType = planType
  checkRecordAPI({
    beginDate: state.date[0],
    endDate: state.date[1],
    planMode: 2,
    planType
  }).then(res => {
    let defaultId
    state.treeData = Object.keys(res.data).map((item, index) => {
      return {
        label: item,
        key: index,
        children: (res.data[item] || []).map(i => {
          if (!Object.keys(state.info).length) { // 初始化数据
            defaultId = i.id
            state.info = i
            pointSmart(i.id)
          }
          return {
            label: i.createDate,
            value: i.id,
            key: i.id,
            info: i
          }
        })
      }
    })
    nextTick(() => {  // 默认节点
      treeRef.value.setCurrentKey(defaultId)
    })
  })
}

// 选择节点
const handleNodeClick = (node) => {
  if (node.value) {
    clearInterval(time)
    state.info = node.info
    pointSmart(node.value)
  }
}

// 智能巡检
const pointSmart = (recordId) => {
  pointSmartAPI({ recordId }).then(res => {
    state.current = 0  //上次值清空
    state.percentage = 0
    state.list = []

    state.total = res.data.length
    state.error = res.data.reduce((pre, item) => {
      return item.state != 1 ? pre + 1 : pre
    }, 0)

    time = setInterval(() => {
      if (state.current >= state.total) {
        return clearInterval(time)
      }
      state.list.push(res.data[state.current])
      state.current += 1
      state.percentage = parseInt((state.current / state.total) * 100)

      nextTick(() => {  // 滚动
        let table = tableRef.value.layout.table.refs;
        let tableScrollEle = table.bodyWrapper.firstElementChild.firstElementChild;
        if (tableScrollEle.scrollHeight > tableScrollEle.offsetHeight) {
          let timer = setInterval(() => {
            tableScrollEle.scrollTop += 1;
            if (tableScrollEle.scrollTop >= tableScrollEle.scrollHeight - tableScrollEle.offsetHeight) {
              console.log(tableScrollEle.scrollTop);
              clearInterval(timer)
            }
          }, 10)
        }
      })
    }, 800)
  })
}

// 时间变化
const changeDate = () => {
  state.date = state.date || []
  getTree(state.planType)
}

defineExpose({
  getTree
})
</script>

<style lang='less' scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;

  .tree {
    width: 300px;
    height: 100%;
    margin-right: 20px;
    flex-shrink: 0;
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
    overflow: auto;
  }

  .record {
    flex: 1;
    background: #FFFFFF;
    border-radius: 10px;
    overflow: hidden;
    padding: 18px;
  }
}

.conent {
  padding: 10px;
  padding-bottom: 0;
  height: calc(100% - 22px);
  display: flex;
  flex-direction: column;

  .basic {
    padding: 10px;
    background-color: rgba(230, 235, 241, 1);
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.0980392156862745);
    border-radius: 10px;
    margin-bottom: 10px;
    flex-shrink: 0;

    .top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      .label {
        font-size: 18px;

      }

      .time {
        color: #999999;
        font-size: 14px;
      }
    }

    .bottom {
      display: flex;
      align-items: center;
      color: #999999;

      .total {
        font-size: 14px;
        text-align: center;

        .num {
          color: #f59a23;
          font-size: 26px;
          font-weight: bold;
        }
      }

      .error {
        font-size: 14px;
        margin: 0 20px;
        text-align: center;

        .num {
          color: #D9001B;
          font-size: 26px;
          font-weight: bold;
        }
      }

      .el-progress {
        flex: 1;
        margin: 0 20px;
        :deep(.el-progress-bar__outer) {
          background-color: rgba(64, 158, 255, 0.247058823529412);
        }
      }

      .percentage {
        font-size: 26px;
        color: #0079fe;
        font-weight: bold;
      }
    }
  }

  .el-table {
    flex: 1;
  }
}
</style>
