package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * CheckRecordPointDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckRecordPointDTO对象")
@Data
public class CheckRecordPointHandleDTO {

    @ApiModelProperty(value = "工单id")
    @NotNull(message = "工单id不能为空！")
    private Long orderId;

    @ApiModelProperty(value = "检查记录id")
    @NotNull(message = "检查记录id不能为空！")
    private Long recordId;

    @ApiModelProperty(value = "检查点id")
    @NotNull(message = "检查点id不能为空！")
    private Long pointId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "附件图片")
    private String img;

    @ApiModelProperty(value = "检查项")
    @NotEmpty(message = "检查项不能为空！")
    @Valid
    private List<CheckRecordPointItemDTO> itemDTOList;

}
