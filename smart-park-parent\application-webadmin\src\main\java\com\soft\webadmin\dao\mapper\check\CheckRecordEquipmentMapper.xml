<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.CheckRecordEquipmentMapper">
    <resultMap type="com.soft.webadmin.model.check.CheckRecordEquipment" id="CheckRecordEquipmentResult">
        <result property="id" column="id" />
        <result property="recordId" column="record_id" />
        <result property="equipmentId" column="equipment_id" />
        <result property="equipmentNo" column="equipment_no" />
        <result property="equipmentSpace" column="equipment_space" />
        <result property="state" column="state" />
        <result property="reportTime" column="report_time" />
    </resultMap>

    <sql id="selectCheckRecordEquipmentVo">
        select id, record_id, equipment_id, equipment_no, equipment_space, state, fault_time from sp_check_record_equipment
    </sql>
    
</mapper>