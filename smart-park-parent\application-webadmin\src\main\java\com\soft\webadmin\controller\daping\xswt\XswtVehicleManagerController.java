package com.soft.webadmin.controller.daping.xswt;


import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.service.daping.xswt.XswtVehicleManagerService;
import com.soft.webadmin.vo.daping.xswt.vehicleManager.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "大屏：车辆管理")
@RestController
@RequestMapping("/daping/xswt/vehicleManager")
public class XswtVehicleManagerController {

    @Resource
    private XswtVehicleManagerService xswtVehicleManagerService;

    @ApiOperation("出入园信息")
    @GetMapping("/inOut-info")
    public ResponseResult<XswtInOutInfoVO> inOutInfo() {
        XswtInOutInfoVO xswtInOutInfoVO = xswtVehicleManagerService.inOutInfo();
        return ResponseResult.success(xswtInOutInfoVO);
    }


    @ApiOperation("停车位概览")
    @GetMapping("/parking-space-overview")
    public ResponseResult<XswtParkingSpaceOverviewVO> parkingSpaceOverview() {
        XswtParkingSpaceOverviewVO xswtParkingSpaceOverviewVO = xswtVehicleManagerService.parkingSpaceOverview();
        return ResponseResult.success(xswtParkingSpaceOverviewVO);
    }


    @ApiOperation("车位24小时使用饱和度")
    @GetMapping("/parking-space-used-24hour")
    public ResponseResult<XswtParkingSpaceUsed24HourVO> parkingSpaceUsed24Hour() {
        XswtParkingSpaceUsed24HourVO xswtParkingSpaceUsed24HourVO = xswtVehicleManagerService.parkingSpaceUsed24Hour();
        return ResponseResult.success(xswtParkingSpaceUsed24HourVO);
    }


    @ApiOperation("车辆停留时长")
    @GetMapping("/stay-length")
    public ResponseResult<XswtStayLengthVO> stayLength() {
        XswtStayLengthVO xswtStayLengthVO = xswtVehicleManagerService.stayLength();
        return ResponseResult.success(xswtStayLengthVO);
    }


    @ApiOperation("24小时车流量统计")
    @GetMapping("/traffic-flow-24hour")
    public ResponseResult<XswtTrafficFlow24HourVO> trafficFlow24Hour() {
        XswtTrafficFlow24HourVO xswtTrafficFlow24HourVO = xswtVehicleManagerService.trafficFlow24Hour();
        return ResponseResult.success(xswtTrafficFlow24HourVO);
    }


    @ApiOperation("车辆类型统计")
    @GetMapping("/vehicle-type")
    public ResponseResult<XswtVehicleTypeVO> vehicleType(String dateType) {
        XswtVehicleTypeVO xswtVehicleTypeVO = xswtVehicleManagerService.vehicleType(dateType);
        return ResponseResult.success(xswtVehicleTypeVO);
    }


    @ApiOperation("车辆通行记录")
    @GetMapping("/pass-record")
    public ResponseResult<List<XswtPassRecordVO>> passRecord() {
        List<XswtPassRecordVO> xswtPassRecordVOS = xswtVehicleManagerService.passRecord();
        return ResponseResult.success(xswtPassRecordVOS);
    }

    @ApiOperation("黑白名单")
    @GetMapping("/black-white-list")
    public ResponseResult<List<XswtBlackWhiteListVO>> blackWhiteList(Integer panel) {
        List<XswtBlackWhiteListVO> xswtBlackWhiteListVOS = xswtVehicleManagerService.blackWhiteList(panel);
        return ResponseResult.success(xswtBlackWhiteListVOS);
    }

}
