<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="queryFormRef" :model="state.queryForm">
        <el-form-item prop="inspectionPlanName">
          <el-input v-model="state.queryForm.inspectionPlanName" placeholder="巡更计划名称"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="queryList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #operate>
      <el-button type="primary" :icon="Plus" @click="onAdd">新建巡更计划</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column v-for="item of state.tableHeader" :label="item.label" :prop="item.prop" :width="item.width">
          <template #default="scope">
            <div v-if="item.prop === 'name'">
              <el-button link type="primary" @click="onView(scope.row)">
                {{ scope.row['name'] }}
              </el-button>
            </div>
            <div v-if="item.prop === 'status'">
              <span style="color: springgreen" v-if="scope.row['status'] === 1">
                生效
              </span>
              <span style="color: red" v-else>
                失效
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click.prevent="onEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.prevent="onDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />

      <plan-dialog ref="planDialogRef" @onClose="queryList"/>
    </template>
  </page-common>
</template>

<script setup>
import {Plus, Refresh, Search} from "@element-plus/icons-vue";
import {deleteInspectionPlanAPI, listInspectionPlanAPI} from "@/api/comprehensiveSecurity/inspectionPlan.js";
import PlanDialog from "@/views/comprehensiveSecurity/inspection/inspectionPlan/component/planDialog.vue";
import {ElMessage, ElMessageBox} from "element-plus";

// 新建、修改组件引用
let planDialogRef = ref()

// 查询表单引用
let queryFormRef = ref()

const state = reactive({
  tableHeight: 100,
  queryForm: {
    inspectionPlanName: null
  },
  tableData: [],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
  tableHeader: [
    { label: '计划名称', prop: 'name', width: '' },
    { label: '巡更路线', prop: 'lineName', width: '' },
    { label: '巡更人员', prop: 'inspectionUsernames', width: '' },
    { label: '计划日期', prop: 'planDate', width: '220' },
    { label: '巡更时间', prop: 'timeRange', width: '' },
    { label: '巡更工作日', prop: 'workday', width: '310' },
    { label: '状态', prop: 'status', width: '' }
  ]
})


// 查询巡更计划列表
const queryList = () => {
  let query = {
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  if (state.queryForm.inspectionPlanName) {
    query.inspectionPlanName = state.queryForm.inspectionPlanName
  }
  listInspectionPlanAPI(query).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList
      // 处理工作日
      for (let plan of state.tableData) {
        let workdayNumStr = plan.planCron
        plan.workday = workdayNumStr.replace('1', '星期一')
          .replace('2', '星期二')
          .replace('3', '星期三')
          .replace('4', '星期四')
          .replace('5', '星期五')
          .replace('6', '星期六')
          .replace('7', '星期天')
          .replaceAll(',', '、')
        plan.planDate = plan.planBeginDate + ' - ' + plan.planEndDate
      }
      state.pageParam.total = res.data.totalCount
    }
  })
}

const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  queryList()
}


const sizeChange = (val) => {
  state.pageParam.pageSize = val
  queryList()
}

const currentChange = (val) => {
  state.pageParam.pageNum = val
  queryList()
}

const onAdd = () => {
  planDialogRef.value.open('新建巡更计划')
}

const onEdit = (val) => {
  planDialogRef.value.open('编辑巡更计划', val)
}

const onView = (val) => {
  planDialogRef.value.open('查看巡更计划', val)
}

const onDelete = (val) => {
  ElMessageBox.confirm(
    '是否删除当前巡更计划?',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    let params = {
      id: val.id
    }
    deleteInspectionPlanAPI(params).then(res => {
      if (res.success) {
        ElMessage.success('删除成功！')
      } else {
        ElMessage.error('删除失败，' + res.errorMessage)
      }
      queryList()
    });
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除！',
    })
  })
}

onMounted(() => {
  queryList()
})
</script>


<style scoped lang="less">
</style>
