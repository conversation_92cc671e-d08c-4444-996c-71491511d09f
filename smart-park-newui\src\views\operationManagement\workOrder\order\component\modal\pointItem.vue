<template>
  <dialog-common ref="dialog" title="检查内容" :formRef="ruleFormRef" :width="800" :showButton="false" class="dialogTextarea">
    <el-form ref="ruleFormRef" :model="form" label-width="70px" label-suffix=":">
      <el-form-item label="检查项">
        <el-table :data="form.itemList" style="margin-top: -4px;">
          <el-table-column label="序号" type="index" width="60"/>
          <el-table-column label="检查项" prop="itemName" show-overflow-tooltip />
          <el-table-column label="检查内容" prop="itemContent" show-overflow-tooltip/>
          <el-table-column label="数值" prop="itemResultVal" width="100">
            <template #default="scope">
              {{ scope.row.itemResultVal ? scope.row.itemResultVal : '' }}
            </template>
          </el-table-column>
          <el-table-column label="检查结果" align="center" width="150">
            <template #default="scope">
              <el-tag
                  v-if="scope.row.itemResult"
                  :type="scope.row.itemResult === 1 ? 'success' : 'danger'"
                  size="small">
                {{ scope.row.itemResult === 1 ? '正常' : '异常' }}
              </el-tag>
              <span v-else>/</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="备注">
        {{ form.remark }}
      </el-form-item>
      <el-form-item label="附件">
        <img-video :list="form.fileList"></img-video>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});
const pointId = ref();
const state = reactive({
  itemList: [],
  fileList: [],
});

const open = () => {
  dialog.value.open();
};

defineExpose({
  form,
  pointId,
  open,
});
</script>

<style lang="less" scoped>
:deep(.el-table .el-table__header th.el-table__cell) {
  padding: 8px 0;
  background-color: #FFFFFF !important;
}
</style>
