import { request } from "@/utils/request.js";

export const maintenanceListAPI = (data) => {
  return request('get', '/cablingSystem/equipment/list', data, 'F');
}

export const saveOrUpdateAPI = (data) => {
  return request('post', '/cablingSystem/equipment/saveOrUpdate', data);
}

export const listAPI = (data) => {
  return request('get', '/cablingSystem/equipment/list', data, 'F');
}

export const deleteAPI = (data) => {
  return request('post', '/cablingSystem/equipment/delete', data, 'F');
}

export const listByEquipmentIdAPI = (data) => {
  return request('get', '/cablingSystem/equipment-port/listByEquipmentId', data, 'F');
}
