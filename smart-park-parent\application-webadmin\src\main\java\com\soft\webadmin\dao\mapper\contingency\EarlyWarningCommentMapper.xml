<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.contingency.EarlyWarningCommentMapper">
    <resultMap type="com.soft.webadmin.model.contingency.EarlyWarningComment" id="EarlyWarningCommentResult">
        <result property="id" column="id" />
        <result property="warningId" column="warning_id" />
        <result property="parentId" column="parent_id" />
        <result property="commentUserId" column="comment_user_id" />
        <result property="commentUserName" column="comment_user_name" />
        <result property="commentTime" column="comment_time" />
        <result property="content" column="content" />
        <result property="imgs" column="imgs" />
    </resultMap>

    <sql id="selectEarlyWarningCommentVo">
        select t.id, t.warning_id, t.parent_id, t.comment_user_id, t.comment_user_name, t.comment_time, t.content, t.imgs from cm_early_warning_comment t
    </sql>
    
</mapper>