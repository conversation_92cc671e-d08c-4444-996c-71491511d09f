import { request } from '@/utils/request';

// 分页查询
export const getStorePageAPI = (query) => {
    return request('get', '/sparePart/storehouse/getPage', query, 'F');
};

// 保存
export const saveStoreAPI = (data) => {
    return request('post', '/sparePart/storehouse/saveOrUpdate', data);
}

//更新仓库状态
export const updateStoreStateAPI = (query) => {
    return request('post', '/sparePart/storehouse/updateState', query, 'F');
}

// 删除
export const deteleStoreAPI = (query) => {
    return request('post', '/sparePart/storehouse/delete', query, 'F');
}
