import {request} from "@/utils/request";


export const pageListAPI = (data) => {
    return request('get', '/admin/upms/tag/pageList', data, 'F');
}

export const deleteAPI = (data) => {
    return request('post', '/admin/upms/tag/delete', data, 'F')
}

export const addAPI = (data) => {
    return request('post', '/admin/upms/tag/add', data)
}

export const updateAPI = (data) => {
    return request('post', '/admin/upms/tag/update', data)
}
