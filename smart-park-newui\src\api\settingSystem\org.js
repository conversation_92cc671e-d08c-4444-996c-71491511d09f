import { request } from "@/utils/request.js";




export const listOrgAPI = (data) => {
  return request('get', '/passage/org/list', data, 'F')
}

export const saveOrUpdateOrgAPI = (data) => {
  return request('post', '/passage/org/saveOrUpdate', data)
}

export const updateOrgStatusAPI = (data) => {
  return request('post', '/passage/org/updateStatus', data)
}


export const deleteOrgAPI = (data) => {
  return request('post', '/passage/org/delete', data, 'F')
}


export const getOrgDetail = (data) => {
  return request('get', '/passage/org/detail', data, 'F')
}
