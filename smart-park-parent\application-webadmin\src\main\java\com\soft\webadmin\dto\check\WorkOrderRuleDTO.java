package com.soft.webadmin.dto.check;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * SpWorkOrderRuleDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@ApiModel("WorkOrderRuleDTO对象")
@Data
public class WorkOrderRuleDTO {

    @ApiModelProperty(value = "${column.columnComment}")
    @NotNull(message = "数据验证失败，${column.columnComment}不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "规则名称")
    @NotBlank(message = "规则名称不能为空！")
    private String ruleName;

    @ApiModelProperty(value = "设备类型")
    @NotNull(message = "设备类型不能为空！")
    private Long equipmentTypeId;

    @ApiModelProperty(value = "优先级，1普通；2紧急；3特急")
    @NotNull(message = "优先级不能为空!")
    private Integer priority;

    // @ApiModelProperty(value = "预计维修时长，单位：分钟")
    // @NotNull(message = "预计维修时间不能为空！")
    // private Long predictRepairDuration;

    @ApiModelProperty(value = "处理时限，单位：分钟")
    @NotNull(message = "处理时限不能为空！")
    private Long handleLimitDuration;

    @ApiModelProperty(value = "派单方式：0自动派单；1手动派单")
    @NotNull(message = "派单方式不能为空！")
    private Integer dispatchType;

    @ApiModelProperty(value = "自动派单工作组 id，逗号分隔")
    private String workGroupIds;

    // @ApiModelProperty(value = "人工费")
    // private BigDecimal laborCost;

    @ApiModelProperty(value = "处理预案")
    private String treatmentPlan;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建者id")
    private Long createUserId;

    @ApiModelProperty(value = "更新者id")
    private Long updateUserId;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标识，1正常；-1已删除")
    private Integer deleteFlag;

}
