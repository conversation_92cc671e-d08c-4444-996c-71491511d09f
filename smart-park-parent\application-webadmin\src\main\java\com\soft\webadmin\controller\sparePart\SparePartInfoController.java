package com.soft.webadmin.controller.sparePart;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.sparePart.SparePartDetailQueryDTO;
import com.soft.webadmin.dto.sparePart.SparePartInfoQueryDTO;
import com.soft.webadmin.dto.sparePart.SparePartInfoSetUpDTO;
import com.soft.webadmin.dto.sparePart.SparePartQuantityChangeQueryDTO;
import com.soft.webadmin.service.sparePart.SparePartDetailService;
import com.soft.webadmin.service.sparePart.SparePartInfoService;
import com.soft.webadmin.vo.sparePart.SparePartFullDetailVO;
import com.soft.webadmin.vo.sparePart.SparePartInfoVO;
import com.soft.webadmin.vo.sparePart.SparePartQuantityChangeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 备品备件信息控制器类
 * 
 * <AUTHOR>
 * @date 2024-03-05
 */
@Api(tags = "物资管理")
@RestController
@RequestMapping("/sparePart/info")
public class SparePartInfoController {
    @Autowired
    private SparePartInfoService sparePartInfoService;

    @Autowired
    private SparePartDetailService sparePartDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<SparePartInfoVO>> getPage(SparePartInfoQueryDTO queryDTO) {
        return ResponseResult.success(sparePartInfoService.getPage(queryDTO));
    }

    @ApiOperation(value = "导出")
    @GetMapping("/export")
    public void export(SparePartInfoQueryDTO queryDTO) {
        sparePartInfoService.export(queryDTO);
    }

    @ApiOperation(value = "备件详情")
    @GetMapping("/detail")
    public ResponseResult<SparePartInfoVO> detail(Long id) {
        return ResponseResult.success(sparePartInfoService.detail(id));
    }

    @ApiOperation(value = "库存变化记录")
    @GetMapping("/getChangePage")
    public ResponseResult<MyPageData<SparePartQuantityChangeVO>> getChangePage(SparePartQuantityChangeQueryDTO queryDTO) {
        return ResponseResult.success(sparePartInfoService.getChangePage(queryDTO));
    }

    @ApiOperation(value = "库存预警设置")
    @PostMapping("/setUpWarning")
    public ResponseResult<Void> setUpWarning(@Validated @RequestBody SparePartInfoSetUpDTO setUpDTO) {
        return sparePartInfoService.setUpWarning(setUpDTO);
    }

    @ApiOperation(value = "备件选择")
    @GetMapping("/getDetailPage")
    public ResponseResult<MyPageData<SparePartFullDetailVO>> getDetailPage(SparePartDetailQueryDTO queryDTO) {
        return ResponseResult.success(sparePartDetailService.getPage(queryDTO));
    }

}
