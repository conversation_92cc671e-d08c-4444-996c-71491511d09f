<template>
  <div style="width: calc(100% - 10px); height:100%; padding: 18px; margin-top: 40px;">
    <head-title title="组织架构图"></head-title>
    <relation-graph ref="relationGraph$" :options="state.options" v-loading="state.g_loading">
      <template #node="{node}">
        <div @click="showUsers(node)" style="text-align: center; height: 100%; width: 100%; display: flex; align-items: center; justify-content: center;">
          <span>{{ node.text }}</span>
        </div>
      </template>
    </relation-graph>
    <el-drawer
      v-model="state.showUsers"
      direction="rtl"
      class="org_drawer"
      size="40%"
      @close="closeDrawer"
    >
      <template #header>
        <span style="font-size: 20px; font-weight: bold;">{{ state.deptName }}</span>
      </template>
      <template #default>
        <el-table :data="state.currentComponentUsers" style="width: 100%" stripe tooltip-effect="light" highlight-current-row :height="780">
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="username" label="名称" width="120"/>
          <el-table-column prop="phone" label="手机号" width="150"/>
          <el-table-column prop="postNames" label="岗位"/>
        </el-table>
      </template>
      <template #footer>
        <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                       :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize"
                       :total="state.pageParam.total"
                       @size-change="sizeChange" @current-change="currentChange"/>
      </template>
    </el-drawer>
  </div>
</template>


<script setup>

// 如果您没有在main.js文件中使用Vue.use(RelationGraph); 就需要使用下面这一行代码来引入relation-graph
// eslint-disable-next-line no-unused-vars
import RelationGraph from 'relation-graph/vue3'
import {getOrganization} from "@/api/settingSystem/depart.js";


const relationGraph$ = ref(RelationGraph);


const state = reactive({
  // 检索条件
  queryForm: {
    dataType: '全部'
  },

  // 组织架构信息
  organizationData: {},
  g_loading: true,
  showUsers: false,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
  currentNode: null,
  // 部门员工
  allComponentUsers: [],
  // 当前页员工
  currentComponentUsers: [],
  options: {
    backgrounImageNoRepeat: true,
    layouts: [
      {
        label: '中心',
        layoutName: 'tree',
        layoutClassName: 'seeks-layout-tree',
        defaultJunctionPoint: 'border',
        centerOffset_x: 0,
        centerOffset_y: -500,
        defaultNodeShape: 0,
        defaultLineShape: 4,
        defaultLineWidth: 2,
        min_per_width: '110',
        min_per_height: '300',
        levelDistance: '300,300,400'
      }
    ],
    defaultExpandHolderPosition: 'bottom',
    defaultLineShape: 4,
    defaultLineWidth: 2,
    defaultJunctionPoint: 'tb',
    defaultNodeShape: 1,
    defaultNodeWidth: '100',
    defaultNodeHeight: '50',
    defaultNodeBorderWidth: 0,
  }
})


// 查询组织架构信息
const queryOrganizational = () => {
  getOrganization().then(res => {
    if (res.success) {
      state.organizationData = res.data
      setGraphData()
    }
  })
}

// 将组织信息添加到组件中
const setGraphData = () => {
  const __graph_json_data = state.organizationData
  let rootId = __graph_json_data.rootId
  setTimeout(() => {
    state.g_loading = false
    relationGraph$.value.setJsonData(__graph_json_data)
    relationGraph$.value.getNodes().forEach(node => {
      if (node.id === rootId) {
        node.width = node.text.length * 15 + 20
      }
    })
  }, 1000)
}

// 修改选项
const onChangeOptions = () => {
  // 当前登录用户
  let loginUserInfo = JSON.parse(localStorage.getItem('loginUserInfo'))
  let logUserId = loginUserInfo.data.userId

  // 当前登录用户的下属
  let childUserIds = []
  const _all_nodes = relationGraph$.value.getNodes();
  const _all_links = relationGraph$.value.getLinks();

  // 过滤掉多余的节点和连线
  _all_nodes.forEach(thisNode => {
    let _isHideThisLine = false;
    if (state.queryForm.dataType === '我的下属') {
      if (thisNode.data.userId === logUserId) {
        childUserIds = thisNode.data.child
      } else if (!childUserIds.includes(thisNode.data.userId)) {
        _isHideThisLine = true;
      }
    }
    // 透明度
    thisNode.opacity = _isHideThisLine ? 0.1 : 1;
    // 隐藏
    // thisNode.isHide = _isHideThisLine;
  });
  relationGraph$.value.getInstance().dataUpdated();
}



// 抽屉关闭
const closeDrawer = () => {
  relationGraph$.value.refresh()

  relationGraph$.value.getNodes().forEach(node => {
    if (node !== state.currentNode) {
      node.opacity = 1
    }
  })

  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  state.pageParam.total = 0
  state.allComponentUsers = []
  state.currentComponentUsers = []
}

const showUsers = (selectNode) => {
  state.currentNode = selectNode
  relationGraph$.value.getNodes().forEach(node => {
    if (selectNode === node) {
      relationGraph$.value.focusNodeById(selectNode.id)
    } else {
      node.opacity = 0.5
    }
  })

  setTimeout(() => {
    state.showUsers = true
    state.deptName = selectNode.text + '成员'
    state.allComponentUsers = selectNode.data
    state.pageParam.total = state.allComponentUsers.length
    pageUsers()
  }, 800)
}

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize
  pageUsers()
}


const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum
  pageUsers()
}

// 分页
const pageUsers = () => {
  let start = (state.pageParam.pageNum - 1) * state.pageParam.pageSize
  let end  = start + state.pageParam.pageSize
  end = end >= state.pageParam.total ? state.pageParam.total : end
  state.currentComponentUsers = state.allComponentUsers.slice(start, end);
}

onMounted(() => {
  queryOrganizational()
})
</script>

<style scoped>
:deep .el-drawer__header {
    margin-bottom: 0;
    padding-bottom: 15px;
    background-color: #F5F7FA;
}
</style>

