package com.soft.webadmin.config.sharding.enums;

import java.util.Objects;

/**
 * @Description 设备类型枚举
 * MN:监控
 * EG:门禁
 * BA:BA
 * EN:能耗
 * LI:照明
 * IM:信息发布
 * @Date 0022, 2023年5月22日 15:01
 * <AUTHOR>
 **/
public enum ShardingTableTypeEnum {

    /**
     * 100007:水表
     */
    EN_WATER(100007L, "水表", "10", "water"),

    /**
     * 100008:电表
     */
    EN_ELECTRICITY(100008L, "电表", "11", "electricity"),

    /**
     * 100009:气表
     */
    EN_GAS(100009L, "气表", "", "gas"),


    ;

    ShardingTableTypeEnum(Long id, String name, String iotCode, String value) {
        this.id = id;
        this.name = name;
        this.iotCode = iotCode;
        this.value = value;
    }


    /**
     * 根据名称获取值
     * @param name
     * @return
     */
    public static String getValueByName(String name) {
        for (ShardingTableTypeEnum type : values()) {
            if(Objects.equals(type.name, name)) {
                return type.value;
            }
        }
        return null;
    }

    private Long id;

    private String name;

    private String value;

    private String iotCode;

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getIotCode() {
        return iotCode;
    }

    public String getValue() {
        return value;
    }

}
