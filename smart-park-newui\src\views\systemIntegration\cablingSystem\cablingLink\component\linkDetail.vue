<template>
  <el-drawer v-model="drawerRef" :title="state.title" size="35%" :before-close="close">
    <template #default>
      <el-form :model="state.tableData" label-position="left" label-suffix=":">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">信息面板</div>
        </div>
        <el-row :gutter="20">
          <el-col :span="11" :offset="2">
            <el-form-item label="位置">
              {{ state.tableData.firstSpaceFullName }}
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="端口类型">
              <el-space>
                <el-tag>信息面板</el-tag>
                <el-tag>{{ state.tableData.portType === 1 ? '语音' : state.tableData.portType === 2
                    ? '数据' : state.tableData.portType === 3 ? '光纤' : state.tableData.portType === 4 ? '设备' : state.tableData.portType }}</el-tag>
              </el-space>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11" :offset="2">
            <el-form-item label="设备">
              {{ state.tableData.firstEquipmentName }}
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="端口">
              {{ state.tableData.firstPortCode }}
            </el-form-item>
          </el-col>
        </el-row>


        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">配线架</div>
        </div>
        <el-row :gutter="20">
          <el-col :span="11" :offset="2">
            <el-form-item label="位置">
              {{ state.tableData.secondSpaceFullName }}
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="设备类型">
              <el-space>
                <el-tag>配线架</el-tag>
                <el-tag>{{ state.tableData.equipmentType }}</el-tag>
              </el-space>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11" :offset="2">
            <el-form-item label="设备">
              {{ state.tableData.secondEquipmentName }}
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="端口">
              {{ state.tableData.secondPortCode }}
            </el-form-item>
          </el-col>
        </el-row>


        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">交换机</div>
        </div>
        <el-row :gutter="20">
          <el-col :span="11" :offset="2">
            <el-form-item label="位置">
              {{ state.tableData.thirdSpaceFullName }}
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="设备类型">
              <el-space>
                <el-tag>交换机</el-tag>
              </el-space>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="11" :offset="2">
            <el-form-item label="设备">
              {{ state.tableData.thirdEquipmentName }}
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="端口">
              {{ state.tableData.thirdPortCode }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
// 实例化 drawer抽屉对象
const drawerRef = ref(false)

const state = reactive({
  title: '',
  // 详情数据对象
  tableData: ''
})

/**
 * 打开抽屉
 * @param title
 * @param val
 */
const open = (title, val) => {
  state.title = title
  drawerRef.value = true
  state.tableData = val
}

/**
 * 关闭抽屉
 */
const close = () => {
  state.title = ''
  state.tableData = ''
  drawerRef.value = false
}

defineExpose({
  open
})
</script>

<style scoped lang="less">

.el-drawer__header {
  background-color: #F5F7FA;
  margin-right: 0;
}

.divFlex {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.divRight {
  margin-left: 8px;
}
.divLeft {
  display: inline-block;

  width: 5px;
  height: 18px;
  background-color: #3f9eff;
}
</style>
