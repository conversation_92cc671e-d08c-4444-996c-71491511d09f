<template>
  <page-common v-model="state.tableHeight" :operate-bool="false">
    <template #query>
      <el-form :model="state.queryForm" ref="queryFormRef" inline>
        <el-form-item prop="approvalStatus">
          <el-select v-model="state.queryForm.approvalStatus" clearable placeholder="审核状态">
            <el-option
              v-for="item in state.approvalStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="roomName">
          <el-input v-model="state.queryForm.roomName" placeholder="会议室名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="queryList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" tooltip-effect="light">
        <el-table-column v-for="tableHeader in state.tableHeader" :label="tableHeader.label" :prop="tableHeader.prop"
                         show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="tableHeader.prop === 'roomName'">
              <el-space>
                <el-image
                  style="width: 2.0rem; height: 2.0rem"
                  :src="imgTransfer(row.roomImg)"
                  :zoom-rate="1.2"
                  :z-index="2"
                  :preview-teleported="true"
                  :preview-src-list="[imgTransfer(row.roomImg)]"
                  :initial-index="0"
                  fit="fill"
                >
                  <template #error>
                    <div class="error-image">
                      <el-icon size="40"><icon-picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <span> {{ row.roomName }} </span>
              </el-space>
            </div>
            <div v-else-if="tableHeader.prop === 'approvalStatus'">
              <el-text v-if="row.approvalStatus === 0" type="warning">待审核</el-text>
              <el-text v-else-if="row.approvalStatus === 1" type="success">通过</el-text>
              <el-text v-else-if="row.approvalStatus === 2" type="danger">未通过</el-text>
            </div>
            <div v-else-if="tableHeader.prop === 'meetingBeginTime' || tableHeader.prop === 'meetingEndTime'">
              <span>{{ row['meetingDate'] + ' ' + row[tableHeader.prop] }}</span>
            </div>
            <div v-else>
              <span>
                {{ row[tableHeader.prop] ? row[tableHeader.prop] : '/' }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              link
              v-if="scope.row.approvalStatus === 0"
              type="primary"
              icon="Edit"
              @click.prevent="onCheck(scope.row, 'check')"
            >
              审核
            </el-button>
            <el-button
              link
              v-else-if="scope.row.approvalStatus !== 0"
              type="primary"
              icon="Tickets"
              @click.prevent="onCheck(scope.row, 'view')"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />

      <meeting-check-dialog ref="meetingCheckDialogRef" @close="queryList" />
    </template>
  </page-common>
</template>

<script setup>

import PageCommon from "@/components/basic/pageCommon.vue";
import {Refresh, Search} from "@element-plus/icons-vue";
import MeetingCheckDialog from "@/views/parkOperation/meeting/check/component/meetingCheckDialog.vue";
import {listMeetingAPI} from "@/api/parkOperation/meeting.js";
import { Picture as IconPicture } from '@element-plus/icons-vue'


const meetingCheckDialogRef = ref()

const queryFormRef = ref()

const state = reactive({
  tableHeight: 100,
  queryForm: {},
  tableData: [],
  tableHeader: [
    { label: '会议室名称', prop: 'roomName' },
    { label: '会议名称', prop: 'meetingName' },
    { label: '申请人', prop: 'createUserName' },
    { label: '开始时间', prop: 'meetingBeginTime' },
    { label: '结束时间', prop: 'meetingEndTime' },
    { label: '会议人数', prop: 'meetingUserCount' },
    { label: '审核状态', prop: 'approvalStatus' }
  ],
  approvalStatusOptions: [
    { label: '待审核', value: '0' },
    { label: '通过', value: '1' },
    { label: '未通过', value: '2' }
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

const queryList = () => {
  let params = {
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
    queryConfine: 3
  }
  if (state.queryForm.approvalStatus) {
    params.approvalStatus = state.queryForm.approvalStatus
  }
  if (state.queryForm.roomName) {
    params.roomName = state.queryForm.roomName
  }

  listMeetingAPI(params).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList
      state.tableData.forEach(meeting => {
        if (meeting.meetingUserIds) {
          let meetingUserIds = meeting.meetingUserIds.split(',');
          meeting.meetingUserCount = meetingUserIds.length
          if (!meetingUserIds.includes(meeting.createUserId)) {
            meeting.meetingUserCount = meeting.meetingUserCount + 1
          }
        }
      })
      state.pageParam.total = res.data.totalCount
    }
  })
}


const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  queryList()
}

// 分页每页查询数量修改触发事件
const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize
  queryList();
}

// 分页页数修改触发事件
const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum
  queryList();
}

// 审批
const onCheck = (val, type) => {
  if (type === 'check') {
    meetingCheckDialogRef.value.open('审核', JSON.parse(JSON.stringify(val)))
  } else if (type === 'view') {
    meetingCheckDialogRef.value.open('查看', JSON.parse(JSON.stringify(val)))
  }
}

const imgTransfer = (name) => {
  if (name) {
    return import.meta.env.VITE_BASE_URL + name
  }
  return name
}

onMounted(() => {
  queryList()
})
</script>

<style scoped lang="less">

</style>
