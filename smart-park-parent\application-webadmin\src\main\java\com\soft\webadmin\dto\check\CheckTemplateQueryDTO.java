package com.soft.webadmin.dto.check;


import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CheckTemplateQueryDTO extends MyPageParam {

    @ApiModelProperty("模板名称")
    private String templateName;


    @ApiModelProperty("模板类型：1 ")
    private Integer templateType;

}
