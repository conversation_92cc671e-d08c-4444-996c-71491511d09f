<template>
  <page-common v-model="state.tableHeight" :queryBool="false">
    <template #table>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane v-for="(tab, index) in state.tabs" :name="tab.value" :label="tab.label" :key="index">
          <el-button type="primary" icon="Plus" @click="addHandle">新增分类</el-button>
          <div style="margin-top: 15px">
            <el-table :height="state.tableHeight - 103" :data="state.treeData" row-key="id">
              <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                :label="item.label" />
              <el-table-column align="center" label="操作" width="230">
                <template #default="scope">
                  <view v-if="(state.equipmentType == '水表' && scope.row.id != '1734854035950800896' && scope.row.id != '1734854101105119232')
                    ||
                    (state.equipmentType == '电表' && scope.row.id != '1735099107715846144' && scope.row.id != '1735099870458417152')

                    ||
                    (state.equipmentType == '气表' && scope.row.id != '1735100001622691840' && scope.row.id != '1735100028411711488')
                    ">
                    <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
                    <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
                  </view>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- 新建&编辑 -->
      <modal-page ref="modal" :title="state.title" @submit="loadTreeData" />
    </template>
  </page-common>
</template>

<script setup>
import { getSubitemTreeAPI, saveSubitemAPI, deleteSubitemAPI } from '@/api/energyManagement/subitem.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import modalPage from './component/modalPage.vue';

const activeName = ref('水表');
const modal = ref();

const state = reactive({
  tableHeight: 100,
  treeData: [],
  tableHeader: [
    {
      prop: 'energyName',
      label: '分类名称',
    },
    {
      prop: 'createTime',
      label: '创建时间',
    },
  ],
  tabs: [
    { label: '水表', value: '水表' },
    { label: '电表', value: '电表' },
    { label: '气表', value: '气表' },
  ],
  equipmentType: '水表',
  title: '',
});

onMounted(() => {
  loadTreeData();
});

const handleClick = (tab, event) => {
  state.equipmentType = tab.props.name;
  loadTreeData();
};

/** 查询数据 */
const loadTreeData = () => {
  getSubitemTreeAPI({ equipmentType: state.equipmentType }).then((res) => {
    state.treeData = res.data;
  });
};

/** 新建 */
const addHandle = () => {
  state.title = '新增分类';
  modal.value.form.id = undefined;
  modal.value.form.equipmentType = state.equipmentType;
  modal.value.open();
};

/** 编辑 */
const editHandle = (row) => {
  state.title = '编辑分类';
  modal.value.form.equipmentType = state.equipmentType;
  modal.value.form.id = row.id;
  modal.value.open();
  nextTick(() => {
    if (!row.equipmentList) {
      if (row.equipmentIds) {
        row.equipmentList = row.equipmentIds.split(",")
      }
    }
    Object.assign(modal.value.form, row);
  });
};

/** 删除 */
const deleteHandle = (row) => {
  let message = '';
  if (row.children) {
    message = '删除后该分类下所有子分类也会删除，确认删除吗？';
  } else {
    message = '是否删除当前分类?';
  }
  ElMessageBox.confirm(message, '提醒', {
    type: 'warning',
  }).then(() => {
    deleteSubitemAPI({ id: row.id }).then((res) => {
      if (res.success) {
        ElMessage.success('删除成功');
        loadTreeData();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};
</script>
