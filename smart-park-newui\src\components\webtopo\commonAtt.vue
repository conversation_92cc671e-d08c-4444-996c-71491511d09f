<template>
  <div class="commonATT">
    <el-form label-width="75px" label-suffix=":">
      <el-form-item v-for="({ key, label }, index) in styleKeys" :key="index" :label="label" >
        <el-color-picker v-if="colorPickerList.includes(key)" v-model="curComponent.style[key]" show-alpha></el-color-picker>
        <el-select v-else-if="key === 'textAlign'" v-model="curComponent.style[key]">
          <el-option v-for="item in alignOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input v-else v-model.number="curComponent.style[key]" type="number"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { styleData } from '@/utils/webtopo/attr.js'
import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent } = storeToRefs(webtopo)

const colorPickerList = ['color','backgroundColor','lineColor','lineBgColor']

const alignOptions = [{ value: 'left', label: '左对齐' }, { value: 'center', label: '居中' }, { value: 'right', label: '右对齐' }]

// 根据组件渲染所需要样式
const styleKeys = computed(() => {
  let itemStyle = Object.keys(curComponent.value.style)
  return styleData.filter(item => itemStyle.includes(item.key))
})
</script>

<style lang='less' scoped></style>
