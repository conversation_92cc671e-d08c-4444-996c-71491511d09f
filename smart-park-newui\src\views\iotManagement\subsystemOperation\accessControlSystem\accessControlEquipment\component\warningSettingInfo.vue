<template>
  <el-drawer :modelValue="drawer" :before-close="cancelClick">
    <template #header>
      <h4>告警配置信息</h4>
    </template>
    <template #default>
      <el-form ref="ruleFormRef" :model="data" label-width="100px" label-suffix=":">
        <el-row>
          <el-col>
            <el-form-item label="配置名称">{{ data.name }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="紧急程度">{{ data.level }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="告警规则">{{ data.ruleDesc }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="关联设备">{{ data.relationEquipmentNameList }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="状态">
              <el-tag :type="data.status == 0 ? 'danger' : 'success'">{{ data.status == 0 ? '停用' : '启用' }}</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="备注说明">{{ data.remark }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
const props = defineProps({
  drawer: {
    type: Boolean,
    default: false,
  },
});
let { drawer } = toRefs(props);
const emit = defineEmits(['cancelClick']);
const data = ref({});

const cancelClick = () => {
  emit('cancelClick');
};

defineExpose({
  data,
});
</script>
