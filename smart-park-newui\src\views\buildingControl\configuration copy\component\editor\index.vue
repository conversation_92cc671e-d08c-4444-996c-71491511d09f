<template>
  <!-- 拖拽画布 -->
  <vue3-draggable-resizable v-model:x="state.x" v-model:y="state.y" :w="200" :h="200" :handles="[]" :draggable="state.draggable"
    @mousewheel.prevent="mouseWheel">
    <!-- 画布 -->
    <div tabindex="0" id="container" :style="{
        ...getCanvasStyle(canvasStyle), width: changeStyleWithScale(canvasStyle.width, canvasStyle.scale) + 'px',
        height: changeStyleWithScale(canvasStyle.height, canvasStyle.scale) + 'px',
      }" @dragover="handleDragOver" @drop="handleDrop" @keydown.ctrl.s="saveHandle" @keydown.ctrl.v="pasteHandle">
      <shape-type @dragMove="dragMove" @saveHandle="saveHandle" @pasteHandle="pasteHandle"></shape-type>
    </div>
  </vue3-draggable-resizable>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { v4 as uuidv4 } from 'uuid'

import shapeType from './shapeType.vue'

import Vue3DraggableResizable from 'vue3-draggable-resizable'
import { getStyle } from '@/utils/webtopo/style.js'
import {changeStyleWithScale, changeComponentsSizeWithScale, getOriginStyle} from '@/utils/webtopo/math.js'

import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { canvasStyle, componentData, copyCurComponent } = storeToRefs(webtopo)

const emit = defineEmits(['dragging'])

const state = reactive({
  x: 40,
  y: 40,
  draggable: true,
  flag: false // 节流阀
})

const getCanvasStyle = (style) => {
  return getStyle(style, ['width', 'height', 'scale'])
}

// 在画布移动
const handleDragOver = (e) => {
  e.preventDefault()
  e.dataTransfer.dropEffect = 'copy'
}

// 目标放在画布上 bool ture 画布拖拽 false 粘贴
const handleDrop = (e) => {
  e.preventDefault()
  e.stopPropagation()
  const component = JSON.parse(e.dataTransfer.getData('info'))
  const rectInfo = document.querySelector('#container').getBoundingClientRect()
  component.style.width = changeStyleWithScale( component.style.width,canvasStyle.value.scale)
  component.style.height = changeStyleWithScale( component.style.height,canvasStyle.value.scale)
  component.style.top = e.clientY - rectInfo.y
  component.style.left = e.clientX - rectInfo.x
  component.id = uuidv4()
  componentData.value.push(component)
}

// 防止拖动组件移动画布
const dragMove = (bool) => {
  state.draggable = bool
}

// 粘贴
const pasteHandle = () => {
  if (copyCurComponent.value) {
    const component = JSON.parse(JSON.stringify(copyCurComponent.value))
    component.style.top = 0
    component.style.left = 0
    componentData.value.push(component)
    ElMessage.success('粘贴成功')
  }
}

// 保存
const saveHandle = (event) => {
  event.returnValue = false // 阻止直接保存网页
}

// 放大或缩小
const mouseWheel = (e) => {
  if (state.flag) return true
  state.flag = true

  let scale = canvasStyle.value.scale

  if(scale + e.deltaY * -0.01 > 10 && scale + e.deltaY * -0.01 < 200){
    canvasStyle.value.scale = changeComponentsSizeWithScale(canvasStyle.value, componentData.value ,scale + e.deltaY * -0.01 ,scale )
    state.x = state.x + parseFloat(changeStyleWithScale(getOriginStyle(e.offsetX, scale),e.deltaY * 0.01).toFixed(2))
    state.y = state.y + parseFloat(changeStyleWithScale(getOriginStyle(e.offsetY, scale),e.deltaY * 0.01).toFixed(2))
  }

  nextTick(() => {
    state.flag = false
  })
}

watch(componentData, () => {
  state.x = 40
  state.y = 40
})
</script>

<style lang='less' scoped>
.vdr-container.active {
  border-color: transparent;
}

#container {
  position: relative;
  outline: 0px;
  background-repeat: no-repeat;
  background-position: center;
}
</style>
