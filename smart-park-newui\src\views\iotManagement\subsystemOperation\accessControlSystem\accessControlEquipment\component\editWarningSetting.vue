<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef" :width="900" class="dialogTextarea">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":">
      <el-row>
        <el-col :span="12">
          <el-form-item label="配置名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入配置名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="紧要程度" prop="level">
            <el-radio-group v-model="form.level">
              <el-radio-button label="一般">一般</el-radio-button>
              <el-radio-button label="重要">重要</el-radio-button>
              <el-radio-button label="紧急">紧急</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联设备" prop="equipmentList">
            <el-tag v-for="tag in form.equipmentList" :key="tag.equipmentId" class="tag" closable @close="handleClose(tag)">
              {{ tag.equipmentName }}
            </el-tag>
            <el-button class="button-new-tag ml-1" size="small" @click="openSelectPage"> + 选择设备 </el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="常开时间" prop="ruleJson">
            <el-select v-model="form.rule" clearable style="width: 40%">
              <el-option v-for="item in options" :key="item.value" :label="item.value" :value="item.value" />
            </el-select>
            <el-input-number v-model="form.ruleValue" :min="1" style="width: 45%" placeholder="最大时长" clearable />
            &nbsp;分钟
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status" v-if="form.id">
            <el-radio-group v-model="form.status">
              <el-radio :label="0">停用</el-radio>
              <el-radio :label="1">启用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注说明" prop="remark">
            <el-input type="textarea" v-model="form.remark" placeholder="请输入备注说明" maxlength="100" :show-word-limit="true" :rows="3" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
  <select-iot ref="modal" @submit="selectedEquipment" />
</template>

<script setup>
import { addWarningSettingAPI } from '@/api/iotManagement/warningSetting.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
const { title } = toRefs(props);
const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({
  subType: 'ENTRANCE_GUARD',
  job: 'entranceGuardWarningHandler',
  equipmentList: [],
});
const options = [
  {
    value: '大于',
    label: '>',
  },
  {
    value: '大于等于',
    label: '>=',
  },
  {
    value: '小于',
    label: '<',
  },
  {
    value: '小于等于',
    label: '<=',
  },
  {
    value: '等于',
    label: '=',
  },
];
const state = reactive({
  relationEquipmentIdList: [],
  rules: {
    name: [{ required: true, message: '配置名称不能为空', trigger: 'blur' }],
    level: [{ required: true, message: '紧要程度不能为空', trigger: 'change' }],
    equipmentList: [{ required: true, message: '关联设备不能为空', trigger: 'change' }],
  },
});
const modal = ref();

const open = () => {
  dialog.value.open();
};

/** 打开选择设备窗口 */
const openSelectPage = () => {
  modal.value.selectedList = JSON.parse(JSON.stringify(form.equipmentList));
  modal.value.open();
};

/** 接收已选的设备 */
const selectedEquipment = (list) => {
  form.equipmentList = list;
  // 校验非空
  ruleFormRef.value.validateField('equipmentList');
};

/** 删除所选的关联设备标签 */
const handleClose = (tag) => {
  form.equipmentList.splice(
    form.equipmentList.findIndex((item) => item.equipmentId === tag.equipmentId),
    1
  );
};

/** 保存 */
const submit = () => {
  form.subType = 'ENTRANCE_GUARD';
  if (form.equipmentList.length > 0) {
    const equipmentIdList = form.equipmentList.map((e) => {
      return e.equipmentId;
    });
    state.relationEquipmentIdList = equipmentIdList;
  }
  form.relationEquipmentIdList = state.relationEquipmentIdList;
  const ruleJson = {
    rule: form.rule,
    value: form.ruleValue,
  };
  form.ruleJson = JSON.stringify(ruleJson);
  if (form.id) {
    subHandle(addWarningSettingAPI, '编辑成功');
  } else {
    subHandle(addWarningSettingAPI, '新建成功');
  }
  function subHandle(req, title) {
    req(form).then((res) => {
      if (res.success) {
        ElMessage.success(title);
        dialog.value.close();
        emit('submit');
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  }
};

defineExpose({
  form,
  open,
});
</script>

<style lang="less" scoped>
.tag {
  margin: 5px 5px 5px 0;
}
</style>
