package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * CheckRecordPointDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@ApiModel("CheckRecordPointDTO对象")
@Data
public class CheckRecordPointDTO {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "数据验证失败，主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "检查记录id")
    private Long recordId;

    @ApiModelProperty(value = "检查点名称")
    private String pointName;

    @ApiModelProperty(value = "检查点类型（EQUIPMENT设备，SPACE空间）")
    private String pointType;

    @ApiModelProperty(value = "设备id/空间id")
    private Long dataId;

    @ApiModelProperty(value = "状态（0未完成，1已完成）")
    private Integer state;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "附件图片")
    private String img;

}
