<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.shifts.ShiftsAttendanceMapper">
    <resultMap type="com.soft.webadmin.model.shifts.ShiftsAttendance" id="ShiftsAttendanceResult">
        <result property="id" column="id" />
        <result property="rosterId" column="roster_id" />
        <result property="businessType" column="business_type" />
        <result property="attendanceDate" column="attendance_date" />
        <result property="shiftsSettingId" column="shifts_setting_id" />
        <result property="startTime" column="start_time" />
        <result property="endTime" column="end_time" />
        <result property="onWorkTime" column="on_work_time" />
        <result property="onWorkResult" column="on_work_result" />
        <result property="delayDuration" column="delay_duration" />
        <result property="offWorkTime" column="off_work_time" />
        <result property="offWorkResult" column="off_work_result" />
        <result property="earlyDepartureDuration" column="early_departure_duration" />
        <result property="clockEquipmentName" column="clock_equipment_name" />
        <result property="clockSpace" column="clock_space" />
        <result property="remarks" column="remarks" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectShiftsAttendanceVo">
        select id, roster_id,business_type, attendance_date,delay_duration,early_departure_duration,shifts_setting_id, start_time, end_time, on_work_time, on_work_result,
               off_work_time, off_work_result, clock_equipment_name, clock_space, remarks, deleted_flag,
               create_user_id, create_time, update_user_id, update_time from sp_shifts_attendance
    </sql>

    <select id="queryList" resultType="com.soft.webadmin.vo.shifts.ShiftsAttendanceVO">
        select ssa.id, ssa.attendance_date,ssa.start_time,ssa.end_time,ssa.on_work_time,ssa.on_work_result,ssa.off_work_time,ssa.off_work_result,
        ssa.clock_space,ssa.clock_equipment_name,sss.show_name,csd.dept_name,
        group_concat(distinct csp.post_name order by csp.post_id) as post_names
        from sp_shifts_attendance ssa
        LEFT JOIN sp_shifts_roster sss on ssa.roster_id = sss.roster_id
        left join common_sys_dept csd on sss.dept_id = csd.dept_id and csd.deleted_flag = 1
        left join sp_shifts_roster_post ssrp on ssrp.roster_id = ssa.roster_id
        left join common_sys_post csp on ssrp.post_id = csp.post_id and csp.deleted_flag = 1
        <where>
            ssa.deleted_flag = 1
            <if test="showName != null and showName != ''">
                and sss.show_name like concat('%', #{showName}, '%')
            </if>
            <if test="businessType != null and businessType != ''">
                and ssa.business_type = #{businessType}
            </if>
            <if test="workResult != null and workResult != ''">
                and ( ssa.on_work_result = #{workResult} or ssa.off_work_result = #{workResult} )
            </if>
            <if test="deptId != null">
                and sss.dept_id = #{deptId}
            </if>
            <if test="cycle != null">
                and ssa.cycle = #{cycle}
            </if>
        </where>
        GROUP BY ssa.id
        order by ssa.update_time desc
    </select>

    <select id="statistic" resultType="com.soft.webadmin.vo.shifts.ShiftsAttendanceStatisticVO">
        SELECT
        sss.show_name,
        sss.roster_id,
        csd.dept_name,
        ssr.cycle,
        sum(
        IF
        ( ssr.allocate_status > - 1, 1, 0 )) AS required_days,
        sum(
        IF
        ( ssa.on_work_time IS NOT NULL OR ssa.off_work_time IS NOT NULL, 1, 0 )) AS real_days,
        sum(
        IF
        ( ssa.on_work_time IS NOT NULL OR ssa.off_work_time IS NOT NULL, 0, 1 )) AS neglect_work_days,
        sum(
        IF
        ( ssa.on_work_time IS NULL OR ssa.off_work_time IS NULL, 1, 0 )) AS lost_card_num,
        sum(
        IF
        ( ssa.delay_duration > 0, 1, 0 )) AS late_num,
        IFNULL(	sum( ssa.delay_duration ),0) AS total_delay_duration,
        sum(
        IF
        ( ssa.early_departure_duration > 0, 1, 0 )) AS early_departure_num,
        IFNULL(	sum( ssa.early_departure_duration ),0) AS total_early_duration
        FROM
        sp_shifts_record ssr
        LEFT JOIN sp_shifts_attendance ssa ON ssr.roster_id = ssa.roster_id
        AND ssr.cycle = ssa.cycle
        AND ssr.attendance_date = ssa.attendance_date
        AND ssr.shifts_setting_id = ssa.shifts_setting_id
        AND ssa.deleted_flag = 1
        LEFT JOIN sp_shifts_roster sss ON ssr.roster_id = sss.roster_id
        LEFT JOIN common_sys_dept csd ON sss.dept_id = csd.dept_id
        AND csd.deleted_flag = 1
        <where>
            ssr.deleted_flag = 1
            and ssr.allocate_status >-1
            and ssr.cycle = #{cycle}
            AND ssr.business_type = #{businessType}
            <if test="showName != null and showName != ''">
                and sss.show_name like concat('%', #{showName}, '%')
            </if>
            <if test="deptId != null">
                and sss.dept_id = #{deptId}
            </if>
        </where>
        GROUP BY
        ssr.roster_id,
        ssr.cycle
    </select>

    <insert id="batchInsert" parameterType="list">
        insert into sp_shifts_attendance (
          id,
          roster_id,
          business_type,
          cycle,
          attendance_date,
          shifts_setting_id,
          start_time,
          end_time,
          on_work_time,
          on_work_result,
          off_work_time,
          off_work_result,
          delay_duration,
          early_departure_duration,
          clock_equipment_name,
          clock_space,
          remarks,
          deleted_flag,
          create_user_id,
          create_time,
          update_user_id,
          update_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (  #{item.id},
            #{item.rosterId},
            #{item.businessType},
            #{item.cycle},
            #{item.attendanceDate},
            #{item.shiftsSettingId},
            #{item.startTime},
            #{item.endTime},
            #{item.onWorkTime},
            #{item.onWorkResult},
            #{item.offWorkTime},
            #{item.offWorkResult},
            #{item.delayDuration},
            #{item.earlyDepartureDuration},
            #{item.clockEquipmentName},
            #{item.clockSpace},
            #{item.remarks},
            #{item.deletedFlag},
            #{item.createUserId},
            #{item.createTime},
            #{item.updateUserId},
            #{item.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        start_time = VALUES(start_time),
        end_time = VALUES(end_time),
        on_work_time = VALUES(on_work_time),
        on_work_result = VALUES(on_work_result),
        off_work_time = VALUES(off_work_time),
        off_work_result = VALUES(off_work_result),
        delay_duration = VALUES(delay_duration),
        early_departure_duration = VALUES(early_departure_duration),
        clock_equipment_name = VALUES(clock_equipment_name),
        clock_space = VALUES(clock_space),
        update_time = VALUES(update_time),
        deleted_flag = VALUES(deleted_flag)
    </insert>
</mapper>