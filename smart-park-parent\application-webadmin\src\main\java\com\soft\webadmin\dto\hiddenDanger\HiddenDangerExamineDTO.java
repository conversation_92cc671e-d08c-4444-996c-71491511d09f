package com.soft.webadmin.dto.hiddenDanger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * HiddenDangerRectifyDTO对象
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@ApiModel("HiddenDangerRectifyDTO对象")
@Data
public class HiddenDangerExamineDTO {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空！")
    private Long id;

    @ApiModelProperty(value = "隐患id")
    @NotNull(message = "隐患id不能为空！")
    private Long hiddenDangerId;

    @ApiModelProperty(value = "审核结果（1完成、2驳回）")
    @NotNull(message = "审核结果不能为空！")
    private Integer examineResult;

    @ApiModelProperty(value = "驳回原因")
    private String examineReason;

}
