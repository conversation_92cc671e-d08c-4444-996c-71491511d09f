<template>
  <dialog-common ref="dialog" title="费用设置" @submit="submit" :formRef="ruleFormRef" :width="450">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":">
      <el-row>
        <el-col>
          <el-form-item label="能耗价格" prop="price">
            <el-input v-model="form.price" placeholder="请输入能耗价格">
              <template #append>元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="单位" prop="unit">
            <el-input v-model="form.unit" placeholder="请输入单位" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { updatePriceAPI } from '@/api/energyManagement/price.js';
import { ElMessage, ElMessageBox } from 'element-plus';

const emit = defineEmits(['submit']);
const dialog = ref();
const ruleFormRef = ref();
const form = reactive({});

// 能耗价格校验
const checkPrice = (rule, value, callback) => {
  const reg = /^\d+.?\d{0,2}$/;
  if (!value) {
    callback(new Error('请输入能耗价格'));
  } else if (!Number(value)) {
    callback(new Error('请输入数字值'));
  } else {
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error('小数点后最多只能输入两位'));
    }
  }
};

const state = reactive({
  rules: {
    price: [{ required: true, validator: checkPrice, trigger: 'blur' }],
    unit: [{ required: true, message: '单位不能为空', trigger: 'blur' }],
  },
});

const open = () => {
  dialog.value.open();
};

/** 保存 */
const submit = () => {
  updatePriceAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success('编辑成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

defineExpose({
  form,
  open,
});
</script>
