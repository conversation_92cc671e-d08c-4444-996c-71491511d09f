package com.soft.webadmin.model.contingency;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.contingency.EmergencyTaskVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 应急预案任务对象 cm_emergency_task
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Data
@TableName(value = "cm_emergency_task")
public class EmergencyTask {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 预案id */
    private Long emergencyId;

    /** 节点id */
    private Long nodeId;

    /** 任务名称 */
    private String taskName;

    /** 应急小组id */
    private Long groupId;


    @Mapper
    public interface EmergencyTaskModelMapper extends BaseModelMapper<EmergencyTaskVO, EmergencyTask> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        EmergencyTask toModel(EmergencyTaskVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        EmergencyTaskVO fromModel(EmergencyTask entity);
    }

    public static final EmergencyTaskModelMapper INSTANCE = Mappers.getMapper(EmergencyTaskModelMapper.class);
}
