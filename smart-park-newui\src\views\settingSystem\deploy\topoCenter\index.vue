<template>
  <page-common>
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="name">
          <el-input v-model="formInline.name" placeholder="项目名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新建组态</el-button>
    </template>
    <template #table>
      <div class="grid">
        <template v-for="(item,index) in state.tableList" :key="index">
          <el-card  shadow="hover" :body-style="{ padding: '0px' }">
          <div class="top">
            <img src="@/assets/img/demo.png" alt="">
            <div class="introduce">
              <h3 class="textellipsis">{{ item.name }}</h3>
              <p class="threeLines" :title="item.remark">{{ item.remark }}</p>
            </div>
          </div>
          <div class="bottom">
            <div class="category" @click="editHandle(item)"> <el-icon><Edit /></el-icon>编辑</div>
            <div class="category" @click="designHandle(item)"> <el-icon><EditPen /></el-icon>设计</div>
            <div class="category" @click="deleteHandle(item)"> <el-icon><Delete /></el-icon>删除</div>
            <div class="category" @click="copyHandle(item)"> <el-icon><DocumentCopy /></el-icon>复制</div>
          </div>
        </el-card>
        </template>
      </div>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <modal-page ref="modal" :title="state.title" @submit="getList"></modal-page>
    </template>
  </page-common>
</template>

<script setup>
import modalPage from './component/modal.vue'

import { ElMessage, ElMessageBox } from 'element-plus'
import { webtopoStore } from '@/store/modules/webtopo.js'

import {topoGetAPI, topoDeteleAPI} from '@/api/settingSystem/topoCenter.js'
import { calcPageNo } from '@/utils/util.js'

const webtopo = webtopoStore()

let formInlineRef = ref({})
let modal = ref()

const formInline = reactive({
  name:""
})

const state = reactive({
  title: '',
  tableList:[],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

// 获取组态
const getList = () => {
  let query = {
    ...formInline,
    ...state.pagetion
  }
  topoGetAPI(query).then(res => {
    state.tableList = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 新建组态
const addHandle = () => {
  state.title = '新建组态'
  modal.value.form.id = ''
  modal.value.open()
}

// 编辑组态
const editHandle = (info) => {
  state.title = '编辑组态'
  modal.value.open()
  nextTick(() => {
    Object.assign(modal.value.form,info)
  })
}

// 设计组态
const designHandle = (info) => {
  webtopo.$reset()
  window.open(`/buildingControl/configuration?configurationId=${info.id}&name=${info.name}`,'_blank')
}

// 删除组态
const deleteHandle = (info) => {
  ElMessageBox.confirm(
    '是否删除当前组态?',
    '提醒',
    {
      type: "warning"
    }
  ).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize)
    topoDeteleAPI({ id: info.id }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 赋值
const copyHandle = ({menuId}) => {
  const el = document.createElement('input')
  el.setAttribute('value', `buildingControl/configurationExhibits/${menuId}`)
  document.body.appendChild(el)
  el.select()
  document.execCommand('copy')
  document.body.removeChild(el)
  ElMessage.success('复制成功')
}
</script>

<style lang='less' scoped>
.grid {
  height: calc(100% - 55px);
  overflow: scroll;
  display: grid;
  grid-gap: 16px 16px;
  grid-template-columns: repeat(auto-fill, calc(25% - 12px));
  grid-template-rows: repeat(auto-fill, 180px);
}

.el-card {
  height: 180px;

  .top {
    height: 140px;
    padding: 12.5px 20px;
    display: flex;

    img {
      height: 70px;
      width: 70px;
      margin-right: 20px;
      flex-shrink: 0;
    }

    .introduce {
      flex: 1;
      overflow: hidden;

      h3 {
        width: 100%;
        color: #666;
        margin-bottom: 10px;
      }

      .text {
        color: #999;
        font-size: 14px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
      }
    }
  }

  .bottom {
    background-color: #F5F7FA;
    display: flex;
    border-top: 1px solid #e4e7ed;
    .category{
      height: 40px;
      line-height: 36px;
      color: #666;
      flex: 1;
      text-align: center;
      cursor: pointer;
      .el-icon{
        vertical-align: middle;
        margin-right: 3px;
      }
      &:hover{
        background-color: #fff;
      }
    }
    .category:nth-child(2){
      border-left: 1px solid #e4e7ed;
      border-right: 1px solid #e4e7ed;
    }
    .category:nth-child(3){
      border-right: 1px solid #e4e7ed;
    }
  }
}

.threeLines{
  overflow: hidden;
  word-break: break-word;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
