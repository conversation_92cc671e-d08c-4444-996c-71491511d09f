package com.soft.webadmin.controller.common;


import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.enums.SpaceBusiTypeEnums;
import com.soft.webadmin.service.common.CommonSpaceService;
import com.soft.webadmin.vo.common.CommonSpaceTreeVO;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/common/space")
public class CommonSpaceController {

    @Resource
    private CommonSpaceService commonSpaceService;


    /**
     * 查询楼层树形列表
     *
     * @param businessName      业务表实体类
     * @param hasData           是否只返回存在业务数据的楼层
     * @return
     */
    @ApiModelProperty("查询楼层树形列表")
    @GetMapping("/tree/floors")
    public ResponseResult<List<CommonSpaceTreeVO>> treeFloors(SpaceBusiTypeEnums spaceBusiType, Boolean hasData) {
        List<CommonSpaceTreeVO> commonSpaceTreeVOS = commonSpaceService.treeFloors(spaceBusiType, hasData);
        return ResponseResult.success(commonSpaceTreeVOS);
    }

}
