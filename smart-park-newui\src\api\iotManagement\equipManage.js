// 设备管理相关接口
import { request } from "@/utils/request";

// 分页查询
export const getEquipListAPI = (data) => {
  return request('get', '/equipment/list', data, 'F');
};


//获取设备列表类别
export const getSubSystemTypesAPI = () => {
  return request('get', '/core/subSystem/integration/list')
}

// 新增设备接口
export const addEquipAPI = (data) => {
  return request('post', '/equipment/insert', data, 'T')
}

// 编辑
export const updateEquipmentAPI = (data) => {
  return request('post', '/equipment/update', data);
};

//获取设备子系统下分类
export const getEquipmentTypeTypesAPI = () => {
  return request('get', '/core/subSystem/equipmentType/list')
}


// 导入excel接口
export const exportFileAPI = (data) => {
  return request('post', '/equipment/import/check', data)
}

// 查看设备详情
export const viewEquipAPI = (data) => {
  return request('get', '/equipment/view', data, 'F')
}


// 设备二维码接口
export const qrCodeAPI = (data) => {
  return request('get', '/equipment/qrcode/' + data)
}


// 导入设备数据接口
export const importEquipDataAPI = (data) => {
  return request('post', '/equipment/import/data', data)
}

// 查看设备生命周期接口
export const checkEquipLifeCycleAPI = (data) => {
  return request('get', '/equipment/lifeCycle/list/' + data, null,'F')
}

// 设备标签接口
export const getEquipLabelAPI = (data) => {
  return request('get', '/equipment/tag/list/' + data, null,'F')
}

// 设备属性接口
export const getEquipKeyAPI = (data) => {
  return request('get','/equipment/attribute/getListByEquipmentId',{equipmentId:data},'F')
}


// 上传设备
export const importEquipmentCheckAPI = (data) => {
  return request('post', '/equipment/import/check', data)
}

// 上传设备数据
export const importEquipmentDataAPI = (data) => {
  return request('post', '/equipment/import/data', data)
}

// 报废
export const scrapAPI = (data) => {
  return request('post', '/equipment/scrap', data)
}

// 删除
export const deleteAPI = (data) => {
  return request('post', '/equipment/delete', data)
}

// 按照设备类型进行分组
export const listEquipmentsGroupByEquipmentTypeAPI = (data) => {
  return request('get', '/equipment/list/groupBy/equipmentType', data, 'F')
}

//根据设别类型查询设备下拉选
export const equipmentListAPI = (data) => {
  return request('get', '/equipment/equipmentList', data, 'F');
}


// 能耗设备分页查询
export const getEnergyEquipmentPageAPI = (data) => {
  return request('get', '/equipment/energyList', data, 'F');
};

export const listEquipmentsByGroupAPI = (data) => {
  return request('get', '/equipment/listByGroup', data, 'F');
}

// 控制
export const doControllerAPI = (data) => {
  return request('post', '/equipment/doControl', data);
}

// 查询设备属性
export const getAttributeListAPI = (data) => {
  return request('get', '/equipment/attribute/getListByEquipmentId', data, 'F');
};

// 预览
export const previewURLsAPI = (data) => {
  return request('post', '/equipment/previewURLs', data);
};

