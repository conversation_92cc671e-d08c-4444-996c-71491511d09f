<template>
  <div class="page" @submit.prevent>
    <div class="query" v-show="queryBool">
      <slot name='query'></slot>
    </div>
    <div class="conent">
      <div class="operate" v-show="operateBool">
        <slot name='operate'></slot>
      </div>
      <div class="pageTable" ref="tableRef">
        <slot name='table'></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { events } from '@/utils/bus.js'

const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  queryBool:{
    type:Boolean,
    default: true
  },
  operateBool:{
    type:Boolean,
    default: true
  }
})

let {queryBool,operateBool} = toRefs(props)

let tableRef = ref()

onMounted(() => {
  resize()
  window.addEventListener('resize',resize)
  events.on('tabClick', () => {
    setTimeout(() => {
      resize()
    })
  })
})

onUnmounted(() => {
  window.removeEventListener('resize',resize)
  events.off('tabClick')
})

const resize = () => {
  let value
  let pagination = tableRef.value.querySelector('.el-pagination')
  if(pagination){
    value = tableRef.value.offsetHeight - pagination.offsetHeight - 18
  }else{
    value = tableRef.value.offsetHeight
  }
  emit('update:modelValue',value)
}

defineExpose({
  resize
})
</script>

<style lang='less' scoped>
.page {
  height: 100%;
  display: flex;
  flex-direction: column;
  .query,.conent{
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
  }
  .query{
    padding-bottom: 0;
  }
}

.query {
  flex-shrink: 0;
  margin-bottom: 15px;
}

.query {
  :deep(.el-form--inline) {
    //height: 35px;
  }
}

.conent{
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .full-screen{
    position: absolute;
    right: 18px;
    cursor: pointer;
  }
  .full-screen:hover{
    transform: scale(1.1);
  }
  .operate{
    display: flex;
    // justify-content: flex-end;
    margin-bottom: 15px;
    flex-shrink: 0;
  }
  .pageTable{
    flex: 1;
    overflow: hidden;
  }
}
</style>
