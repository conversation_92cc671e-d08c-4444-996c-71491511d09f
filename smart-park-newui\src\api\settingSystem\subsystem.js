import { request } from "@/utils/request";

// list
export const subsystemListAPI = (query) => {
  return request('get','/core/subSystem/config/list',query,'F')
}

// listFactoryNames
export const FactoriesListAPI = (query) => {
  return request('get','/core/subSystem/config/listFactories',query,'F')
}

// saveOrUpdate
export const saveOrUpdate = (data) => {
  return request('post','/core/subSystem/config/saveOrUpdate',data)
}

// delete
export const deleteAPI = (data) => {
  return request('post','/core/subSystem/config/delete',data,'F')
}

// 同步数据
export const syncData = (data) => {
  return request('post','/core/subSystem/syncData',data)
}

/**
 * 根据id获取子系统配置
 * @param id
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export const getSubSystemConfigById = (id) => {
  return request('get','/core/subSystem/config/' + id, {}, 'F')
}

/**
 * 保存同步任务
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export const saveSyncTAPI = (data) => {
  return request('post','/core/subSystem/config/saveSyncTAPI',data)
}

/**
 * 获取集成设备列表
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export const getIntegrationList = () => {
  return request('get','/core/subSystem/integration/list',{}, 'F')
}


