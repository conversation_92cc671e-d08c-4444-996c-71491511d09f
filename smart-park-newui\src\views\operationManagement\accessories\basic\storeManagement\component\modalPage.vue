<template>
  <dialog-common ref="dialog" :title="props.title" @submit="submit" @onClose="onClose" :formRef="ruleFormRef" :width="900" class="dialogTextarea">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-row>
        <el-col :span="12">
          <el-form-item label="仓库名称" prop="storehouseName">
            <el-input v-model="form.storehouseName" placeholder="请输入仓库名称"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="容量m³" prop="capacity">
            <el-input v-model.number="form.capacity"  placeholder="请输入容量"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="负责人" prop="headUserId">
            <el-select v-model="form.headUserId" filterable clearable placeholder="请选择负责人(可直接搜索)">
              <el-option v-for="item in props.headUserIdOptions" :label="item.showName + (item.deptName ? ' - ' + item.deptName : '')" :value="item.userId"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="位置信息" prop="spaceId">
            <el-cascader v-model="form.spaceId" ref="cascader" :options="state.spaceOptions" :props="optionsProps" clearable
                         placeholder="请选择位置信息"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
      </el-row>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit
                  placeholder="请输入备注"/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {ElMessage} from 'element-plus';

import { treeAPI } from '@/api/iotManagement/space.js'
import  { saveStoreAPI } from '@/api/operationManagement/storeManagement.js'

// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'id',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};

const validateNumber = (rule, value, callback) => {
  if (value <= 0) {
    return callback(new Error('请输入正确的数值'));
  } else {
    callback();
  }
};

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  headUserIdOptions: {
    type: Array,
    default: []
  },
})

const emit = defineEmits(['submit']);

const ruleFormRef = ref();
const dialog = ref();
const cascader = ref()

const form = reactive({});
const state = reactive({
  rules: {
    storehouseName: [{required: true, message: '请输入仓库名称'}],
    capacity: [ { validator: validateNumber, trigger: 'blur' }],
    spaceId: [{required: true, message: '请选择位置信息'}]
  },
});

onMounted(() => {
  getSpaceTree()
})

// 设备位置
const getSpaceTree = () => {
  treeAPI({deep: 4}).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
};

// 关闭dialog
const onClose = () => {
  delete form.id
  delete form.state
}

// 提交表单
const submit = () => {
  let subForm = {
    ...form,
    businessType: 'OPERATIONS'
  }

  saveStoreAPI(subForm).then(res => {
    if (res.success) {
      ElMessage.success(form.id ? '保存成功' : '新增成功');
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = () => {
  dialog.value.open();
}

defineExpose({
  form,
  open,
});
</script>
