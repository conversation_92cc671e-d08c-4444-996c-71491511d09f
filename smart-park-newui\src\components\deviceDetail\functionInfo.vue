<template>
  <div v-if="state.form.equipmentAttributes && state.form.equipmentAttributes.length > 0">
    <el-form :model="state.form" ref="formRef" label-width="70px" label-suffix=":">
      <el-space wrap :size="50">
        <el-card v-for="equipmentAttribute of state.form.equipmentAttributes" :key="equipmentAttribute.id"
          style="width: 400px; margin-left: 70px">
          <template #header>
            <div class="card-header">
              <span v-if="equipmentAttribute.attributeName">{{ equipmentAttribute.attributeName }}</span>
              <span v-else>{{ equipmentAttribute.attributeKey }}</span>
              <el-button type="primary" size="small" style="float: right"
                @click="onController(equipmentAttribute)">应用</el-button>
            </div>
          </template>
          <el-form-item class="item__content-noBg" label-width="0">
            <el-select v-if="equipmentAttribute.attributeOperType === 'SELECT'"
              v-model="equipmentAttribute.attributeValue" :disabled="equipmentAttribute.modifiable"
              style="flex: 1;margin-right: 10px">
              <el-option v-for="(value, key) in JSON.parse(equipmentAttribute.attributeValueEnum)" :key="key"
                :label="value" :value="key" />
            </el-select>

            <el-switch v-else-if="equipmentAttribute.attributeOperType === 'SWITCH'"
              v-model="equipmentAttribute.attributeValue"
              style="--el-switch-on-color: #13ce66; --el-switch-off-color: #409eff; flex: 1;margin-right: 10px;"
              :active-text="equipmentAttribute.attributeValueEnum[Object.keys(equipmentAttribute.attributeValueEnum)[1]]"
              :active-value="Object.keys(equipmentAttribute.attributeValueEnum)[1]"
              :inactive-text="equipmentAttribute.attributeValueEnum[Object.keys(equipmentAttribute.attributeValueEnum)[0]]"
              :inactive-value="Object.keys(equipmentAttribute.attributeValueEnum)[0]"
              :disabled="!equipmentAttribute.modifiable" />

            <el-input v-else :disabled="!equipmentAttribute.modifiable" v-model="equipmentAttribute.attributeValue"
              style="flex: 1;margin-right: 10px" />
            <el-input v-model="equipmentAttribute.unit" placeholder="单位" style="width:50px;" />
          </el-form-item>
        </el-card>
      </el-space>
      <el-form-item class="item__content-noBg">
        <div style="width: 100%;text-align: center;margin-top: 20px">
          <el-button type="primary" @click="onControllerBatch">
            一键应用
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
  <div v-else>
    <el-empty description="暂无数据" />
  </div>
</template>

<script setup>

import { getEquipKeyAPI } from "@/api/iotManagement/equipManage.js";
import { equipContorlAPI } from "@/api/settingSystem/topoConnect.js";
import { ElMessage } from "element-plus";

const formRef = ref()

const equipmentId = ref()

const state = reactive({
  form: {
    equipmentAttributes: []
  }
})

const queryEquipmentAttributes = async () => {
  // 获取设备属性
  const { success, data } = await getEquipKeyAPI(equipmentId.value)
  if (success) {
    state.form.equipmentAttributes = data
  }
}


// 设备属性控制
const onController = (equipmentAttribute) => {
  let params = {
    equipmentId: equipmentId.value,
    attributes: [
      {
        id: equipmentAttribute.id,
        key: equipmentAttribute.attributeKey,
        value: equipmentAttribute.attributeValue,
        unit: equipmentAttribute.unit
      }
    ]
  }
  doController(params)
}

// 设备属性批量控制
const onControllerBatch = () => {
  let params = {
    equipmentId: equipmentId.value,
    attributes: state.form.equipmentAttributes.map(equipmentAttribute => {
      return {
        id: equipmentAttribute.id,
        key: equipmentAttribute.attributeKey,
        value: equipmentAttribute.attributeValue,
        unit: equipmentAttribute.unit
      }
    })
  }
  doController(params)
}

const doController = (params) => {
  equipContorlAPI(params).then(res => {
    if (res.success) {
      ElMessage.success("操作成功！")
    } else {
      ElMessage.error("操作失败，" + res.errorMessage)
    }
  })
}

const init = () => {
  queryEquipmentAttributes()
}

defineExpose({
  equipmentId,
  init
})
</script>

<style lang="less" scoped></style>
