// 菜单
.el-menu {
  .el-menu-item {
    border-radius: 8px;
  }

  .el-menu-item.is-active:not(.is-disabled) {
    background: linear-gradient(90deg, #2166D8, #0072FF, #51ABDA);
    box-shadow: 0px 9px 18px 0px #76BAF9;
    color: #fff !important;
  }
}

.el-menu--horizontal {
  border-bottom: 0 !important;

  .el-menu-item {
    border-bottom: none !important;
    margin: 0 2px !important;
  }

  .el-menu-item:not(.is-disabled):focus, .el-menu-item:not(.is-disabled):hover {
    background: linear-gradient(90deg, #2166D8, #0072FF, #51ABDA);
    box-shadow: 0px 9px 18px 0px #76BAF9;
    color: #fff !important;
  }
}

.el-menu-vertical-demo, .el-menu--popup {
  border-right: 0 !important;
  margin: 0 10px !important;

  .el-sub-menu {
    .el-menu-item {
      height: 42px;
    }

    .el-sub-menu__icon-arrow {
      right: 8px;
    }
  }

  .el-menu-item:not(.is-disabled):focus, .el-menu-item:not(.is-disabled):hover, .el-sub-menu__title:hover {
    background-color: transparent;
    color: #0081ff;
  }

  .el-sub-menu.is-active {
    .el-sub-menu__title {
      color: #0081ff;
    }
  }

  .el-menu-item {
    height: 40px;
  }

  &:not(.el-menu--collapse) {
    width: 180px;
    min-height: 100%;
  }
}

// 表格
.el-table {
  .el-table__header {
    th.el-table__cell {
      padding: 18px 0;
      color: #242425;
      background-color: #F5F7FA !important;
    }
  }

  .el-table__body {
    .el-table__cell {
      color: #939395;
      padding: 14px 0;
    }
  }
}


// 页签
.el-pagination {
  justify-content: flex-end;
  margin-top: 18px;
}


//下拉菜单
.el-dropdown-link:focus {
  outline: none;
}

.el-form--inline .el-select {
  width: 175px;
}


// 描述框
.el-descriptions__body {
  .el-descriptions__label {
    width: 150px;
  }

  .el-descriptions__content {
    width: 300px;
  }
}

// 提示框高度
.el-popper {
  max-width: 800px;
}

//表单
.el-form-item__label{
  pointer-events: none;
}

// 卡片
.el-card{
  .el-card__body{
    .el-form-item{
      .el-form-item__label {
        color: #8E95A5;
      }
      .el-form-item__content{
        .el-input-number,.el-input-group,.el-date-editor.el-input{
          flex: 1;
        }
      }
    }
  }
}


// 级联选择器
.el-cascader-panel {
  .el-radio {
    width: 100%;
    height: 100%;
    z-index: 10;
    position: absolute;
    right: 0;
  }
  .is-disabled {
    // 不允许选中样式
    cursor: not-allowed;
  }
  .el-radio__input {
    visibility: hidden;
  }
}


// tab标签样式
.el-tabs__nav-wrap {
  .el-tabs__item {
    color: #9C9B9B;
  }
  .is-active {
    color: var(--el-color-primary);
  }

  &::after{
    display: none;
  }
}

// 五列
.el-col.el-col-5{
  width: 20%;
  flex: 0 0 20%;
}
