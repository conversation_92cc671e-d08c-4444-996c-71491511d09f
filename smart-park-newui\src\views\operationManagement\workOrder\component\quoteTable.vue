<template>
  <div class="quoteTable">
    <el-table :data="data.workQuoteRelationVOList" style="margin-bottom: 10px;">
      <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
        :align="item.align" :formatter="item.formatter" />
    </el-table>
    <el-row >
      <el-col :span="6">备件费合计：{{ data.sparePartCost }}</el-col>
      <el-col :span="6">人工费：{{ data.laborCost }}</el-col>
      <el-col :span="6">其他费用合计：{{ data.otherCost }}</el-col>
      <el-col :span="6">费用合计：{{ data.totalCost }}</el-col>
    </el-row>
    <el-row >
      备注：{{ data.quoteRemark }}
    </el-row>
    <el-row >
      <el-col :span="6">报价时间：{{ data.createTime }}</el-col>
      <el-col :span="6">报价人：{{ data.createUserName }}</el-col>
      <el-col :span="6">联系电话：{{ data.createUserPhone }}</el-col>
      <el-col :span="6">出库状态：
        <el-tag :type="state.outStateTypeOptions[data.outState]">{{ state.outStateOptions[data.outState] }}</el-tag>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => { }
  }
})

let { data } = toRefs(props)

const state = reactive({
  tableData: [],
  tableHeader: [
    {
      prop: 'sparePartVO.sparePartName',
      label: '备件名称'
    },
    {
      prop: 'sparePartVO.classifyName',
      label: '备件分类'
    },
    {
      prop: 'sparePartVO.model',
      label: '规格型号'
    },
    {
      prop: 'sparePartVO.unit',
      label: '单位'
    },
    {
      prop: 'storehouseName',
      label: '所在仓库'
    },
    {
      prop: 'receiveQuantity',
      label: '领用数量'
    },
    {
      prop: 'returnQuantity',
      label: '退回数量',
      formatter: (row, column, cellValue) => {
        if (cellValue === 0) {
          return '';
        } else {
          return cellValue;
        }
      }
    },
    {
      prop: 'unitPrice',
      label: '单价（元）'
    },
    {
      prop: 'cost',
      label: '合计（元）'
    }
  ],
  outStateOptions: {
    1: '待出库',
    2: '已出库',
  },
  outStateTypeOptions: {
    1: 'warning',
    2: 'success'
  }
});
</script>

<style lang='less' scoped>
.quoteTable{
  border: 1px solid #eee;
  .el-row{
    padding-left: 12px;
    margin-bottom: 35px;
    font-size: 14px;
    color: #464444;
    word-break: break-all;
  }
  .el-row:last-child{
    margin-bottom: 10px;
  }
}
</style>
