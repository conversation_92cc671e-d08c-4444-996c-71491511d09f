package com.soft.webadmin.dto.shifts;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * ShiftsSettingDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@ApiModel("ShiftsSettingDTO对象")
@Data
public class ShiftsSettingDTO {

    @ApiModelProperty(value = "班次设置表ID")
    @NotNull(message = "数据验证失败，班次设置表ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @NotNull(message = "班次名称不能为空")
    @ApiModelProperty(value = "班次名称")
    private String shiftsName;

    @NotNull(message = "出勤开始时间不能为空")
    @ApiModelProperty(value = "出勤开始时间")
    private String startTime;

    @NotNull(message = "出勤结束时间不能为空")
    @ApiModelProperty(value = "出勤结束时间")
    private String endTime;

    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房")
    private String businessType;

    @ApiModelProperty(value = "删除标记(1: 正常 -1: 已删除)")
    private Integer deletedFlag;

    @ApiModelProperty(value = "颜色")
    private String color;

    @ApiModelProperty(value = "创建者Id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者Id")
    private Long updateUserId;

    @ApiModelProperty(value = "最后更新时间")
    private Date updateTime;

}
