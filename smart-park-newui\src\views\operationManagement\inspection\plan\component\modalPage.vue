<template>
  <el-card class="box-card">
    <template #header>
      <el-row justify="space-between" align="middle">
        <strong>{{ title }}</strong>
        <el-button type="primary" icon="Back" @click="showPage">返回</el-button>
      </el-row>
    </template>

    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":"
      label-position="top">
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">基本信息</div>
        </div>
        <div class="detail-area">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item  label="计划名称" prop="planName">
                <el-input v-model="form.planName" placeholder="请输入计划名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="巡检方式" prop="planMode">
                <el-select v-model="form.planMode" placeholder="请选择巡检方式">
                  <el-option v-for="item in state.planModeList" :value="item.value" :label="item.label" :key="item.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5" v-if="form.planMode === 1">
              <el-form-item  label="工作班组" prop="workGroupId">
                <el-select v-model="form.workGroupId" filterable placeholder="请选择工作班组">
                  <el-option v-for="item in state.workGroupList" :value="item.id" :label="item.name" :key="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item  label="开始时间" prop="startTime">
                <el-time-select v-model="form.startTime" start="00:00" step="00:10" end="23:50" placeholder="请选择开始时间"
                  clearable />
              </el-form-item>
            </el-col>
            <el-col :span="5" v-if="form.planMode === 1">
              <el-form-item label="处理时限"
                prop="handleLimitDuration">
                <el-input-number v-model="form.handleLimitDuration" :min="1" :precision="0" controls-position="right"
                  placeholder="请输入处理时限" clearable />
                &nbsp;小时
              </el-form-item>
            </el-col>
            <el-col :span="5" v-if="form.planMode === 1">
              <el-form-item  label="派单时间" prop="advanceTime">
                <el-input v-model.number="form.advanceTime" placeholder="请输入派单时间" clearable>
                  <template #prepend>提前</template>
                </el-input>
                &nbsp;小时
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item  label="执行频率" prop="scheduleType">
                <el-tabs v-model="form.scheduleType" type="border-card">
                  <el-tab-pane label="按月" name="MONTH">
                    <el-form-item v-if="form.scheduleType === 'MONTH'" label="" prop="dayList">
                      <el-checkbox-group v-model="form.dayList" style="padding-left: 18px">
                        <el-checkbox v-for="i in state.dayList" :key="i" :value="i">{{ i }}</el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </el-tab-pane>
                  <el-tab-pane label="按周" name="WEEK">
                    <el-form-item  v-if="form.scheduleType === 'WEEK'" label=""
                      prop="weekList">
                      <el-checkbox-group v-model="form.weekList" style="padding-left: 18px">
                        <el-checkbox v-for="item in state.weekList" :key="item.value" :value="item.value">{{
                          item.label
                        }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </el-tab-pane>
                  <el-tab-pane label="自定义" name="CUSTOM">
                    <el-form-item  v-if="form.scheduleType === 'CUSTOM'" label=""
                      prop="dateList">
                      <div>
                        <el-calendar>
                          <template #date-cell="{ data }">
                            <p style="height: 100%" @click="calendarClick(data.day)">
                              {{ data.day.split('-')[2] }}
                            </p>
                          </template>
                        </el-calendar>
                        <div style="margin-left: 11px">
                          <el-tag v-for="day in form.dateList" :key="day" @close="handleClose(day)" closable
                            style="margin-left: 8px">{{ day }}
                          </el-tag>
                        </div>
                      </div>
                    </el-form-item>
                  </el-tab-pane>
                </el-tabs>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item v-if="form.planMode === 2" label="巡检设备" prop="equipmentList">
                <el-tag v-for="tag in form.equipmentList" :key="tag.equipmentId" class="tag" closable @close="removeEquipmentHandle(tag)">
                  {{ tag.equipmentName }}
                </el-tag>
                <el-button class="button-new-tag ml-1" ref="dataRef" size="small" @click="openSelectPage(true)"> + 选择设备 </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div v-if="form.planMode === 1" style="margin-top: 15px">
        <el-row justify="space-between" align="middle">
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">巡检点</div>
          </div>
          <el-button @click="addHandle()" style="margin-bottom: 10px">添加</el-button>
        </el-row>
        <div class="detail-area">
          <el-table :data="form.checkPlanPointList">
            <el-table-column label="序号" type="index" width="60" />
            <el-table-column v-for="(column, index) in state.columnList" :label="column.label" :prop="column.prop"
              :key="index">
              <template #default="scope">
                <div v-if="column.prop === 'pointType'">
                  <span v-show="scope.$index !== editIndex">{{
                    scope.row.pointType === 'EQUIPMENT' ? '设备' : '空间'
                  }}</span>
                  <el-form-item  v-show="scope.$index === editIndex"
                    :prop="`checkPlanPointList.${scope.$index}.${column.prop}`" label-width="0">
                    <el-select v-model="scope.row[column.prop]" @change="pointTypeChange(scope.$index)">
                      <el-option value="EQUIPMENT" label="设备" />
                      <el-option value="SPACE" label="空间" />
                    </el-select>
                  </el-form-item>
                </div>
                <div v-if="column.prop === 'dataId'">
                  <span v-show="scope.row.pointType === 'EQUIPMENT'">{{ scope.row.dataName }}</span>
                  <span v-show="scope.row.pointType === 'SPACE' && scope.$index !== editIndex">{{
                    scope.row.dataName
                  }}</span>
                  <el-form-item  v-show="scope.$index === editIndex"
                    :prop="`checkPlanPointList.${scope.$index}.${column.prop}`" :rules="{
                      required: true,
                      message: `请选择${column.label}`,
                      trigger: 'blur',
                    }" label-width="0">
                    <el-button v-if="scope.row.pointType === 'EQUIPMENT'" class="button-new-tag ml-1" ref="dataRef"
                      size="small" @click="openSelectPage(false)"> + 选择设备
                    </el-button>
                    <el-cascader v-if="scope.row.pointType === 'SPACE'" v-model="scope.row[column.prop]"
                      :options="state.spaceOptions" :props="optionsProps" ref="dataRef" clearable placeholder="请选择空间"
                      @change="spaceChange(scope.$index)" />
                  </el-form-item>
                </div>
                <div v-if="column.prop === 'templateId'">
                  <span v-show="scope.$index !== editIndex">{{ scope.row.templateName }}</span>
                  <el-form-item  v-show="scope.$index === editIndex"
                    :prop="`checkPlanPointList.${scope.$index}.${column.prop}`" :rules="{
                      required: true,
                      message: `请选择${column.label}`,
                      trigger: 'blur',
                    }" label-width="0">
                    <el-select v-if="scope.row.pointType === 'EQUIPMENT'" v-model="scope.row[column.prop]" filterable
                      placeholder="请选择检查模板" @change="templateChange(scope.row.pointType, $event)">
                      <el-option v-for="item in state.equipmentTemplateList" :key="item.id" :value="item.id"
                        :label="item.templateName" />
                    </el-select>
                    <el-select v-if="scope.row.pointType === 'SPACE'" v-model="scope.row[column.prop]" filterable
                      placeholder="请选择检查模板" @change="templateChange(scope.row.pointType, $event)">
                      <el-option v-for="item in state.spaceTemplateList" :key="item.id" :value="item.id"
                        :label="item.templateName" />
                    </el-select>
                  </el-form-item>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="210">
              <template #default="scope">
                <div>
                  <el-button v-show="scope.$index !== editIndex" link icon="Edit" type="primary"
                    @click="editHandle(scope.row)">编辑
                  </el-button>
                  <el-button v-show="scope.$index !== editIndex" link icon="Delete" type="danger"
                    @click="deleteHandle(scope.row)">删除
                  </el-button>
                  <!--<el-button v-show="scope.$index !== editIndex" link icon="Document" type="success" @click="viewHandle(scope.row)">详情</el-button>-->
                  <el-button v-show="scope.$index === editIndex" link icon="Document" type="success"
                    @click="saveHandle(scope.row)" style="margin: 0">保存
                  </el-button>
                  <el-button v-show="scope.$index === editIndex" link icon="Delete" type="danger"
                    @click="closeHandle(scope.row)">删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div style="text-align: center; margin-top: 20px;">
        <el-button type="info" @click="showPage">取消</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </div>
    </el-form>
    <select-operate ref="modal" :multiple="state.isMultiple" :category="form.planMode === 2 ? 'IOT' :  ''" @rowClick="equipSelect" @submit="selectedEquipment" />
    <drawer-point-page ref="drawer" :drawer="state.drawer" @cancelClick="state.drawer = false" />
  </el-card>
</template>

<script setup>
import dayjs from "dayjs";

import { saveCheckPlanAPI } from '@/api/operationManagement/checkPlan.js';
import { getWorkGroupPageAPI } from '@/api/operationManagement/workGroup.js';
import { treeAPI } from '@/api/iotManagement/space.js';
import { getTemplatePageAPI, getTemplateItemListAPI } from '@/api/operationManagement/template.js';
import { viewEquipAPI } from '@/api/operationManagement/equipManage.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import drawerPointPage from './drawerPointPage.vue';

const emit = defineEmits(['showPage'])

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  info: {
    type: Object,
    default: {}
  }
});
const { title, info } = toRefs(props);
const ruleFormRef = ref();
const dialog = ref();
const drawer = ref();
const form = reactive({
  scheduleType: 'MONTH',
  planType: 'PATROL_INSPECTION',
  dayList: [],
  dateList: [],
  weekList: [],
  checkPlanPointList: [],
  equipmentList: [],
});

/** 校验派单时间 */
const checkAdvanceTime = (rule, value, callback) => {
  if (!value && isNaN(value)) {
    callback(new Error('派单时间不能为空'));
  } else if (isNaN(value)) {
    callback(new Error('请输入数字值'));
  } else if (value < 0) {
    callback(new Error('请输入不小于0的数字'));
  } else {
    callback();
  }
};

const state = reactive({
  planModeList: [
    { value: 1, label: '人工巡检' },
    { value: 2, label: '智能巡检' },
  ],
  workGroupList: [],
  templateList: [],
  dayList: [],
  weekList: [
    { value: '1', label: '周一' },
    { value: '2', label: '周二' },
    { value: '3', label: '周三' },
    { value: '4', label: '周四' },
    { value: '5', label: '周五' },
    { value: '6', label: '周六' },
    { value: '7', label: '周日' },
  ],
  dateList: [],
  columnList: [
    { prop: 'pointType', label: '巡检点类型' },
    { prop: 'dataId', label: '设备' },
    { prop: 'templateId', label: '检查模板' },
  ],
  equipmentTemplateList: [],
  spaceTemplateList: [],
  rules: {
    planName: [{ required: true, message: '计划名称不能为空', trigger: 'blur' }],
    planMode: [{ required: true, message: '请选择巡检方式', trigger: 'change' }],
    workGroupId: [{ required: true, message: '请选择工作班组', trigger: 'change' }],
    startTime: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],
    handleLimitDuration: [{ required: true, message: '处理时限不能为空', trigger: 'blur' }],
    advanceTime: [{ required: true, validator: checkAdvanceTime, trigger: 'blur' }],
    dayList: [{ required: true, message: '请选择至少一个日期', trigger: 'blur' }],
    dateList: [{ required: true, message: '请选择至少一个日期', trigger: 'blur' }],
    weekList: [{ required: true, message: '请选择至少一个日期', trigger: 'blur' }],
    scheduleType: [{ required: true, message: '请选择执行频率', trigger: 'blur' }],
    equipmentList: [{ required: true, message: '请选择巡检设备', trigger: 'blur' }],
  },
  drawer: false,
  isMultiple: false,
});
const editIndex = ref(-1);
const modal = ref();
const dataRef = ref();

onMounted(() => {
  Object.assign(form, props.info);
  // 处理时限，分钟转换成小时
  if (form.handleLimitDuration) {
    form.handleLimitDuration = form.handleLimitDuration / 60;
  }
  for (let i = 1; i <= 31; i++) {
    state.dayList.push(i.toString());
  }
  loadWorkGroupList();
  loadTemplateList();
  loadSpaceTree();
})

// 空间级联选择配置
const optionsProps = {
  label: 'name',
  value: 'id',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};

/** 查询空间 */
const loadSpaceTree = () => {
  treeAPI({ deep: 4 }).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
};

const pointTypeChange = (index) => {
  form.checkPlanPointList[editIndex.value].dataId = '';
  form.checkPlanPointList[editIndex.value].dataName = '';
};

/** 选择空间 */
const spaceChange = (index) => {
  const space = dataRef.value[index].getCheckedNodes()[0].data;
  form.checkPlanPointList[editIndex.value].dataName = space.fullName;
  // 隐藏面板
  dataRef.value[index].togglePopperVisible();
};

/** 查看维保点详情 */
const viewHandle = (row) => {
  nextTick(() => {
    viewEquipAPI({ equipmentId: row.dataId }).then((res) => {
      drawer.value.form.equipment = res.data;
    });
    getTemplateItemListAPI({ templateId: row.templateId }).then((res) => {
      drawer.value.form.templateItemList = res.data;
    });
    state.drawer = true;
  });
};

const validateFun = async () => {
  let dataIdValid = true;
  let templateIdValid = true;

  await ruleFormRef.value.validateField(
    `checkPlanPointList.${editIndex.value}.dataId`,
    (valid) => {
      dataIdValid = valid;
    }
  );

  await ruleFormRef.value.validateField(
    `checkPlanPointList.${editIndex.value}.templateId`,
    (valid) => {
      templateIdValid = valid;
    }
  );

  return dataIdValid && templateIdValid
}

/** 维保点新增 */
const addHandle = async () => {
  let res = await validateFun();

  if (res) {
    const data = { pointType: 'EQUIPMENT', dataId: undefined, templateId: undefined };
    form.checkPlanPointList.push(data);
    editIndex.value = form.checkPlanPointList.length - 1;
  }
};

/** 维保点编辑 */
const editHandle = async (row) => {
  let res = await validateFun();

  if(res){
    editIndex.value = form.checkPlanPointList.indexOf(row);
  }
};

/** 维保点删除 */
const deleteHandle = async (row) => {
  let res = await validateFun();
  if(res){
    let index = form.checkPlanPointList.indexOf(row);
    form.checkPlanPointList.splice(index, 1);
  }
};

/** 维保点保存 */
const saveHandle = async (row) => {
  let res = await validateFun();

  if(res){
    editIndex.value = -1;
  }
};

/** 编辑状态时删除 */
const closeHandle = (row) => {
  let index = form.checkPlanPointList.length - 1;
  form.checkPlanPointList.splice(index, 1);
};

const open = () => {
  dialog.value.open();
  loadWorkGroupList();
  loadTemplateList();
  editIndex.value = -1;
};

const showPage = () => {
  emit('showPage', 0)
}

/** 查询工作班组 */
const loadWorkGroupList = () => {
  getWorkGroupPageAPI().then((res) => {
    state.workGroupList = res.data.dataList;
  });
};

/** 日期选择 */
const calendarClick = (val) => {
  let index = form.dateList.indexOf(val);
  if (index === -1) {
    form.dateList.push(val);
  }
};

/** 删除已选择的日期 */
const handleClose = (val) => {
  let index = form.dateList.indexOf(val);
  form.dateList.splice(index, 1);
};

/** 打开选择设备窗口 */
const openSelectPage = (isMultiple) => {
  modal.value.selectedList = form.equipmentList
  state.isMultiple = isMultiple;
  modal.value.open();
};

// 设备选择
const equipSelect = (row) => {
  form.checkPlanPointList[editIndex.value].dataId = row.equipmentId;
  form.checkPlanPointList[editIndex.value].dataName = row.equipmentName;
};

/** 接收已选的设备 */
const selectedEquipment = (list) => {
  form.equipmentList = list;
  // 校验非空
  ruleFormRef.value.validateField('equipmentList');
};

/** 移除所选设备 */
const removeEquipmentHandle = (tag) => {
  form.equipmentList.splice(
    form.equipmentList.findIndex((item) => item.equipmentId === tag.equipmentId),
    1
  );
};

/** 查询维保模板 */
const loadTemplateList = () => {
  state.equipmentTemplateList = [];
  getTemplatePageAPI({ templateType: 1 }).then((res) => {
    state.equipmentTemplateList = res.data.dataList;
  });
  state.spaceTemplateList = [];
  getTemplatePageAPI({ templateType: 2 }).then((res) => {
    state.spaceTemplateList = res.data.dataList;
  });
};

/** 选择维保模板 */
const templateChange = (pointType, val) => {
  let obj = {};
  if (pointType === 'EQUIPMENT') {
    obj = state.equipmentTemplateList.find((item) => {
      return item.id === val;
    });
  } else {
    obj = state.spaceTemplateList.find((item) => {
      return item.id === val;
    });
  }
  form.checkPlanPointList[editIndex.value].templateName = obj.templateName;
};

/** 提交表单 */
const submit = () => {
  ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      if (form.planMode === 1 && form.checkPlanPointList.length === 0) {
        ElMessageBox.alert('请至少添加一个巡检点', '提醒', {
          type: 'warning',
        });
        return false;
      }
      if (form.scheduleType === 'MONTH') {
        form.dayList.sort((a, b) => a - b)
        form.scheduleRule = form.dayList.join(',');
      } else if (form.scheduleType === 'WEEK') {
        form.weekList.sort((a, b) => a - b)
        form.scheduleRule = form.weekList.join(',');
      } else if (form.scheduleType === 'CUSTOM') {
        form.dateList.sort((a,b) => dayjs(b).isAfter(a) ? -1 : 1)
        form.scheduleRule = form.dateList.join(',');
      }
      if (form.planMode === 2 && form.equipmentList.length > 0) {
        // 智能巡检，处理巡检设备参数
        form.equipmentIds = form.equipmentList.map(e => e.equipmentId).join(',');
      }

      saveCheckPlanAPI(form).then((res) => {
        if (res.success) {
          ElMessage.success(title.value + '成功');
          emit('showPage', 0)
        } else {
          ElMessage.error(res.errorMessage);
        }
      });
    } else {
      console.log('error submit!', fields)
    }
  })
};

defineExpose({
  form,
  open,
});
</script>

<style lang="less" scoped>
.el-tabs--border-card {
  width: 710px;
}

.el-calendar {
  width: 90%;
}

:deep(.el-calendar__header) {
  padding: 0 20px 10px;
}

:deep(.el-calendar__body) {
  padding: 0 20px 10px;
}

:deep(.el-calendar-table thead th) {
  padding: 0;
}

:deep(.el-calendar-table .el-calendar-day) {
  height: 35px;
}

:deep(.el-checkbox) {
  width: 36px;
}

.tag {
  margin: 5px 5px 5px 0;
}

:deep(.el-input__inner::placeholder) {
  text-align: left;
}
</style>
