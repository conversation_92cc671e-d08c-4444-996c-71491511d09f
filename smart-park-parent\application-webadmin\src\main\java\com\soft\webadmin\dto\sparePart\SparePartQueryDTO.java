package com.soft.webadmin.dto.sparePart;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SparePartDTO对象
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@ApiModel("SparePartDTO对象")
@Data
public class SparePartQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "名称或编号")
    private String nameOrCode;

    // @ApiModelProperty(value = "分类id")
    // private Long classifyId;

}
