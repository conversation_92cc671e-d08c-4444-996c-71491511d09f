import { request } from '@/utils/request';

// 分页查询
export const commentPageAPI = (params) => {
    return request('get', '/contingency/comment/getList', params, 'F');
};

// 发表评论
export const commentSaveAPI = (data) => {
    return request('post', '/contingency/comment/save', data);
};

// 删除评论
export const commentDeteleAPI = (params) => {
    return request('post', '/contingency/comment/delete', params, 'F');
};
