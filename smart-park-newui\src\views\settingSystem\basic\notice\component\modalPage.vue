<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" @onClose="onClose" :formRef="ruleFormRef" :width="900" class="dialogTextarea">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":">
      <el-row>
        <el-col>
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" clearable />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio-button label="公告" />
              <el-radio-button label="通知" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col>
          <el-col>
            <el-form-item v-if="form.type === '公告'" label="有效期" prop="daterange">
              <div style="width: 100%">
                <el-date-picker
                  v-model="form.daterange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  placeholder="请选择上线时间"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-form-item v-if="form.type === '通知'" label="通知人" prop="userIdList">
            <el-select v-model="form.userIdList" placeholder="请选择通知人" multiple clearable>
              <el-option v-for="user in state.userList" :key="user.userId" :label="user.showName" :value="user.userId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="内容" prop="content">
            <div style="border: 1px solid #ccc"  v-if="state.diaShow">
              <Toolbar
                style="border-bottom: 1px solid #ccc"
                :editor="editorRef"
                :defaultConfig="state.toolbarConfig"
                :mode="state.mode"
              />
              <Editor
                style="height: 370px; overflow-y: hidden"
                v-model="form.content"
                :defaultConfig="state.editorConfig"
                :mode="state.mode"
                @onCreated="handleCreated"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item v-if="!form.id" label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio-button label="1">发布</el-radio-button>
              <el-radio-button label="0">未发布</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { saveNoticeAPI } from '@/api/settingSystem/notice.js';
import { getPageAPI } from '@/api/settingSystem/user.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onBeforeUnmount, ref, shallowRef, onMounted } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
const { title } = toRefs(props);
const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({
  userIdList: [],
});
const editorRef = shallowRef();

/** 校验内容 */
const checkContent = (rule, value, callback) => {
  if (!value || value === '<p><br></p>') {
    callback(new Error('内容不能为空'));
  }  else {
    callback();
  }
};

const state = reactive({
  diaShow: false,
  toolbarConfig: {
    excludeKeys: [
      // 排除菜单组，写菜单组 key 的值即可
      'group-video', //去掉视频
      'group-image', //去掉图片
    ],
  },
  editorConfig: {
    placeholder: '请输入内容...',
  },
  mode: 'default',
  userList: [],
  rules: {
    title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
    daterange: [{ required: true, message: '有效期不能为空', trigger: 'change' }],
    userIdList: [{ required: true, message: '通知人不能为空', trigger: 'change' }],
    content: [{ required: true, validator: checkContent, trigger: 'blur' }],
  },
});

const open = () => {
  state.diaShow = true
  dialog.value.open();
  setTimeout(() => {
    loadUserList();
  }, 200);
};

const onClose = () => {
  state.diaShow = false
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

// 记录 editor 实例，重要！
const handleCreated = (editor) => {
  editorRef.value = editor;
};

/** 查询用户 */
const loadUserList = () => {
  const sysUserDtoFilter = {};
  getPageAPI({ sysUserDtoFilter }).then((res) => {
    state.userList = res.data.dataList;
  });
};

/** 保存 */
const submit = () => {
  if (form.type === '公告') {
    form.userIdList = [];
    form.beginTime = form.daterange[0];
    form.endTime = form.daterange[1];
  } else {
    form.beginTime = undefined;
    form.endTime = undefined;
  }
  saveNoticeAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success((form.id ? '编辑' : '新建') + form.type + '成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
};

defineExpose({
  form,
  open,
});
</script>
