package com.soft.webadmin.controller.check;

import cn.hutool.core.collection.CollectionUtil;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.sub.model.equipment.Equipment;
import com.soft.webadmin.dto.check.CheckPlanDTO;
import com.soft.webadmin.dto.check.CheckPlanQueryDTO;
import com.soft.webadmin.model.equipment.EquipmentOm;
import com.soft.webadmin.service.check.CheckPlanPointService;
import com.soft.webadmin.service.check.CheckPlanService;
import com.soft.webadmin.vo.check.CheckPlanPointVO;
import com.soft.webadmin.vo.check.CheckPlanVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 检查计划控制器类
 * 
 * <AUTHOR>
 * @date 2023-12-12
 */
@Api(tags = "检查计划接口")
@RestController
@RequestMapping("/check/plan")
public class CheckPlanController {

    @Autowired
    private CheckPlanService checkPlanService;

    @Autowired
    private CheckPlanPointService checkPlanPointService;

    @ApiOperation(value = "查询")
    @GetMapping("/getPage")
    public ResponseResult<MyPageData<CheckPlanVO>> list(CheckPlanQueryDTO queryDTO) {
        return ResponseResult.success(checkPlanService.getPage(queryDTO));
    }

    @ApiOperation(value = "保存")
    @PostMapping("/save")
    public ResponseResult<Void> save(@Validated @RequestBody CheckPlanDTO saveDTO) {
        return checkPlanService.saveOrUpdate(saveDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        return checkPlanService.delete(id);
    }

    @ApiOperation(value = "启停用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "计划id"),
            @ApiImplicitParam(name = "state", value = "状态：0停用，1启用")
    })
    @PostMapping("/updateState")
    public ResponseResult<Void> updateState(@RequestParam Long id, @RequestParam Boolean state) {
        return checkPlanService.updateState(id, state);
    }

    @ApiOperation(value = "查询点位")
    @GetMapping("/point/list")
    public ResponseResult<List<CheckPlanPointVO>> pointList(@RequestParam Long planId) {
        List<CheckPlanPointVO> checkPlanPointVOList =
                checkPlanPointService.queryListByPlanIdList(CollectionUtil.toList(planId));
        return ResponseResult.success(checkPlanPointVOList);
    }

    @ApiOperation(value = "查询智能巡检设备")
    @GetMapping("/equipment/list")
    public ResponseResult<List<EquipmentOm>> equipmentList(@RequestParam String equipmentIds) {
        return ResponseResult.success(checkPlanService.getEquipmentList(equipmentIds));
    }

}
