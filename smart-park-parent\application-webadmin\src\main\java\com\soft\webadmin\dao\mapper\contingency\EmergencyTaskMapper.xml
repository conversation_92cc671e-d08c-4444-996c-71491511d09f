<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.contingency.EmergencyTaskMapper">
    <resultMap type="com.soft.webadmin.model.contingency.EmergencyTask" id="EmergencyTaskResult">
        <result property="id" column="id" />
        <result property="emergencyId" column="emergency_id" />
        <result property="nodeId" column="node_id" />
        <result property="taskName" column="task_name" />
        <result property="groupId" column="group_id" />
    </resultMap>

    <sql id="selectEmergencyTaskVo">
        select t.id, t.emergency_id, t.node_id, t.task_name, t.group_id from cm_emergency_task t
    </sql>
    
</mapper>