<template>
  <dialog-common ref="dialogRef" :title="state.title" @submit="submit" :formRef="ruleFormRef" :width="900"
    class="dialogTextarea">
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.rules" label-width="100px" label-suffix=":">
      <el-row>
        <el-col :span="12">
          <el-form-item label="告警名称" prop="name">
            <el-input v-model="state.form.name" placeholder="请输入告警名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="告警等级" prop="level">
            <el-select v-model="state.form.level" clearable placeholder="告警等级">
              <el-option v-for="item in state.levelOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="告警项" prop="itemName">
            <el-select v-model="state.form.itemName" clearable placeholder="请选择告警项">
              <el-option v-for="item in state.warningItemOptions" :key="item.name" :label="item.name"
                :value="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            {{
              state.form.itemName ? state.warningItemOptions.find(warningItem => warningItem.name ===
                state.form.itemName).unit : ''
            }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上限值" prop="maxValue">
            <el-input-number v-model="state.form.maxValue" :controls="false" :precision="0" :min="1" @change="value => {
              if (state.form.minValue) {
                if (state.form.minValue > value) {
                  ElMessage.warning('上限值不能小于下限值！')
                  state.form.maxValue = state.form.minValue
                }
              }
            }" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="下限值" prop="minValue">
            <el-input-number v-model="state.form.minValue" :controls="false" :precision="0" :min="1" @change="value => {
              if (state.form.maxValue) {
                if (state.form.maxValue < value) {
                  ElMessage.warning('下限值不能大于上限值！')
                  state.form.minValue = state.form.maxValue
                }
              }
            }" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="关联设备" prop="equipmentList">
            <el-tag v-for="tag in state.form.equipmentList" :key="tag.equipmentId" class="tag" closable
              @close="handleClose(tag)">
              {{ tag.equipmentName }}
            </el-tag>
            <el-button class="button-new-tag ml-1" size="small" @click="openSelectPage"> + 选择设备</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
  <select-iot ref="equipmentSelectPageRef" @submit="selectedEquipment" />
</template>

<script setup>
import { addWarningSettingAPI, getWarningItemsAPI } from '@/api/iotManagement/warningSetting.js';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';

const props = defineProps({
  subType: {
    type: String,
    default: ''
  },
  equipmentType: {
    type: Object,
    default: {}
  },
  job: {
    type: String,
    default: ''
  }
});
const { subType, equipmentType, job } = toRefs(props);

const emit = defineEmits(['onClose']);

const ruleFormRef = ref();
const dialogRef = ref();


const equipmentSelectPageRef = ref();

const state = reactive({
  title: '',
  form: {
    equipmentList: []
  },
  equipments: [],
  levelOptions: [
    '普通', '紧急', '特急'
  ],
  warningItemOptions: [],
  rules: {
    name: [{ required: true, message: '配置名称不能为空', trigger: 'blur' }],
    level: [{ required: true, message: '告警等级不能为空', trigger: 'blur' }],
    itemName: [{ required: true, message: '告警项不能为空', trigger: 'blur' }],
    equipmentList: [{ required: true, message: '关联设备不能为空', trigger: 'change' }],
  },
});


const open = (title, val) => {
  dialogRef.value.open();
  nextTick(() => {
    state.title = title
    if (val) {
      state.form = val
      state.form.equipmentList = val.relationEquipmentList
    }
  })
};

const loadWarningItemOptions = () => {
  let param = {
    warningSettingType: equipmentType.value.type
  };
  getWarningItemsAPI(param).then(res => {
    if (res.success) {
      state.warningItemOptions = res.data
    }
  })
}


/** 打开选择设备窗口 */
const openSelectPage = () => {
  equipmentSelectPageRef.value.selectedList = JSON.parse(JSON.stringify(state.form.equipmentList));
  equipmentSelectPageRef.value.open();
};

/** 接收已选的设备 */
const selectedEquipment = (list) => {
  state.form.equipmentList = list;
  // 校验非空
  ruleFormRef.value.validateField('equipmentList');
};

/** 删除所选的关联设备标签 */
const handleClose = (tag) => {
  state.form.equipmentList.splice(
    state.form.equipmentList.findIndex((item) => item.equipmentId === tag.equipmentId),
    1
  );
};

/** 保存 */
const submit = () => {
  let param = {
    id: state.form.id,
    name: state.form.name,
    level: state.form.level,
    subType: subType.value,
    equipmentType: equipmentType.value.name,
    job: job.value,
    status: 1
  }
  if (state.form.equipmentList.length > 0) {
    param.relationEquipmentIdList = state.form.equipmentList.map((e) => {
      return e.equipmentId;
    });
  }
  const warningRuleItem = {
    name: state.form.itemName,
    attributeKey: state.form.attributeKey,
    unit: state.form.itemName ? state.warningItemOptions.find(warningItem => warningItem.name === state.form.itemName).unit : ''
  };

  let warningRuleArray = []
  if (warningRuleItem.name) {
    let warningRules = state.warningItemOptions.find(warningItem => warningItem.name === warningRuleItem.name).warningRules;

    let maxRule = warningRules.find(warningRule => warningRule.ruleName === "上限值");
    warningRuleArray.push({
      ruleType: maxRule.ruleType,
      ruleName: maxRule.ruleName,
      ruleOperator: maxRule.ruleOperator,
      value: state.form.maxValue
    })
    let minRule = warningRules.find(warningRule => warningRule.ruleName === "下限值");
    warningRuleArray.push({
      ruleType: minRule.ruleType,
      ruleName: minRule.ruleName,
      ruleOperator: minRule.ruleOperator,
      value: state.form.minValue
    })
  }
  warningRuleItem.warningRules = warningRuleArray
  param.ruleJson = JSON.stringify(warningRuleItem);
  if (state.form.id) {
    subHandle(addWarningSettingAPI, param, '编辑成功');
  } else {
    subHandle(addWarningSettingAPI, param, '新建成功');
  }

  function subHandle(req, form, title) {
    req(form).then((res) => {
      if (res.success) {
        ElMessage.success(title);
        dialogRef.value.close();
        emit('onClose');
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  }
};

onMounted(() => {
  loadWarningItemOptions()
})

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.tag {
  margin: 5px 5px 5px 0;
}
</style>
