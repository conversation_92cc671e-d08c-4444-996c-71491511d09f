<template>
  <div class="page">
    <el-tabs v-model="state.activeTag" closable @tab-remove="removeTab" @tab-change="changeTab" type="border-card"
      class="pageTabs">
      <template v-for="(item, index) in state.tabList" :key="item.name">
        <el-tab-pane :name="item.id">
          <template #label>
            <span @dblclick.native="editHandle(item)"  @contextmenu.prevent.native="openMenu($event)">{{ item.name }}</span>
          </template>
        </el-tab-pane>
      </template>
    </el-tabs>
    <el-icon class="commonIcon" :size="20" :style="{ transform: `translateY(${state.tabList.length ? '-10px' : '0'})` }"
      @click="copyHandle" title="复制组件路由">
      <DocumentCopy />
    </el-icon>
    <el-icon class="commonIcon" :size="20" :style="{ transform: `translateY(${state.tabList.length ? '-10px' : '0'})` }"
      @click="addHandle">
      <Plus />
    </el-icon>

    <dialog-common ref="dialog" :title="state.title" @submit="submit" @didMounted="didMounted" :formRef="ruleFormRef">
      <el-form ref="ruleFormRef" label-width="100px" :model="form" :rules="state.rules" label-suffix=":" @submit.prevent>
        <el-form-item label="页签名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入页签名称" />
        </el-form-item>
        <el-form-item label="位置" prop="spaceId">
          <el-cascader v-model="form.spaceId" :options="state.spaceOptions" :props="optionsProps" clearable
                       placeholder="请选择位置" />
        </el-form-item>
      </el-form>
    </dialog-common>

    <ul class='contextmenu' v-show="state.visible" :style="{left:(state.left + 20)+'px',top:(state.top - 40 ) +'px'}">
      <li @click="handleCreate">创建副本</li>
    </ul>
  </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'

import { events } from '@/utils/bus.js'

import { topoTabGetAPI, topoTabSaveAPI, topoTabDeleteAPI } from '@/api/settingSystem/topoCenter.js'
import { treeAPI } from '@/api/iotManagement/space.js'

import { webtopoStore } from '@/store/modules/webtopo.js'

import { changeStyleWithScale } from '@/utils/webtopo/math.js'


// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'id',
  checkStrictly: false,
  emitPath: false,
  expandTrigger: '',
};

const webtopo = webtopoStore()
let { canvasStyle, componentData } = storeToRefs(webtopo)

const route = useRoute()

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({})

const state = reactive({
  activeTag: '',
  title: '',
  tabList: [],
  spaceOptions: [],
  rules: {
    name: [{ required: true, message: '请输入页签名称', trigger: 'blur' },],
  },
  resetCanvasData:{
    width: 1200,
    height: 740,
    scale: 100,
    opacity: 1,
    backgroundColor: '',
    backgroundImage: ''
  },
  visible: false,
  top: 0,
  left: 0,
})

onMounted(() => {
  getSpaceTree()
  events.on('saveHandle', () => {
    saveHandle()
  })
  document.onclick = () => {
    state.visible = false
  }
})

onBeforeUnmount(() => {
  events.off('saveHandle')
})

// 设备位置
const getSpaceTree = () => {
  treeAPI({ deep: 3 }).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
};

// 获取页签
const getList = () => {
  topoTabGetAPI({ configurationId: route.query.configurationId }).then(res => {
    state.tabList = res.data

    if(state.tabList && state.tabList.length){
      !state.activeTag && volutionHandle(state.tabList[0] || {})
    }else {
      addHandle()
    }
  })
}

// 赋值操作
const volutionHandle = (current) => {
  state.activeTag = current.id
  let content = current.content && JSON.parse(current.content)
  if (content instanceof Object) {
    canvasStyle.value = content.canvasStyle;
    componentData.value = content.componentData
    preReset(JSON.parse(JSON.stringify(content.canvasStyle)))
  } else {
    resetDraw()
  }
}

// 画布居中
const preReset = (canvas) => {
  if (canvas instanceof Object) {
    const exhibits = document.querySelector('.drawMain').getBoundingClientRect()
    canvas.width = changeStyleWithScale(canvas.width, canvas.scale)
    canvas.height = changeStyleWithScale(canvas.height, canvas.scale)

    let x,y
    if (canvas.width < exhibits.width) {
      x = (exhibits.width - canvas.width) / 2
    } else {
      x = -(canvas.width - exhibits.width) / 2
    }

    if (canvas.height < exhibits.height) {
      y = (exhibits.height - canvas.height) / 2
    } else {
      y = -(canvas.height - exhibits.height) / 2
    }
    events.emit('canvasInit', {x, y})
  }
}

// 复制组件路径
const copyHandle = () => {
  const el = document.createElement('input')
  el.setAttribute('value', `buildingControl/configurationExhibits1/${state.activeTag}`)
  document.body.appendChild(el)
  el.select()
  document.execCommand('copy')
  document.body.removeChild(el)
  ElMessage.success('复制成功')
}

// 弹出框样式
const didMounted = () => {
  getList()
}

// 新建页签
const addHandle = () => {
  state.title = '新建页签'
  form.id = ''
  form.content = JSON.stringify({
    canvasStyle: state.resetCanvasData,
    componentData: []
  })
  dialog.value.open()
}

// 编辑页签
const editHandle = (info) => {
  state.title = '编辑页签'
  dialog.value.open()
  nextTick(() => {
    Object.assign(form, info)
    form.content = JSON.stringify({
      canvasStyle: canvasStyle.value,
      componentData: componentData.value
    })
  })
}

// 提交页签表单
const submit = () => {
  let data = {
    configurationId: route.query.configurationId,
    ...form,
  }
  topoTabSaveAPI(data).then(res => {
    if (res.success) {
      if (form.id) {
        ElMessage.success('编辑成功')
      } else {
        ElMessage.success('添加成功')
      }
      dialog.value.close()
      getList()
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

// 点击页签
const changeTab = (TabPaneName) => {
  let currentInfo = state.tabList.find(item => item.id == TabPaneName) || {}
  volutionHandle(currentInfo)
}

// 移除页签
const removeTab = (targetName) => {
  ElMessageBox.confirm(
    '是否删除当前页签?',
    '提醒',
    {
      type: "warning"
    }
  ).then(() => {
    topoTabDeleteAPI({ id: targetName }).then(res => {
      if (res.success) {
        if (targetName == state.activeTag) {
          state.activeTag = ''
          resetDraw()
        }
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 保存
const saveHandle = () => {
  let data = {
    id: state.activeTag,
    configurationId: route.query.configurationId,
    content: JSON.stringify({
      canvasStyle: canvasStyle.value,
      componentData: componentData.value
    })
  }
  topoTabSaveAPI(data).then(res => {
    if (res.success) {
      ElMessage.success('保存成功')
      getList()
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

// 清空画布
const resetDraw = () => {
  canvasStyle.value = state.resetCanvasData
  componentData.value = []
  preReset(JSON.parse(JSON.stringify(canvasStyle.value)))
}

// 右键
const openMenu = (e) => {
  state.visible = true
  state.left = e.clientX
  state.top = e.clientY
}

// 创建副本
const handleCreate = () => {
  state.title = '新建页签'
  form.id = ''
  form.content = JSON.stringify({
    canvasStyle: canvasStyle.value,
    componentData: componentData.value
  })
  dialog.value.open()
}

watch(() => state.tabList, (newVal, oldVal) => {
  if (newVal.length > oldVal.length && oldVal.length) {
    state.activeTag = newVal[newVal.length - 1].id
    changeTab(state.activeTag)
  }
})
</script>

<style lang='less' scoped>
.page {
  .pageTabs {
    width: calc(100% - 80px);
    display: inline-block;

    :deep(.is-active) {
      color: #E5723A !important;
    }

    :deep(.el-tabs__item):hover {
      color: #E5723A !important;
    }

    :deep(.el-tabs__header) {
      border-bottom: none;
    }

    :deep(.el-tabs__nav-wrap) {
      margin-bottom: 0;
    }

    :deep(.el-tabs__content) {
      display: none;
    }
  }

  .commonIcon {
    width: 40px;
    height: 40px;
    background: #f5f7fa;
    border-top: 1px solid #dcdfe6;
    cursor: pointer;
  }

  .commonIcon:nth-child(2) {
    border-right: 1px solid #dcdfe6;
  }
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 2;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
}
</style>
