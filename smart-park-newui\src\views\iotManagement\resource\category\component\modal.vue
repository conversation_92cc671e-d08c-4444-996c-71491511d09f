<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="100px" :model="form" :rules="state.rules" label-suffix=":">
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入分类名称" />
      </el-form-item>
      <el-form-item label="上级名称" prop="parentId">
        <el-tree-select v-model="form.parentId" :data="tableData" check-strictly :render-after-expand="false"
          node-key="id" :props="treeProps" placeholder="请选择上级名称" />
      </el-form-item>
      <el-form-item label="子系统分类" prop="subType">
        <el-select v-model="form.subType"  placeholder="请选择子系统分类">
          <el-option v-for="(value,key) in state.subTypeOptions" :key="key" :label="key" :value="key" />
        </el-select>
      </el-form-item>
      <el-form-item label="分类图标" prop="iconPath">
        <el-upload class="avatar-uploader" :action="state.action" :headers="state.headers" :show-file-list="false"
          :on-success="handleIconSuccess" :before-upload="beforeIconUpload">
          <img v-if="form.iconPath" :src="imgTransfer(form.iconPath)" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon">
            <Plus />
          </el-icon>
        </el-upload>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { ElMessage } from 'element-plus'

import { equipmentAddAPI, equipmentEidtAPI } from '@/api/iotManagement/category.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  tableData: {
    type: Array,
    default() {
      return []
    }
  }
})

let { tableData, title } = toRefs(props)

const emit = defineEmits(['submit'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({})

const state = reactive({
  rules: {
    name: [{ required: true, message: '请输入分类名称', trigger: 'blur' },]
  },
  action: import.meta.env.VITE_BASE_URL + '/core/file/upload',
  headers: {
    Authorization: localStorage.getItem('Authorization')
  },
  subTypeOptions: {
    'BA': '楼控',
    'ENTRANCE_GUARD': '门禁',
    'FIRE_FIGHTING': '消防',
    'INFORMATION': '信息发布',
    'LADDER_CONTROL': '梯控',
    'LIGHTING': '照明',
    'MONITOR': '监控',
    'PARKING': '停车'
  }
})

const treeProps = computed(() => {
  return {
    label: 'name'
  }
})

const open = () => {
  dialog.value.open()
}

const imgTransfer = (name) => {
  return import.meta.env.VITE_BASE_URL + name
}

// 限制上传大小
const beforeIconUpload = (rawFile) => {
  if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图标大小请控制在 2MB以内!')
    return false
  }
}

// 上传图标
const handleIconSuccess = (response) => {
  form.iconPath = response.data.filePath
}

// 提交菜单
const submit = () => {
  if (form.id) {
    subHandle(equipmentEidtAPI, '编辑成功')
  } else {
    subHandle(equipmentAddAPI, '添加成功')
  }

  function subHandle(req, title) {
    req(form).then(res => {
      if (res.success) {
        ElMessage.success(title)
        dialog.value.close()
        emit('submit')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  }
}


defineExpose({
  form,
  open
})
</script>

<style lang="less" scoped>
.avatar-uploader .avatar {
  width: 58px;
  height: 58px;
  display: block;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 58px;
  height: 58px;
  text-align: center;
}
</style>
