<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="queryFormRef" :model="state.queryForm">
        <el-form-item prop="inspectionLineName">
          <el-input v-model="state.queryForm.inspectionLineName" placeholder="巡更路线名称"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="queryList">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #operate>
      <el-button type="primary" :icon="Plus" @click="onAdd">新建巡更路线</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" tooltip-effect="light">
        <template v-for="item of state.tableHeader">
          <el-table-column :label="item.label" :prop="item.prop" :width="item.width"
                           :show-overflow-tooltip="true">
          </el-table-column>
        </template>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click.prevent="onEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.prevent="onDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />

      <!--  新增、编辑 dialog组件  -->
      <line-dialog ref="lineDialogRef" @onClose="queryList"/>
    </template>
  </page-common>
</template>

<script setup>


import {Plus, Refresh, Search} from "@element-plus/icons-vue";
import {deleteInspectionLineAPI, listInspectionLineAPI} from "@/api/comprehensiveSecurity/inspectionLine.js";
import LineDialog from "@/views/comprehensiveSecurity/inspection/inspectionLine/component/lineDialog.vue";
import {ElMessage, ElMessageBox} from "element-plus";


// 子组件引用
let lineDialogRef =ref()

// 表单对象
let queryFormRef = ref()

const state = reactive({
  tableHeight: 100,
  queryForm: {
    inspectionLineName: null
  },
  tableData: [],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
  tableHeader: [
    { label: '路线名称', prop: 'name', width: '350' },
    { label: '巡更点数量', prop: 'count', width: '200' },
    { label: '巡更点位', prop: 'pointNames', width: '' },
    { label: '巡更备注', prop: 'remark', width: '400' }
  ]
})


// 查询列表数据
const queryList = () => {
  let query = {
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }
  if (state.queryForm.inspectionLineName) {
    query.inspectionLineName = state.queryForm.inspectionLineName
  }
  listInspectionLineAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pageParam.total = res.data.totalCount
  })
}

// 重置
const onReset = () => {
  queryFormRef.value.resetFields()
  state.pageParam.pageNum = 1
  state.pageParam.pageSize = 10
  queryList()
}


const sizeChange = (val) => {
  state.pageParam.pageSize = val
  queryList()
}

const currentChange = (val) => {
  state.pageParam.pageNum = val
  queryList()
}


const onAdd = () => {
  lineDialogRef.value.open('新建巡更路线')
}

const onEdit = (val) => {
  lineDialogRef.value.open('编辑巡更路线', val)
}

const onDelete = (val) => {
  ElMessageBox.confirm(
    '是否删除当前巡更路线?',
    '提醒',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    let params = {
      id: val.id
    }
    deleteInspectionLineAPI(params).then(res => {
      if (res.success) {
        ElMessage.success('删除成功！')
      } else {
        ElMessage.error('删除失败！' + res.errorMessage)
      }
      queryList()
    });
  }).catch(() => {
    ElMessage({
      type: 'info',
      message: '取消删除！',
    })
  })
}


onMounted(() => {
  queryList()
})
</script>


<style scoped lang="less">
</style>
