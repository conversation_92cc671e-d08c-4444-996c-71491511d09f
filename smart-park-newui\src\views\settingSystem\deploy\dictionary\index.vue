<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item  prop="dictName">
          <el-input v-model="formInline.dictName" placeholder="字典名称"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" :icon="Plus" @click="addHandle">新建字典</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" />
        <el-table-column align="center" label="操作" width="260">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
            <el-button link type="primary" icon="Tickets" @click="viewHandle(scope.row)">查看字典项</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <modal-page ref="modal" :title="state.title" @submit="getList"></modal-page>
      <drawer-page ref="drawer" :drawer="state.drawer" :dictCode="state.dictCode" @cancelClick="cancelClick"></drawer-page>
    </template>
  </page-common>
</template>

<script setup>
import drawerPage from './component/drawerPage.vue'
import modalPage from './component/modalPage.vue'

import {
  Delete, Plus, Search, Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { dictionListAPI, dictionDelAPI} from '@/api/settingSystem/dictionary.js'
import { calcPageNo } from '@/utils/util.js'
import { nextTick } from 'vue'

let formInlineRef = ref()
let modal = ref()
let drawer = ref()

const formInline = reactive({})

const state = reactive({
  title: '',
  drawer: false,
  dictCode: '', // 当前字典编码
  tableHeight: 100,
  tableData: [],
  tableItemData: [],
  tableHeader: [
    {
      prop: 'dictCode',
      label: '字典编码'
    },
    {
      prop: 'dictName',
      label: '字典名称'
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()
})

// 获取字典
const getList = () => {
  let query = {
    globalDictDtoFilter: formInline,
    pageParam: state.pagetion
  }
  dictionListAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 删除字典
const deleteHandle = (info) => {
  ElMessageBox.confirm(
    '是否删除当前字典?',
    '提醒',
    {
      type: "warning"
    }
  ).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize)
    dictionDelAPI({ dictId: info.dictId }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}

// 新建字典
const addHandle = () => {
  modal.value.form.dictId = ''
  state.title = '新建字典'
  modal.value.open()
}

// 编辑字典
const editHandle = (info) => {
  state.title = '编辑字典'
  modal.value.open()
  nextTick(() => {
    Object.assign(modal.value.form, { ...info })
  })
}

// 查看当前字典的字典项
const viewHandle = (info) => {
  state.dictCode = info.dictCode
  nextTick(() => {
    drawer.value.getList()
    state.drawer = true
  })
}

// 关闭抽屉
const cancelClick = (info) => {
  state.drawer = false
}
</script>

<style lang='less' scoped></style>
