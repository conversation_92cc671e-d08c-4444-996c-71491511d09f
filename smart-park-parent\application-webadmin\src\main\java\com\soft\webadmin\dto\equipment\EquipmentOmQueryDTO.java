package com.soft.webadmin.dto.equipment;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * EquipmentDTO对象
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@ApiModel("EquipmentQueryDTO对象")
@Data
public class EquipmentOmQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "设备编号或名称")
    private String equipmentWord;

    @ApiModelProperty(value = "位置Id")
    private Long spaceId;

    @ApiModelProperty(value = "设备类型")
    private Long equipmentTypeId;

    @ApiModelProperty(value = "运维状态：1正常、5报废、11维保中、12维修中")
    private Integer maintenanceStatus;

    @ApiModelProperty(value = "IOT物联设备，OM运维设备")
    private String category;

    @ApiModelProperty(value = "排除的设备状态", hidden = true)
    private Integer excludeStatus;

}
