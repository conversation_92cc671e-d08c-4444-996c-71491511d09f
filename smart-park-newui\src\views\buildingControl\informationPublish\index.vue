<template>
    <tree-page-common v-model="state.tableHeight" :leftBool="false" :operateBool="false">
        <template #query>
            <el-form :inline="true" ref="formInlineRef" :model="formInline" label-suffix=":">
                <el-form-item prop="equipmentName">
                    <el-input v-model="formInline.equipmentName" placeholder="设备名称/编号" />
                </el-form-item>
                <el-form-item prop="spaceIdList">
                    <el-cascader v-model="formInline.spaceIdList" :options="state.options3" :props="{
                        children: 'children',
                        value: 'id',
                        label: 'name',
                        checkStrictly: true,
                    }" clearable placeholder="安装位置" />
                </el-form-item>
                <el-form-item prop="runStatus">
                    <el-select v-model="formInline.runStatus" placeholder="在线状态">
                        <el-option v-for="item in state.options1" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
                    <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template #table>
            <el-table :data="state.tableData" :height="state.tableHeight">
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                    :label="item.label" :align="item.align" :formatter="item.formatter" />
                <el-table-column align="center" label="预览">
                    <template #default="scope">
                        <el-button link type="primary" icon="Edit" @click="preview(scope.row)">预览</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
                @size-change="sizeChange" @current-change="currentChange" />
            <el-dialog v-model="state.dialogVisible" title="预览" width="70%" :before-close="handleClose">
                <el-image style="width: 1168px; height: 500px" :src="state.url" v-if="state.show" />
            </el-dialog>
        </template>
    </tree-page-common>
</template>
  
<script setup>
import { previewURLsAPI } from '@/api/iotManagement/equipManage.js';
import { Delete, Plus, Search, Refresh } from '@element-plus/icons-vue'
import { deviceList } from "@/api/iotManagement/device.js";
import { ElMessage, ElLoading, dayjs, ElTag } from "element-plus";
import { treeAPI } from "@/api/iotManagement/space.js";

let formInlineRef = ref()
const formInline = reactive({
    spaceIdList: []
})

const state = reactive({
    url: '',
    show: false,
    options1: [{
        value: '1',
        label: '在线',
    }, {
        value: '0',
        label: '离线',
    }],
    options2: [{
        value: '1',
        label: '正常',
    }, {
        value: '0',
        label: '告警',
    }],
    dialogDate: {},
    attribute: {},
    dialogVisible: false,
    deviceId: '',
    drawer: false,
    tableHeight: 100,
    tableData: [],
    tableHeader: [
        {
            prop: 'equipmentNo',
            label: '设备编号'
        },
        {
            prop: 'equipmentName',
            label: '设备名称'
        },
        {
            prop: 'subType',
            label: '设备类别'
        },
        {
            prop: 'equipmentType',
            label: '设备型号'
        },
        {
            prop: 'spaceFullName',
            label: '安装位置'
        },
        {
            prop: 'runStatus',
            label: '运行状态',
            formatter: (row, column, cellValue) => {
                if (cellValue == 1) {
                    return h(ElTag, { type: "success" }, { default: () => "在线" })
                } else {
                    return h(ElTag, { type: "danger" }, { default: () => "离线" })
                }
            }
        },
    ],
    pagetion: {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
})

onMounted(() => {
    getList([])
    getTree()
})

const getTree = () => {
    let query = {
        deep: 4
    }
    treeAPI(query).then(res => {
        state.options3 = res.data
    })
}

const handleClose = () => {
    state.dialogVisible = false;
    state.dialogDate = {};
}


//预览
const preview = (info) => {
    state.show = false
    const loading = ElLoading.service({
        lock: true,
        text: '加载中...',
    })
    previewURLsAPI({ equipmentId: info.equipmentId })
        .then(res => {

            loading.close()
            if (res.success) {
                state.dialogVisible = true;
                state.show = true;
                state.url = res.data
            } else {
                ElMessage.error(res.errorMessage);
            }
        }).catch((e) => {
            ElMessage.error('系统异常!');
            loading.close()
        });
}

//分页
const getList = (array) => {
    console.log(formInline.spaceIdList);
    let query = {
        spaceIdList: formInline.spaceIdList.join(","),
        equipmentWord: formInline.equipmentName,
        runStatus: formInline.runStatus,
        warningStatus: formInline.warningStatus,
        subType: 'INFORMATION',
        pageNum: state.pagetion.pageNum,
        pageSize: state.pagetion.pageSize
    }
    deviceList(query).then(async res => {
        for (let e of res.data.dataList) {
            var equipmentGroupName = ""
            var relationEquipmentName = ''
            if (e.equipmentRelationList) {
                e.equipmentRelationList.forEach(i => {
                    relationEquipmentName += i.relatedEquipmentName + ","
                })
                relationEquipmentName = relationEquipmentName.substring(0, relationEquipmentName.length - 1)
                e.relationEquipmentName = relationEquipmentName
            }
            e.equipmentGroupList.forEach(i => {
                equipmentGroupName += i.itemName + ","
            })
            equipmentGroupName = equipmentGroupName.substring(0, equipmentGroupName.length - 1)
            e.equipmentGroupName = equipmentGroupName
        }
        state.tableData = res.data.dataList
        state.pagetion.total = res.data.totalCount * 1
    })
}

//查询方法
const onSubmit = () => {
    state.pagetion = {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
    getList()
}

//重置方法
const onReset = () => {
    formInlineRef.value.resetFields()
    onSubmit()
}

//分页方法
const currentChange = (pageNum) => {
    state.pagetion.pageNum = pageNum
    getList()
}

//分页方法
const sizeChange = (pageSize) => {
    state.pagetion.pageSize = pageSize
    getList()
}

defineExpose({
    getList
})
</script>
  
<style lang='less' scoped></style>