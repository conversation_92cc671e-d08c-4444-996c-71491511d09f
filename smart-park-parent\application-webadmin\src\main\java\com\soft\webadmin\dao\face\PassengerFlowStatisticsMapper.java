package com.soft.webadmin.dao.face;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.model.face.PassengerFlowStatistics;
import com.soft.webadmin.vo.daping.xswt.staffMonitor.StaffFlow24HVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface PassengerFlowStatisticsMapper extends BaseMapper<PassengerFlowStatistics> {
    List<PassengerFlowStatistics> listGroupByDeviceIp(@Param("start") LocalDateTime start,
                                                      @Param("end") LocalDateTime end);


    PassengerFlowStatistics sum(@Param("start") LocalDateTime start,
                                                @Param("end") LocalDateTime end);

    List<StaffFlow24HVO> staffFlow24H(@Param("startDate") String startDate,
                                      @Param("endDate") String endDate);

}
