<template>
  <div class="login-bg"></div>
  <div class="login">
    <div class="login-form" v-show="!state.isLoading">
      <div class="logo">
        <img src="/assets/img/title.svg" class="logo-img">
        <span class="title">智慧园区管理平台</span>
      </div>
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="state.rules" class="demo-ruleForm" label-width="0"
               status-icon @submit.native.prevent="onSubmit">
        <el-form-item prop="loginName">
          <el-input v-model="ruleForm.loginName" :prefix-icon="User" placeholder=""/>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="ruleForm.password" :prefix-icon="Lock" autocomplete="new-password" placeholder=""
                    show-password type="password">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" native-type="submit">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="container" v-show="state.isLoading">
      <div class="loader">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
      <p>欢迎</p>
    </div>
    <Vcode :show="state.isShow" :canvasWidth="400" :canvasHeight="250" :puzzleScale="1" :sliderSize="40" :range="10"
           successText="验证成功！" failText="验证失败，请重试！" sliderText="拖动滑块验证" @success="onSuccess" @close="onClose" />
  </div>
</template>

<script setup>
import {ElMessage} from 'element-plus'
import {Lock, User} from '@element-plus/icons-vue'

import Vcode from "vue3-puzzle-vcode";

import {encrypt} from '@/utils/util.js'
import {loginAPI, loginInfoAPI} from '@/api/login'

const router = useRouter()

const ruleFormRef = ref()
const ruleForm = reactive({})
const state = reactive({
  isShow: false,
  isLoading: false,
  rules: {
    loginName: [
      {
        required: true,
        message: '请输入用户名',
        trigger: 'blur',
      }
    ],
    password: [
      {
        required: true,
        message: '请输入密码',
        trigger: 'blur',
      }
    ],
  }
})


const onSubmit = async () => {
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      state.isShow = true
    } else {
      console.log('error submit!', fields)
    }
  })
}

const onSuccess = () => {
  loginAPI({
    loginName: ruleForm.loginName,
    password: encrypt(ruleForm.password)
  }).then(async res => {
    if(res.success){
      localStorage.setItem('Authorization', res.data)
      let info = await loginInfoAPI()
      localStorage.setItem('userInfo',JSON.stringify(info.data))

      onClose()
      state.isLoading = true
      setTimeout(() => {
        router.push('/')
      },2000)
    }else{
      ElMessage.error(res.errorMessage)
    }
  }).catch(err => {
    console.log(err)
  }).finally(() => {
    state.isShow = false
  })
}

const onClose = () => {
  state.isShow = false
}
</script>

<style lang='less' scoped>
.login-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-size: 100% 120%;
  background-image: url("@/assets/window/background.jpg");
}

.login {
  width: 100vw;
  height: 100vh;
  display: flex;
  overflow: auto;
  background: #2221365b;
  backdrop-filter: blur(15px);
  position: relative;
  display: flex;
}

.login-form{
  margin: auto;
  text-align: center;
  transform: translateY(-100px);
  display: flex;
  flex-direction: column;
  align-items: center;
  .logo{
    margin-bottom: 50px;
    &-img{
      width: 100px;
      height: 100px;
      vertical-align: bottom;
      margin-right: 20px;
    }

  }

  .title {
    font-size: 30px;
    color: #fff;
    white-space: nowrap;
    transform: translateY(-10px);
    display: inline-block;
  }

  .el-input{
    width: 300px;
    height: 36px;
  }

  .el-button--primary {
    height: 34px;
    width: 300px;
    box-shadow: 0px 0px 16px 0px rgba(59, 133, 246, 0.34);
  }
}

.container {
  width: 400px;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: #fff;
  font-size: 20px;
  margin: auto;
}

.loader {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 20px;
}
.loader span {
  position: absolute;
  width: 100px;
  height: 100px;
  animation: animate 3.5s linear infinite;
}
.loader span::before {
  position: absolute;
  content: "";
  background-color: #FFF;
  width: 10px;
  height: 10px;
  bottom: 0;
  left: calc(50% - 5px);
  border-radius: 50%;
}
.loader span:nth-child(1) {
  animation-delay: 0.1s;
}
.loader span:nth-child(2) {
  animation-delay: 0.2s;
}
.loader span:nth-child(3) {
  animation-delay: 0.3s;
}
.loader span:nth-child(4) {
  animation-delay: 0.4s;
}
.loader span:nth-child(5) {
  animation-delay: 0.5s;
}
@keyframes animate {
  74%{
    transform: rotate(600deg);
  }
  79% {
    transform: rotate(720deg);
    opacity: 1;
  }
  80% {
    transform: rotate(720deg);
    opacity: 0;
  }
  100% {
    transform: rotate(810deg);
    opacity: 0;
  }
}

</style>
