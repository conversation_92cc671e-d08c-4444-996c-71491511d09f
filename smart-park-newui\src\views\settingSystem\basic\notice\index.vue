<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="title">
          <el-input v-model="formInline.title" placeholder="标题" />
        </el-form-item>
        <el-form-item prop="type">
          <el-select v-model="formInline.type" placeholder="类型">
            <el-option label="公告" value="公告" />
            <el-option label="通知" value="通知" />
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <el-select v-model="formInline.status" placeholder="状态">
            <el-option label="已发布" value="1" />
            <el-option label="未发布" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新建通知公告</el-button>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column label="序号" align="center" type="index" width="55" />
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" />
        <el-table-column label="有效期">
          <template #default="scope">
            <span v-if="scope.row.beginTime && scope.row.endTime">{{ scope.row.beginTime }} 至 {{ scope.row.endTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status == 1 ? 'success' : 'warning'">
              {{ scope.row.status == 1 ? '已发布' : '未发布' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" align="center" width="320">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
            <el-button
              link
              icon="Select"
              type="success"
              @click="updateStatusHandle(scope.row, 1, '发布')"
              v-if="scope.row.status === 0"
              >发布</el-button
            >
            <el-button
              link
              icon="RefreshLeft"
              type="warning"
              @click="updateStatusHandle(scope.row, 0, '撤销')"
              v-if="scope.row.status === 1"
              >撤销</el-button
            >
            <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
            <el-button link icon="Document" type="success" @click="viewHandle(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum"
        :page-size="state.pageParam.pageSize"
        :total="state.pageParam.total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
      <!-- 新建、编辑 -->
      <modal-page ref="modal" :title="state.title" @submit="getList"></modal-page>
      <!-- 查看 -->
      <drawer-page ref="drawer" :drawer="state.drawer" @cancelClick="state.drawer = false" />
    </template>
  </page-common>
</template>

<script setup>
import { getNoticePageAPI, updateStatusNoticeAPI, deleteNoticeAPI } from '@/api/settingSystem/notice.js';
import modalPage from './component/modalPage.vue';
import drawerPage from './component/drawerPage.vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const modal = ref();
const drawer = ref();
const formInlineRef = ref();
const formInline = reactive({});
const state = reactive({
  tableData: [],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  title: '',
  drawer: false,
});

onMounted(() => {
  getList();
});

const getList = () => {
  let params = {
    projectId: localStorage.getItem('projectId'),
    source: 'pc', //查询来源
    title: formInline.title,
    type: formInline.type,
    status: formInline.status,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };
  getNoticePageAPI(params).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

/** 创建 */
const addHandle = () => {
  state.title = '新建通知公告';
  modal.value.form.id = undefined;
  modal.value.form.type = '公告';
  modal.value.form.status = 1;
  modal.value.form.daterange = [];
  modal.value.form.userIdList = [];
  modal.value.open();
};

/** 编辑 */
const editHandle = (row) => {
  state.title = '编辑通知公告';
  modal.value.open();
  nextTick(() => {
    Object.assign(modal.value.form, row);
    modal.value.form.daterange = [row.beginTime, row.endTime];
    if (row.type === '通知') {
      let userIdList = row.userIds.split(',');
      modal.value.form.userIdList = userIdList;
    }
  });
};

/** 发布or撤销 */
const updateStatusHandle = (row, status, operate) => {
  ElMessageBox.confirm('是否' + operate + '该' + row.type + '?', '提醒', {
    type: 'warning',
  }).then(() => {
    const params = {
      id: row.id,
      status: status,
    };
    updateStatusNoticeAPI(params).then((res) => {
      if (res.success) {
        ElMessage.success(operate + '成功');
        getList();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};

/** 删除 */
const deleteHandle = (row) => {
  ElMessageBox.confirm('是否删除当前' + row.type + '?', '提醒', {
    type: 'warning',
  }).then(() => {
    const params = {
      id: row.id,
      type: row.type,
    };
    deleteNoticeAPI(params).then((res) => {
      if (res.success) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};

/** 查看 */
const viewHandle = (row) => {
  nextTick(() => {
    drawer.value.form = JSON.parse(JSON.stringify(row));
    state.drawer = true;
  });
};
</script>
