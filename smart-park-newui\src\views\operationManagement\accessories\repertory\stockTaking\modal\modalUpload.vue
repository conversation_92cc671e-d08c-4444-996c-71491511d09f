<template>
  <dialog-common ref="dialog" title="导入数据"  :width="900" @onClose="onClose" :showButton="false">
    <el-card class="box-card">
      <el-steps :active="state.active" align-center>
        <el-step title="上传文件"/>
        <el-step title="导入数据"/>
        <el-step title="导入完成"/>
      </el-steps>
      <div class="conent">
        <div v-show="state.active === 0" v-loading="state.loading" element-loading-text="正在导入...">
          <el-card style="margin: 15px 30px;">
            <h4>填写导入盘点的信息</h4>
            <span
                class="introduce">请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除，单次导入的数据不超过1000条。</span>
            <div>
              <el-select v-model="state.storehouseId" placeholder="请选择仓库" clearable filterable style="margin: 10px 0;width: 197px ">
                <el-option v-for="item in props.storehouseOptions" :label="item.storehouseName" :value="item.id"  />
              </el-select>
              <div>
                <el-button type="primary" link @click="downTem">下载模板</el-button>
              </div>
            </div>
          </el-card>
          <el-card style="margin: 15px 30px;">
            <h4>上传填好的盘点信息表</h4>
            <span class="introduce">文件后缀名必须为xls或xlsx(即Excel格式),文件大小不得大于10M</span>
            <el-upload :limit="1" action="#" accept=".xls,.xlsx" v-model:file-list="state.fileList" :auto-upload="false"
                       ref="upload">
              <el-button type="primary" icon="Upload">上传文件</el-button>
            </el-upload>
          </el-card>
        </div>
        <div v-show="state.active === 1">
          <el-card style="margin: 15px 30px;">
            <h4>正常数量条数</h4>
            <div>
              <el-button type="primary" link>{{ uploadData.success }}</el-button>
            </div>
          </el-card>
          <el-card style="margin: 15px 30px;">
            <h4>异常数量条数</h4>
            <div>
              <el-button type="primary" link>{{ uploadData.error }}
              </el-button>
            </div>
          </el-card>
        </div>
        <div v-show="state.active === 2" class="successPage">
          <el-icon size="50" color="#67c23a">
            <SuccessFilled/>
          </el-icon>
          <span class="introduce" style="margin: 10px 0 20px">成功导入数据: {{ uploadData.success }} 条</span>
        </div>
      </div>
      <div style="text-align: center;" v-show="state.active === 0">
        <el-button type="primary" size="large" icon="Right" @click="stepHandleFirst">下一步</el-button>
      </div>
      <div style="text-align: center;" v-show="state.active === 1">
        <el-button type="primary" size="large" @click="state.active += 1">下一步</el-button>
        <el-button size="large" icon="Back" @click="state.active -= 1">返回重新上传</el-button>
      </div>
      <div style="text-align: center;" v-show="state.active === 2">
        <el-button type="primary" size="large" icon="Check" @click="handleSuccess">完成</el-button>
      </div>
    </el-card>
  </dialog-common>
</template>


<script setup>
import {ElMessage} from 'element-plus'
import {exportFile} from '@/utils/down.js'

import { stockUploadAPI } from "@/api/operationManagement/stockTaking.js";

const props = defineProps({
  storehouseOptions: {
    type:Array,
    default: []
  }
})

const emit = defineEmits(['submit'])

// 导入的数据
const uploadData = ref({})

// dialog组件
const dialog = ref();

const state = reactive({
  active: 0,
  loading: false,
  fileList: [],
  storehouseId: '',
})

// 上传成功
const handleSuccess = () => {
  dialog.value.close()
}

const onClose = () => {
  state.active = 0
  state.fileList = []
  state.storehouseId = ''
  emit('submit')
}

// 下载模板
const downTem = () => {
  if(!state.storehouseId){
    return ElMessage.error('请选择对应仓库')
  }
  let name = props.storehouseOptions.find(item => item.id == state.storehouseId)?.storehouseName
  console.log(name)
  exportFile('/sparePart/stocktaking/exportTemplate', {businessType:'OPERATIONS',storehouseId: state.storehouseId}, `${name}盘点模版.xlsx`)
}

const stepHandleFirst = () => {
  // 步骤一
  if (!state.fileList.length) {
    return ElMessage.error('请上传文件')
  }
  const formData = new FormData()
  formData.append('file', state.fileList[0].raw)
  state.loading = true

  // 上传文件
  stockUploadAPI(formData).then(res => {
    if (res.success) {
      console.log(res.data)
      state.active += 1
      uploadData.value = res.data
    } else {
      ElMessage.error(res.errorMessage)
    }
  }).finally(() => {
    state.loading = false
  })
}

const open = () => {
  dialog.value.open();
};

defineExpose({
  open,
});

</script>

<style scoped lang="less">
.box-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  margin: 10px;

  :deep(.el-card__header) {
    background-color: rgb(249 249 249);
  }

  :deep(.el-card__body) {
    flex: 1;
    overflow: hidden;
  }
}

.conent {
  height: calc(100% - 100px);
  overflow: auto;
}

.introduce {
  color: #7b7b7b;
  font-size: 14px;
}

.successPage {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
