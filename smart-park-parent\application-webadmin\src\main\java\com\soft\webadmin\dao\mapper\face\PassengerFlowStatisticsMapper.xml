<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.face.PassengerFlowStatisticsMapper">
    <resultMap type="com.soft.webadmin.model.face.PassengerFlowStatistics" id="PassengerFlowStatistics">
        <result property="id" column="id" />
        <result property="deviceIp" column="device_ip" />
        <result property="enterCount" column="enter_count" />
        <result property="leaveCount" column="leave_count" />
        <result property="byMode" column="by_mode" />
        <result property="startTime" column="start_time" />
        <result property="endTime" column="end_time" />
        <result property="createTime" column="create_time" />
    </resultMap>
    <select id="listGroupByDeviceIp"
            resultType="com.soft.webadmin.model.face.PassengerFlowStatistics">
        select * from (
        select * from passenger_flow_statistics
        <where>
            by_mode = 0
            <if test="start != null">
                and start_time >= #{start}
            </if>
            <if test="end != null">
                and end_time &lt;= #{end}
            </if>
        </where>
        GROUP BY id order by enter_count desc, leave_count desc
        ) as t group by t.device_ip
    </select>

    <select id="sum"
            resultType="com.soft.webadmin.model.face.PassengerFlowStatistics">
        select ifnull(sum(enter_count),0) as enterCount ,ifnull(SUM(leave_count),0) as leaveCount from passenger_flow_statistics
        <where>
            by_mode = 1
            <if test="start != null">
                and start_time >= #{start}
            </if>
            <if test="end != null">
                and end_time &lt;= #{end}
            </if>
        </where>
    </select>

    <select id="staffFlow24H" resultType="com.soft.webadmin.vo.daping.xswt.staffMonitor.StaffFlow24HVO">
        select hour(start_time) `hour`, sum(enter_count) enter_count, sum(leave_count) leave_count
        from passenger_flow_statistics
        <where>
            by_mode = 1
            <if test="startDate != null">
                and start_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and end_time &lt;= concat(#{endDate}, ' 23:59:59')
            </if>
        </where>
        group by hour
    </select>

</mapper>