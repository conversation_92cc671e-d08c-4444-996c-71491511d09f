import { request } from '@/utils/request';

// 责任区域
export const workManageAreaAPI = (query) => {
    return request('get', '/shifts/record/liabilityArea', query, 'F');
};

// 查询
export const workManagePageAPI = (query) => {
    return request('get', '/shifts/record/attendance', query, 'F');
};

// 添加人员
export const workManageAddAPI = (query) => {
    return request('get', '/shifts/record/attendanceAdd', query, 'F');
};

// 删除人员
export const workManageDeteleAPI = (query) => {
    return request('get', '/shifts/record/attendanceDel', query, 'F');
}

// 导入
export const  workManageUploadAPI = (data) => {
    return request('post', '/shifts/record/importExcel?businessType=OPERATIONS', data);
}
