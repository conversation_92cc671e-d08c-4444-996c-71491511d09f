<template>
  <dialog-common ref="dialog" title="定时任务" @submit="submit" :formRef="formRef" :width="700">
    <el-form ref="formRef" :model="form" label-width="100px" label-suffix=":">
      <div v-for="item in form.syncTaskParams">
        <el-checkbox v-model="item.check" :value="item.dataType" :key="item.dataTypeName">{{ item.dataTypeName
          }}</el-checkbox>
        <el-popover trigger="click" width="650px">
          <vue3CronPlus i18n="cn" @change="(val) => changeCron(val, item)" max-height="200px" />
          <template #reference>
            <span>
              <el-form-item label="Cron" prop="syncTaskParams" v-if="item.check">
                <el-input v-model="item.syncTaskCron" placeholder="请选择cron表达式"></el-input>
              </el-form-item>
            </span>
          </template>
        </el-popover>
      </div>
    </el-form>
  </dialog-common>
</template>
<script setup>

import { reactive, ref } from "vue";
import { vue3CronPlus } from 'vue3-cron-plus'
import 'vue3-cron-plus/dist/index.css' // 引入样式
import { getSubSystemConfigById, saveSyncTAPI } from "@/api/iotManagement/subsystem.js";
import { ElMessage } from "element-plus";
import DialogCommon from "@/components/basic/dialogCommon.vue";

// dialog 弹出框
const dialog = ref();

// 表单
const formRef = ref();

let form = reactive({
  id: Number(),
  syncTaskParams: []
})

/**
 * 弹窗 open事件
 */
const open = () => {
  nextTick(() => {
    getSubSystemConfigById(form.id).then(res => {
      Object.assign(form, res.data)
      let syncTaskParams = form.syncTaskParams;
      for (let syncTaskParam of syncTaskParams) {
        if (syncTaskParam.syncTaskCron) {
          syncTaskParam.check = true
        } else {
          syncTaskParam.check = false
        }
      }
      dialog.value.open()
    })
  })
}

const changeCron = (val, item) => {
  if (typeof val == 'string')
    item.syncTaskCron = val
  // if (typeof (val) !== 'string') return false
  // form.syncTaskCron = val
}

/**
 * 提交表单
 */
const submit = () => {
  saveSyncTAPI(form).then(res => {
    if (res.success) {
      ElMessage.success("保存成功")
      dialog.value.close()
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}



/**
 * 暴露属性，用来外部控制表单内容和弹窗状态
 */
defineExpose({
  open,
  form
})
</script>

<style scoped></style>
