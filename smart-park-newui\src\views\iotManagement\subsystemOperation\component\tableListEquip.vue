<template>
  <page-common v-model="state.tableHeight" :operateBool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="equipmentWord">
          <el-input v-model="formInline.equipmentWord" placeholder="设备名称/编号" />
        </el-form-item>
        <el-form-item prop="spacePath">
          <el-cascader v-model="formInline.spacePath" :options="state.spaceOptions" :props="optionsProps" clearable
            placeholder="安装位置" />
        </el-form-item>
        <el-form-item prop="runStatus">
          <el-select v-model="formInline.runStatus" placeholder="在线状态">
            <el-option v-for="(value, key) in state.runStatusOptions" :key="key" :label="value" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" />
        <el-table-column v-if="props.equipmentTypeId !== '100022'" align="center" label="操作" width="290">
          <template #default="scope">

            <el-button v-if="props.equipmentTypeId === '100010'" link type="primary" icon="Monitor"
              @click="onPreview(scope.row)">实时节目</el-button>

            <el-button v-else-if="props.equipmentTypeId === '100020'" link type="primary" icon="Document"
              @click="onPreview(scope.row)">查看</el-button>

            <el-button v-else link type="primary" icon="Edit" @click="control(scope.row)">控制</el-button>

            <el-button link type="primary" icon="Tickets" @click.prevent="onDetail(scope.row.equipmentId)">
              详情
            </el-button>
          </template>
        </el-table-column>
        <el-table-column v-else align="center" label="操作" width="290">
          <template #default="scope">
            <el-button link type="primary" icon="Tickets" @click.prevent="onDetail(scope.row.equipmentId)">
              详情
            </el-button>
          </template>
        </el-table-column>

      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />

      <!-- 设备详情 -->
      <!-- <device-drawer ref="drawer" :drawer="state.drawer" @cancelClick="state.drawer = false" /> -->

      <!-- 控制 -->
      <equip-control ref="equip" :isReFetchEquipmentAttributes="true" title="设备控制" />

      <!-- 视频预览 -->
      <video-modal title="视频预览" ref="videoRef"></video-modal>
    </template>
  </page-common>
</template>

<script setup>
import { ElTag, ElLink, ElMessage } from 'element-plus'

import { getEquipListAPI } from "@/api/iotManagement/equipManage.js";
import { treeAPI } from '@/api/iotManagement/space.js';
import { perviewURLs } from "@/api/iotManagement/realtime.js";

const emit = defineEmits(['showPage'])

const props = defineProps({
  equipmentTypeId: {
    type: String,
    default: ''
  }
})

// 级联选择配置
const optionsProps = {
  label: 'name',
  value: 'path',
  checkStrictly: true,
  emitPath: false,
  expandTrigger: 'hover',
};

// 类型
const typeOptions = {
  BA: 'BA',
  ENTRANCE_GUARD: '门禁',
  FIRE_FIGHTING: '消防',
  INFORMATION: '信息发布',
  LADDER_CONTROL: '梯控',
  LIGHTING: '照明',
  MONITOR: '监控',
  PARKING: '停车',
  ENERGY: '能耗',
  CANTEEN: '餐厨',
  INTRUSION_ALARM: '入侵报警',
  ONE_CARD: '一卡通'
}

const drawer = ref()
const formInlineRef = ref()
const equip = ref()
const formInline = reactive({})


const videoRef = ref()

const state = reactive({
  drawer: false,
  spaceOptions: [],
  runStatusOptions: {
    0: '离线',
    1: '在线'
  },
  equipmentStatusOptions: {
    0: '故障',
    1: '正常'
  },
  statusOptions: {
    0: 'danger',
    1: 'success'
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'equipmentCode',
      label: '设备编号'
    },
    {
      prop: 'equipmentName',
      label: '设备名称',
      // formatter: (row, column, cellValue) => {
      //   return h(ElLink, { type: 'primary', onClick: () => { handleView(row.equipmentId) } }, { default: () => cellValue })
      // }
    },
    {
      prop: 'subType',
      label: '设备类别',
      // formatter: (row, column, cellValue) => {
      //   return h(ElTag, { type: 'info' }, { default: () => typeOptions[cellValue] })
      // }
    },
    {
      prop: 'equipmentModel',
      label: '设备型号'
    },
    {
      prop: 'spaceFullName',
      label: '安装位置',
    },
    {
      prop: 'runStatus',
      label: '在线状态',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { type: state.statusOptions[cellValue] }, { default: () => state.runStatusOptions[cellValue] })
      }
    },
    {
      prop: 'equipmentStatus',
      label: '设备状态',
      formatter: (row, column, cellValue) => {
        return h(ElTag, { type: state.statusOptions[cellValue] }, { default: () => state.equipmentStatusOptions[cellValue] })
      }
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getSpaceTree()
  getList()
})

/** 查看详情 */
const onDetail = (equipmentId) => {
  emit('showPage', 1, equipmentId);
};

// 设备位置
const getSpaceTree = () => {
  treeAPI({ deep: 4 }).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
};

// 获取设备
const getList = () => {
  let query = {
    ...formInline,
    equipmentTypeId: props.equipmentTypeId,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
  }
  getEquipListAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
  // state.tableData = [
  //   {
  //     equipmentId: '1813102673776533506'
  //   }
  // ]
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}


// 查看详情
// const handleView = (equipmentId) => {
//   state.drawer = true;
//   drawer.value.state.deviceId = equipmentId
//   drawer.value.loadEquipmentInfo();
// }

//控制
const control = (info) => {
  equip.value.open(info)
}


/**
 * 视频预览
 * @param val
 */
const onPreview = (val) => {
  perviewURLs({ equipmentId: val.equipmentId }).then(res => {
    if (res.success) {
      videoRef.value.open(res.data)
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped></style>
