<template>
  <dialog-common
    ref="dialog"
    title="报废"
    @submit="submit"
    :formRef="ruleFormRef"
    :width="500"
    class="dialogTextarea"
  >
    <el-form
      ref="ruleFormRef"
      :model="form"
      :rules="state.rules"
      label-width="120px"
      label-suffix=":"
    >
      <div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="实际报废日期" prop="realScrapDate">
              <el-date-picker
                v-model="form.realScrapDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择实际报废日期"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="报废设施" prop="equipmentIds">
              <el-select
                :disabled="true"
                v-model="form.equipmentIds"
                multiple
                placeholder=""
              >
                <el-option
                  v-for="item in form.equipmentNameList"
                  :key="item.equipmentId"
                  :label="item.equipmentName"
                  :value="item.equipmentId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="报废原因" prop="scrapReason">
              <el-input
                v-model="form.scrapReason"
                :rows="5"
                type="textarea"
                :maxlength="200"
                show-word-limit
                placeholder="请输入报废原因"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { ElMessage,ElMessageBox } from "element-plus";
import {scrapAPI} from "@/api/iotManagement/equipManage.js";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["submit"]);

const dialog = ref();
const ruleFormRef = ref();
const form = reactive({scrapReason:'到期报废'});

const state = reactive({
  rules: {
    realScrapDate: [
      { required: true, message: "实际报废日期不能为空", trigger: "blur" },
    ],
    scrapReason: [{ required: true, message: "报废原因不能为空", trigger: "blur" }],
  },
});

const open = () => {
  dialog.value.open();
};

/** 保存 */
const submit = () => {
  ElMessageBox.confirm("您确定报废吗?", "提醒", {
    type: "warning",
  }).then(() => {
    scrapAPI(form).then((res) => {
      if (res.success) {
        ElMessage.success("报废成功");
        dialog.value.close()
        emit("submit");
      }
    });
  });
};

defineExpose({
  form,
  open,
});
</script>

<style lang="less" scoped>
</style>
