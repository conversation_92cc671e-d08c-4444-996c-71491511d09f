<template>
  <div>
    <div class="window-logo">
      <div class="window-logo-area">
        <div class="window-logo-1 window-logo-square"></div>
        <div class="window-logo-2 window-logo-square"></div>
        <div class="window-logo-3 window-logo-square"></div>
        <div class="window-logo-4 window-logo-square"></div>
      </div>
    </div>
    <Transition name="slide-fade">
      <div class="window-oparate" v-show="state.show">
        <div class="top">
          <desktop-cp :dblclick="false"></desktop-cp>
        </div>
        <el-row justify="space-between" align="middle" class="bottom" @click.stop>
          <div>
            <img src="@/assets/window/avater.png" class="avater">
            <span>{{ state.userInfo.showName }}</span>
            <img src="@/assets/window/password.png" class="password" title="修改密码" @click="handlePassword">
          </div>
          <el-icon size="17" @click="handleBack" class="close"><SwitchButton /></el-icon>
        </el-row>
      </div>
    </Transition>
    <modifyPassword ref="modal"></modifyPassword>
  </div>
</template>

<script setup>
import { events } from '@/utils/bus.js'
import { ElMessageBox } from "element-plus";

import desktopCp from '../desktopCp.vue'
import modifyPassword from '@/Layout/component/modify-password.vue'

const router = useRouter()

const modal = ref()

const state = reactive({
  show: false,
  userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}')
})

onMounted(() => {
  events.on('closeWindowPopup', () => {
    state.show = false
  })
})

onBeforeUnmount(() => {
  events.off('closeWindowPopup')
})

const handlePassword = () => {
  modal.value.open()
}

const handleBack = () => {
  ElMessageBox.confirm(
      '退出登录',
      {
        type: "warning"
      }
  ).then(() => {
    router.push('/login')
  })
}

parent['document'].addEventListener('click', (e) => {
  if (typeof e.target.className == 'string' && e.target.className.startsWith('window-logo')) {
    // 点击开始按钮切换显示状态
    state.show = !state.show
  } else {
    // 点击其他部分隐藏
    state.show = false
  }
});
</script>

<style scoped lang="less">
.window-logo{
  border-radius: 2px;
  .window-logo-1{
    background-image: linear-gradient(135deg,#71b7ff,#0a84d9);
    backdrop-filter: blur(10px);
  }
  .window-logo-2{
    background-image: linear-gradient(225deg,#41c4f8,#0a84d9);
    backdrop-filter: blur(10px);
  }
  .window-logo-3{
    background-color: #009dff;
  }
  .window-logo-4{
    background-color: #009dff;
  }
  &-area{
    display: grid;
    grid-template-columns: 11px 11px;
    grid-template-rows: 11px 11px;
    gap: 2px;
    padding: 8px;
  }
  &-area:active{
    transform: scale(0.8);
  }
}

.window-logo:hover{
  background-color: rgba(250,250,250,0.7);
}

.window-logo:active{
  .window-logo-area{
    transform: scale(0.8);
  }
}

.window-oparate{
  width: 550px;
  height: 600px;
  background-color: #ccc;
  position: fixed;
  bottom: 60px;
  left: 50%;
  z-index: 999;
  transform: translateX(-40%);
  border-radius: 5px;
  box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.5);
  .top{
    height: 536px;
    background-image: linear-gradient(45deg,#fff,#d8ebff);
    border-radius: 5px 5px 0 0;
    padding: 30px 30px 0;
    .desktop{
      height: calc(100% - 50px);
      width: 100%;
      grid-auto-flow: row ;
      :deep(.ico){
        color: #000;
        img{
          width: 34px;
          height: 34px;
        }
      }
    }
  }
  .bottom{
    height: 64px;
    background-image: linear-gradient(0deg,#fff,#ecf5ff);
    border-top: 1px solid #ccc;
    border-radius: 0 0 5px 5px;
    padding: 0 40px;
    font-size: 12px;
    .avater{
      width: 34px;
      height: 34px;
      vertical-align: middle;
      margin-right: 10px;
    }
    .password{
      width: 20px;
      height: 20px;
      vertical-align: middle;
      margin-left: 10px;
      cursor: pointer;
    }
    .close{
      cursor: pointer;
    }
  }
}

.slide-fade-enter-active,
.slide-fade-leave-active{
  transition: all 0.2s ease-in-out;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translate(-40% , 10px);
  opacity: 0;
}
</style>
