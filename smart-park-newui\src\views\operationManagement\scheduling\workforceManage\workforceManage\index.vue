<template>
  <div class="workForceManage">
    <div class="layout-left" :class="{ 'layout-left-border': state.areaList.length > 0 }">
      <template v-for="(item, index) in state.areaList">
        <div class="block-tag" :class="{ active: index == state.currentIndex }" :title="item"
          @click="handleArea(index)">
          <span>{{ item }}</span>
        </div>
      </template>
    </div>
    <div class="right">
      <div class="layout-top">
        <div class="top-date">
          <el-icon color="#c8c9cc" @click="handleOperate(false)">
            <ArrowLeftBold />
          </el-icon>
          <el-date-picker v-model="state.cycle" type="month" value-format="YYYY-MM" @change="dateChange"
            :clearable="false" />
          <el-icon color="#c8c9cc" @click="handleOperate(true)">
            <ArrowRightBold />
          </el-icon>
        </div>
        <el-button type="primary" icon="Upload" @click="handleUpload" style="float: right">导入</el-button>
      </div>
      <div class="layout-main">
        <div class="layout-right">
          <el-calendar :range="state.range" v-loading="state.loading">
            <template #header="{ date }">
              <div></div>
            </template>
            <template #date-cell="{ data }">
              <div @click="handleClick(data)" class="date">
                <p :class="data.isSelected ? 'is-selected' : ''">
                  {{ dayjs(data.day).format('MM-DD') }}
                </p>
                <template v-for="(item, index) in state.calendarObj[dayjs(data.day).format('MM-DD')]" :key="index">
                  <div style="margin-bottom: 5px">
                    <el-tag closable :color="item.color" @close="handleClose(item)">
                      {{ item.showName }} {{ item.shiftsName }}
                    </el-tag>
                  </div>
                </template>
                <el-link style="font-size: 12px" v-if="isShowDate(data)" @click.stop="handleAdd(data)">
                  添加人员
                  <el-icon :size="12">
                    <Plus />
                  </el-icon>
                </el-link>
              </div>
            </template>
          </el-calendar>
          <div class="tip">
            <div style="margin-right: 20px" v-for="(item, index) in state.tipList" :key="index">
              <div class="tip-embellish" :style="{ background: item.color }"></div>{{ item.shiftsName }} {{
                item.startTime
              }} - {{
                item.endTime }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <modalPage ref="modal" :attendance-date="state.attendanceDate" :liability-area="state.areaList[state.currentIndex]"
      @submit="getList"></modalPage>
    <modalUpload ref="upload" @submit="handleSubmit"></modalUpload>
  </div>
</template>

<script setup>
import dayjs from "dayjs";
import { ElMessage, ElMessageBox } from "element-plus";

import modalPage from './component/modalPage.vue'
import modalUpload from './component/modalUpload.vue'

import { workManageAreaAPI, workManagePageAPI, workManageDeteleAPI } from '@/api/operationManagement/workforceManage.js'

const modal = ref()
const upload = ref()

const state = reactive({
  loading: false,
  range: [dayjs().startOf('month').toDate(), dayjs().endOf('month').toDate()],
  cycle: dayjs().format('YYYY-MM'),
  currentIndex: -1,
  areaList: [],
  tipList: [],
  calendarObj: {},
  attendanceDate: '' //	出勤日期
})

onMounted(() => {
  getArea()
})

// 获取责任区域
const getArea = () => {
  workManageAreaAPI({
    businessType: 'OPERATIONS',
    cycle: state.cycle
  }).then(res => {
    state.areaList = res.data
    state.currentIndex = state.currentIndex == -1 ? 0 : state.currentIndex
    getList()
  })
}

// 获取排班
const getList = () => {
  state.loading = true
  state.tipList = []
  state.calendarObj = {}

  workManagePageAPI({
    businessType: 'OPERATIONS',
    cycle: state.cycle,
    liabilityArea: state.areaList[state.currentIndex]
  }).then(res => {
    //   创建日期
    let currentDay = dayjs(state.cycle).startOf('month');
    while (currentDay.isBefore(dayjs(state.cycle).endOf('month'))) {
      state.calendarObj[currentDay.format('MM-DD')] = []
      currentDay = currentDay.add(1, 'day');
    }

    // 添加人员
    res.data.forEach((item, index) => {
      state.tipList.push({
        shiftsName: item.shiftsName,
        startTime: item.startTime,
        endTime: item.endTime,
        color: item.color
      })

      item.rosterAttendanceDateVOS.forEach(i => {
        if (state.calendarObj[dayjs(i.attendanceDate).format('MM-DD')]) {
          i.rosterList = i.rosterList.map(roster => {
            return {
              attendanceDate: i.attendanceDate,
              shiftsSettingId: item.shiftsSettingId,
              shiftsName: i.shiftsName,
              color: item.color,
              ...roster,
            }
          })
          state.calendarObj[dayjs(i.attendanceDate).format('MM-DD')].push(...(i.rosterList || []))
        }
      })
    })

  }).finally(() => {
    state.loading = false
  })
}

// 区域
const handleArea = (index) => {
  state.currentIndex = index
  getList()
}

// 加减日期
const handleOperate = (bool) => {
  state.cycle = bool ? dayjs(state.cycle).add(1, 'month').format('YYYY-MM') : dayjs(state.cycle).subtract(1, 'month').format('YYYY-MM')
  dateChange()
}

// 日期变换
const dateChange = () => {
  nextTick(() => {
    state.range = [dayjs(state.cycle).startOf('month').toDate(), dayjs(state.cycle).endOf('month').toDate()]
    state.currentIndex = -1
    getArea()
  })
}


// 日期选中
const handleClick = (data) => {
  if (state.cycle != dayjs(data.day).format('YYYY-MM')) {
    state.cycle = dayjs(data.day).format('YYYY-MM')
    dateChange()
  }
}

// 是否显示添加按钮
const isShowDate = (data) => {
  return dayjs(data.day).format('YYYY-MM') == state.cycle && state.areaList && state.areaList.length
}

// 添加人员
const handleAdd = (data) => {
  state.attendanceDate = dayjs(data.day).format('YYYY-MM-DD')
  modal.value.open()
}

// 关闭人员
const handleClose = (item) => {
  console.log(item)
  ElMessageBox.confirm('是否删除当前排班?', '提醒', {
    type: 'warning',
  }).then(() => {
    workManageDeteleAPI({ ...item, businessType: 'OPERATIONS', liabilityArea: state.areaList[state.currentIndex] }).then((res) => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    });
  });
}

// 上传重新获取数据
const handleSubmit = () => {
  state.currentIndex = 0
  getArea()
}

// 导入
const handleUpload = () => {
  upload.value.open()
}
</script>

<style scoped lang="less">
.workForceManage {
  height: 100%;
  background: #FFFFFF;
  border-radius: 10px;
  display: flex;
}

.layout-left {
  flex-shrink: 0;
  overflow-y: scroll;

  .block-tag {
    width: 125px;
    text-align: center;
    padding: 30px 10px;
    cursor: pointer;
    font-size: 16px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .block-tag:hover,
  .active {
    color: #3f9eff;

    span {
      padding-bottom: 6px;
      border-bottom: solid #3f9eff 3px;
    }
  }
}

.layout-left-border {
  border-right: solid 1px #E0E2E2;
}

.right {
  padding: 18px 18px 18px 0;
}


.layout-top {
  margin-bottom: 15px;

  .top-date {
    display: inline-block;
    margin-left: 50%;
    transform: translateX(-50%);

    .el-icon {
      vertical-align: middle;
      cursor: pointer;
    }

    :deep(.el-date-editor) {
      width: 110px;

      .el-input__wrapper {
        box-shadow: none;

        .el-input__inner {
          width: 60px;
        }
      }
    }
  }
}

.layout-main {
  height: calc(100% - 37px);
  display: flex;
  // border: solid red 1px;

  .layout-right {
    flex: 1;
    display: flex;
    flex-direction: column;

    .el-calendar {
      // border: solid red 1px;
      flex: 1;
      overflow: auto;
      margin-bottom: 20px;
      padding-right: 5px;

      :deep(.el-calendar__header) {
        padding: 0;
      }

      :deep(.el-calendar__body) {
        padding: 0;

        .el-calendar-day {
          height: inherit;

          .date {
            min-height: 85px;
          }
        }
      }

      .is-selected {
        color: #16BCC2;
      }
    }

    .tip {
      // border: solid red 1px;
      flex-shrink: 0;
      display: flex;
      flex-wrap: wrap;
      padding-right: 13px;
      padding-left: 10px;

      .tip-embellish {
        height: 10px;
        width: 10px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }
}

.el-tag {

  :deep(.el-tag__content),
  :deep(.el-tag__close) {
    color: #fff;
  }
}
</style>
