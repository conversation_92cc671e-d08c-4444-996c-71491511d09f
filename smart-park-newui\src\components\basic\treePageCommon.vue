<template>
  <div class="page">
    <div class="left" v-if="leftBool">
      <slot name='leftTree'></slot>
    </div>
    <div class="right" :style="{ width: `${state.rightWidth}px` }">
      <div class="query">
        <slot name='query'></slot>
      </div>
      <div class="conent">
        <div style="padding-bottom: 18px;" v-show="operateBool" >
          <slot name='operate'></slot>
        </div>
        <div class="pageTable" ref="tableRef">
          <slot name='table'></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { events } from '@/utils/bus.js'
import { nextTick } from "vue"

const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  leftBool: {
    type: Boolean,
    default: true
  },
  operateBool:{
    type:Boolean,
    default: true
  }
})

let tableRef = ref()

let { leftBool } = toRefs(props)

const state = reactive({
  heightSize: 0,
  rightWidth: 0,
})

onMounted(() => {
  resize()
  window.addEventListener('resize', resize)
  events.on('tabClick', () => {
    setTimeout(() => {
      resize()
    })
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', resize)
  events.off('tabClick')
})

const resize = () => {
  nextTick(() => {
    let value
    let pagination = tableRef.value.querySelector('.el-pagination')
    if (pagination) {
      value = tableRef.value.offsetHeight - pagination.offsetHeight - 18
    } else {
      value = tableRef.value.offsetHeight
    }
    emit('update:modelValue', value)
  })
}
defineExpose({
  resize
})
</script>

<style lang='less' scoped>
.page {
  height: 100%;
  display: flex;

  .left {
    width: 300px;
    margin-right: 20px;
    align-items: stretch;
    overflow: auto;
  }

  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }

  .query,
  .left,
  .conent {
    background: #FFFFFF;
    border-radius: 10px;
    padding: 18px;
  }

  .query {
    padding-bottom: 0;
  }
}

.query {
  flex-shrink: 0;
  margin-bottom: 15px;
}

.conent {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 90%;

  .pageTable {
    flex: 1;
    overflow: hidden;
  }
}
</style>
