const needUnit = [
  'fontSize',
  'width',
  'height',
  'lineHeight',
  'top',
  'left',
  'borderWidth',
  'letterSpacing',
  'borderRadius'
]

export function getStyle(style,filter = []) {
  const result = {}
  Object.keys(style).forEach(key => {
    if (!filter.includes(key)) {
      if (style[key] !== '') {
        result[key] = style[key]
        if (needUnit.includes(key)) {
          result[key] += 'px'
        } else if(key === 'backgroundImage'){
          result[key] = `url(${import.meta.env.VITE_BASE_URL + result[key]})`
        } else if(key === 'transform'){
          result[key] = `rotate(${result[key]}deg)`
        }
      }
    }
  })
  return result
}
