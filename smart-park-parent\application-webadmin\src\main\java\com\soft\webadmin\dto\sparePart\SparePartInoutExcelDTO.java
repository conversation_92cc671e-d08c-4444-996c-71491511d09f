package com.soft.webadmin.dto.sparePart;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * SparePartInoutExcelDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
@ApiModel("SparePartInoutExcelDTO对象")
@Data
@ColumnWidth(value = 15)
public class SparePartInoutExcelDTO {

    @ExcelProperty(value = "*入库日期")
    private String inoutDate;

    // @ExcelProperty(value = "*申请人")
    // private String applyUserName;

    @ExcelProperty(value = "*备件名称")
    private String sparePartName;

    @ExcelProperty(value = "*备件编号")
    @ColumnWidth(value = 25)
    private String sparePartNo;

    @ExcelProperty(value = "*备件分类")
    private String classifyName;

    @ExcelProperty(value = "*规格型号")
    private String model;

    // @ExcelProperty(value = "条码")
    // private String barCode;

    @ExcelProperty(value = "*单位")
    @ColumnWidth(value = 10)
    private String unit;

    @ExcelProperty(value = "*单价（元）")
    private BigDecimal unitPrice;

    @ExcelProperty(value = "*入库仓库")
    private String storehouseName;

    @ExcelProperty(value = "*入库数量")
    private Integer changeQuantity;

}
