<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form ref="formInlineRef" :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item prop="equipmentWord">
          <el-input v-model="formInline.equipmentWord" placeholder="设施名称/编号"/>
        </el-form-item>
        <el-form-item prop="equipmentTypeId">
          <el-tree-select v-model="formInline.equipmentTypeId" :data="equipmentAllTypeOPtions"
                          :props="{label: 'name'}" check-strictly :render-after-expand="false"
                          clearable node-key="id" placeholder="设施类型"/>
        </el-form-item>
        <el-form-item prop="spaceId">
          <el-cascader v-model="formInline.spaceId" :options="state.spaceOptions" :props="state.spaceProps" clearable
                       placeholder="安装位置"/>
        </el-form-item>
        <el-form-item prop="maintenanceStatus">
          <el-select v-model="formInline.maintenanceStatus" clearable filterable placeholder="运维状态">
            <el-option v-for="(value,key) in state.maintenanceStateOptions" :label="value" :value="key"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="Search" type="primary" @click="onSubmit">查询</el-button>
          <el-button icon="Refresh" type="primary" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button icon="Delete" type="danger" @click="deleteEquip">删除</el-button>
      <el-button icon="Close" type="primary" @click="scrapEquip">报废</el-button>
      <el-button icon="Download" type="primary" @click="exportExcel">下载设备二维码</el-button>
      <el-button icon="Upload" type="primary" @click="handleUpload">导入</el-button>
      <el-button icon="Plus" type="primary" @click="addHandle">新增设备</el-button>
    </template>
    <template #table>
      <el-table ref="table" :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column type="selection" width="55" :selectable="isRowSelectable"/>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :formatter="item.formatter"
                         :label="item.label" :prop="item.prop" :width="item.width">
        </el-table-column>
        <el-table-column align="center" label="设备二维码" width="160">
          <template #default="scope">
            <img src="@/assets/qrcode.svg" @click="qrCode(scope.row)">
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="240">
          <template v-slot:default="scope">
            <el-button icon="Edit" link type="primary" @click.prevent="editHandle(scope.row)" :disabled="scope.row.category == 'IOT'">
              编辑
            </el-button>
            <el-button icon="Delete" link type="danger" @click="deleteHandle(scope.row.equipmentId)"  :disabled="scope.row.category == 'IOT'">删除</el-button>
            <el-button icon="Tickets" link type="primary" @click.prevent="onDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize" :page-sizes="[10, 20, 30, 50]"
                     :total="state.pageParam.total" background
                     layout="total, sizes, prev, pager, next, jumper"
                     @size-change="sizeChange" @current-change="currentChange"/>

      <el-dialog v-model="dialogVisible" title="预览" width="300px">
        <div>
          <el-col>
            <el-card>
              <img :src="equipmentQrCodeRef.qrcodeUrl" alt="二维码">
            </el-card>
          </el-col>
        </div>
        <template #footer>
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="onDownQrCode">下载</el-button>
        </template>
      </el-dialog>

      <modelPage ref="modalPageRef" :equipmentTypeOptions="equipmentTypeOptions" :title="state.title"
                 @submit="getEquipList"></modelPage>
      <scrapModal ref="scrapModalRef" :equipmentNameList="state.equipmentNameList" @submit="getEquipList"></scrapModal>
      <modalUpload ref="upload" @submit="getEquipList"></modalUpload>
    </template>
  </page-common>
</template>


<script setup>
import {ElMessage, ElMessageBox} from 'element-plus'

import modelPage from '../modal/modelPage.vue';
import scrapModal from '../modal/scrapModal.vue';
import modalUpload from "../modal/modalUpload.vue";

import {equipDeleteAPI, getEquipListAPI, qrCodeAPI} from "@/api/operationManagement/equipManage.js";
import {equipTypTreeAPI} from '@/api/operationManagement/equipType.js'
import {treeAPI} from '@/api/iotManagement/space.js';

import {calcPageNo} from '@/utils/util.js'
import {exportFile} from '@/utils/down.js'

const emit = defineEmits(['showPage'])
const equipmentQrCodeRef = ref({
  qrcodeUrl: '',
  equipmentName: ''
})

const formInlineRef = ref();
const table = ref()
// 设备id
const equipmentId = ref()

// 设备类别（子系统类型）
const equipmentTypeOptions = ref([])
const equipmentAllTypeOPtions = ref([])

const modalPageRef = ref();
const scrapModalRef = ref()
const upload = ref()

const formInline = reactive({});

const state = reactive({
  srcList: [],
  imageVisible: false,
  deptList: [],
  equipmentNameList: [],
  spaceOptions: [],
  spaceProps: {
    emitPath: false,
    checkStrictly: true,
    label: 'name',
    value: 'id',
    expandTrigger: 'hover',
  },   // 位置级联选择配置
  maintenanceStateOptions: {
    1: '正常',
    5: '报废',
    11: '维保中',
    12: '维修中'
  },
  maintenanceStateColor: {
    1: '#19BE6B',
    5: '#AAAAAA',
    11: '#F59A23',
    12: '#F59A23'
  },
  tableData: [],
  tableHeight: 100,
  tableHeader: [
    {
      prop: 'equipmentCode',
      label: '设备编号'
    },
    {
      prop: 'equipmentName',
      label: '设备名称'
    },
    {
      prop: 'equipmentTypeName',
      label: '设备类型'
    },
    {
      prop: 'spaceFullName',
      label: '安装位置'
    },
    {
      prop: 'maintenanceStatus',
      label: '运维状态',
      width: 160,
      formatter: (row, column, cellValue) => {
        let color = state.maintenanceStateColor[cellValue];
        return h('div', [h('span', {
          class: 'status-circle',
          style: 'background-color: ' + color
        }), state.maintenanceStateOptions[cellValue]]);
      }
    }
  ],
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
});

onMounted(() => {
  loadSelectOptions()
  getEquipGroup()
  getEquipList()
})

// 获取下拉数据
const loadSelectOptions = () => {
  /** 查询位置列表 */
  treeAPI({deep: 4}).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
}

// 获取二维码
const dialogVisible = ref(false)
const qrCode = (row) => {
  equipmentId.value = row.equipmentId
  qrCodeAPI(equipmentId.value).then((res) => {
    equipmentQrCodeRef.value.qrcodeUrl = `data:image/png;base64,${res.data}`
    equipmentQrCodeRef.value.equipmentName = row.equipmentName
    dialogVisible.value = true
  })
}

/**
 * 下载二维码
 * @param scope
 */
const onDownQrCode = async () => {
  // 如果浏览器支持msSaveOrOpenBlob方法（也就是使用IE浏览器的时候），那么调用该方法去下载图片
  const imgUrl = equipmentQrCodeRef.value.qrcodeUrl
  if (window.navigator.msSaveOrOpenBlob) {
    let bstr = atob(imgUrl.split(',')[1])
    let n = bstr.length
    let u8arr = new Uint8Array(n)
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    let blob = new Blob([u8arr])
    window.navigator.msSaveOrOpenBlob(blob, equipmentQrCodeRef.value.equipmentName + '.' + 'png')
  } else {
    // 这里就按照chrome等新版浏览器来处理
    let a = document.createElement('a')
    a.href = imgUrl
    a.setAttribute('download', equipmentQrCodeRef.value.equipmentName)
    a.click()
  }
}

// 导出
const exportExcel = async () => {
  let spaceQuery = {
    ...formInline,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  }

  await exportFile(
      "/om/equipment/qrcode/export",
      spaceQuery,
      "设备二维码.xlsx"
  );
};


// 获取设备类别列表
const getEquipGroup = async () => {
  const res = await equipTypTreeAPI({category:'OM'})
  const resALL = await equipTypTreeAPI()

  equipmentTypeOptions.value = res.data
  equipmentAllTypeOPtions.value = resALL.data
}

// 分页查询
const getEquipList = () => {
  let query = {
    ...formInline,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize
  };
  getEquipListAPI(query).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
}

// 查询
const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getEquipList();
};

//重置表单
const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getEquipList();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getEquipList();
};

// 新增设备
const addHandle = () => {
  state.title = '新增设备'
  modalPageRef.value.open();
}

// 编辑设备
const editHandle = (info) => {
  state.title = '编辑设备'
  modalPageRef.value.open()
  nextTick(() => {
    Object.assign(modalPageRef.value.form, {...info})
  })
}

// 批量删除
const deleteEquip = () => {
  let equipmentIds = table.value.getSelectionRows().map((item) => item.equipmentId)

  if (!equipmentIds.length) {
    return ElMessage.warning("请选择设施！");
  }

  deleteHandle(equipmentIds);
};

// 删除设备
const deleteHandle = (equipmentIds) => {
  ElMessageBox.confirm(
      '是否删除当前设备?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    if (equipmentIds instanceof Array) {
      state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize, equipmentIds.length)
    } else {
      equipmentIds = [equipmentIds]
      state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize)
    }
    equipDeleteAPI({idList: equipmentIds}).then(res => {
      if (res.success) {
        getEquipList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

// 禁选
const isRowSelectable = (row, index) => {
  return row.equipmentStatus != 5 && row.category == 'OM'
}

// 报废
const scrapEquip = () => {
  if (!table.value.getSelectionRows().length) {
    return ElMessage.warning("请选择设施！");
  }

  state.equipmentNameList = table.value.getSelectionRows()

  scrapModalRef.value.open();

  nextTick(() => {
    Object.assign(scrapModalRef.value.form, {
      equipmentIds: state.equipmentNameList.map(item => item.equipmentId),
    });
  });
}

/** 查看详情 */
const onDetail = ({equipmentId}) => {
  emit('showPage', 2, equipmentId);
};

// 导入
const handleUpload = () => {
  upload.value.open()
}


defineExpose({
  getEquipList
})

</script>

<style lang="less" scoped>
:deep(.status-circle) {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
  margin-bottom: 1px;
}
</style>
