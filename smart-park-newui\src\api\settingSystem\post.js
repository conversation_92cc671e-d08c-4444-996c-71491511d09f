import { request } from "@/utils/request";

// 分页查询
export const getPageAPI = (data) => {
    return request('post','/admin/upms/sysPost/list',data)
}

// 创建岗位
export const addPostAPI = (data) => {
    return request('post','/admin/upms/sysPost/add',data)
}

// 查询岗位
export const getPostInfoAPI = (query) => {
    return request('get','/admin/upms/sysPost/view',query,'F')
}

// 更新岗位
export const updatePostAPI = (data) => {
    return request('post','/admin/upms/sysPost/update',data)
}

// 删除岗位
export const deletePostAPI = (data) => {
    return request('post','/admin/upms/sysPost/delete',data)
}

// 根据部门查询岗位
export const listSysDeptPost = (data) => {
    return request('post','/admin/upms/sysDept/listSysDeptPost',data)
}

// 修改别名
export const updateSysDeptPost = (data) => {
    return request('post','/admin/upms/sysDept/updateSysDeptPost',data)
}

// 删除岗位
export const deleteSysDeptPost = (data) => {
    return request('post','/admin/upms/sysDept/deleteSysDeptPost',data)
}

// 岗位设置
export const listNotInSysDeptPost = (data) => {
    return request('post','/admin/upms/sysDept/listNotInSysDeptPost',data)
}

// 添加岗位
export const addSysDeptPost = (data) => {
    return request('post','/admin/upms/sysDept/addSysDeptPost',data)
}
