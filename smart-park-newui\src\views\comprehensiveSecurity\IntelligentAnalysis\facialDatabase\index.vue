<template>
    <div style="height: 100%;overflow: hidden;">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-change="change">
            <el-tab-pane label="黑名单" name="first">
                <roll-call ref="roll1" :listType='1' />
            </el-tab-pane>
            <el-tab-pane label="白名单" name="second" lazy>
                <roll-call ref="roll2" :listType='2' />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup>
import rollCall from './component/rollCall.vue'
import { events } from '@/utils/bus.js'

const activeName = ref('first')
const roll1 = ref()
const roll2 = ref()

const change = () => {
    nextTick(() => {
        console.log(activeName.value)
        if (activeName.value == 'first') {
            roll1.value.getList();
        } else {
            roll2.value.getList();
        }

        events.emit('tabClick')
    })
}
</script>
<style lang='less' scoped>
.el-tabs {
    height: 100%;

    :deep(.el-tabs__content) {
        height: calc(100% - 55px);

        .el-tab-pane {
            height: 100%;
        }
    }
}
</style>