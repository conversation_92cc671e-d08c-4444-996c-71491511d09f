import { request } from '@/utils/request';

// 分页查询
export const groupPageAPI = (params) => {
    return request('get', '/contingency/group/getPage', params, 'F');
};


// 保存
export const groupSaveAPI = (data) => {
    return request('post', '/contingency/group/saveOrUpdate', data);
};

// 删除
export const groupDeteleAPI = (params) => {
    return request('post', '/contingency/group/delete', params, 'F');
};

// 详情
export const groupDetailAPI = (params) => {
    return request('get', '/contingency/group/detail', params, 'F');
}