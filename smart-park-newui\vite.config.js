import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import {resolve} from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import DefineOptions from 'unplugin-vue-define-options/vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    DefineOptions(),
    AutoImport({
      imports:["vue","vue-router","pinia"],
      dts:'./auto-import.d.ts'    // 路径下自动生成文件夹存放全局指令
    })
  ],
  server:{
    host:'0.0.0.0'
  },
  resolve:{
    alias:{
      '@': resolve(__dirname,'./src')
    }
  }
})
