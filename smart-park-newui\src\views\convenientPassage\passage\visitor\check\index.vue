<template>
    <div style="height: 100%;overflow: hidden;">
         <el-tabs v-model="activeName" class="demo-tabs" @tab-change="change">
              <el-tab-pane label="待审核" name="first">
                   <pendingCheck ref="pendingCheckRef">
                   </pendingCheck>
              </el-tab-pane>
              <el-tab-pane label="已审核" name="second" lazy>
                   <checked ref="checkedRef"></checked>
              </el-tab-pane>
         </el-tabs>
    </div>
</template>

<script setup>
import pendingCheck from './component/pendingCheck.vue'
import checked from './component/checked.vue'
const activeName = ref('first')
const pendingCheckRef = ref()
const checkedRef = ref()

const change = () => {
    if(activeName.value == 'first'){
         pendingCheckRef.value.getList()
    }else{
         checkedRef.value.getList()
    }
}
</script>

<style lang='less' scoped>
.el-tabs {
 height: 100%;

 :deep(.el-tabs__content) {
   height: calc(100% - 55px);

   .el-tab-pane {
     height: 100%;
   }
 }
}
</style>