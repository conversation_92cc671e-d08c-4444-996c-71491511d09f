<template>
  <dialog-common ref="dialog" title="退回" @submit="submit" :formRef="ruleFormRef" :width="650">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-form-item label="退回原因" prop="reason">
        <el-input v-model="form.reason" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit placeholder="请输入退回原因"/>
      </el-form-item>
      <el-form-item label="上传附件">
        <el-upload
            v-model:file-list="state.fileList"
            list-type="picture-card"
            :on-preview="handleImgPreview"
            :http-request="httpRequest"
            :on-success="(response) => { fileSuccess(response, state.fileList) }"
            :limit="3"
            :class="{'disUpload': state.fileList.length == 3 }">
          <el-icon>
            <Plus />
          </el-icon>
          <template #tip>
            <div class="el-upload__tip">
              支持格式：jpg、png ，单个文件不能超过5MB，最多支持3张
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
  </dialog-common>
  <el-dialog v-model="state.dialogVisible" id="imgDialog">
    <img w-full :src="state.dialogImageUrl" alt="Preview Image" style="width: 100%;" />
  </el-dialog>
</template>

<script setup>
import { workBackAPI } from '@/api/operationManagement/workOrder.js';
import { ElMessage } from 'element-plus';
import { annexUpload } from '@/api/file.js';

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  rules: {
    reason: [{required: true, message: '请输入退回原因', trigger: 'blur'},],
  },
  fileList: [],
  dialogVisible: false,
  dialogImageUrl: '',
});

// 覆盖Http
const httpRequest = (option) => {
  const formData = new FormData()
  formData.append('file', option.file)
  return annexUpload(formData)
}

// 查看图片
const handleImgPreview = (uploadFile) => {
  state.dialogImageUrl = uploadFile.url
  state.dialogVisible = true
}

// 上传图片
const fileSuccess = (response, fileList) => {
  fileList[fileList.length - 1].filePath = response.data.filePath
}

// 提交表单
const submit = () => {
  form.img = (state.fileList || []).map(img => img.filePath).join(',');
  workBackAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success('操作成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

const open = () => {
  dialog.value.open();
}

defineExpose({
  form,
  open,
});
</script>
