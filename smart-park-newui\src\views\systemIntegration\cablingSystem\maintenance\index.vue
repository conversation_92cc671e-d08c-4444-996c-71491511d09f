<template>
    <page-common v-model="state.tableHeight">
        <template #query>
            <el-form :inline="true" ref="formInlineRef" label-suffix=":" :model="formInline">
                <el-form-item prop="classify">
                    <el-select v-model="formInline.classify" clearable class="m-2" placeholder="请选择类型">
                        <el-option v-for="item in DKoptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="formInline.name" placeholder="请输入设备名称关键词" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
                    <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template #operate>
            <div class="button">
                <el-button type="primary" :icon="Plus" @click="addHandle">新建设备</el-button>
                <el-popover v-model:visible="state.showPopover" placement="bottom" :width="300" trigger="click"
                    @hide="hiddenPopover">
                    <template #reference>
                        <el-button type="primary" icon="Upload">导入</el-button>
                    </template>
                    <template #default>
                        <el-button type="primary" size="small" style="margin-left: 10px" @click="lead(2)">导入配线架
                        </el-button>
                        <el-button type="primary" size="small" style="margin-left: 10px" @click="lead(3)">导入交换机
                        </el-button>
                        <el-button type="primary" size="small" style="margin-left: 10px" @click="lead(4)">导入机柜
                        </el-button>
                    </template>
                </el-popover>
            </div>
        </template>
        <template #table>
            <el-table :data="state.tableData" :height="state.tableHeight">
                <el-table-column type="index" width="60" label="序号">
                </el-table-column>
                <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop"
                    :label="item.label" :align="item.align" :formatter="item.formatter" />
                <el-table-column label="设备二维码" align="center">
                    <template #default="scope">
                        <div>
                            <el-popover placement="top" :width="100" trigger="click">
                                <template #reference>
                                    <el-button link type="primary" :icon="Check">查看</el-button>
                                </template>
                                <qrcode-vue :value="scope.row.qrCode" :size="122" level="H" />
                            </el-popover>
                            <el-button link type="primary" :icon="Download" @click="onDownQrc(scope)">下载</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作">
                    <template #default="scope">
                        <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
                        <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
                :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
                @size-change="sizeChange" @current-change="currentChange" />

            <!-- 新建、编辑设备Dialog -->
            <modal-page ref="modal" :title="state.title" @submit="getList"></modal-page>

            <el-dialog v-model="state.dialogVisible" :title="state.title" width="30%">
                <el-upload ref="uploadRef" :http-request="onUploadFile" align="center">
                    <template #trigger>
                        <el-button size="small" type="primary">上传数据</el-button>
                    </template>
                    <template #default>
                        <el-button type="primary" size="small" style="margin-left: 10px" @click="downTemplate(2)"
                            v-if="state.title === '导入配线架'">下载配线架模板
                        </el-button>
                        <el-button type="primary" size="small" style="margin-left: 10px" @click="downTemplate(3)"
                            v-if="state.title === '导入交换机'">下载交换机模板
                        </el-button>
                        <el-button type="primary" size="small" style="margin-left: 10px" @click="downTemplate(4)"
                            v-if="state.title === '导入机柜'">下载机柜模板
                        </el-button>
                    </template>
                    <template #tip>
                        <div class="el-upload__tip" align="center">
                            只能上传一个xlsx文件
                        </div>
                    </template>
                </el-upload>
            </el-dialog>
        </template>
    </page-common>
</template>

<script setup>
import {
    Delete, Plus, Search, Refresh, Download, Check
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
import { listApi, deleteAPI, listByEquipmentIdApi } from '@/api/systemIntegration/cablingSystem/maintenance.js';
import { calcPageNo } from '@/utils/util.js'
import { exportFile, uploadFile } from "@/utils/down.js";
import modalPage from './component/modalPage.vue';
import QrcodeVue from 'qrcode.vue'

let formInlineRef = ref()

const formInline = reactive({})

const modal = ref();

const uploadRef = ref()

const DKoptions = [
    {
        value: 1,
        label: '信息面板'
    },
    {
        value: 2,
        label: '配线架'
    },
    {
        value: 3,
        label: '交换机'
    },
    {
        value: 4,
        label: '机柜'
    },
]

const state = reactive({
    qrCode: '',
    title: '',
    dialogVisible: false,
    tableHeight: 100,
    tableData: [],
    tableHeader: [
        {
            prop: 'code',
            label: '系统编号'
        },
        {
            prop: 'name',
            label: '设备名称'
        },
        {
            prop: 'classify',
            label: '设备分类',
            formatter: (row, column, cellValue) => {
                if (cellValue == 1) {
                    return h(ElTag, { type: "success" }, { default: () => "信息面板" })
                } else if (cellValue == 2) {
                    return h(ElTag, { type: "success" }, { default: () => "配线架" })
                } else if (cellValue == 3) {
                    return h(ElTag, { type: "success" }, { default: () => "交换机" })
                } else if (cellValue == 4) {
                    return h(ElTag, { type: "success" }, { default: () => "机柜" })
                }
            }
        },
        {
            prop: 'typeName',
            label: '类型/属性'
        },
        {
            prop: 'portQuantity',
            label: '端口数量'
        },
        {
            prop: 'spaceFullName',
            label: '位置'
        },
        {
            prop: 'managementRoomName',
            label: '所在管理间'
        },
        {
            prop: 'cabinetSpaceFullName',
            label: '机柜位置'
        },
        {
            prop: 'createTime',
            label: '创建时间'
        }
    ],
    pagetion: {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
})

onMounted(() => {
    getList()
})

const lead = (e) => {
    state.title = e == 2 ? '导入配线架' : e == 3 ? '导入交换机' : e == 4 ? '导入机柜' : ''
    state.dialogVisible = true
}

/**
 * 上传弹窗隐藏后触发事件，清除上传的文件列表
 */
const hiddenPopover = () => {
    uploadRef.value.clearFiles()
}

const onDownQrc = (scope) => {
    //获取canvas标签
    let canvas = document.getElementsByTagName('canvas')
    //创建a标签
    let a = document.createElement('a')
    //获取二维码的 url并赋值为 a.href
    // console.log(canvas);
    a.href = canvas[scope.$index].toDataURL('img/png')
    //设置下载文件的名字
    let val = scope.row
    a.download = val.code + '-' + val.name
    //点击事件，相当于下载
    a.click()
    //提示信息
    ElMessage.success('下载中，请稍后...')
    a.remove()
}

//查询方法
const onSubmit = () => {
    state.pagetion = {
        pageNum: 1,
        pageSize: 10,
        total: 0
    }
    getList()
}

//重置方法
const onReset = () => {
    formInlineRef.value.resetFields()
    onSubmit()
}

// 下载模板文件
const downTemplate = async (e) => {
    let fileName = '设备维护.xlsx'
    if (e === 2) {
      fileName = '配线架设备模板.xlsx'
    } else if (e === 3) {
      fileName = '交换机设备模板.xlsx'
    } else if (e === 4) {
      fileName = '机柜设备模板.xlsx'
    }
    await exportFile('/cablingSystem/equipment/export?classify=' + e, null, fileName)
}

// 上传文件
const onUploadFile = async (fileData) => {

    var classify = state.title == '导入配线架' ? 2 : state.title == '导入交换机' ? 3 : state.title == '导入机柜' ? 4 : ''
    let { data: res } = await uploadFile('/cablingSystem/equipment/import?classify=' + classify, fileData.file);
    if (res.success) {
        ElMessage.success('上传成功！');
        // 2s 后关闭气泡框
        setTimeout(() => {
            state.dialogVisible = false
        }, 2000)
    } else {
        ElMessage.error("上传 " + res.errorMessage)
    }
    getList()
}

/**
 * 分页查询（条数）
 * @param pageSize
 */
const sizeChange = (pageSize) => {
    state.pagetion.pageSize = pageSize;
    getList();
};

/**
 * 分页查询（页码）
 * @param pageNum
 */
const currentChange = (pageNum) => {
    state.pagetion.pageNum = pageNum;
    getList();
};

/** 创建设备 */
const addHandle = () => {
    state.title = '新建设备';
    modal.value.open();
};

//编辑设备
const editHandle = (row) => {
    state.title = '编辑设备';
    row.classify = row.classify.toString()
    modal.value.form.classify = row.classify


    if (row.portQuantity) {
        row.portQuantity = row.portQuantity.toString()
    }
    row.spaceIds = row.spaceId
    row.cabinetIds = row.cabinetId
    if (row.classify != "1") {
        row.spaceIdLists = row.spaceId
    }
    if (row.classify == "1") {
        listByEquipmentIdApi({ equipmentId: row.id }).then(res => {
            if (res.success) {
                row.portTypes = res.data
                let newObj = JSON.parse(JSON.stringify(row))
                delete newObj.classify
                modal.value.open();
                nextTick(() => {
                    Object.assign(modal.value.form, { ...newObj });
                })
            }
        })
    } else {
        let newObj = JSON.parse(JSON.stringify(row))
        delete newObj.classify
        modal.value.open();
        nextTick(() => {
            Object.assign(modal.value.form, { ...newObj });
        })
    }
}

//删除事件
const deleteHandle = (info) => {
    ElMessageBox.confirm("是否删除当前线路设备?", "提醒", {
        type: "warning",
    }).then(() => {
        state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, state.pagetion.pageSize)
        deleteAPI({ id: info.id }).then(res => {
            if (res.success) {
                getList()
                ElMessage.success('删除成功')
            } else {
                ElMessage.error(res.errorMessage)
            }
        })
    });
}

//分页
const getList = () => {
    let query = {
        pageNum: state.pagetion.pageNum,
        pageSize: state.pagetion.pageSize,
        name: formInline.name,
        classify: formInline.classify
    }
    listApi(query).then(res => {
        state.tableData = res.data.dataList
        state.tableData.forEach(e => {
            let qrData = {
                id: e.id + '',
                type: 'cabling-system',
                classify: e.classify + ''
            }
            e.qrCode = JSON.stringify(qrData)
        })

        state.pagetion.total = res.data.totalCount * 1
    })
}
</script>

<style lang='less' scoped></style>
