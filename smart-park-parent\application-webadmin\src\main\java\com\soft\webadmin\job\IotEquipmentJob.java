package com.soft.webadmin.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.soft.sub.dao.equipment.EquipmentMapper;
import com.soft.sub.dao.equipment.EquipmentTypeMapper;
import com.soft.sub.dao.equipment.EquipmentWarningMapper;
import com.soft.sub.model.equipment.Equipment;
import com.soft.sub.model.equipment.EquipmentType;
import com.soft.sub.model.equipment.EquipmentWarning;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class IotEquipmentJob {

    @Resource
    private EquipmentTypeMapper equipmentTypeMapper;

    @Resource
    private EquipmentMapper equipmentMapper;

    @Resource
    private EquipmentWarningMapper equipmentWarningMapper;

    private static final String SCRAP_HANDLE_NAME = "iotEquipmentScrapJob";

    private static final String ALARM_STATUS_HANDLE_NAME = "iotEquipmentAlarmStatusJob";


    /**
     * 设备故障状态定时同步：每三分钟执行一次
     */
    @XxlJob(ALARM_STATUS_HANDLE_NAME)
    public void alarmHandle() {
        List<EquipmentType> equipmentTypes = equipmentTypeMapper.selectList(Wrappers.lambdaQuery(EquipmentType.class)
                .eq(EquipmentType::getCategory, "IOT"));
        if (CollectionUtil.isEmpty(equipmentTypes)) {
            return;
        }
        List<Equipment> equipments = equipmentMapper.selectList(Wrappers.lambdaQuery(Equipment.class)
                .in(Equipment::getEquipmentStatus, 0, 1)
                .in(Equipment::getEquipmentTypeId, equipmentTypes.stream().map(EquipmentType::getId).collect(Collectors.toSet())));
        if (CollectionUtil.isNotEmpty(equipments)) {
            Map<Long, Equipment> equipmentMap = equipments.stream().collect(Collectors.toMap(Equipment::getEquipmentId, equipment -> equipment));
            List<Long> equipmentIds = equipments.stream().map(Equipment::getEquipmentId).collect(Collectors.toList());
            // 查询报警记录
            List<Map<String, Object>> equipmentWarningMaps = equipmentWarningMapper.selectMaps(Wrappers.query(new EquipmentWarning())
                    .select("equipment_id as equipmentId", "sum(if(status = 0, 1, 0)) as total")
                    .in("equipment_id", equipmentIds)
                    .groupBy("equipment_id"));
            for (Map<String, Object> equipmentWarningMap : equipmentWarningMaps) {
                Long equipmentId = (Long) equipmentWarningMap.get("equipmentId");
                Long total = (Long) equipmentWarningMap.get("total");
                Equipment equipment = equipmentMap.get(equipmentId);
                if (equipment != null) {
                    // 未解决的报警
                    if (total > 0) {
                        equipment.setEquipmentStatus(0);
                    } else {
                        equipment.setEquipmentStatus(1);
                    }
                }
            }
            equipmentMapper.batchUpdate(equipmentMap.values());
        }
    }

    /**
     * 消防设施报废 cron：每十分钟执行一次
     */
    @XxlJob(SCRAP_HANDLE_NAME)
    public void scrapHandle() {
        List<EquipmentType> equipmentTypes = equipmentTypeMapper.selectList(Wrappers.lambdaQuery(EquipmentType.class)
                .eq(EquipmentType::getCategory, "IOT"));
        if (CollectionUtil.isEmpty(equipmentTypes)) {
            return;
        }
        // 查询所有IOT设施(非报废和待报废状态)
        List<Equipment> equipments = equipmentMapper.selectList(Wrappers.lambdaQuery(Equipment.class)
                .ne(Equipment::getEquipmentStatus, 5)
                .in(Equipment::getEquipmentTypeId, equipmentTypes.stream().map(EquipmentType::getId).collect(Collectors.toSet())));

        // 过滤出待报废的设施
        if (CollectionUtil.isNotEmpty(equipments)) {
            List<Equipment> scrapEquipments = equipments.stream()
                    .filter(equipment -> equipment.getScrapDate() != null && DateUtil.compare(new Date(), equipment.getScrapDate(), "yyyy-MM-dd") >= 0)
                    .peek(equipment -> {
                        equipment.setEquipmentStatus(5);
                    })
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(scrapEquipments)) {
                equipmentMapper.batchUpdate(scrapEquipments);
            }
        }
    }

}
