<template>
  <div class="container">
    <div class="operate">
      <el-button type="primary" @click="changeHeight" v-if="state.switchPage===1 && state.showBtn">
        <el-icon><CaretRight/></el-icon>
        组织管理
      </el-button>
      <el-button type="primary" @click="state.switchPage = 1" v-if="state.switchPage===2 && state.showBtn">
        <el-icon><CaretLeft/></el-icon>
        组织架构
      </el-button>
    </div>

    <transition name="el-zoom-in-center">
      <organization v-if="state.switchPage === 1" />
    </transition>
    <transition name="el-zoom-in-center">
      <page-list v-if="state.switchPage === 2" @changeShow="onChangeShow"/>
    </transition>
  </div>
</template>

<script setup>
import Organization from "@/views/settingSystem/basic/depart/component/organization.vue";
import pageList from "@/views/settingSystem/basic/depart/component/pageList.vue";

import {CaretLeft, CaretRight} from "@element-plus/icons-vue";
import { nextTick } from "vue";
import { events } from '@/utils/bus.js'

const state = reactive({
  switchPage: 2,
  showBtn: true
})

// 重新计算高度
const changeHeight = () => {
  state.switchPage = 2
  nextTick(() => {
    events.emit('tabClick')
  })
}

const onChangeShow = () => {
  state.showBtn = !state.showBtn
}
</script>

<style scoped lang="less">
.container {
  height: 100%;
  background: #FFFFFF;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  .operate{
    position: absolute;
    top: 18px;
    right: 18px;
  }
}
</style>
