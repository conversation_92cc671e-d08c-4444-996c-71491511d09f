<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" @onClose="close" :width="900" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="100px" :model="state.form" :rules="state.rules" label-suffix=":">
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">规则信息</div>
        </div>

        <el-row>
          <el-col :span="24">
            <el-form-item label="权限组名称" prop="name">
              <el-input v-model="state.form.name" placeholder="请输入权限组名称"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="描述" prop="desc">
              <el-input type="textarea" :autosize="{ minRows: 5 }" maxlength="500" show-word-limit v-model="state.form.desc" placeholder="请输入描述"/>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">权限信息</div>
        </div>


        <el-row>
          <el-col :span="24">
            <el-form-item label="空间权限" prop="hasAllSpace">
              <el-radio-group v-model="state.form.hasAllSpace">
                <el-radio :value="1">全部</el-radio>
                <el-radio :value="0">部分</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item prop="spaceIds" v-if="state.form.hasAllSpace === 0">
              <el-cascader v-model="state.form.spaceIds"
                           :options="state.spaceOptions"
                           :props="{
                              checkStrictly: true,
                              multiple: true,
                              emitPath: false,
                              label: 'name',
                              value: 'id',

                           }"
                           collapse-tags
                           collapse-tags-tooltip
                           :max-collapse-tags="4"
                           clearable
                           placeholder="空间位置"/>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="24">
            <el-form-item label="设备权限" prop="hasAllEquipment">
              <el-radio-group v-model="state.form.hasAllEquipment">
                <el-radio :value="1">全部</el-radio>
                <el-radio :value="0">部分</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div>
              <el-form-item prop="equipmentIds" v-show="state.form.hasAllEquipment === 0">
                <el-collapse v-model="state.activeNames" style="width: 100%;">
                  <div v-for="(items, index) in state.equipmentOptions" :key="index">
                    <el-collapse-item>
                      <template #title>
                        <el-checkbox
                            v-model="items.check"
                            :indeterminate="items.isIndeterCheck"
                            @change="(val) => handleCheckAllChange(val,items)"
                            @click.stop
                            :value="items.name"
                        >{{ items.name }}
                        </el-checkbox>
                      </template>
                      <el-space wrap>
                        <el-checkbox-group
                            v-model="items.checkList"
                            @change="(value) =>  handleCheckedCitiesChange(value,items)"
                        >
                          <el-checkbox v-for="item in items.list" :value="item.equipmentId" :key="item.equipmentId">
                            {{ item.equipmentName }}
                          </el-checkbox>
                        </el-checkbox-group>
                      </el-space>
                    </el-collapse-item>
                  </div>
                </el-collapse>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </dialog-common>
</template>


<script setup>
// 定义属性，父组件可进行调用
import {detailPowerGroupAPI, saveOrUpdatePowerGroupAPI} from "@/api/settingSystem/powerGroup.js";
import {treeAPI} from "@/api/iotManagement/space.js";
import {ElMessage} from "element-plus";
import {listEquipmentsGroupByEquipmentTypeAPI} from "@/api/iotManagement/equipManage.js";

const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})
// 将属性解构为对象，进行调用
let {title} = toRefs(props)

// 声明父组件中的事件
let emits = defineEmits('onClose');

const dialog = ref()

// 表单引用
const ruleFormRef = ref()

const state = reactive({
  form: {},
  activeNames: [],
  spaceOptions: [],
  equipmentOptions: [],
  rules: {
    name: [
      {required: true, message: '权限组名称不能为空！', trigger: 'blur'}
    ]
  }
})


const open = (id) => {
  dialog.value.open()
  // 设置数据，否则只会设置默认值
  nextTick(() => {
    if (id) {
      detailPowerGroupAPI(id).then(res => {
        if (res.success) {
          state.form = res.data
        }
      })
    }
    querySpaceOptions()
    queryEquipments()
  })
}


// 查询空间列表
const querySpaceOptions = () => {
  treeAPI({deep: 4}).then(res => {
    if (res.success) {
      state.spaceOptions = res.data
    }
  })
}


const queryEquipments = () => {
  listEquipmentsGroupByEquipmentTypeAPI().then(res => {
    if (res.success) {
      console.log(res.data)
      state.equipmentOptions = Object.keys(res.data || {}).map(key => {
        let equipmentsGroup = res.data[key];
        let checkList = []
        if (equipmentsGroup && state.form.equipmentIds) {
          checkList = equipmentsGroup.map(equipment => equipment.equipmentId)
              .filter(equipmentId => {
                return state.form.equipmentIds.includes(equipmentId)
              })
        }
        let check = false;
        let isIndeterCheck = false
        if (checkList.length === equipmentsGroup.length) {
          check = true
        } else {
          if (checkList.length > 0) {
            isIndeterCheck = true
          }
        }
        return {
          name: key,
          list: equipmentsGroup,
          check: check,
          checkList: checkList,
          isIndeterCheck: isIndeterCheck
        }
      })
    }
  })
}

// 多选
const handleCheckAllChange = (val, item) => {
  item.checkList = val ? item.list.map(item => item.equipmentId) : []
  item.isIndeterCheck = false
}

// 单选
const handleCheckedCitiesChange = (value, item) => {
  const checkedCount = value.length
  item.check = checkedCount === item.list.length
  item.isIndeterCheck = checkedCount > 0 && checkedCount < item.list.length
}

// 提交保存
const submit = () => {
  let param = state.form
  param.equipmentIds = state.equipmentOptions.reduce((pre, item) => {
    return [...pre, ...(item.checkList || [])]
  }, [])
  console.log(param);
  saveOrUpdatePowerGroupAPI(param).then(res => {
    if (res.success) {
      ElMessage.success('保存成功！')
      dialog.value.close()
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

const close = () => {
  state.form.id = undefined
  state.form.spaceIds = []
  state.form.equipmentIds = []
  state.activeNames = []
}

// 向父组件暴露函数
defineExpose({
  open
})
</script>


<style scoped>
.divFlex {
  margin-bottom: 10px;
}

.divRight {
  margin-left: 8px;
  font-size: 16px;
  display: inline-block;
}

.divLeft {
  width: 7px;
  height: 20px;
  background-color: #3f9eff;
  display: inline-block;
  vertical-align: top;
}
</style>
