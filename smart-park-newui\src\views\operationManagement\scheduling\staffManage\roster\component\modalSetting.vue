<template>
  <dialog-common ref="dialog" title="设置" @submit="submit" :formRef="ruleFormRef" :width="450">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="130px" label-suffix=":">
      <el-form-item label="操作证到期前" prop="days">
        <el-input-number v-model="form.days" :min="1" style="width: 200px" step-strictly/>  <span class="tip">天提醒</span>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {ElMessage} from "element-plus";

import { rosterGetRemindAPI, rosterSetRemindAPI } from '@/api/operationManagement/roster.js'

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  rules: {
    days: [{required: true, message: '请输入天数'}],
  },
});

// 提交表单
const submit = () => {
  rosterSetRemindAPI({
    days:form.days,
  }).then(res => {
    if (res.success) {
      ElMessage.success('设置成功');
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = () => {
  dialog.value.open();
  rosterGetRemindAPI().then(res => {
    form.days = res.data
  })
}

defineExpose({
  form,
  open,
});
</script>

<style lang="less" scoped>
.tip{
  display: inline-block;
  margin-left: 12px;
}
</style>
