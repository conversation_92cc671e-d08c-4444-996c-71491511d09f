package com.soft.webadmin.config;

import com.soft.webadmin.interceptor.AuthenticationInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 所有的项目拦截器都在这里集中配置
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AuthenticationInterceptor())
                .addPathPatterns("/admin/**")
                .addPathPatterns("/core/**")
                .addPathPatterns("/system/**")
                .addPathPatterns("/equipment/**")
                .addPathPatterns("/energy/**")
                .addPathPatterns("/inspection/**")
                .addPathPatterns("/cablingSystem/**")
                .addPathPatterns("/check/**")
                .addPathPatterns("/sparePart/**")
                .addPathPatterns("/shifts/**")
                .addPathPatterns("/passage/**")
                .addPathPatterns("/knowledge/**")
                .addPathPatterns("/meeting/**")
                .addPathPatterns("/oa/**")
                .addPathPatterns("/visitor/**")
                .addPathPatterns("/complaint/**")
                .addPathPatterns("/contingency/**")
                .addPathPatterns("/hiddenDanger/**")
                .addPathPatterns("/om/equipment/**")
                .addPathPatterns("/message/record/**")
                .addPathPatterns("/wx/**")
                .addPathPatterns("/face/**");
    }
}
