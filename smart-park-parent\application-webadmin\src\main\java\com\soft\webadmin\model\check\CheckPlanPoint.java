package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.webadmin.vo.check.CheckPlanPointVO;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 检查计划点位对象 sp_check_plan_point
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@Data
@TableName(value = "sp_check_plan_point")
public class CheckPlanPoint {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 检查计划id */
    private Long planId;

    /** 检查点类型（EQUIPMENT设备，SPACE空间） */
    private String pointType;

    /** 设备id/空间id */
    private Long dataId;

    /** 模板id */
    private Long templateId;


    @Mapper
    public interface CheckPlanPointModelMapper extends BaseModelMapper<CheckPlanPointVO, CheckPlanPoint> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        CheckPlanPoint toModel(CheckPlanPointVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        CheckPlanPointVO fromModel(CheckPlanPoint entity);
    }

    public static final CheckPlanPointModelMapper INSTANCE = Mappers.getMapper(CheckPlanPointModelMapper.class);
}
