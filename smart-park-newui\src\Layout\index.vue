<template>
  <el-container class="layout">
    <el-header class="header">
      <page-header @routerChange="routerChange"></page-header>
    </el-header>
    <el-container>
      <el-aside>
        <aside-menu></aside-menu>
      </el-aside>
      <el-container class="is-vertical" >
        <thumb ref="thumbRef"></thumb>
        <el-main>
          <router-view v-slot="{ Component }" v-if="isRouterAlive">
            <transition name="fade" mode="out-in" appear>
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </el-container>
</template>

<script setup>
import pageHeader from './component/pageHeader.vue';
import asideMenu from './component/asideMenu.vue'
import thumb from './component/thumb.vue';

let thumbRef = ref()
let isRouterAlive = ref(true)

// 路由改变
const routerChange = (info) => {
  nextTick(() => {
    thumbRef.value.addTags(info.path,info.title)
  })
}

// 重新加载
const reload = () => {
  isRouterAlive.value = false
  nextTick(() => {
    isRouterAlive.value = true
  })
}

provide('reload',reload)
</script>

<style lang='less' scoped>
.layout{
  width: 100%;
  height: 100%;
  .header{
    padding-left: 10px;
  }
  .el-aside{
    width: auto;
  }
  .el-container{
    overflow: hidden;
  }
  .el-main{
    background-color: #F5F7FA;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease-in-out;
}

.fade-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.fade-enter-from{
  opacity: 0;
  transform: translateX(-20px);
}

</style>

<style lang="less">
.fade-enter-active,
.fade-leave-active {
  .el-zoom-in-center-enter-active,
  .el-zoom-in-center-leave-active {
    transition: all 0s ease-in-out;
  }
}
</style>
