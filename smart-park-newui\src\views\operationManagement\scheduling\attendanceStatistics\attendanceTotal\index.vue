<template>
  <page-common v-model="state.tableHeight" :operate-bool="false">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="showName">
          <el-input v-model="formInline.showName" placeholder="姓名"/>
        </el-form-item>
        <el-form-item prop="deptId">
          <el-cascader
              v-model="formInline.deptId"
              :clearable="true"
              :loading="state.deptInfo.impl.loading"
              :options="state.deptList"
              :props="{
              value: 'deptId',
              label: 'deptName',
              checkStrictly: true,
              emitPath: false
            }"
              placeholder="部门"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item prop="cycle">
          <el-date-picker
              v-model="formInline.cycle"
              type="month"
              value-format="YYYY-MM"
              placeholder="出勤周期"
              :clearable="false"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" row-key="id" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter"/>
      </el-table>
      <el-pagination
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="state.pagetion.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
    </template>
  </page-common>
</template>

<script setup>
import dayjs from "dayjs";

import { getDeptListAPI } from '@/api/settingSystem/user.js';
import { attendanceTotalPageAPI } from '@/api/operationManagement/attendanceTotal.js'

import {DropdownWidget} from "@/utils/widget.js";

/** 查询部门list */
const loadDeptDropdownList = () => {
  return new Promise((resolve, reject) => {
    getDeptListAPI({})
        .then((res) => {
          resolve(res.data.dataList);
        })
        .catch((e) => {
          reject(e);
        });
  });
}


let formInlineRef = ref()

const formInline = reactive({
  cycle:dayjs().format('YYYY-MM'),
})

const state = reactive({
  deptList: [],
  deptInfo: {
    impl: new DropdownWidget(loadDeptDropdownList, true, 'deptId'),
    value: [],
  },
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'showName',
      label: '姓名',
    },
    {
      prop: 'deptName',
      label: '部门'
    },
    {
      prop: 'postNames',
      label: '岗位'
    },
    {
      prop: 'cycle',
      label: '月份',

    },
    {
      prop: 'requiredDays',
      label: '应出勤天数',
    },
    {
      prop: 'realDays',
      label: '实际出勤天数'
    },
    {
      prop: 'neglectWorkDays',
      label: '旷工天数'
    },
    {
      prop: 'lostCardNum',
      label: '缺卡次数',

    },
    {
      prop: 'lateNum',
      label: '迟到次数',
    },
    {
      prop: 'totalDelayDuration',
      label: '迟到时长（秒）'
    },
    {
      prop: 'earlyDepartureNum',
      label: '早退次数'
    },
    {
      prop: 'totalEarlyDuration',
      label: '早退时长（秒）',
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

onMounted(() => {
  getList()

  state.deptInfo.impl.onVisibleChange(true).then((res) => {
    state.deptList = res;
  });
})

// 获取记录
const getList = () => {
  let query = {
    ...formInline ,
    pageNum: state.pagetion.pageNum,
    pageSize: state.pagetion.pageSize,
    businessType: 'OPERATIONS'
  }
  attendanceTotalPageAPI(query).then(res => {
    state.tableData = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getList()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}

// 页数改变
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getList()
}

// 页容量改变
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getList()
}
</script>

<style lang='less' scoped></style>
