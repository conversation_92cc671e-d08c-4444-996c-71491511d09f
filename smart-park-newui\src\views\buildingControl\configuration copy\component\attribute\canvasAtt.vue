<template>
  <div class="drawAttribute">
    <h4>画布属性</h4>
    <el-form label-width="75px" label-suffix=":">
      <el-form-item v-for="(key, index) in Object.keys(state.options)" :key="index" :label="state.options[key]">
        <el-color-picker v-if="key == 'backgroundColor'" v-model="canvasStyle[key]" show-alpha></el-color-picker>
        <el-input v-else v-model.number="canvasStyle[key]" type="number" />
      </el-form-item>
      <el-form-item label="画布比例">
        <el-input v-model.number="state.scale" @input="handleScaleChange" type="number" />
      </el-form-item>
      <el-form-item>
        <el-upload ref="upload" class="upload-demo" :file-list="state.fileList" :onChange="onChangeFile"
          :auto-upload="false">
          <template #trigger>
            <el-button type="primary">上传图片</el-button>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { changeComponentsSizeWithScale, changeStyleWithScale } from '@/utils/webtopo/math.js'

import { annexUpload } from '@/api/file.js'

import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { canvasStyle, componentData } = storeToRefs(webtopo)

const state = reactive({
  show: true,
  options: {
    width: '宽度',
    height: '高度',
    backgroundColor: '背景色',
    opacity: '透明度'
  },
  scale: 100
})

// 初始化画布
const initCanvas = () => {
  state.scale = canvasStyle.value.scale
}

initCanvas()

// 上传图片
const onChangeFile = (uploadFile) => {
  state.fileList = [uploadFile.raw]
  const reader = new FileReader()
  reader.readAsDataURL(state.fileList[0])

  reader.onload = () => {
    // 转换成base64格式
    const base64Img = reader.result

    // 获取图片高度
    let img = new Image();
    img.src = base64Img
    img.onload = function () {
      canvasStyle.value.width =  img.width
      canvasStyle.value.height =  img.height
      canvasStyle.value.backgroundSize = `${changeStyleWithScale(img.width,canvasStyle.value.scale)}px ${changeStyleWithScale(img.height,canvasStyle.value.scale)}px`

      console.log(img.width)
      console.log(canvasStyle.value)
    }
  }


  let formData = new FormData()
  formData.append('file',state.fileList[0])
  annexUpload(formData).then(res => {
    canvasStyle.value.backgroundImage = res.data.filePath
  })
}


// 画布百分比
const handleScaleChange = () => {
  clearTimeout(state.timer)
  state.timer = setTimeout(() => {
    if (parseInt(state.scale) > 10 && parseInt(state.scale) < 200)
    canvasStyle.value.scale = changeComponentsSizeWithScale(canvasStyle.value,componentData.value,state.scale ,canvasStyle.value.scale )
  }, 1000)
}

watch(() => canvasStyle.value.scale, (newVal) => {
  state.scale = newVal
})
</script>

<style lang='less' scoped>
.drawAttribute {
  height: 100%;
  background-color: #FBFBFB;
  padding: 10px;
  border-left: 1px solid #dcdfe6;

  h4 {
    letter-spacing: 1.1px;
    color: #2E2E2E;
    text-align: center;
    margin-bottom: 10px;
    font-weight: 500;
  }
}

:deep(.el-upload-list--text) {
  display: none;
}
</style>
