<template>
  <dialog-common ref="dialog" title="处理" @submit="submit" @onClose="onClose" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="110px" :model="form" :rules="state.rules" label-suffix=":">
      <el-form-item label="处理结果" prop="status">
        <el-select v-model="form.status" filterable clearable placeholder="请选择处理结果">
          <el-option v-for="(value,key) in state.statusOptions" :label="value" :value="Number(key)"/>
        </el-select>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { ElMessage } from 'element-plus'

import { warningDealAPI } from '@/api/comprehensiveSecurity/earlyWarning.js'

const emit = defineEmits(['submit'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({})

const state = reactive({
  statusOptions: {
    1:'误报',
    2:'已解决'
  },
  rules: {
    status: [{ required: true, message: '请选择处理结果', trigger: 'blur' },],
  },
})

const open = () => {
  dialog.value.open()
}

// 关闭
const onClose = () => {
  form.id = ''
}

// 提交
const submit = () => {
  warningDealAPI({...form}).then((res) => {
    if (res.success) {
      ElMessage.success('保存成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}


defineExpose({
  form,
  open
})
</script>

<style lang="less" scoped>
</style>
