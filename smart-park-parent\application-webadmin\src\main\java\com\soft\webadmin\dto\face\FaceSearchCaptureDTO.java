package com.soft.webadmin.dto.face;


import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class FaceSearchCaptureDTO extends MyPageParam {

    @ApiModelProperty("人脸图片")
    private String faceImage;

    @ApiModelProperty("设备编号")
    private List<String> equipmentNos;

    @ApiModelProperty("相似度，1-100")
    private Integer similarity;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

}
