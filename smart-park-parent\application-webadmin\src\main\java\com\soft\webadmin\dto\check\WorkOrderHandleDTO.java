package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * WorkOrderDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@ApiModel("WorkOrderHandleDTO对象")
@Data
public class WorkOrderHandleDTO {

    @ApiModelProperty(value = "工单id")
    @NotNull(message = "工单id不能为空！")
    private Long id;

    @ApiModelProperty(value = "过程描述")
    @NotNull(message = "过程描述不能为空！")
    private String describe;

    @ApiModelProperty(value = "附件")
    private String img;

    @ApiModelProperty(value = "备件领用信息")
    @Valid
    private List<WorkOrderSparePartDTO> sparePartList;

    @ApiModelProperty(value = "备注")
    private String remarks;

}
