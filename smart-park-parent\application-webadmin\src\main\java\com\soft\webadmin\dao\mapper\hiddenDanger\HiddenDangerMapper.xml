<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.hiddenDanger.HiddenDangerMapper">
    <resultMap type="com.soft.webadmin.model.hiddenDanger.HiddenDanger" id="HiddenDangerResult">
        <result property="id" column="id" />
        <result property="type" column="type" />
        <result property="level" column="level" />
        <result property="spaceId" column="space_id" />
        <result property="spacePath" column="space_path" />
        <result property="spaceFullName" column="space_full_name" />
        <result property="specificCircumstance" column="specific_circumstance" />
        <result property="reportImgs" column="report_imgs" />
        <result property="whetherRectification" column="whether_rectification" />
        <result property="inspectionUserId" column="inspection_user_id" />
        <result property="handleUserId" column="handle_user_id" />
        <result property="status" column="status" />
        <result property="deletedFlag" column="deleted_flag" />
        <result property="createUserId" column="create_user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateUserId" column="update_user_id" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectHiddenDangerVo">
        t.id, t.type, t.level, t.space_id, t.space_path, t.space_full_name, t.specific_circumstance, t.report_imgs, t.whether_rectification, t.inspection_user_id, t.handle_user_id, t.status, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="queryList" resultType="com.soft.webadmin.vo.hiddenDanger.HiddenDangerVO">
        select <include refid="selectHiddenDangerVo" />, u.show_name create_user_name
        from sp_hidden_danger t
        left join common_sys_user u on t.create_user_id = u.user_id
        <where>
            and t.deleted_flag = 1
            <if test="type != null">
                and t.type = #{type}
            </if>
            <if test="level != null">
                and t.level = #{level}
            </if>
            <if test="spaceId != null">
                and t.space_path like concat('%', #{spaceId}, '%')
            </if>
            <if test="status != null">
                and t.status = #{status}
            </if>
            <if test="beginDate != null and beginDate != ''">
                and date_format(t.create_time, '%Y-%m-%d') &gt;= date_format(#{beginDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and date_format(t.create_time, '%Y-%m-%d') &lt;= date_format(#{endDate}, '%Y-%m-%d')
            </if>
            <if test="createUserName != null and createUserName != ''">
                and u.show_name like concat('%', #{createUserName},'%')
            </if>
            <if test="createUserId != null">
                and t.create_user_id = #{createUserId}
            </if>
            <if test="handleUserId != null">
                and t.handle_user_id = #{handleUserId}
            </if>
        </where>
        order by t.create_time desc
    </select>
    
</mapper>