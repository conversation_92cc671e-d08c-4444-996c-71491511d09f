package com.soft.webadmin.dao.shifts;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.shifts.ShiftsRecordQueryDTO;
import com.soft.webadmin.model.shifts.ShiftsRecord;
import com.soft.webadmin.vo.shifts.ShiftsRecordVO;

import java.util.List;

/**
 * 排班记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface ShiftsRecordMapper extends BaseMapper<ShiftsRecord> {

    /**
     * 查询
     * @param queryDTO
     * @return
     */
    List<ShiftsRecordVO> queryList(ShiftsRecordQueryDTO queryDTO);

    /**
     * 批量插入
     * @param recordList
     */
    void batchInsert(List<ShiftsRecord> recordList);
}
