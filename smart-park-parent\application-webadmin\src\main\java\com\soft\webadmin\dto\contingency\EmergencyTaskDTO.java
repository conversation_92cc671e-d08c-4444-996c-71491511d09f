package com.soft.webadmin.dto.contingency;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * EmergencyTaskDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EmergencyTaskDTO对象")
@Data
public class EmergencyTaskDTO {

    @ApiModelProperty(value = "任务名称")
    @NotBlank(message = "任务名称不能为空！")
    private String taskName;

    @ApiModelProperty(value = "应急小组id")
    private Long groupId;

}
