import { request } from '@/utils/request';

// 分页查询
export const stockPageAPI = (query) => {
    return request('get', '/sparePart/stocktaking/getPage', query, 'F');
};


// 保存
export const stockSaveAPI = (data) => {
    return request('post', '/sparePart/stocktaking/saveOrUpdate', data);
};

// 详情
export const stockDetailAPI = (query) => {
    return request('get', '/sparePart/stocktaking/detail', query, 'F');
}

// 删除
export const stockDeleteAPI = (query) => {
    return request('post', '/sparePart/stocktaking/detele', query, 'F');
}

// 入库
export const stockIntoAPI = (query) => {
    return request('post', '/sparePart/stocktaking/putIn', query, 'F');
}

// 出库
export const stockOutAPI = (query) => {
    return request('post', '/sparePart/stocktaking/out', query, 'F');
}

// 导入
export const stockUploadAPI = (data) => {
    return request('post', '/sparePart/stocktaking/importExcel?businessType=OPERATIONS', data);
}
