package com.soft.webadmin.enums;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.sub.dao.equipment.EquipmentMapper;
import org.springframework.util.ClassUtils;

import java.beans.Introspector;

public enum SpaceBusiTypeEnums {

    EQUIPMENT_IOT(EquipmentMapper.class),
    ;


    SpaceBusiTypeEnums(Class<? extends BaseMapper<?>> mapperClazz) {
        this.mapperClazz = mapperClazz;
    }

    private final Class<? extends BaseMapper<?>> mapperClazz;

    /**
     * 获取 Mapper 实现类的类名
     *
     * @return
     */
    public String getMapperClazzName() {
        return Introspector.decapitalize(ClassUtils.getShortName(mapperClazz));
    }

    /**
     * 获取 Mapper 类
     *
     * @return
     */
    public Class<? extends BaseMapper<?>> getMapperClazz() {
        return this.mapperClazz;
    }

    /**
     * 获取 Mapper 实例
     *
     * @return
     */
    public BaseMapper<?> getMapper() {
        return SpringUtil.getBean(getMapperClazzName(), getMapperClazz());
    }
}
