<template>
  <div style="height: 100%;overflow: hidden;">
    <transition name="el-zoom-in-center">
      
      <el-tabs v-if="showAllTab"  v-model="activeName" class="demo-tabs" @tab-change="onTabChange" v-show="pageIndex == 0">
        <el-tab-pane label="设备信息" name="first">
          <tableListEquip @showPage="showPage" :equipmentTypeId="props.equipmentType.id"></tableListEquip>
        </el-tab-pane>
        <el-tab-pane label="设备组控" name="second">
          <equipment-group-control ref="equipmentGroupControlRef" :group-control-type="props.groupControlType" />
        </el-tab-pane>
        <el-tab-pane label="告警记录" name="third">
          <alarm-record ref="alarmRecordRef" :equipment-type="props.equipmentType" />
        </el-tab-pane>
      </el-tabs>

      <tableListEquip v-else v-show="pageIndex == 0" @showPage="showPage" :equipmentTypeId="props.equipmentType.id"></tableListEquip>
    </transition>
    <transition name="el-zoom-in-center">
      <detail v-show="pageIndex == 1" @showPage="showPage" ref="detailRef"></detail>
    </transition>
  </div>
</template>

<script setup>
import tableListEquip from './tableListEquip.vue'
import equipmentGroupControl from "./equipmentGroupControl.vue";
import alarmRecord from "./alarmRecord.vue";
import detail from '@/components/deviceDetail/index.vue'

import { events } from "@/utils/bus.js";

const equipmentGroupControlRef = ref()
const alarmRecordRef = ref()
const detailRef = ref()

const activeName = ref('first')
const pageIndex = ref(0)

const props = defineProps({
  equipmentType: {
    type: Object,
    default: {
      id: '',
      name: ''
    }
  },
  groupControlType: {
    type: Object,
    default: {
      name: '',
      type: ''
    }
  },
  showAllTab:{
    type: Boolean,
    default: true
  }
})


const onTabChange = () => {
  nextTick(() => {

    if (activeName.value === 'second') {
      equipmentGroupControlRef.value.init()
    } else if (activeName.value === 'third') {
      alarmRecordRef.value.init()
    }

    events.emit('tabClick')
  })
}

const showPage = (index, equipmentId) => {
  pageIndex.value = index

  if (index == 0) { //刷新
    if (activeName.value === 'second') {
      equipmentGroupControlRef.value.init()
    } else if (activeName.value === 'third') {
      alarmRecordRef.value.init()
    }
  } else if (index == 1) {
    detailRef.value.equipmentId = equipmentId
    detailRef.value.init();
  }
}
</script>
<style lang='less' scoped>
.el-tabs {
  height: 100%;

  :deep(.el-tabs__content) {
    height: calc(100% - 55px);

    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>