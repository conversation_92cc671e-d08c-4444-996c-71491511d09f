package com.soft.webadmin.dto.contingency;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * EarlyWarningDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EarlyWarningDTO对象")
@Data
public class EarlyWarningDTO {

    @ApiModelProperty(value = "应急事件id")
    @NotNull(message = "应急事件id不能为空")
    private Long eventId;

    @ApiModelProperty(value = "类别：1真实事件、2应急演练", hidden = true)
    private Integer type = 1;

    @ApiModelProperty(value = "位置id")
    @NotNull(message = "位置不能为空")
    private Long spaceId;

    @ApiModelProperty(value = "详细位置")
    private String location;

    @ApiModelProperty(value = "事件描述")
    @NotBlank(message = "事件描述不能为空")
    private String description;

    @ApiModelProperty(value = "上报人id")
    private Long reportUserId;

    @ApiModelProperty(value = "联系电话")
    private String reportUserPhone;

}
