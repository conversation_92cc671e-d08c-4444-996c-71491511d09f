<template>
  <el-drawer :modelValue="drawer" :before-close="cancelClick" size="750">
    <template #header>
      <h4>巡检点信息</h4>
    </template>
    <template #default>
      <el-form ref="ruleFormRef" :model="form" label-width="100px" label-suffix=":">
        <div>
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">基本信息</div>
          </div>
          <el-row v-if="form.pointType === 'EQUIPMENT'">
            <el-col :span="12">
              <el-form-item label="设备编号" prop="equipmentNo">
                {{ form.equipment.equipmentNo }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备名称" prop="equipmentName">
                {{ form.equipment.equipmentName }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="form.pointType === 'EQUIPMENT'">
            <el-col>
              <el-form-item label="设备位置" prop="spaceFullName">
                {{ form.equipment.spaceFullName }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="form.pointType === 'SPACE'">
            <el-col>
              <el-form-item label="空间位置" prop="dataName">
                {{ form.dataName }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div>
          <div class="divFlex">
            <div class="divLeft"></div>
            <div class="divRight">检查项目</div>
          </div>
          <el-table :data="form.templateItemList">
            <el-table-column label="序号" type="index" width="60" />
            <el-table-column v-for="column in state.columnList" :label="column.label" :prop="column.prop" :key="column.prop">
              <template #default="scope">
                <span v-show="scope.$index !== editIndex">{{ scope.row[column.prop] }}</span>
                <el-input v-show="scope.$index === editIndex" v-model="scope.row[column.prop]" />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup>
const props = defineProps({
  drawer: {
    type: Boolean,
    default: false,
  },
});
let { drawer } = toRefs(props);
const emit = defineEmits(['cancelClick']);
const form = ref({
  equipment: {},
});
const state = reactive({
  columnList: [
    { prop: 'itemName', label: '检查项' },
    { prop: 'itemContent', label: '检查内容' },
  ],
});

const cancelClick = () => {
  emit('cancelClick');
};

defineExpose({
  form,
});
</script>

<style lang="less" scoped>
</style>
