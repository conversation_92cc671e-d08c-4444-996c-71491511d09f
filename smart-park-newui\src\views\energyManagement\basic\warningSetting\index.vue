<template>
  <tree-page-common v-model="state.tableHeight">
    <template #leftTree>
      <el-input placeholder="请输入关键词" v-model="filterText" clearable />
      <el-tree ref="treeRef" class="filter-tree" :data="state.treeData" node-key="id" :props="treeProps"
        style="margin-top: 10px" default-expand-all show-checkbox @change="handleCheckChange"
        :filter-node-method="filterNode" />
    </template>
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="equipmentWord">
          <el-input v-model="formInline.equipmentWord" placeholder="设备名称或编号" />
        </el-form-item>
        <el-form-item prop="equipmentType">
          <el-select v-model="formInline.equipmentType" placeholder="设备类型">
            <el-option v-for="item in equipmentTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="isSet">
          <el-select v-model="formInline.isSet" placeholder="是否已设置阀值">
            <el-option label="是" value="true" />
            <el-option label="否" value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column prop="equipmentNo" label="设备编号" />
        <el-table-column prop="equipmentName" label="设备名称" />
        <el-table-column prop="energyName" label="设备分类" />
        <el-table-column prop="equipmentType" label="设备类型" />
        <el-table-column prop="spaceFullName" label="安装位置" />
        <el-table-column prop="dayUpperLimit" label="日用量上限" />
        <el-table-column prop="monthUpperLimit" label="月用量上限" />
        <el-table-column align="center" label="操作" width="230">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link icon="Delete" type="danger" @click="clearHandle(scope.row)">清除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize" :total="state.pageParam.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <!-- 设置 -->
      <modal-page ref="modal" @submit="getList" />
    </template>
  </tree-page-common>
</template>

<script setup>
import { treeAPI } from '@/api/iotManagement/space.js';
import { getEnergyPageAPI, deleteWarningSettingAPI } from '@/api/iotManagement/warningSetting.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import modalPage from './component/modalPage.vue';

const modal = ref();
const treeRef = ref();
const filterText = ref('');
const formInlineRef = ref();
const formInline = reactive({});

const treeProps = {
  children: 'children',
  label: 'name',
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.includes(value);
};

const handleCheckChange = () => {
  getList();
};

watch(filterText, (val) => {
  treeRef.value.filter(val);
});

const equipmentTypeOptions = ref([
  { value: '水表', label: '水表' },
  { value: '电表', label: '电表' },
  { value: '气表', label: '气表' },
]);

const state = reactive({
  treeData: [],
  tableData: [],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  title: '',
  drawer: false,
});

onMounted(() => {
  getSpaceTree();
  getList();
});

const getSpaceTree = () => {
  treeAPI({ deep: 4 }).then((res) => {
    if (res.success) {
      state.treeData = res.data;
    }
  });
};

const getList = () => {
  let spaceIdArray = [];
  if (treeRef.value) {
    treeRef.value.getCheckedNodes().forEach((e) => {
      spaceIdArray.push(e.id);
    });
  }
  let params = {
    equipmentType: formInline.equipmentType,
    equipmentWord: formInline.equipmentWord,
    isSet: formInline.isSet,
    spaceIdList: spaceIdArray.length > 0 ? spaceIdArray.join(',') : undefined,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };
  getEnergyPageAPI(params).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
    state.tableData.map((item) => {
      if (item.ruleJson) {
        const ruleJson = JSON.parse(item.ruleJson);
        item.dayUpperLimit = ruleJson.dayUpperLimit;
        item.monthUpperLimit = ruleJson.monthUpperLimit;
      }
    });
  });
};

/** 预警设置 */
const editHandle = (row) => {
  modal.value.open();
  nextTick(() => {
    Object.assign(modal.value.form, row);
    modal.value.form.id = row.warningConfigId;
    modal.value.form.relationEquipmentIdList = [row.equipmentId];
  });
};

/** 清除预警设置 */
const clearHandle = (row) => {
  ElMessageBox.confirm('是否清除当前预警设置?', '提醒', {
    type: 'warning',
  }).then(() => {
    deleteWarningSettingAPI({ id: row.warningConfigId }).then((res) => {
      if (res.success) {
        ElMessage.success('预警设置清除成功');
        getList();
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};
</script>
