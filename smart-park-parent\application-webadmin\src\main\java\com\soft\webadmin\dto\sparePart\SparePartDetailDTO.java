package com.soft.webadmin.dto.sparePart;

import com.soft.common.core.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * SparePartDetailDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartDetailDTO对象")
@Data
public class SparePartDetailDTO {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "数据验证失败，主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "备件id")
    private Long sparePartId;

    @ApiModelProperty(value = "所在仓库")
    private Long storehouseId;

    @ApiModelProperty(value = "当前库存")
    private Integer inventoryQuantity;

    @ApiModelProperty(value = "删除标记(1: 正常 -1: 已删除)")
    private Integer deletedFlag;

    @ApiModelProperty(value = "创建者id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者id")
    private Long updateUserId;

    @ApiModelProperty(value = "最后更新时间")
    private Date updateTime;

}
