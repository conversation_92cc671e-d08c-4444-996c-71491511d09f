<template>
  <div>
    <img :src="imgTransfer(element.image)" v-if="element.image">
    <span class="iconfont icon-charutupian picture" :style="{ fontSize: `${element.style.width}px` }" v-else>
      <el-upload ref="upload" class="upload-demo" :auto-upload="false" :file-list="state.fileList" :onChange="onChangeFile">
        <template #trigger>
          <el-button type="primary" :style="{ width: `${element.style.width - 2}px`,height: `${element.style.width - 2}px`}"></el-button>
        </template>
      </el-upload>
    </span>
  </div>
</template>

<script setup>
import { annexUpload } from '@/api/file.js'

import { webtopoStore } from '@/store/modules/webtopo.js'

const webtopo = webtopoStore()

let { curComponent } = storeToRefs(webtopo)

const props = defineProps({
  element: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

let { element } = toRefs(props)

const state = reactive({
  fileList:[]
})

const imgTransfer = (name) => {
  return import.meta.env.VITE_BASE_URL + name
}

// 上传图片
const onChangeFile = (uploadFile) => {
  state.fileList = [uploadFile.raw]
  const reader = new FileReader()
  reader.readAsDataURL(state.fileList[0])

  reader.onload = () => {
    // 转换成base64格式
    const base64Img = reader.result

    // 获取图片高度
    let img = new Image();
    img.src = base64Img
    img.onload = function () {
      curComponent.value.style.width = img.width
      curComponent.value.style.height = img.height
    }
  }


  let formData = new FormData()
  formData.append('file',state.fileList[0])
  annexUpload(formData).then(res => {
    curComponent.value.image = res.data.filePath
  })
}

</script>

<style lang='less' scoped>

img{
  width: 100%;
  height: 100%;
  border-radius: inherit;
}
.picture{
  display: inline-block;
  position: relative;
  .upload-demo{
    position: absolute;
    top: 1px;
    left: 1px;
    opacity: 0;
  }
}

:deep(.el-upload-list--text) {
  display: none;
}
</style>
