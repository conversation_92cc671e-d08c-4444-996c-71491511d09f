<template>
  <dialog-common ref="dialog" :title="props.title" @submit="submit" @onClose="onClose"  :formRef="ruleFormRef" :width="450">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="120px" label-suffix=":">
      <el-form-item label="班次名称" prop="shiftsName">
        <el-input v-model="form.shiftsName" placeholder="请输入班次名称"/>
      </el-form-item>
      <el-form-item label="出勤开始时间" prop="startTime">
        <el-time-picker
            v-model="form.startTime"
            placeholder="请选择出勤开始时间"
            value-format="HH:mm"
            format="HH:mm"
        />
      </el-form-item>
      <el-form-item label="出勤结束时间" prop="endTime">
        <el-time-picker
            v-model="form.endTime"
            placeholder="请选择出勤结束时间"
            value-format="HH:mm"
            format="HH:mm"
        />
      </el-form-item>
      <el-form-item label="标签颜色" prop="color">
        <el-color-picker v-model="form.color" />
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {ElMessage} from "element-plus";

import { settingSaveAPI } from '@/api/operationManagement/workforceSetting.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
})

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();

const form = reactive({});
const state = reactive({
  rules: {
    shiftsName: [{required: true, message: '请输入班次名称'}],
    startTime: [{required: true, message: '请选择出勤开始时间'}],
    endTime: [{required: true, message: '出勤结束时间'}]
  },
});

// 关闭dialog
const onClose = () => {
  delete form.id
}

// 提交表单
const submit = () => {
  settingSaveAPI({
    ...form,
    businessType: 'OPERATIONS',
  }).then(res => {
    if (res.success) {
      ElMessage.success(form.id ? '保存成功' : '新增成功');
      dialog.value.close()
      emit('submit')
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

const open = () => {
  dialog.value.open();
}

defineExpose({
  form,
  open,
});
</script>
