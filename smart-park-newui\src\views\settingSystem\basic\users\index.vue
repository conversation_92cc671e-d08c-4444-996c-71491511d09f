<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="showName">
          <el-input v-model="formInline.showName" placeholder="用户姓名" />
        </el-form-item>
        <el-form-item prop="roleId">
          <el-select v-model="formInline.roleId" placeholder="用户角色" clearable>
            <el-option v-for="role in state.roleOptions" :key="role.roleId" :label="role.roleName"
              :value="role.roleId" />
          </el-select>
        </el-form-item>

        <el-form-item prop="deptId">
          <el-cascader v-model="formInline.deptId" :clearable="true" placeholder="所属组织"
            :loading="state.deptInfo.impl.loading" :props="{
              value: 'deptId',
              label: 'deptName',
              checkStrictly: true,
              emitPath: false
            }" :options="state.deptList">
          </el-cascader>
        </el-form-item>

        <el-form-item prop="postId">
          <el-select v-model="formInline.postId" placeholder="岗位" clearable>
            <el-option v-for="post in state.postOptions" :key="post.postId" :label="post.postName"
              :value="post.postId" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" :icon="Plus" @click="addHandle">新建用户</el-button>
      <el-button type="primary" :icon="Refresh" @click="syncHandle">OA同步</el-button>
      <!-- <el-button type="primary" :icon="Promotion" @click="pushHandle">推送数据到海康</el-button> -->
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column label="序号" align="center" type="index" width="55" />
        <el-table-column prop="showName" label="用户姓名" />
        <el-table-column prop="phone" label="手机号码" />
        <el-table-column prop="shortPhone" label="短号" />
        <el-table-column prop="loginName" label="登录账号" />
        <el-table-column prop="sex" label="性别">
          <template #default="{ row }">
            <span v-if="row.sex == 1">男</span>
            <span v-else-if="row.sex == 2">女</span>
            <span v-else-if="row.sex == 3">未知</span>
          </template>
        </el-table-column>
        <el-table-column prop="userStatus" label="状态">
          <template #default="scope">
            <!-- 用户状态(0: 正常 1: 锁定) -->
            <el-switch v-model="scope.row.userStatus" class="ml-2" :active-value="0" :inactive-value="1"
              @change="onChangeUserStatus(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column prop="roleNames" label="用户角色" />
        <el-table-column prop="deptName" label="所属组织" />
        <el-table-column prop="postNames" label="岗位" />

        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" align="center" width="300">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link icon="Delete" type="danger" @click="deleteHandle(scope.row)">删除</el-button>
            <el-button link icon="Refresh" type="primary" @click="resetPassword(scope.row)">重置密码</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize" :total="state.pageParam.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <!-- 新建、编辑用户Dialog -->
      <modal-page ref="modal" :title="state.title" @submit="onQuery"></modal-page>

      <el-image-Viewer :url-list="state.srcList" hide-on-click-modal v-if="state.imageVisible"
        @close="state.imageVisible = false">
      </el-image-Viewer>
    </template>
  </page-common>
</template>

<script setup>
import {
  listUsersAPI,
  updateUserStatusAPI,
  getDeptListAPI,
  deleteUserAPI,
  resetPasswordAPI,
} from '@/api/settingSystem/user.js';
import { roleListAPI } from '@/api/settingSystem/role.js';
import { getPageAPI as getPost } from '@/api/settingSystem/post.js';
import { syncAll, pushAllToHikVision } from "@/api/settingSystem/oa.js";
import { Plus, Search, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref } from 'vue';
import { calcPageNo } from '@/utils/util.js';
import { DropdownWidget } from '@/utils/widget.js';
import modalPage from './component/modalPage.vue';

const modal = ref();
const formInlineRef = ref();
const formInline = reactive({
  showName: ''
});
const state = reactive({
  srcList: [],
  imageVisible: false,
  roleOptions: [],
  postOptions: [],
  deptList: [],
  tableData: [],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  deptInfo: {
    impl: new DropdownWidget(loadDeptDropdownList, true, 'deptId'),
    value: [],
  },
  title: '',
});

onMounted(() => {
  onQuery()
  queryRoles()
  queryPosts()
  state.deptInfo.impl.onVisibleChange(true).then((res) => {
    state.deptList = res;
  });
});


// 查询角色列表
const queryRoles = () => {
  roleListAPI({}).then(res => {
    if (res.success) {
      state.roleOptions = res.data.dataList
    }
  })
}


// 查询岗位列表
const queryPosts = () => {
  getPost({}).then(res => {
    if (res.success) {
      state.postOptions = res.data.dataList
    }
  })
}


/** 查询部门list */
function loadDeptDropdownList() {
  return new Promise((resolve, reject) => {
    getDeptListAPI({})
      .then((res) => {
        resolve(res.data.dataList);
      })
      .catch((e) => {
        reject(e);
      });
  });
}



// 查询用户列表
const onQuery = () => {
  let param = {}
  Object.assign(param, formInline)
  param.pageNum = state.pageParam.pageNum
  param.pageSize = state.pageParam.pageSize
  listUsersAPI(param).then(res => {
    if (res.success) {
      state.tableData = res.data.dataList;
      state.pageParam.total = res.data.totalCount;
    }
  })
}

// 修改用户状态
const onChangeUserStatus = (row) => {
  updateUserStatusAPI(row.userId).then(res => {
    if (res.success) {
      onQuery()
    }
  })
}



// 修改后回调方法
const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  onQuery();
};

// 重置
const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  onQuery();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  onQuery();
};

/** 创建用户 */
const addHandle = () => {
  state.title = '新建用户';
  modal.value.open(null);
};

/**
 * OA同步
 */
const syncHandle = () => {
  syncAll().then((res) => {
    ElMessage.success('数据开始同步');
  });
};

/**
 * 推送数据给海康
 */
const pushHandle = () => {
  pushAllToHikVision().then((res) => {
    ElMessage.success('数据推送成功');
  });
};

/** 编辑用户 */
const editHandle = (row) => {
  state.title = '编辑用户';
  modal.value.open(row.userId);
};

/** 删除用户 */
const deleteHandle = (row) => {
  ElMessageBox.confirm('是否删除当前用户?', '提醒', {
    type: 'warning',
  }).then(() => {
    state.pageParam.pageNum = calcPageNo(state.pageParam.total, state.pageParam.pageNum, state.pageParam.pageSize);
    deleteUserAPI({ userId: row.userId }).then((res) => {
      ElMessage.success('删除成功');
      onQuery();
    });
  });
};

/** 重置密码 */
const resetPassword = (row) => {
  ElMessageBox.confirm('是否重置用户' + row.showName + '的密码?', '提醒', {
    type: 'warning',
  }).then(() => {
    resetPasswordAPI({ userId: row.userId }).then((res) => {
      ElMessage.success('重置密码成功');
    });
  });
};
</script>

<style lang="less" scoped>
.el-dropdown-link {
  margin-left: 10px;
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  line-height: 22px;

}
</style>
