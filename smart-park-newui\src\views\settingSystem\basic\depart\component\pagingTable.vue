<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRefPost" :model="formInlinePost" label-suffix=":">
        <el-form-item prop="postName">
          <el-input v-model="formInlinePost.postName" placeholder="岗位名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmitPost">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onResetPost">重置</el-button>
        </el-form-item>
      </el-form>
      <div link type="primary" @click="back" class="page-close-box">
        <img src="@/assets/img/back.png" alt="">
        <span>返回</span>
      </div>
    </template>
    <template #operate>
      <el-button type="primary" :icon="Plus" @click="postSetting">岗位关联</el-button>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableDatePost" row-key="deptId">
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="postName" label="岗位名称" />
        <el-table-column prop="sysDeptPost.postShowName" label="岗位别名">
          <template #default="scope">
            <el-input v-model="scope.row.sysDeptPost.postShowName" placeholder="请输入" clearable
              v-if="scope.row.postShowNameShow"></el-input>
          </template>
        </el-table-column>
        <el-table-column v-for="(item, index) in state.tableHeader1" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" />
        <el-table-column prop="" label="领导岗位">
          <template #default="scope">
            <el-tag :type="scope.row.leaderPost ? 'success' : 'danger'">{{
              scope.row.leaderPost ? "是" : "否"
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="230">
          <template #default="scope">
            <div v-if="!state.postShowNameAddButton">
              <el-button link type="primary" icon="Edit" @click="updateName(scope.row)">修改别名</el-button>
              <el-button link type="danger" icon="Delete" @click="deletePost(scope.row)">移除</el-button>
            </div>
            <div v-if="state.postShowNameAddButton && scope.row.postShowNameShow">
              <el-button link type="primary" icon="Edit" @click="confirm(scope.row)">保存</el-button>
              <el-button link type="danger" icon="Delete" @click="cancel(scope.row)">取消</el-button>
            </div>
          </template>
        </el-table-column>·
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />

      <dialog-common @submit="submit" ref="dialogPostSetting" title="岗位关联" :formRef="formInlineRefPostSetting" :width="900"
        :showButton="true">
        <el-form :inline="true" ref="formInlineRefPostSetting" :model="formInlinePostSetting" label-suffix=":">
          <el-form-item prop="postName">
            <el-input v-model="formInlinePostSetting.postName" placeholder="岗位名称" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="onSubmitPostSetting">查询</el-button>
            <el-button type="primary" :icon="Refresh" @click="onResetPostSetting">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table :height="400" :data="state.PostSetting" row-key="deptId" @selection-change="handleSelectionChange">
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column type="selection" width="55" />
          <el-table-column prop="postName" label="岗位名称" />

          <el-table-column v-for="(item, index) in state.tableHeader1" :key="index" :prop="item.prop"
            :label="item.label" :align="item.align" :formatter="item.formatter" />
          <el-table-column prop="" label="领导岗位">
            <template #default="scope">
              <el-tag :type="scope.row.leaderPost ? 'success' : 'danger'">{{
                scope.row.leaderPost ? "是" : "否"
              }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pagetionPostSetting.pageNum" :page-size="state.pagetionPostSetting.pageSize"
          :total="state.pagetionPostSetting.total" @size-change="sizeChangePostSetting"
          @current-change="currentChangePostSetting" />
      </dialog-common>
    </template>
  </page-common>
</template>

<script setup>

import {
  Delete, Plus, Search, Refresh
} from '@element-plus/icons-vue'
import { listSysDeptPost, updateSysDeptPost, deleteSysDeptPost, listNotInSysDeptPost, addSysDeptPost } from '@/api/settingSystem/post.js'

import { ElTag, ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  deptId: {
    type: String,
    default: ''
  }
})

let { show, deptId } = toRefs(props)

const emit = defineEmits(['backOn'])

const formInlinePostSetting = reactive({})

let formInlineRefPost = ref()

const state = reactive({
  tableHeight: 100,
  postSettingList:[],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
  pagetionPostSetting: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  },
})

const formInlinePost = reactive({})
let formInlineRefPostSetting = ref()

let dialogPostSetting = ref()

//返回方法
const back = () => {
  emit('backOn')
}
//dialog2
// 岗位分页
const getPostList = () => {
  let query = {
    deptId: deptId.value,
    orderParam: [{ fieldName: 'postLevel', asc: 1 }],
    pageParam: state.pagetion,
    sysPostDtoFilter: formInlinePost
  }
  listSysDeptPost(query).then(res => {
    state.tableDatePost = res.data.dataList
    state.pagetion.total = res.data.totalCount * 1
  })
}

// 保存
const confirm = (row) => {
  let query = {
    sysDeptPostDto: row.sysDeptPost
  }
  updateSysDeptPost(query).then(res => {
    if (res.success) {
      ElMessage.success('修改成功')
      getPostList()
      state.postShowNameAddButton = false
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

// 取消
const cancel = (row) => {
  state.postShowNameAddButton = false
  row.postShowNameShow = false
}
// 重置
const onResetPost = () => {
  formInlineRefPost.value.resetFields()
  onSubmitPost()
}

// 查询
const onSubmitPost = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getPostList()
}

// 岗位设置
const postSetting = () => {
  getPostListPostSetting()
  dialogPostSetting.value.open()
}

// 修改别名
const updateName = (row) => {
  state.postShowNameAddButton = true
  row.postShowNameShow = true
}

// 删除岗位
const deletePost = (info) => {

  ElMessageBox.confirm(
    '是否移除当前岗位?',
    '提醒',
    {
      type: "warning"
    }
  ).then(() => {
    deleteSysDeptPost({ deptId: info.sysDeptPost.deptId, postId: info.sysDeptPost.postId }).then(res => {
      if (res.success) {
        getPostList()
        ElMessage.success('移除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

//分页
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum
  getPostList()
}

//分页
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize
  getPostList()
}

//dialog3
//添加岗位
const submit = () => {

  if (!state.postSettingList.length) {
    return ElMessage.warning("请先勾选岗位!");
  }

  let query = {
    deptId: deptId.value,
    sysDeptPostDtoList: state.postSettingList
  }
  addSysDeptPost(query).then(res => {
    if (res.success) {
      dialogPostSetting.value.close()
      getPostList()
      ElMessage.success('添加成功')
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

//修改别名
const handleSelectionChange = (val) => {
  var list = []
  val.forEach(e => {
    list.push({ postId: e.postId, postShowName: e.postName })
  })
  state.postSettingList = list
}

//岗位设置分页查询
const getPostListPostSetting = () => {
  let query = {
    deptId: deptId.value,
    orderParam: [{ fieldName: 'postLevel', asc: 1 }],
    pageParam: state.pagetionPostSetting,
    sysPostDtoFilter: formInlinePostSetting
  }
  listNotInSysDeptPost(query).then(res => {
    state.PostSetting = res.data.dataList
    state.pagetionPostSetting.total = res.data.totalCount * 1
  })
}

//分页
const currentChangePostSetting = (pageNum) => {
  state.pagetionPostSetting.pageNum = pageNum
  getPostListPostSetting()
}

//分页
const sizeChangePostSetting = (pageSize) => {
  state.pagetionPostSetting.pageSize = pageSize
  getPostListPostSetting()
}


//分页
const onSubmitPostSetting = () => {
  state.pagetionPostSetting = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  getPostListPostSetting()
}

//重置
const onResetPostSetting = () => {
  formInlineRefPostSetting.value.resetFields()
  onSubmitPostSetting()
}

defineExpose({
  getPostList
})
</script>

<style lang='less' scoped>
// #0092ff
.page-close-box {
  position: absolute;
  top: 110px;
  right: 20px;
  cursor: pointer;

  img {
    width: 18px;
    margin-right: 6px;
    vertical-align: bottom;
  }

  span{
    font-size: 16px;
    color: #0092ff;
  }
}
</style>
