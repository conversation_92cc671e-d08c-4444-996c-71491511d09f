<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="sysOperationLogDtoFilter" label-suffix=":">
        <el-form-item prop="operatorLogin">
          <el-input v-model="sysOperationLogDtoFilter.operatorLogin" placeholder="账号" />
        </el-form-item>
        <el-form-item prop="operatorName">
          <el-input v-model="sysOperationLogDtoFilter.operatorName" placeholder="中文名" />
        </el-form-item>
        <el-form-item prop="operationType">
          <el-select v-model="sysOperationLogDtoFilter.operationType" class="m-2" placeholder="操作类型" size="default"
            clearable>
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="success">
          <el-select v-model="sysOperationLogDtoFilter.success" class="m-2" placeholder="操作状态" size="default" clearable>
            <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="traceId">
          <el-input v-model="sysOperationLogDtoFilter.traceId" placeholder="Trace Id" />
        </el-form-item>
        <el-form-item prop="time">
          <el-date-picker v-model="sysOperationLogDtoFilter.time" type="daterange" range-separator="-"
            start-placeholder="开始时间" end-placeholder="结束时间" size="default" prefix-icon="Calendar" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" :icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight" show-overflow-tooltip>
        <el-table-column type="index" width="60" label="序号">
        </el-table-column>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" :width="item.width" />
        <el-table-column align="center" label="操作" width="80">
          <template #default="scope">
            <el-button @click="editHandle(scope.row)" link icon="Tickets" type="primary">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pagetion.pageNum" :page-size="state.pagetion.pageSize" :total="state.pagetion.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <!-- <dialog-common ref="dialog" :title="state.title" :formRef="ruleFormRef" :width="700">
        <el-form ref="ruleFormRef" label-width="100px" :model="form" label-suffix=":">
          <el-form-item label="操作模块" prop="serviceName">
            {{ form.serviceName }}
          </el-form-item>
          <el-form-item label="操作人员" prop="operatorName">
            {{ form.operatorName }}
          </el-form-item>
          <el-form-item label="请求地址" prop="requestUrl">
            {{ form.requestUrl }}
            <el-tag style="margin-left: 20px" class="ml-2">{{
              form.requestMethod
            }}</el-tag>
          </el-form-item>
          <el-form-item label="主机地址" prop="requestIp">
            {{ form.requestIp }}
          </el-form-item>
          <el-form-item label="操作方法" prop="apiMethod">
            {{ form.apiMethod }}
          </el-form-item>
          <el-form-item label="Trace Id" prop="requestIp">
            {{ form.traceId }}
          </el-form-item>
          <el-form-item label="Session ID" prop="sessionId">
            {{ form.sessionId }}
          </el-form-item>
          <el-form-item label="请求参数" prop="requestArguments">
            <JsonViewer :value="form.requestArguments" boxed sort></JsonViewer>
          </el-form-item>
          <el-form-item label="返回结果" prop="responseResult">
            <JsonViewer :value="form.responseResult" boxed sort></JsonViewer>
          </el-form-item>
          <el-form-item label="登录状态" prop="successName">
            <el-tag class="ml-2" type="success" v-if="form.successName == '成功'">{{ form.successName }}</el-tag>
            <el-tag class="ml-2" type="danger" v-if="form.successName == '失败'">{{ form.successName }}</el-tag>
          </el-form-item>
        </el-form>
      </dialog-common> -->

      <drawer-page ref="drawer" :drawer="state.drawer" :tableData="drawerTableData"
        @cancelClick="cancelClick"></drawer-page>
    </template>
  </page-common>
</template>

<script setup>
import drawerPage from './component/drawerPage.vue'
import { Delete, Plus, Search, Refresh } from "@element-plus/icons-vue";

import { operationListAPI } from "@/api/settingSystem/formSysOperationLog.js";
import dayjs from "dayjs";
import "vue3-json-viewer/dist/index.css";

let drawer = ref()
let formInlineRef = ref();
let obj = {
  name: "qiu", //字符串
  age: 18, //数组
  isMan: false, //布尔值
  date: new Date(),
  fn: () => { },
  arr: [1, 2, 5],
};
const sysOperationLogDtoFilter = reactive({});
const drawerTableData = reactive({});
const options = [
  {
    value: 0,
    label: "登录",
  },
  {
    value: 5,
    label: "登出",
  },
  {
    value: 10,
    label: "新增",
  },
  {
    value: 15,
    label: "修改",
  },
  {
    value: 20,
    label: "删除",
  },
  {
    value: 25,
    label: "新增多对多关联",
  },
  {
    value: 30,
    label: "移除多对多关联",
  },
  {
    value: 35,
    label: "查询",
  },
  {
    value: 40,
    label: "分组查询",
  },
  {
    value: 45,
    label: "导出",
  },
  {
    value: 50,
    label: "上传",
  },
  {
    value: 55,
    label: "下载",
  },
  {
    value: 60,
    label: "重置缓存",
  },
];
const options1 = [
  {
    value: 1,
    label: "成功",
  },
  {
    value: 0,
    label: "失败",
  },
];
const state = reactive({
  roleId: "",
  //权限选中id
  defaultChecked: [],
  defaultProps: {
    children: "children",
    label: "menuName",
  },
  drawer: false,
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: "serviceName",
      label: "系统模块",
    },
    {
      prop: "operationTypeName",
      label: "操作类型",
    },
    {
      prop: "operatorLogin",
      label: "账号",
    },
    {
      prop: "operatorName",
      label: "中文名",
    },
    {
      prop: "requestIp",
      label: "登录 IP",
    },
    {
      prop: "requestUrl",
      label: "调用地址",
    },
    {
      prop: "elapse",
      label: "调用时长",
    },
    {
      prop: "successName",
      label: "状态",
    },
    {
      prop: "operationTime",
      label: "操作时间",
      width: 160,
      formatter: (row, column, cellValue) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
      }
    },
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
});

onMounted(() => {
  getList();
});

//分页
const getList = () => {
  if (sysOperationLogDtoFilter.time) {
    // var dayjs = require('dayjs')
    sysOperationLogDtoFilter.operationTimeEnd = dayjs(
      sysOperationLogDtoFilter.time[1]
    ).format("YYYY-MM-DD HH:mm:ss");
    sysOperationLogDtoFilter.operationTimeStart = dayjs(
      sysOperationLogDtoFilter.time[0]
    ).format("YYYY-MM-DD HH:mm:ss");
  } else {
    sysOperationLogDtoFilter.operationTimeEnd = ''
    sysOperationLogDtoFilter.operationTimeStart = ''
  }
  let query = {
    sysOperationLogDtoFilter: sysOperationLogDtoFilter,
    pageParam: state.pagetion,
    orderParam: [{ asc: false, fieldName: 'logId' }]
  };
  operationListAPI(query).then((res) => {
    res.data.dataList.forEach((e) => {
      if (e.success) {
        e.successName = "成功";
      } else {
        e.successName = "失败";
      }
      switch (e.operationType) {
        case 0:
          e.operationTypeName = "登录";
          break;
        case 5:
          e.operationTypeName = "登出";
          break;
        case 10:
          e.operationTypeName = "新增";
          break;
        case 15:
          e.operationTypeName = "修改";
          break;
        case 20:
          e.operationTypeName = "删除";
          break;
        case 25:
          e.operationTypeName = "新增多对多关联";
          break;
        case 30:
          e.operationTypeName = "移除多对多关联";
          break;
        case 35:
          e.operationTypeName = "查询";
          break;
        case 40:
          e.operationTypeName = "分组查询";
          break;
        case 45:
          e.operationTypeName = "导出";
          break;
        case 50:
          e.operationTypeName = "上传";
          break;
        case 55:
          e.operationTypeName = "下载";
          break;
        case 60:
          e.operationTypeName = "重置缓存";
          break;
      }
    });
    state.tableData = res.data.dataList;
    state.pagetion.total = res.data.totalCount * 1;
  });
};

//编辑按钮事件
const editHandle = (info) => {
  nextTick(() => {
    if (info.requestArguments) {
      if (typeof info.requestArguments != "object") {
        info.requestArguments = JSON.parse(info.requestArguments);
      }
    } else {
      info.requestArguments = {};
    }
    if (info.responseResult) {
      if (typeof info.responseResult != "object") {
        info.responseResult = JSON.parse(info.responseResult);
      }
    } else {
      info.responseResult = {};
    }
    Object.assign(drawerTableData, { ...info });
  });
  state.drawer = true
};

//关闭dialog方法
const cancelClick = (info) => {
  state.drawer = false
}

//查询方法
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

//重置方法
const onReset = () => {
  sysOperationLogDtoFilter.operationTimeEnd = ''
  sysOperationLogDtoFilter.operationTimeStart = ''
  formInlineRef.value.resetFields();
  onSubmit();
};

//分页方法
const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

//分页方法
const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};
</script>

<style lang="less" scoped></style>
