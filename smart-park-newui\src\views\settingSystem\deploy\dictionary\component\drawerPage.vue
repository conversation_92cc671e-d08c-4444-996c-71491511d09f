<template>
  <el-drawer :modelValue="drawer" direction="rtl" :before-close="cancelClick">
    <template #header>
      <h4>字典项</h4>
    </template>
    <template #default>
      <el-table :data="state.tableData">
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
          :align="item.align" :formatter="item.formatter" />
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancelClick">取消</el-button>
        <el-button type="primary" @click="addItemHanlde">新建</el-button>
      </div>
    </template>
  </el-drawer>
  <dialog-common ref="dialog" title="新建字典项" @submit="submit" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="100px" :model="form" :rules="state.rules" label-suffix=":">
      <el-form-item label="数据项Id" prop="itemId">
        <el-input v-model="form.itemId" placeholder="请输入数据项Id"/>
      </el-form-item>
      <el-form-item label="数据项名称" prop="itemName">
        <el-input v-model="form.itemName" placeholder="请输入数据项名称"/>
      </el-form-item>
      <el-form-item label="显示顺序" prop="showOrder">
        <el-input v-model="form.showOrder" type="number" placeholder="请输入显示顺序"/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { dictionListItemAPI, dictionAddItemAPI, dictionDelItemAPI } from '@/api/settingSystem/dictionary.js'

import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  drawer: {
    type: Boolean,
    default: false
  },
  dictCode:{
    type: String,
    default: ''
  }
})

let {drawer, dictCode} = toRefs(props)

const emit = defineEmits(['cancelClick'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({})

const state = reactive({
  tableData:[],
  tableHeader: [
    {
      prop: 'itemId',
      label: '数据项Id'
    },
    {
      prop: 'name',
      label: '数据项名称'
    }
  ],
  rules: {
    itemId: [{ required: true, message: '请输入数据项Id', trigger: 'blur' },],
    itemName: [{ required: true, message: '请输入数据项名称', trigger: 'blur' }],
    showOrder: [{ required: true, message: '请输入显示顺序', trigger: 'blur' }]
  }
})

const cancelClick = () => {
  emit('cancelClick')
}

// 新建字典项
const addItemHanlde = () => {
  dialog.value.open()
}

// 获取字典项
const getList = () => {
  dictionListItemAPI({ dictCode: dictCode.value }).then(res => {
    state.tableData = res.data.cachedResultList
  })
}

// 提交字典项
const submit = () => {
  let data = {
    globalDictItemDto: { ...form, dictCode: dictCode.value }
  }
  dictionAddItemAPI(data).then(res => {
    if (res.success) {
      ElMessage.success('添加成功')
      dialog.value.close()
      getList()
    } else {
      ElMessage.error(res.errorMessage)
    }
  })
}

// 删除字典项
const deleteHandle = (info) => {
  ElMessageBox.confirm(
    '是否删除当前字典项?',
    '提醒',
    {
      type: "warning"
    }
  ).then(() => {
    dictionDelItemAPI({ id: info.id }).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}

defineExpose({
  getList
})
</script>

<style lang='less' scoped></style>
