package com.soft.webadmin.dto.contingency;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * EventDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EventDTO对象")
@Data
public class EventDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "事件名称")
    @NotBlank(message = "事件名称不能为空！")
    private String name;

    @ApiModelProperty(value = "事件等级：1普通、2重要、3严重")
    @NotNull(message = "事件等级不能为空！")
    private Integer level;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "图片")
    private String img;

}
