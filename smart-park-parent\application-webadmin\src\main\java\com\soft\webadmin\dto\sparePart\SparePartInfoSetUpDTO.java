package com.soft.webadmin.dto.sparePart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * SparePartInfoDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartInfoDTO对象")
@Data
public class SparePartInfoSetUpDTO {

    @ApiModelProperty(value = "主键id")
    @NotEmpty(message = "备件id不能为空！")
    private List<Long> ids;

    @ApiModelProperty(value = "最低库存预警值")
    @NotNull(message = "最低库存预警值不能为空！")
    @Min(value = 1)
    private Integer earlyWarningLeast;

    @ApiModelProperty(value = "最高库存预警值")
    @NotNull(message = "最高库存预警值不能为空！")
    @Min(value = 1)
    private Integer earlyWarningMost;

}
