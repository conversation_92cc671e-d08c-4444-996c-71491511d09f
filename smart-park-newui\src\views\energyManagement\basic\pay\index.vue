<template>
  <tree-page-common v-model="state.tableHeight">
    <template #leftTree>
      <el-input placeholder="请输入关键词" v-model="filterText" clearable />
      <el-tree ref="treeRef" class="filter-tree" :data="state.treeData" node-key="id" :props="treeProps"
        style="margin-top: 10px" default-expand-all show-checkbox @change="handleCheckChange"
        :filter-node-method="filterNode" />
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="editHandle">提醒设置</el-button>
    </template>
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="equipmentNo">
          <el-input v-model="formInline.equipmentNo" placeholder="设备名称" />
        </el-form-item>
        <el-form-item prop="equipmentName">
          <el-input v-model="formInline.equipmentName" placeholder="设备编号" />
        </el-form-item>
        <el-form-item prop="equipmentType">
          <el-select v-model="formInline.equipmentType" placeholder="设备类型">
            <el-option v-for="item in equipmentTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
      <el-table :data="state.tableData" :height="state.tableHeight">
        <el-table-column prop="equipmentNo" label="设备编号" />
        <el-table-column prop="equipmentName" label="设备名称" />
        <el-table-column prop="equipmentType" label="设备类型" />
        <el-table-column prop="attributeValue" label="剩余金额" />
        <el-table-column prop="status" label="剩余金额">
          <template #default="scope">
            {{ scope.row.status == 0 ? '缴费中' : scope.row.status == 1 ? '缴费成功' : scope.row.status == 2 ? '缴费失败' : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="最近充值时间" />
        <el-table-column align="center" label="操作" width="200" v-if="formInline.equipmentType == '电表'">
          <template #default="scope">
            <el-button link icon="Edit" type="primary" @click="payPrice(scope.row)">充值</el-button>
            <el-button link icon="View" type="primary" @click="recordOpen(scope.row)">充值记录</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :page-sizes="[10, 20, 30, 50]" layout="total, sizes, prev, pager, next, jumper"
        :current-page="state.pageParam.pageNum" :page-size="state.pageParam.pageSize" :total="state.pageParam.total"
        @size-change="sizeChange" @current-change="currentChange" />
      <!-- 设置 -->
      <modal-page ref="modal" @submit="getList" />
      <pay-page ref="pay" @submit="getList" />
      <pay-record-page ref="record" @submit="getList" />
    </template>
  </tree-page-common>
</template>

<script setup>
import { treeAPI } from '@/api/iotManagement/space.js';
import { selectPagePayAPI } from '@/api/energyManagement/pay.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import modalPage from './component/modalPage.vue';
import payPage from './component/payPage.vue';
import payRecordPage from './component/payRecordPage.vue';

const modal = ref();
const pay = ref();
const record = ref();
const treeRef = ref();
const filterText = ref('');
const formInlineRef = ref();
const formInline = reactive({ equipmentType: '电表' });

const treeProps = {
  children: 'children',
  label: 'name',
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.includes(value);
};

const handleCheckChange = (data, checked, indeterminate) => {
  getList();
};

watch(filterText, (val) => {
  treeRef.value.filter(val);
});

const equipmentTypeOptions = ref([
  { value: '水表', label: '水表' },
  { value: '电表', label: '电表' },
  // { value: '气表', label: '气表' },
]);

const state = reactive({
  treeData: [],
  tableData: [],
  tableHeight: 100,
  pageParam: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  },
  title: '',
  drawer: false,
});

onMounted(() => {
  getSpaceTree();
  getList();
});

const getSpaceTree = () => {
  treeAPI({ deep: 4 }).then((res) => {
    if (res.success) {
      state.treeData = res.data;
    }
  });
};

const getList = () => {
  let spaceIdArray = [];
  if (treeRef.value) {
    treeRef.value.getCheckedNodes().forEach((e) => {
      spaceIdArray.push(e.id);
    });
  }
  let params = {
    equipmentType: formInline.equipmentType,
    equipmentNo: formInline.equipmentNo,
    equipmentName: formInline.equipmentName,
    spaceIdList: spaceIdArray.length > 0 ? spaceIdArray.join(',') : undefined,
    pageNum: state.pageParam.pageNum,
    pageSize: state.pageParam.pageSize,
  };
  selectPagePayAPI(params).then((res) => {
    state.tableData = res.data.dataList;
    state.pageParam.total = res.data.totalCount;
  });
};

const onSubmit = () => {
  state.pageParam = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pageParam.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pageParam.pageSize = pageSize;
  getList();
};

const recordOpen = (row) => {
  nextTick(() => {
    record.value.form.equipmentId = row.equipmentId
    record.value.open();
  });
}

const payPrice = (row) => {
  pay.value.open();
  nextTick(() => {
    pay.value.form.equipmentId = row.equipmentId
  });
}

/** 预警设置 */
const editHandle = () => {
  modal.value.open();
  nextTick(() => {
    // Object.assign(modal.value.form, row);
  });
};

</script>
