<template>
  <el-timeline class="timeline-box-card" style="margin-left: 1px;">
    <el-timeline-item placement="top" v-for="(item, index) in state.equipmentLifeCycles" :key="index"
      type="primary" :hollow="true" :timestamp="item.createTime">
      <el-card class="life-circle">
        <div class="top">
          <h4>{{ operateOptions[item.cycleType] }}</h4>
        </div>
        <div v-if="item.cycleType === 0">
          <div class="item">报废日期：<span class="item-inline-content">{{ JSON.parse(item.busiData).scrapDate }}</span></div>
          <div class="item">报废原因：
            <div class="item-content">{{ JSON.parse(item.busiData).scrapReason }}</div>
          </div>
        </div>
        <div v-if="item.cycleType === 2">
          <div class="item"><span class="item-inline-content">{{ JSON.parse(item.busiData).alarmContent }}</span></div>
          <div class="item">告警编号：<span class="item-inline-content">{{ JSON.parse(item.busiData).workOrderNo }}</span></div>
        </div>
        <div v-if="item.cycleType === 3">
          <div class="item"><span class="item-inline-content">{{ JSON.parse(item.busiData).repairContent }}</span></div>
          <div class="item">工单编号：<span class="item-inline-content">{{ JSON.parse(item.busiData).workOrderNo }}</span></div>
        </div>
        <div v-if="item.cycleType === 5">
          <div class="item">保养计划：<span class="item-inline-content">{{ JSON.parse(item.busiData).planName }}</span></div>
          <div class="item">维保记录：<span class="item-inline-content">{{ JSON.parse(item.busiData).recordName}}</span></div>
        </div>
      </el-card>
    </el-timeline-item>
  </el-timeline>
  <template v-if="state.equipmentLifeCycles.length === 0">
    <el-empty description="暂无数据" />
  </template>
</template>

<script setup>
import { checkEquipLifeCycleAPI } from '@/api/iotManagement/equipManage.js'

const props = defineProps({
  equipmentId: {
    type: String,
    default: ''
  }
})

const { equipmentId } = toRefs(props)


const operateOptions = ref({
  0: '设施报废',
  1: '新增设备',
  2: '设备故障',
  3: '设备修复',
  5: '设备保养'
})

const state = reactive({
  equipmentLifeCycles: []
})

// 生命周期
const loadEquipLifeCycleList = async () => {
  const { success, data } = await checkEquipLifeCycleAPI(equipmentId.value)
  if (success) {
    state.equipmentLifeCycles = data
  }
}


const init = () => {
  loadEquipLifeCycleList()
}

defineExpose({
  init
})
</script>

<style lang="less" scoped></style>
