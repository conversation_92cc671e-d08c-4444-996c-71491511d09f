<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.contingency.GroupMemberMapper">
    <resultMap type="com.soft.webadmin.model.contingency.GroupMember" id="GroupMemberResult">
        <result property="id" column="id" />
        <result property="groupId" column="group_id" />
        <result property="userId" column="user_id" />
        <result property="name" column="name" />
        <result property="phone" column="phone" />
        <result property="shortPhone" column="shortPhone" />
        <result property="duties" column="duties" />
    </resultMap>

    <sql id="selectGroupMemberVo">
        select t.id, t.group_id, t.user_id, t.name, t.phone, t.shortPhone, t.duties from cm_group_member t
    </sql>
    
</mapper>