<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="100px" :model="form" :rules="state.rules" label-suffix=":">
      <el-form-item label="角色名称" prop="roleName">
        <el-input v-model="form.roleName" placeholder="请输入角色名称" />
      </el-form-item>
      <el-form-item label="角色描述" prop="roleDesc">
        <el-input type="textarea" v-model="form.roleDesc" :maxlength="500" show-word-limit :autosize="{ minRows: 5}"
                placeholder="请输入角色描述" />
      </el-form-item>
      <el-form-item label="权限配置" prop="defaultChecked">
        <el-tree ref="treeRef" :data="treeData" show-checkbox node-key="menuId" :props="state.defaultProps" :check-strictly="state.strictly"
          :default-checked-keys="form.defaultChecked"  />
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { roleAddAPI, roleEditAPI } from '@/api/settingSystem/role.js'

import { ElMessage } from 'element-plus'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  treeData: {
    type: Array,
    default: []
  },
})
let treeRef = ref();

const { title } = toRefs(props)
const emit = defineEmits(['submit'])

let ruleFormRef = ref()
let dialog = ref()

const form = reactive({})
const state = reactive({
  strictly:false,
  defaultProps: {
    children: 'children',
    label: 'menuName'
  },
  rules: {
    roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' },]
  }
})

const open = () => {
  dialog.value.open()
}

const submit = () => {
  var treeList = [...treeRef.value.getCheckedNodes(false,true)]
  if (treeList.length == 0) {
    ElMessage.error("请选择角色的菜单权限")
    return
  }
  var treeIds = [];
  treeList.forEach(e => {
    treeIds.push(e.menuId)
  })
  let data = {
    sysRoleDto: form,
    menuIdListString: treeIds.join(",")
  }
  if (data.sysRoleDto.roleId) {
    subHandle(roleEditAPI, '编辑成功')
  } else {
    subHandle(roleAddAPI, '添加成功')
  }

  function subHandle(req, title) {
    req(data).then(res => {
      if (res.success) {
        ElMessage.success(title)
        dialog.value.close()
        emit('submit')
        treeRef.value.setCheckedKeys([])
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  }
}

defineExpose({
  state,
  form,
  open
})
</script>

<style lang='less' scoped></style>
