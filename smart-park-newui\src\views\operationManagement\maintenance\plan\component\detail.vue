<template>
  <el-card class="box-card card-textBg">
    <template #header>
      <el-row justify="space-between" align="middle">
        <strong>{{ title }}</strong>
        <el-button type="primary" icon="Back" @click="showPage">返回</el-button>
      </el-row>
    </template>

    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-position="top">
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">基本信息</div>
        </div>
        <div class="detail-area">
          <el-row :gutter="40">
            <el-col :span="5">
              <el-form-item label="计划名称" prop="planName">
                {{ form.planName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="工作班组" prop="workGroupId">
                {{ form.workGroupName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="首保时间" prop="scheduleRule">
                {{ form.scheduleRule }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="维保周期" prop="maintenanceCycle">
                {{ form.maintenanceCycle }}月
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="开始时间" prop="startTime">
                {{ form.startTime }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理时限" prop="handleLimitDuration">
                {{ form.handleLimitDuration }}小时
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="派单时间" prop="advanceTime">
                提前{{ form.advanceTime }}小时
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="启停状态" prop="state">
                <el-tag class="ml-2" :type="form.state ? 'success' : 'danger'">{{
                    form.state ? '启用' : '停用'
                  }}
                </el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="创建人" prop="createUserName">
                {{ form.createUserName }}
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="创建时间" prop="createTime">
                {{ form.createTime }}
              </el-form-item>
            </el-col>
            <el-col :span="5"></el-col>
            <el-col :span="5"></el-col>
          </el-row>
        </div>
      </div>
      <div style="margin-top: 15px">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">维保点</div>
        </div>
        <div class="detail-area">
          <el-table :data="form.checkPlanPointList">
            <el-table-column label="序号" type="index" width="60"/>
            <el-table-column v-for="(column, index) in state.columnList" :label="column.label" :prop="column.prop"
                             :key="index"/>
          </el-table>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>

const emit = defineEmits(['showPage'])

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  info: {
    type: Object,
    default: {}
  }
});
const {title, info} = toRefs(props);
const ruleFormRef = ref();
const dialog = ref();
const drawer = ref();
const form = reactive({
  scheduleType: 'MONTH',
  planType: 'MAINTENANCE',
  checkPlanPointList: [],
});

/** 校验派单时间 */
const checkAdvanceTime = (rule, value, callback) => {
  if (!value && isNaN(value)) {
    callback(new Error('不能为空'));
  } else if (isNaN(value)) {
    callback(new Error('请输入数字值'));
  } else if (value < 0) {
    callback(new Error('请输入不小于0的数字'));
  } else {
    callback();
  }
};

const state = reactive({
  workGroupList: [],
  templateList: [],
  columnList: [
    {prop: 'dataName', label: '设备'},
    {prop: 'dataSpace', label: '位置'},
    {prop: 'templateName', label: '检查模板'},
  ],
  rules: {},
  drawer: false,
});
const modal = ref();

onMounted(() => {
  Object.assign(form, props.info);
  // 处理时限，分钟转换成小时
  if (form.handleLimitDuration) {
    form.handleLimitDuration = form.handleLimitDuration / 60;
  }
})

const showPage = () => {
  emit('showPage', 0)
}
</script>

<style lang="less" scoped>
:deep(.el-form-item__label) {
  color: #73767a;
}
</style>
