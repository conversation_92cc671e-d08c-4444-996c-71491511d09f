<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline" class="demo-form-inline">
        <el-form-item prop="name">
          <el-input v-model="formInline.name" placeholder="预案名称"/>
        </el-form-item>
        <el-form-item prop="eventId">
          <el-select v-model="formInline.eventId" filterable clearable placeholder="事件类型">
            <el-option v-for="item in state.eventOptions" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #operate>
      <el-button type="primary" icon="Plus" @click="addHandle">新建预案</el-button>
    </template>
    <template #table>
      <el-table :height="state.tableHeight" :data="state.tableData" row-key="id" show-overflow-tooltip>
        <el-table-column v-for="(item, index) in state.tableHeader" :key="index" :prop="item.prop" :label="item.label"
                         :align="item.align" :formatter="item.formatter" :width="item.width"/>
        <el-table-column align="center" label="操作" width="160">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editHandle(scope.row)">编辑</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="state.pagetion.pageNum"
          :page-size="state.pagetion.pageSize"
          :total="state.pagetion.total"
          @size-change="sizeChange"
          @current-change="currentChange"
      />
      <saveModal ref="save" :title="state.title" :eventOptions="state.eventOptions"
                 @submit="getList"></saveModal>
    </template>
  </page-common>
</template>

<script setup>
import saveModal from './component/saveModal.vue'

import {ElButton, ElMessage, ElMessageBox} from 'element-plus'

import { planPageAPI, planDeteleAPI, planDetailAPI} from '@/api/comprehensiveSecurity/plan.js'
import { eventsPageAPI } from "@/api/comprehensiveSecurity/events.js";

import {calcPageNo} from "@/utils/util.js";

let save = ref()

const formInlineRef = ref();
const formInline = reactive({});

const state = reactive({
  title: '',
  eventOptions: [],
  tableHeight: 100,
  tableData: [],
  tableHeader: [
    {
      prop: 'name',
      label: '预案名称'
    },
    {
      prop: 'eventName',
      label: '事件类型'
    },
    {
      prop: 'createUserName',
      label: '创建人'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160
    }
  ],
  pagetion: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  }
})

onMounted(() => {
  getList()
  getEvent()
})

const getEvent = () => {
  eventsPageAPI().then(res => {
    state.eventOptions = res.data.dataList
  })
}

// 获取预案
const getList = () => {
  let query = {
    ...formInline,
    ...state.pagetion
  };
  planPageAPI(query).then(res => {
    state.tableData = res.data.dataList;
    state.pagetion.total = res.data.totalCount;
  })
}
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
  };
  getList();
};

const onReset = () => {
  formInlineRef.value.resetFields();
  onSubmit();
};

const currentChange = (pageNum) => {
  state.pagetion.pageNum = pageNum;
  getList();
};

const sizeChange = (pageSize) => {
  state.pagetion.pageSize = pageSize;
  getList();
};


// 新建预案
const addHandle = () => {
  state.title = '新建预案'
  save.value.open()
}

// 编辑预案
const editHandle = ({id}) => {
  state.title = '编辑预案'

  planDetailAPI({id}).then(res => {
    if (res.success) {
      save.value.open()
      nextTick(() => {

        // 数据处理
        res.data.nodeDTOList = res.data.nodeList.map(item => {
          return {...item,taskDTOList:item.taskList}
        })

        res.data.annexList = JSON.parse(res.data.annex || '[]').map(item => {
          return {
            name: item.fileName,
            url: item.filePath,
            filePath: item.filePath
          }
        })

        Object.assign(save.value.form, res.data)
      })
    } else {
      ElMessage.error(res.errorMessage);
    }
  })
}

// 删除预案
const deleteHandle = ({id}) => {
  ElMessageBox.confirm(
      '是否删除当前预案?',
      '提醒',
      {
        type: "warning"
      }
  ).then(() => {
    state.pagetion.pageNum = calcPageNo(state.pagetion.total, state.pagetion.pageNum, 1);
    planDeteleAPI({id}).then(res => {
      if (res.success) {
        getList()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  })
}


</script>

<style lang='less' scoped></style>
