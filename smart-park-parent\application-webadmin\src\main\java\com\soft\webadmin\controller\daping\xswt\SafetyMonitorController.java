package com.soft.webadmin.controller.daping.xswt;

import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.service.daping.xswt.SafetyService;
import com.soft.webadmin.vo.daping.xswt.SafetyMonitor.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName ParkOverviewController
 * @description:
 * @date 2024年08月12日
 */
@Api(tags = "智慧安防")
@RestController
@RequestMapping("/daping/xswt/safety")
public class SafetyMonitorController {

    @Autowired
    private SafetyService safetyService;

    @ApiOperation("任务完成统计")
    @GetMapping("/workOrderStatis")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginDate", value = "开始时间", required = true),
            @ApiImplicitParam(name = "endDate", value = "结束时间", required = true),
            @ApiImplicitParam(name = "orderType", value = "订单类型：PATROL_INSPECTION巡检，MAINTENANCE维保", required = true)
    })
    public ResponseResult<List<WorkOrderStatisVO>> workOrderStatis(@RequestParam String beginDate,
                                                                   @RequestParam String endDate,
                                                                   @RequestParam String orderType) {
        return ResponseResult.success(safetyService.workOrderStatis(beginDate, endDate, orderType));
    }

    @ApiOperation("安防设备信息")
    @GetMapping("/equipmentInfo")
    public ResponseResult<List<EquipmentInfoVO>> equipmentInfo() {
        return ResponseResult.success(safetyService.equipmentInfo());
    }

    @ApiOperation("告警事件统计")
    @GetMapping("/equipmentWarningStatis")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginDate", value = "开始时间", required = true),
            @ApiImplicitParam(name = "endDate", value = "结束时间", required = true)
    })
    public ResponseResult<EquipmentWarningStatisVO> equipmentWarningStatis(@RequestParam String beginDate,
                                                                           @RequestParam String endDate) {
        return ResponseResult.success(safetyService.equipmentWarningStatis(beginDate, endDate));
    }

    @ApiOperation("预警事件")
    @GetMapping("/earlyWarningStatis")
    public ResponseResult<List<EarlyWarningStatisVO>> earlyWarningStatis() {
        return ResponseResult.success(safetyService.earlyWarningStatis());
    }

    @ApiOperation("今日安防事件")
    @GetMapping("/earlyWarningList")
    public ResponseResult<List<EquipmentWarningInfoVO>> earlyWarningList() {
        return ResponseResult.success(safetyService.earlyWarningList());
    }


    @ApiOperation("出入信息")
    @GetMapping("/passage/inOut")
    public ResponseResult<PassageInOutInfoVO> passageInOut(Integer statisticsType) {
        PassageInOutInfoVO passageInOutInfoVO = safetyService.passageInOut(statisticsType);
        return ResponseResult.success(passageInOutInfoVO);
    }

}
