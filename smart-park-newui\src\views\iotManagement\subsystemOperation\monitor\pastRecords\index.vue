<template>
  <page-common v-model="state.tableHeight">
    <template #query>
      <el-form :inline="true" ref="formInlineRef" :model="formInline">
        <el-form-item prop="dictName">
          <el-tree-select v-model="formInline.parentId" :data="state.data" filterable :render-after-expand="false"
            node-key="id" placeholder="选择设备通道" clearable />
        </el-form-item>
        <el-form-item prop="date">
          <el-date-picker v-model="formInline.date" type="datetimerange" range-separator="-" start-placeholder="开始时间"
            end-placeholder="结束时间" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button type="primary" icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #table>
    </template>
  </page-common>
</template>

<script setup>
import { equipTree } from '@/api/iotManagement/realtime.js'

let formInlineRef = ref()
const formInline = reactive({})

const state = reactive({
  tableHeight: 100,
  data: []
})

onMounted(() => {
  getMonitor()
})

// 获取监控树形
const getMonitor = () => {
  equipTree({
    subSystemEnums: 'MONITOR'
  }).then(res => {
    state.data = treeTransfer(res.data)
  })
}

// 树形转化
const treeTransfer = (list) => {
  return list.map(item => {
    if (item.children) {
      item.children = treeTransfer(item.children)
    }
    return {
      ...item,
      label: item.name,
      disabled: item.type == 1 && !item.children
    }
  });
}

// 切换历史视频
const switchPast = () => {

}

// 查询
const onSubmit = () => {
  state.pagetion = {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
  switchPast()
}

// 重置
const onReset = () => {
  formInlineRef.value.resetFields()
  onSubmit()
}
</script>

<style lang='less' scoped></style>