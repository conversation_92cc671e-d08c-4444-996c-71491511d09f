<template>
  <div class="personnel">
    <div class="personnel-wrapper el-input__wrapper" @click="handleOpen" @mouseenter="state.hover = true"
         @mouseleave="state.hover = false">
      <div class="personnel-showTag">
        <div v-if="multiple && modelValue &&  modelValue.length">
          <el-tag v-if=" modelValue.length > 0" class="tag-left" closable type="info"
                  @close="handleTagClose(modelValue[0],true)">{{ state.personObj[modelValue[0]] }}
          </el-tag>
          <el-tag v-if="modelValue.length - 1 > 0" type="info">+ {{ modelValue.length - 1 }}</el-tag>
        </div>
        <div v-else-if="!multiple && modelValue">
          {{ state.personObj[modelValue] }}
        </div>
        <div v-else class="placeholder">
          {{ placeholder }}
        </div>
      </div>
      <el-icon v-if="state.hover && (modelValue instanceof Array ? modelValue.length : modelValue ) " color="#a8abb2"
               @click.stop="handleCloseAll">
        <CircleClose/>
      </el-icon>
      <el-icon v-else color="#a8abb2">
        <ArrowDown/>
      </el-icon>
    </div>
    <dialog-common ref="dialog" :width="multiple ? 1080 : 900" title="人员选择" @submit="submit">
      <el-row :gutter="20" class="personnel-container">
        <el-col :span="multiple ? 6 : 7">
          <div class="dept-container">
            <el-tree
                :current-node-key="state.currentKey"
                :data="state.deptList"
                :expand-on-click-node="false"
                :props="defaultProps"
                accordion
                check-strictly
                highlight-current
                node-key="deptId"
                @node-click="handleNodeClick"
            />
          </div>
        </el-col>
        <el-col :span="multiple ? 14 : 17">
          <el-checkbox
              v-model="state.checkAll"
              :class="!multiple && 'disabel-check'"
              :indeterminate="state.isIndeterminate"
              :style="{  pointerEvents: !multiple && 'none'}"
              :validate-event="false"
              @change="handleCheckAllChange"
          >
            {{ state.currentDetpName }}
          </el-checkbox>
          <el-checkbox-group
              v-model="state.currentPersonKey"
              :validate-event="false"
              @change="handleCheckChange"
          >
            <el-checkbox v-for="item in state.currentPersonList" :class="!multiple && 'disabel-check'"
                         :label="item.userId"
                         :value="item.userId"
                        >
              {{ item.showName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-col>
        <el-col v-if="multiple" :span="4">
          <h4>已选中人员</h4>
          <el-tag v-for="(value,key) in state.currentModelValue" :key="key" class="tag-style" closable
                  size="large" @close="handleTagClose(key)">
            {{ state.personObj[key] }}
          </el-tag>
        </el-col>
      </el-row>
    </dialog-common>
  </div>
</template>

<script setup>
import {listUsersAPI, getDeptListAPI} from '@/api/settingSystem/user.js';

import {treeDataTranslate} from '@/utils/index.js'

const props = defineProps({
  placeholder: {
    type: String,
    default: '请选择'
  },
  modelValue: {
    type: [String, Array],
  },
  multiple: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const defaultProps = {
  label: 'deptName',
  value: 'deptId',
}

const dialog = ref()

const state = reactive({
  hover: false,
  currentKey: '',
  currentDetpName: '',
  checkAll: false,
  isIndeterminate: false,
  currentPersonKey: [],
  currentPersonList: [],
  deptList: [],
  personObj: {},
  currentModelValue: {},
})

onMounted(() => {
  loadDeptDropdownList()
  loadAllPersonList()
})


/*
* 数据
* */
// 获取部门
const loadDeptDropdownList = () => {
  getDeptListAPI({})
      .then((res) => {
        state.deptList = treeDataTranslate(res.data.dataList, 'deptId')
        loadPersonList(res.data.dataList[0])
      })
      .catch((e) => {
        reject(e);
      });
}

// 获取人员
const loadPersonList = ({deptId, deptName}) => {
  state.currentKey = deptId
  state.currentDetpName = deptName
  listUsersAPI({deptId}).then(res => {
    if (res.success) {
      state.currentPersonList = res.data.dataList
      if (props.multiple) {
        state.currentPersonKey = state.currentPersonList.map(item => state.currentModelValue[item.userId] ? item.userId : '').filter(i => i)
        handleCheckChange(state.currentPersonKey)
      }
    }
  })
}

// 获取全部人员
const loadAllPersonList = () => {
  listUsersAPI().then(res => {
    if (res.success) {
      res.data.dataList.forEach(item => {
        state.personObj[item.userId] = item.showName
      })
    }
  })
}

// 全选
const handleCheckAllChange = (val) => {
  if (val) {
    state.currentPersonKey = state.currentPersonList.map(item => {
      state.currentModelValue[item.userId] = true
      return item.userId
    })
  } else {
    state.currentPersonKey = []
    state.currentPersonList.forEach(item => delete state.currentModelValue[item.userId])
  }
  state.isIndeterminate = false
}

// 单选
const handleCheckChange = (value) => {

  if (props.multiple) {
    const checkedCount = value.length
    state.checkAll = state.currentPersonList.length > 0 ? checkedCount === state.currentPersonList.length : false
    state.isIndeterminate = checkedCount > 0 && checkedCount < state.currentPersonList.length

    state.currentPersonList.forEach(item => {
      if (!state.currentModelValue[item.userId] && value.indexOf(item.userId) != -1) {
        state.currentModelValue[item.userId] = true
      } else if (state.currentModelValue[item.userId] && value.indexOf(item.userId) == -1) {
        delete state.currentModelValue[item.userId]
      }
    })
  } else {
    state.currentPersonKey = [state.currentPersonKey[state.currentPersonKey.length - 1]]
    emit('update:modelValue', state.currentPersonKey[0])
    emit('change', state.currentPersonKey[0])
    dialog.value.close()
  }
}

// 关闭人员
const handleTagClose = (key, update) => {
  if (state.currentPersonList.map(item => item.userId).indexOf(key) != -1) {
    state.currentPersonKey = state.currentPersonKey.filter(item => item != key)
    handleCheckChange(state.currentPersonKey)
  } else {
    delete state.currentModelValue[key]
  }

  if (update) {
    emit('update:modelValue', Object.keys(state.currentModelValue).map(key => key))
  }
}

// 清空
const handleCloseAll = () => {
  if (props.multiple) {
    state.currentModelValue = {}
    emit('update:modelValue', Object.keys(state.currentModelValue).map(key => key))
  } else {
    state.currentPersonKey = []
    emit('update:modelValue', '')
  }
}

// 打开弹框
const handleOpen = () => {
  if (props.multiple) {
    assignOperate(props.modelValue)
    state.currentPersonKey = state.currentPersonList.map(item => state.currentModelValue[item.userId] ? item.userId : '').filter(i => i)
    handleCheckChange(state.currentPersonKey)
  }
  dialog.value.open()
}

// 赋值
const assignOperate = (newVal) => {
  if (props.multiple) {
    state.currentModelValue = {};
    (newVal || []).forEach(item => {
      state.currentModelValue[item] = true
    })
  }
}

// 节点选择
const handleNodeClick = (node) => {
  loadPersonList(node)
}

// 确认人选
const submit = () => {
  emit('update:modelValue', Object.keys(state.currentModelValue).map(key => key))
  dialog.value.close()
}

watch(() => props.modelValue, (newVal) => {
  assignOperate(newVal)
}, {immediate: true})
</script>

<style lang="less" scoped>
.personnel {
  width: 85%;

  &-wrapper {
    cursor: pointer;
    width: 100%;
    height: var(--el-component-size);
    padding: 4px 12px;
    font-size: 14px;
    position: relative;
    display: flex;
    align-items: center;
    vertical-align: middle;
    border-radius: var(--el-border-radius-base, var(--el-border-radius-base));
    box-shadow: 0 0 0 1px var(--el-border-color) inset;

    .placeholder {
      color: var(--el-text-color-placeholder);
    }
  }

  &-wrapper:active {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }

  &-showTag {
    flex: 1;
    transform: translateY(-1px);
  }
}

.personnel-container {
  height: calc(57vh - 32px);

  .el-col {
    height: 100%;
    overflow: auto;
  }

  .el-col:nth-child(2) {
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
  }
}

.tag-style {
  width: 100%;
  margin: 5px 0;
}

.tag-left {
  margin-left: -8px;
  margin-right: 8px;
}

.disabel-check {
  cursor: pointer;

  :deep(.el-checkbox__input) {
    display: none;
  }
}

.remove-display {
  :deep(.el-form-item__content) {
    display: block;
  }
}
</style>
