<template>
  <div class="pageHeader">
    <div class="logo">
      <img :src="iconPath" alt="" class="logo-img">
      <span class="logo-title">{{ systemName }}</span>
    </div>
    <el-menu mode="horizontal" @select="handleSelect" :default-active="activeMenu">
      <template v-for="(item, index) in menuList" :key="index">
        <el-menu-item :index="String(index)" v-show="item.meta.isHidden">
          <el-icon>
            <component :is="item.meta.icon">
            </component>
          </el-icon>
          <template #title>{{ item.meta.title }}</template>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import { menuStore } from '@/store/modules/menu.js'

let menu = menuStore()
let { menuList, activeMenu, activeMenuItem,itemMenu,systemName , systemIcon } = storeToRefs(menu)

const emit = defineEmits(['routerChange'])

const route  = useRoute()

// 图标地址
const iconPath = computed(() => {
  return new URL(`/src/assets/window/${systemIcon.value}.png`, import.meta.url).href
})

const handleSelect = (key) => {
  routerChoose(key)
}

const initMenu = () => {
  activeMenuItem.value = route.fullPath.slice(1,)

  activeMenu.value = String(menuList.value.findIndex(item => {
    return deep(item.children)
  }))

  function deep(arrMenu) {
    return (arrMenu || []).some(i => {
      if (i.path == route.fullPath.slice(1,)) {
        emit('routerChange', { path: route.fullPath, title: i.menuName })
        return true
      } else {
        if (i.children) {
          return deep(i.children)
        }
      }
    })
  }

  routerChoose(activeMenu.value)
}

const routerChoose = (currnetIndex) => {
  let currnet = menuList.value[currnetIndex]
  console.log(itemMenu.value);
  if (itemMenu.value != currnet.children){
    itemMenu.value = currnet.children
  }
}

watch(route, () => {
  initMenu()
}, { immediate: true })
</script>

<style lang='less' scoped>
.pageHeader {
  height: 100%;
  display: flex;
  align-items: center;

  .logo {
    width: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    &-img{
      width: 34px;
      height: 34px;
      margin-right: 10px;
    }
    &-title{
      font-size: 24px;
      color: #303133;
    }
  }

  .el-menu--horizontal {
    flex: 1;
    height: 100%;
    margin: 0 10px;
    padding: 10px 0;
    overflow: hidden;
  }
}

.el-dropdown-link {
  cursor: pointer;

  .avatar {
    margin-left: 10px;
    vertical-align: middle;
  }
}
</style>
