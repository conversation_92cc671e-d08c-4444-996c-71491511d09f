<template>
  <dialog-common ref="dialog" :title="state.title" @submit="saveOrUpdate" :width="450" :formRef="ruleFormRef">
    <el-form :model="state.spaceData" ref="ruleFormRef" :rules="state.rules" label-width="100px" label-suffix=":">
      <div v-if="state.spaceType === 'AREA'">
<!--            <el-form-item label="所属项目" prop="selectId">-->
<!--              <el-cascader v-model="state.spaceData.selectId" :options="spaceOptions"-->
<!--                           :disabled="state.operatorType === 'UPDATE'"-->
<!--                           :props="optionsProps" placeholder="请选择所属项目" clearable/>-->
<!--            </el-form-item>-->
            <el-form-item label="区域编码" prop="code">
              <el-input v-model="state.spaceData.code" placeholder="请输入区域编码"/>
            </el-form-item>
            <el-form-item label="区域名称" prop="name">
              <el-input v-model="state.spaceData.name" placeholder="请输入区域名称"/>
            </el-form-item>
      </div>
      <div v-if="state.spaceType === 'BUILDING'">
        <el-form-item label="选择区域" prop="selectId">
          <el-cascader v-model="state.spaceData.selectId" :options="spaceOptions" :props="optionsProps"
                       placeholder="请选择所在区域"/>
        </el-form-item>
        <el-form-item label="楼栋编码" prop="code">
          <el-input v-model="state.spaceData.code" placeholder="请输入楼栋编码"/>
        </el-form-item>
        <el-form-item label="楼栋名称" prop="name">
          <el-input v-model="state.spaceData.name" placeholder="请输入楼栋名称"/>
        </el-form-item>
      </div>
      <div v-if="state.spaceType === 'FLOOR'">
        <el-form-item label="选择楼栋" prop="selectId">
          <el-cascader v-model="state.spaceData.selectId" :options="spaceOptions" checkStrictly="false"
                       :props="optionsProps"
                       placeholder="请选择所在楼栋"/>
        </el-form-item>
        <div v-if="state.operatorType === 'CREATE'">
          <el-form-item v-for="(codeName, index) in state.dynamicItems">
            <template #label>
              <el-icon color="blur" size="26px" @click="addItem(index)">
                <CirclePlus/>
              </el-icon>
              <el-icon color="red" size="26px" @click="removeItem(index)">
                <Remove/>
              </el-icon>
            </template>
            <el-col :span="11">
              <el-input
                  v-model="codeName.code"
                  placeholder="请输入楼层编码"
              />
            </el-col>
            <el-col :span="11">
              <el-input
                  v-model="codeName.name"
                  placeholder="请输入楼层名称"
              />
            </el-col>
          </el-form-item>
          <div v-if="state.dynamicItems.length === 0">
            <el-form-item>
              <template #label>
                <el-icon color="blur" size="26px" @click="addItem(0)">
                  <CirclePlus/>
                </el-icon>
              </template>
            </el-form-item>
          </div>
        </div>
        <div v-else>
          <el-form-item label="楼层编码" prop="code">
            <el-input v-model="state.spaceData.code" placeholder="请输入楼层编码"/>
          </el-form-item>
          <el-form-item label="楼层名称" prop="name">
            <el-input v-model="state.spaceData.name" placeholder="请输入楼层名称"/>
          </el-form-item>
        </div>
      </div>
      <div v-if="state.spaceType === 'POINT'">
        <el-form-item label="选择位置" prop="selectId">
          <el-cascader v-model="state.spaceData.selectId" :options="spaceOptions" :props="optionsProps"
                       placeholder="请选择所在位置"/>
        </el-form-item>
        <div v-if="state.operatorType === 'CREATE'">
          <el-form-item v-for="(codeName, index) in state.dynamicItems">
            <template #label>
              <el-icon color="blur" size="26px" @click="addItem(index)">
                <CirclePlus/>
              </el-icon>
              <el-icon color="red" size="26px" @click="removeItem(index)">
                <Remove/>
              </el-icon>
            </template>
            <el-col :span="11" style="margin-bottom: 5px;">
              <el-input
                  v-model="codeName.code"
                  placeholder="请输入点位编码"
              />
            </el-col>
            <el-col :span="11">
              <el-input
                  v-model="codeName.name"
                  placeholder="请输入点位名称"
              />
            </el-col>
            <el-col :span="11">
              <el-input-number v-model="codeName.coordinate.x" :controls="false" :precision="3" placeholder="请输入x坐标"
                               clearable style="margin-bottom: 5px">
                <template #prepend>三维坐标x:</template>
              </el-input-number>
            </el-col>
            <el-col :span="11">
              <el-input-number v-model="codeName.coordinate.y" :controls="false" :precision="3" placeholder="请输入y坐标"
                               clearable style="margin-bottom: 5px">
                <template #prepend>三维坐标y:</template>
              </el-input-number>
            </el-col>
            <el-col :span="11">
              <el-input-number v-model="codeName.coordinate.z" :controls="false" :precision="3" placeholder="请输入z坐标"
                               clearable style="margin-bottom: 5px">
                <template #prepend>三维坐标z:</template>
              </el-input-number>
            </el-col>
          </el-form-item>
          <div v-if="state.dynamicItems.length === 0">
            <el-form-item>
              <template #label>
                <el-icon color="blur" size="26px" @click="addItem(0)">
                  <CirclePlus/>
                </el-icon>
              </template>
            </el-form-item>
          </div>
        </div>
        <div v-else>
          <el-form-item label="点位编码" prop="code">
            <el-input v-model="state.spaceData.code" placeholder="请输入点位编码"/>
          </el-form-item>
          <el-form-item label="点位名称" prop="name">
            <el-input v-model="state.spaceData.name" placeholder="请输入点位名称"/>
          </el-form-item>
          <el-form-item label="三维坐标" prop="coordinate">
            <el-input-number v-model="state.spaceData.coordinate.x" :controls="false" :precision="3" placeholder="请输入x坐标"
                             clearable style="margin-bottom: 5px">>
            </el-input-number>
            <el-input-number v-model="state.spaceData.coordinate.y" :controls="false" :precision="3" placeholder="请输入y坐标"
                             clearable style="margin-bottom: 5px">>
            </el-input-number>
            <el-input-number v-model="state.spaceData.coordinate.z" :controls="false" :precision="3" placeholder="请输入z坐标"
                             clearable style="margin-bottom: 5px">>
            </el-input-number>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {treeAPI, addAPI, updateAPI} from '@/api/iotManagement/space.js'
import {getProjectListAPI} from '@/api/settingSystem/user.js'
import {ElMessage} from "element-plus"


// 实例化 defineEmits，父组件
const emits = defineEmits(['closeClick'])

// 表单验证
const ruleFormRef = ref()
// 弹出框对象
const dialog = ref()


const state = reactive({
  // 空间类型
  spaceType: '',
  // 操作类型，CREATE、UPDATE
  operatorType: '',
  title: '新建空间',
  // 表单对象
  spaceData: {
    coordinate: {},
    // 下拉级联选择
    selectId: []
  },

  // 动态 输入框表单项
  dynamicItems: [
    {
      code: '',
      name: ''
    }
  ],

  // 校验规则
  rules: {
    selectId: [
      {required: true, message: '选项不能为空', trigger: 'blur'}
    ],
    code: [
      {required: true, message: '编码不能为空', trigger: 'blur'}
    ],
    name: [
      {required: true, message: '名称不能为空', trigger: 'blur'}
    ]
  }
})

//区域级联选择列表
const spaceOptions = ref([])
// 级联选择配置
const optionsProps = {
  checkStrictly: false,
  label: "name",
  value: "id"
}

// 动态表单项，添加表单项
const addItem = (index) => {
  state.dynamicItems.splice(index + 1, 0, {
    code: '',
    name: '',
    coordinate: {}
  })
}
// 移除表单项
const removeItem = (index) => {
  state.dynamicItems.splice(index, 1)
}


// 打开弹出框
const openDialog = (operatorType, spaceType, spaceData) => {
  // 打开弹窗，必须放在首位
  dialog.value.open()

  // 初始化动态表单
  state.dynamicItems = [
    {
      code: '',
      name: '',
      coordinate: {}
    }
  ]

  // 根据空间类型，选择下拉框是否可以支持截停选择
  if (spaceType === 'POINT') {
    optionsProps.checkStrictly = true
  } else {
    // 级联下拉框是否可以选择 非叶子选项，只有点位可以选择非叶子选项
    optionsProps.checkStrictly = false
  }

  // 空间类型
  state.spaceType = spaceType
  // 操作类型，CREATE、UPDATE
  state.operatorType = operatorType

  // 查询下拉列表
  querySelect()

  // 新建空间
  if (operatorType === 'CREATE') {
    if (state.spaceType === 'AREA') {
      state.title = '新建区域'
    } else if (state.spaceType === 'BUILDING') {
      state.title = '新建楼栋'
    } else if (state.spaceType === 'FLOOR') {
      state.title = '新建楼层'
    } else if (state.spaceType === 'POINT') {
      state.title = '新建点位'
    }
    state.spaceData.selectId = []
  } else { // 编辑
    if (state.spaceType === 'AREA') {
      state.title = '编辑区域'
    } else if (state.spaceType === 'BUILDING') {
      state.title = '编辑楼栋'
    } else if (state.spaceType === 'FLOOR') {
      state.title = '编辑楼层'
    } else if (state.spaceType === 'POINT') {
      state.title = '编辑点位'
    }
    // 等待弹窗模板页面渲染完成后，进行赋值
    nextTick(() => {
      state.spaceData = spaceData
      if (spaceType === 'AREA') {
        state.spaceData.selectId = [spaceData.projectId]
      } else {
        let selectIds = spaceData.path.split('/');
        // 移除 path 数组最后一个元素，即本身 ID
        selectIds.pop()
        state.spaceData.selectId = selectIds
        state.spaceData.coordinate = JSON.parse( state.spaceData.coordinate  || "{}" )
      }
    })
  }
}

// 保存
const saveOrUpdate = () => {
  if (state.operatorType === 'CREATE') {
    let spaceSave = {
      type: state.spaceType,
      selectId: state.spaceData.selectId[state.spaceData.selectId.length - 1]
    }
    if (state.spaceType === 'AREA') {
      spaceSave.codeNames = [
        {
          code: state.spaceData.code,
          name: state.spaceData.name
        }
      ]
    } else if (state.spaceType === 'BUILDING') {
      spaceSave.codeNames = [
        {
          code: state.spaceData.code,
          name: state.spaceData.name
        }
      ]
    } else if (state.spaceType === 'FLOOR') {
      spaceSave.codeNames = state.dynamicItems
    } else if (state.spaceType === 'POINT') {
      spaceSave.codeNames = state.dynamicItems
    }

    addAPI(spaceSave).then(res => {
      if (res.success) {
        ElMessage.success("保存成功！")
        // 关闭弹窗
        dialog.value.close();

        // 调用父组件定义的方法
        emits('closeClick')
      } else {
        ElMessage.error("保存失败！" + res.errorMessage)
      }
    }).catch(e => {
      ElMessage.error("发生异常，保存失败！")
    })
  } else if (state.operatorType === 'UPDATE') {
    let spaceUpdate = {
      id: state.spaceData.id,
      name: state.spaceData.name,
      code: state.spaceData.code
    }

    // 区域没有父空间
    if (state.spaceType !== 'AREA') {
      spaceUpdate.parentId = state.spaceData.selectId[state.spaceData.selectId.length - 1]
    }

    updateAPI(spaceUpdate).then(res => {
      if (res.success) {
        ElMessage.success("保存成功！")
        // 关闭弹窗
        dialog.value.close();

        // 调用父组件定义的方法
        emits('closeClick')
      } else {
        ElMessage.error("保存失败！" + res.errorMessage)
      }
    }).catch(e => {
      ElMessage.error("发生异常，保存失败！")
    })
  }
}

// 查询下拉选择
const querySelect = () => {
  if (state.spaceType === 'AREA') {
    // 查询项目列表
    // getProjectListAPI().then(res => {
    //   if (res.success) {
    //     spaceOptions.value = res.data.dataList
    //   }
    // })
  } else if (state.spaceType === 'BUILDING') {
    // 查询区域列表
    spaceTree(1)
  } else if (state.spaceType === 'FLOOR') {
    spaceTree(2)
  } else if (state.spaceType === 'POINT') {
    spaceTree(3)
  }
}
// 查询空间的树形结构，指定深度
const spaceTree = (deep) => {
  let query = {
    deep: deep
  }
  treeAPI(query).then(res => {
    if (res.success) {
      spaceOptions.value = res.data
    }
  })
}

// 向父组件暴露属性或方法
defineExpose({
  state,
  openDialog
});
</script>

<style lang='less' scoped>
:deep(.el-form-item__label){
  pointer-events: auto;
}
</style>
