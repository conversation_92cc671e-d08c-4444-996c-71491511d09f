package com.soft.webadmin.dto.contingency;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * EmergencyDTO对象
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@ApiModel("EmergencyDTO对象")
@Data
public class EmergencyQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "预案名称")
    private String name;

    @ApiModelProperty(value = "应急事件id")
    private Long eventId;

}
