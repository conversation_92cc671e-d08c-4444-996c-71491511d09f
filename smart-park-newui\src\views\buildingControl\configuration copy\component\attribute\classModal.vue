<template>
  <el-dialog v-model="state.dialogVisible" title="样式设置" :width="classType == 'topoTextShow' || classType == 'topoPrompt' ? 450 : 900" class="dialogCommon">
    <el-form label-suffix=":">
      <!-- 文本提示 -->
      <el-form-item label="提示内容" v-if="classType == 'topoPrompt'">
        <el-input v-model="state.itemInfo.conent" placeholder="请输入"  :rows="2" type="textarea"/>
      </el-form-item>

      <el-row v-else>
        <el-col :span=" classType == 'topoTextShow' || classType == 'topoPrompt' ? 24 : 12">
          <el-form-item label="设备属性">
            <el-select placeholder="请选择" v-model="state.itemInfo.attribute" filterable style="width: 70%">
              <el-option v-for="item in attributeOptions" :key="item" :label="item" :value="item" />
            </el-select>
            <el-button type="primary" @click="addHandle" style="margin-left: 15px;" v-if="!(classType == 'topoTextShow' || classType == 'topoPrompt')">添加</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 隐藏 -->
    <el-table :data="state.itemInfo.list" v-if="classType == 'toposhowHide'">
      <el-table-column label="最小值(<=)">
        <template #default="{ row }">
          <el-input v-model.number="row.min" placeholder="请输入" />
        </template>
      </el-table-column>

      <el-table-column label="最大值(>=)">
        <template #default="{ row }">
          <el-input v-model.number="row.max" placeholder="请输入" />
        </template>
      </el-table-column>

      <el-table-column label="类型" align="center">
        <template #default>
          <span>隐藏</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作">
        <template #default="{ $index }">
          <el-button link type="danger" @click="deleteHandle($index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 闪烁 -->
    <el-table :data="state.itemInfo.list" v-else-if="classType == 'topoTwinkle'">
      <el-table-column prop="min" label="最小值(<=)">
        <template #default="{ row }">
          <el-input v-model.number="row.min" placeholder="请输入" />
        </template>
      </el-table-column>

      <el-table-column prop="max" label="最大值(>=)">
        <template #default="{ row }">
          <el-input v-model.number="row.max" placeholder="请输入" />
        </template>
      </el-table-column>

      <el-table-column label="类型" align="center">
        <template #default>
          <span>闪烁</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作">
        <template #default="{ $index }">
          <el-button link type="danger" @click="deleteHandle($index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>


    <!-- 切换 -->
    <el-table :data="state.itemInfo.list" v-else-if="classType == 'topoCheckout'">
      <el-table-column prop="min" label="最小值(<=)">
        <template #default="{ row }">
          <el-input v-model.number="row.min" placeholder="请输入" />
        </template>
      </el-table-column>

      <el-table-column prop="max" label="最大值(>=)">
        <template #default="{ row }">
          <el-input v-model.number="row.max" placeholder="请输入" />
        </template>
      </el-table-column>

      <el-table-column label="设置" align="center">
        <template #default="{ row }">
          <el-link icon="Picture" @click="chooseImage(row)">
            <span style="margin-left: 5px;">选择图片</span>
          </el-link>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作">
        <template #default="{ $index }">
          <el-button link type="danger" @click="deleteHandle($index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="close">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 图片选择 -->
  <el-drawer v-model="state.drawer" title="图片选择" direction="ltr">
    <el-radio-group v-model="state.pictureInfo.picture">
      <el-collapse>
        <template v-for="(item, index) in pictureList" :key="index">
          <el-collapse-item :title="item.title">
            <template v-for="(i, index) in item.list" :key="index">
              <el-radio :value="i.image" size="large">
                <img :src="imgTransfer(i.image)">
              </el-radio>
            </template>
          </el-collapse-item>
        </template>
      </el-collapse>
    </el-radio-group>
  </el-drawer>
</template>

<script setup>
import componetJosn from '../../dataJson/index.js'

const props = defineProps({
  classType: {
    type: String,
    default: ''
  },
  attributeOptions:{
    type: Array,
    default:[]
  }
})

let { classType,attributeOptions } = toRefs(props)

const state = reactive({
  itemInfo: {},
  pictureInfo: {},
  dialogVisible: false,
})

const pictureList = computed(() => {
  return componetJosn.filter(item => item.categorytype == 'image')
})

const imgTransfer = (name) => {
  return new URL(`/src/assets/webtopoImg/${name}`, import.meta.url).href
}


// 打开
const open = () => {
  state.dialogVisible = true
}

// 关闭
const close = () => {
  state.dialogVisible = false
}

// 添加条件
const addHandle = () => {
  state.itemInfo.list.push({})
}

// 删除条件
const deleteHandle = (index) => {
  state.itemInfo.list.splice(index, 1)
}

// 选择图片
const chooseImage = (row) => {
  state.drawer = true
  state.pictureInfo = row
}

defineExpose({
  open,
  state
})
</script>

<style lang='less' scoped>
:deep(.el-radio__label) {
  img {
    width: 30px;
    height: 30px;
  }
}

.el-radio-group,
.el-collapse {
  width: 100%;
}
</style>
