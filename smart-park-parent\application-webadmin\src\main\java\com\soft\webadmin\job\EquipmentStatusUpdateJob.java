package com.soft.webadmin.job;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.sub.dao.equipment.EquipmentMapper;
import com.soft.sub.enums.EquipmentRunStatusEnums;
import com.soft.sub.enums.EquipmentTypeEnum;
import com.soft.sub.model.equipment.Equipment;
import com.soft.sub.service.equipment.EquipmentService;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class EquipmentStatusUpdateJob {

    @Resource
    private EquipmentService equipmentService;
    @Resource
    private EquipmentMapper equipmentMapper;

    private static final String JOB_NAME = "equipmentStatusUpdateJob";

    private static final Integer EQUIPMENT_STATUS_NORMAL = 1;
    private static final Integer EQUIPMENT_STATUS_FAULT = 0;

    /**
     * 定时任务：每1分钟执行一次，检测广播系统设备在线状态，并更新 run_status 和 equipment_status
     */
    @XxlJob(JOB_NAME)
    public void updateDeviceStatus() {
        try {
            // 1. 查询所有广播系统设备
            List<Equipment> equipments = equipmentService.list(new LambdaQueryWrapper<Equipment>()
                .eq(Equipment::getEquipmentTypeId, EquipmentTypeEnum.BROADCAST_SYSTEM.getId())
                .eq(Equipment::getDeletedFlag, GlobalDeletedFlag.NORMAL).orderByDesc(Equipment::getEquipmentId));

            if (equipments.isEmpty()) {
                log.info("未找到广播系统设备");
                return;
            }

            // 2. 过滤需要更新的设备
            List<Equipment> updateList = equipments.stream().filter(e -> {
                // boolean isReachable = NetworkUtils.ping(e.getIp());
                boolean isReachable = true;
                // 当前状态
                Integer currentRunStatus = e.getRunStatus();

                // 状态变化判断
                if (isReachable && currentRunStatus != EquipmentRunStatusEnums.NORMAL.getValue()) {
                    // Ping 成功，但状态不是“正常”，需要更新为正常状态
                    e.setRunStatus(EquipmentRunStatusEnums.NORMAL.getValue());
                    e.setEquipmentStatus(EQUIPMENT_STATUS_NORMAL);
                    return true;
                } else if (!isReachable && currentRunStatus == EquipmentRunStatusEnums.NORMAL.getValue()) {
                    // Ping 失败，但状态是“正常”，需要更新为离线+故障
                    e.setRunStatus(EquipmentRunStatusEnums.OFFLINE.getValue());
                    e.setEquipmentStatus(EQUIPMENT_STATUS_FAULT);
                    return true;
                }
                // 对于其他情况（如设备本身已经是离线 + Ping 也失败；或设备是在线 + Ping 成功），则不需要修改。
                return false;
            }).collect(Collectors.toList());

            // 3. 批量更新
            if (!updateList.isEmpty()) {
                Integer updateResult = equipmentMapper.batchUpdate(updateList);
                log.info("批量更新设备状态，数量：{}，结果：{}", updateList.size(), updateResult);
            } else {
                log.info("所有设备状态正常，无需更新");
            }
        } catch (Exception e) {
            log.error("广播系统设备状态检测任务异常", e);
        }
    }
}
