<template>
  <el-dialog v-model="dialogRef" :title="state.title" :width="800" @close="close" class="dialogCommon">
    <template #default>
      <el-form ref="tableDataRef" :model="state.tableData" :label-width="100" label-position="right" label-suffix=":"
               :disabled="true">
        <el-container>
          <el-container>
            <el-main>
              <el-col :span="22">
                <el-form-item label="编号" prop="roomNo">
                  <el-text>{{ state.tableData.roomNo }}</el-text>
                </el-form-item>
                <el-form-item label="名称" prop="roomName">
                  <el-text>{{ state.tableData.roomName }}</el-text>
                </el-form-item>
                <el-form-item label="位置" prop="roomSpaceFullName">
                  <el-text>{{ state.tableData.roomSpaceFullName }}</el-text>
                </el-form-item>
                <el-form-item label="容纳人数" prop="capacity">
                  <el-text>{{ state.tableData.capacity }}</el-text>
                </el-form-item>
              </el-col>
            </el-main>
            <el-aside width="30%">
              <div class="demo-image__error">
                <div class="block">
                  <el-form-item label-width="0" prop="roomImg">
                    <el-image :src="imgTransfer(state.tableData.roomImg)" fit="fill">
                      <template #error>
                        <div class="image-slot">
                          <el-icon>
                            <icon-picture/>
                          </el-icon>
                        </div>
                      </template>
                    </el-image>
                  </el-form-item>
                </div>
              </div>
            </el-aside>
          </el-container>
          <el-footer :height="'100'">
            <el-form-item label="会议室设备" prop="devices">
              <el-space>
                <template v-for="device in state.tableData.devices">
                  <el-tag>{{ device }}</el-tag>
                </template>
              </el-space>
            </el-form-item>
            <el-form-item label="管理员" prop="ownerName">
              <el-text>{{ state.tableData.ownerName }}</el-text>
            </el-form-item>
            <el-form-item label="联系电话" prop="ownerPhone">
              <el-text>{{ state.tableData.ownerPhone }}</el-text>
            </el-form-item>
            <el-form-item label="预约审批" prop="isEnableApproval">
              <el-tag v-if="state.tableData.isEnableApproval === 1">是</el-tag>
              <el-tag v-else>否</el-tag>
            </el-form-item>
          </el-footer>
        </el-container>
      </el-form>
    </template>
    <template #footer>
      <el-button @click="close">关闭</el-button>
    </template>
  </el-dialog>
</template>


<script setup>
import { Picture as IconPicture } from '@element-plus/icons-vue'

const emits = defineEmits(['onClose'])


const dialogRef = ref(false)

const tableDataRef = ref()


const state = reactive({
  title: '',
  tableData: {}
})

const imgTransfer = (name) => {
  if (name) {
    return import.meta.env.VITE_BASE_URL + name
  }
  return name
}

const open = (title, val) => {
  dialogRef.value = true
  nextTick(() => {
    state.title = title
    if (val) {
      state.tableData = val
      state.tableData.devices = val.devices.split(',')
    }
  })
}

const close = () => {
  tableDataRef.value.resetFields()
  dialogRef.value = false
  emits('onClose')
}


defineExpose({
  open
})
</script>


<style scoped lang="less">
.demo-image__error .block {
  text-align: center;
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
  vertical-align: top;
}

.demo-image__error .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}

.demo-image__error .el-image {
  padding: 0 5px;
  max-width: 300px;
  max-height: 280px;
  width: 100%;
  height: 220px;
}

.demo-image__error .image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}

.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}
</style>
