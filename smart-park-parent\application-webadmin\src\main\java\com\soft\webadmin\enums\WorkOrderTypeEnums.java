package com.soft.webadmin.enums;

/**
 * 工单类型枚举类
 */
public enum WorkOrderTypeEnums {

    REPAIR("维修工单"),

    MAINTENANCE("维保工单"),

    PATROL_INSPECTION("巡检工单"),
    ;

    private String workOrderTypeName;

    WorkOrderTypeEnums(String workOrderTypeName) {
        this.workOrderTypeName = workOrderTypeName;
    }

    public String getWorkOrderTypeName() {
        return workOrderTypeName;
    }

    public static String getWorkOrderTypeName(WorkOrderTypeEnums workOrderType) {
        for (WorkOrderTypeEnums workOrderTypeEnums : WorkOrderTypeEnums.values()) {
            if (workOrderTypeEnums == workOrderType) {
                return workOrderTypeEnums.getWorkOrderTypeName();
            }
        }
        return "";
    }
}
