<template>
  <div>
    <el-form label-suffix=":">
      <el-form-item label="设备">
        <el-input :value="equipNameShow" class="inputCursor" placeholder="请选择设备" readonly
                  @click="equipClick" type="textarea"/>
      </el-form-item>
    </el-form>

    <span class="commonTitle">样式</span>
    <el-divider style="margin: 14px 0;"/>
    <el-checkbox-group v-model="curComponent.params.classInfo.checkList">
      <el-checkbox v-for="item in styleList" :value="item.class">{{ item.label }}
        <el-icon size="18" @click.stop.prevent="addClassHandle(item.class)">
          <Setting/>
        </el-icon>
      </el-checkbox>
    </el-checkbox-group>

    <div v-if="curComponent.params.eventInfo">
      <span class="commonTitle">事件</span>
      <el-divider style="margin: 14px 0;"/>
      <el-form label-suffix=":">
        <el-form-item label="类型">
          <el-row align="middle" style="width: 100%">
            <el-select v-model="curComponent.params.eventInfo.eventType" clearable style="flex: 1"
                       @change="clearHandle">
              <el-option v-for="item in state.typeOption" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
            <Setting v-show="['attributeControl', 'pageJump'].includes(curComponent.params.eventInfo.eventType)" class="settingIcon"
                     @click="addEventList"/>
          </el-row>
        </el-form-item>
      </el-form>
    </div>

    <class-modal ref="classRef" :attributeOptions="state.attributeOptions" :classType="state.classType"></class-modal>
    <event-modal ref="eventRef" :attributeOptions="state.attributeOptions"></event-modal>
    <select-iot ref="equipRef" :multiple="false" @rowClick="equipSelect"/>
  </div>
</template>

<script setup>
import classModal from './classModal.vue';
import eventModal from './eventModal.vue';

import {webtopoStore} from '@/store/modules/webtopo.js'

import useSubTypeOptions from '@/hooks/option/useSubTypeOptions.js'

// 设备类别（子系统类型）
const { subTypeDictionaries } = useSubTypeOptions()

const webtopo = webtopoStore()

let {curComponent} = storeToRefs(webtopo)

let classRef = ref()
let eventRef = ref()
let equipRef = ref()

const state = reactive({
  classType: '',
  attributeOptions: [],
  typeOption: [
    {
      value: 'videoPre',
      label: '视频预览'
    },
    {
      value: 'equipControl',
      label: '设备控制'
    },
    {
      value: 'attributeControl',
      label: '属性控制'
    },
    {
      value: 'pageJump',
      label: '页面跳转'
    },
  ],
})

const styleList = computed(() => {

  const classDiction = {
    text: [
      {
        label: '隐藏',
        class: 'toposhowHide'
      },
      {
        label: '值显示',
        class: 'topoTextShow'
      }
    ],
    evnet: [
      {
        label: '隐藏',
        class: 'toposhowHide'
      },
      {
        label: '闪烁',
        class: 'topoTwinkle'
      },
      {
        label: '切换',
        class: 'topoCheckout'
      }
    ]
  }

  return classDiction[curComponent.value.type]
})

const equipNameShow = computed(() => {
  if (Object.keys(curComponent.value.params.equipInfo).length)
    return subTypeDictionaries[curComponent.value.params.equipInfo.subType] + '-' + curComponent.value.params.equipInfo.equipmentName
})

// 设备触发选择
const equipClick = () => {
  equipRef.value.open();
}

// 设备选择
const equipSelect = (row) => {
  (row.equipmentAttributeList || []).forEach(item => delete item.attributeValue)
  curComponent.value.params.equipInfo = {...row}
  state.attributeOptions = (row.equipmentAttributeList || []).map(item => item.attributeKey);

  if (curComponent.value.params.eventInfo?.attribute) {   //清空源有属性
    curComponent.value.params.eventInfo.attribute = null
  }
}

// 当设备属性
const getEquipAtt = (info) => {
  let {equipInfo} = info?.params instanceof Object && info.params
  if (equipInfo instanceof Object && Object.keys(equipInfo).length) {
    state.attributeOptions = (equipInfo.equipmentAttributeList || []).map(key => key.attributeKey)
  }
}

// 添加class对象
const addClassHandle = (info) => {
  state.classType = info

  let {classInfo} = curComponent.value.params

  // 查找当前class对象
  let current = classInfo.classList.find(item => item.classType === info)

  // 如果不存在  重新赋值  关联(当前组件)
  if (!current) {
    if (info == 'topoTextShow') {
      classInfo.classList.push({
        classType: info
      })
    } else {
      classInfo.classList.push({
        classType: info,
        list: [
          {
            min: null,
            max: null
          }
        ]
      })
    }
    current = classInfo.classList.find(item => item.classType == info)
  }

  classRef.value.state.itemInfo = current

  classRef.value.open()
}

// 添加事件
const addEventList = () => {
  let {eventInfo} = curComponent.value.params
  eventRef.value.state.eventInfo = eventInfo
  eventRef.value.open()
}

// 清空之前值
const clearHandle = (value) => {
  curComponent.value.params.eventInfo = {
    eventType: value
  }
}

watch(curComponent, (newVal) => {
  if (newVal)
    getEquipAtt(newVal)
}, {immediate: true})

</script>

<style lang='less' scoped>
.inputCursor {
  :deep(.el-input__inner) {
    cursor: pointer;
  }
}

.commonTitle {
  font-size: 14px;
  color: #606266;
}

.el-checkbox {
  display: block;
  margin-right: 10px;
}

.el-icon {
  position: absolute;
  right: 0;
  cursor: pointer;
}

.settingIcon {
  width: 18px;
  height: 18px;
  margin-left: 7px;
  color: #606266;
  cursor: pointer;
}
</style>
