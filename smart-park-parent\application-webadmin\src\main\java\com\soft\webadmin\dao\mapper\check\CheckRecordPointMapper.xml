<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.check.CheckRecordPointMapper">
    <resultMap type="com.soft.webadmin.model.check.CheckRecordPoint" id="CheckRecordPointResult">
        <result property="id" column="id" />
        <result property="recordId" column="record_id" />
        <result property="pointName" column="point_name" />
        <result property="pointType" column="point_type" />
        <result property="dataId" column="data_id" />
        <result property="state" column="state" />
        <result property="remark" column="remark" />
        <result property="img" column="img" />
    </resultMap>

    <sql id="selectCheckRecordPointVo">
        id, record_id, point_name, point_type, data_id, state, remark, img
    </sql>

    <select id="queryListByRecordId" resultType="com.soft.webadmin.vo.check.CheckRecordPointVO">
        select <include refid="selectCheckRecordPointVo" />,
        (
        case when t.point_type = 'EQUIPMENT' then (select equipment_no from sp_equipment where equipment_id = t.data_id)
        when t.point_type = 'SPACE' then (select code from sp_space where id = t.data_id) end
        ) data_no,
        (
        case when t.point_type = 'EQUIPMENT' then (select space_full_name from sp_equipment where equipment_id = t.data_id)
        when t.point_type = 'SPACE' then (select full_name from sp_space where id = t.data_id) end
        ) data_space
        from sp_check_record_point t where record_id = #{recordId}
    </select>
    
</mapper>