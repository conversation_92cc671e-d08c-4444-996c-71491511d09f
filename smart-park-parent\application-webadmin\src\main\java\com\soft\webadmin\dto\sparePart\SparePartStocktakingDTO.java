package com.soft.webadmin.dto.sparePart;

import com.soft.webadmin.enums.BusinessTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * SparePartStocktakingDTO对象
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@ApiModel("SparePartStocktakingDTO对象")
@Data
public class SparePartStocktakingDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "业务类型：OPERATIONS运维、STOREHOUSE库房", required = true)
    private BusinessTypeEnums businessType = BusinessTypeEnums.OPERATIONS;

    @ApiModelProperty(value = "盘点单号")
    @NotBlank(message = "盘点单号不能为空！")
    private String stocktakingNo;

    @ApiModelProperty(value = "盘点单名称")
    @NotBlank(message = "盘点单名称不能为空！")
    private String stocktakingName;

    @ApiModelProperty(value = "盘点开始时间")
    @NotNull(message = "盘点开始时间不能为空！")
    private Date beginTime;

    @ApiModelProperty(value = "盘点结束时间")
    @NotNull(message = "盘点结束时间不能为空！")
    private Date endTime;

    @ApiModelProperty(value = "负责人")
    private Long headUserId;

    @ApiModelProperty(value = "盘点仓库")
    @NotNull(message = "盘点仓库不能为空！")
    private Long storehouseId;

    @ApiModelProperty(value = "备件分类id")
    private List<Long> classifyIds;

    @ApiModelProperty(value = "盘点规则：1不需要拍照、2需要拍照")
    private Integer rule = 1;

    @ApiModelProperty(value = "盘点结果：0草稿")
    private Integer result;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "盘点资产")
    @NotEmpty(message = "盘点资产不能为空！")
    @Valid
    private List<SparePartStocktakingDetailDTO> detailDTOList;

}
