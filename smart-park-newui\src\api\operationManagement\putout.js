import { request } from '@/utils/request';

// 分页查询
export const putoutPageAPI = (query) => {
    return request('get', '/sparePart/inout/getPage', query, 'F');
};

// 新增出入库
export const putoutSaveAPI = (data) => {
    return request('post', '/sparePart/inout/create', data);
};

// 查询出入库明细
export const putoutDetailAPI = (query) => {
    return request('get', '/sparePart/inout/detail', query, 'F');
};

// 导入
export const putoutUploadAPI = (data) => {
    return request('post', '/sparePart/inout/importExcel?businessType=OPERATIONS',data );
};

// 审核
export const putoutAuditAPI = (query) => {
    return request('post', '/sparePart/inout/examine', query, 'F');
}

