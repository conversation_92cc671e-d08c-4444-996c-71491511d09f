package com.soft.webadmin.dao.check;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.check.CheckRepairLogQueryDTO;
import com.soft.webadmin.model.check.CheckRepairLog;
import com.soft.webadmin.vo.check.CheckRepairLogVO;
import com.soft.webadmin.vo.check.WorkOrderOfReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报修记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-12
 */
public interface CheckRepairLogMapper extends BaseMapper<CheckRepairLog> {

    List<CheckRepairLogVO> queryList(CheckRepairLogQueryDTO queryDTO);

    List<WorkOrderOfReportVO> queryListOfOrder(CheckRepairLogQueryDTO queryDTO);

    CheckRepairLogVO queryById(@Param("id") Long id);

}
