<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" @onClose="onClose" :formRef="ruleFormRef" :width="900" :showButton="!isRead" class="dialogTextarea">
    <el-form ref="ruleFormRef" label-width="0" :model="form" :rules="state.rules" label-suffix=":" >
      <div class="divFlex">
        <div class="divLeft"></div>
        <div class="divRight">基本信息</div>
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="小组名称" prop="name" label-width="110px">
            <el-input v-model="form.name" placeholder="请输入小组名称" :disabled="isRead"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="职责" prop="duty" label-width="110px">
        <el-input v-model="form.duty" style="width: 100%;" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit placeholder="请输入职责" :disabled="isRead"/>
      </el-form-item>

      <el-row justify="space-between" v-if="!isRead">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">成员信息</div>
        </div>
        <el-button link type="primary" icon="CirclePlus" @click="handleAdd">新增</el-button>
      </el-row>

      <el-table :data="form.memberDTOList" class="table-form">
        <el-table-column prop="" label="成员">
          <template #default="{row,$index}">
            <el-form-item :prop="'memberDTOList.' + $index + '.userId'"
                          :rules="{required: true, message: '请选择成员'}" v-if="row.isEdit">
              <el-select v-model="row.userId" filterable clearable placeholder="成员(可搜索)" @change="(value) => handleChoose(value,row)">
                <el-option v-for="item in state.headUserIdOptions" :label="item.showName + (item.deptName ? ' - ' + item.deptName : '')" :value="item.userId"/>
              </el-select>
            </el-form-item>
            <span v-else>{{ state.headUserIdOptions.find(item => item.userId == row.userId)?.showName }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="" label="手机号">
          <template #default="{row,$index}">
            <el-form-item :prop="'memberDTOList.' + $index + '.phone'" :rules="[{required: true, message: '请输入手机号'},{ validator: (rule,value,callback) => validateRegular(value,callback, /^1[3456789]\d{9}$/ ,'请输入正确的手机号'),trigger: 'blur'}]"
                          v-if="row.isEdit">
              <el-input v-model.number="row.phone" placeholder="请输入手机号"  maxlength="11"/>
            </el-form-item>
            <span v-else>{{ row.phone }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="" label="手机短号">
          <template #default="{row,$index}">
            <el-form-item :prop="'memberDTOList.' + $index + '.shortPhone'"
                          :rules="[{required: true, message: '请输入手机短号'}]" v-if="row.isEdit">
              <el-input v-model.number="row.shortPhone" placeholder="请输入手机短号"   maxlength="6"/>
            </el-form-item>
            <span v-else>{{ row.shortPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="" label="组内职务">
          <template #default="{row,$index}">
            <el-form-item :prop="'memberDTOList.' + $index + '.duties'" :rules="{required: true, message: '请选择组内职务'}"
                          v-if="row.isEdit">
              <el-select v-model="row.duties" filterable clearable placeholder="请选择组内职务">
                <el-option v-for="(value,key) in state.dutiesOptions" :label="value" :value="Number(key)"/>
              </el-select>
            </el-form-item>
            <span v-else>{{ state.dutiesOptions[row.duties] }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" label="操作" width="150" v-if="!isRead">
          <template #default="{row,$index}">
            <div :class="row.isEdit && 'table-btn'">
              <el-button link type="primary" icon="Edit" @click="handleEdit(row)" v-if="!row.isEdit">编辑</el-button>
              <el-button link type="success" icon="Document" @click="handleSave(row,$index)" v-if="row.isEdit">保存
              </el-button>
              <el-button link type="danger" icon="Delete" @click="handleDelete($index)">删除</el-button>
            </div>
          </template>
        </el-table-column>

      </el-table>

    </el-form>
  </dialog-common>
</template>

<script setup>
import { ElMessage } from 'element-plus'

import { groupSaveAPI } from '@/api/comprehensiveSecurity/group.js'
import { listUsersAPI } from '@/api/settingSystem/user.js'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  isRead:{
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit'])

let dialog = ref()
let ruleFormRef = ref()

const form = reactive({
  memberDTOList: []
})

// 正则校验
const validateRegular = (value,callback,Reg,tip) => {
  if (value && !Reg.test(value)) {
    callback(new Error(tip))
  } else {
    callback()
  }
}

const state = reactive({
  headUserIdOptions: [],
  dutiesOptions: {
    1: '组长',
    2: '组员'
  },
  rules: {
    name: [{ required: true, message: '请输入小组名称', trigger: 'blur' },],
    duty: [{ required: true, message: '请输入职责', trigger: 'blur' }]
  }
})

onMounted(() => {
  getHandUser()
})

// 获取系统用户
const getHandUser = () => {
  listUsersAPI({}).then((res) => {
    state.headUserIdOptions = res.data.dataList || [];
  });
}

const open = () => {
  dialog.value.open()
}

const handleChoose = (value,row) => {
  let userInfo = state.headUserIdOptions.find(item => item.userId == value)
  console.log(userInfo)
  row.phone = userInfo.phone
  row.shortPhone = userInfo.shortPhone
}

// 新增成员
const handleAdd = () => {
  form.memberDTOList.push({
    isEdit: true
  })
}

// 编辑
const handleEdit = (row) => {
  row.isEdit = true
}

// 保存
const handleSave = (row, index) => {
  ruleFormRef.value.validateField(
      ['userId', 'phone', 'shortPhone', 'duties']
          .map(key => 'memberDTOList.' + index + '.' + key)).then(valid => {
    row.isEdit = false
  })
}

// 删除
const handleDelete = (index) => {
  form.memberDTOList.splice(index, 1)
}

// 关闭
const onClose = () => {
  form.id = ''
  form.eventId = ''
  form.memberDTOList = []
}

// 提交
const submit = () => {
  const subFrom = JSON.parse(JSON.stringify(form))

  if(subFrom.memberDTOList.filter(item => item.duties == 1).length != 1){
    ElMessage.warning('请选择一位组长')
    return false
  }

  groupSaveAPI({...subFrom}).then((res) => {
    if (res.success) {
      ElMessage.success('保存成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}


defineExpose({
  form,
  open
})
</script>
