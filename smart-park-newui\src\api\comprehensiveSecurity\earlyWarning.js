import { request } from '@/utils/request';

// 分页查询
export const warningPageAPI = (params) => {
    return request('get', '/contingency/warning/getPage', params, 'F');
};

// 保存
export const warningSaveAPI = (data) => {
    return request('post', '/contingency/warning/save', data);
};

// 详情
export const warningDetailAPI = (params) => {
    return request('get', '/contingency/warning/detail', params, 'F');
}

//事件处理
export const warningDealAPI = (params) => {
    return request('post', '/contingency/warning/handle', params, 'F');
}

//事件总结
export const warningSumupAPI = (data) => {
    return request('post', '/contingency/warning/summarize', data);
}

//监控视频
export const warningVideoAPI = (params) => {
    return request('get', '/contingency/warning/previewFlvUrls', params, 'F');
}
