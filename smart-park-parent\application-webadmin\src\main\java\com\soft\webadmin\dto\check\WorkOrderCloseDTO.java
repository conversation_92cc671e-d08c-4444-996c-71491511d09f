package com.soft.webadmin.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * WorkOrderDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@ApiModel("WorkOrderRejectDTO对象")
@Data
public class WorkOrderCloseDTO {

    @ApiModelProperty(value = "工单id")
    @NotNull(message = "工单id不能为空！")
    private Long id;

    @ApiModelProperty(value = "关闭原因")
    @NotNull(message = "关闭原因不能为空！")
    private String reason;

}
