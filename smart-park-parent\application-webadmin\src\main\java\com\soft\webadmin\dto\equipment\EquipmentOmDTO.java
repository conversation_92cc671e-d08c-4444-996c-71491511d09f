package com.soft.webadmin.dto.equipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * EquipmentDTO对象
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@ApiModel("EquipmentSaveDTO对象")
@Data
public class EquipmentOmDTO {

    @ApiModelProperty(value = "设备id")
    private Long equipmentId;

    @ApiModelProperty(value = "设备编号")
    private String equipmentCode;

    @ApiModelProperty(value = "设备名称")
    @NotBlank(message = "设备名称不能为空！")
    private String equipmentName;

    @ApiModelProperty(value = "设备型号")
    private String equipmentModel;

    @ApiModelProperty(value = "设备类型Id")
    private Long equipmentTypeId;

    @ApiModelProperty(value = "生产厂家")
    private String factory;

    @ApiModelProperty(value = "位置")
    @NotNull(message = "位置不能为空！")
    private Long spaceId;

    @ApiModelProperty(value = "保修开始日期")
    private Date warrantyBeginDate;

    @ApiModelProperty(value = "保修结束日期")
    private Date warrantyEndDate;

    @ApiModelProperty(value = "启用日期")
    private Date activationDate;

    @ApiModelProperty(value = "管理科室id")
    private Long deptId;

    @ApiModelProperty(value = "负责人id")
    private Long owner;

    @ApiModelProperty(value = "备注")
    private String remark;
}
