<template>
  <dialog-common ref="dialog" title="评价" @submit="submit" :formRef="ruleFormRef" :width="650">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="110px" label-suffix=":">
      <el-form-item label="评分" prop="score">
        <el-rate v-model="form.score"/>
      </el-form-item>
      <el-form-item label="评价内容" prop="content">
        <el-input v-model="form.content" :autosize="{ minRows: 5 }" type="textarea" :maxlength="500" show-word-limit placeholder="请输入评价内容"/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { workEvaluateAPI } from '@/api/operationManagement/workOrder.js';
import { ElMessage, ElMessageBox } from 'element-plus';

const emit = defineEmits(['submit']);
const ruleFormRef = ref();
const dialog = ref();
const form = reactive({});

const state = reactive({
  rules: {
    score: [{required: true, message: '请选择评分', trigger: 'change'},],
  },
});

// 提交表单
const submit = () => {
  if (form.score < 1) {
    ElMessage({
      showClose: true,
      message: '请选择评分',
      type: 'error',
    })
    return;
  }
  workEvaluateAPI(form).then((res) => {
    if (res.success) {
      ElMessage.success('操作成功');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

const open = () => {
  dialog.value.open();
}

defineExpose({
  form,
  open,
});
</script>
