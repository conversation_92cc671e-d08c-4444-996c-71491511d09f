package com.soft.webadmin.enums;

import cn.hutool.core.util.StrUtil;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 缓存Key枚举
 * @date 2023-09-07  10:47:02
 */
public enum RedisKeyEnums {
    WX_KEY("微信缓存开头", "WX_KEY:", 60L, TimeUnit.MINUTES),
    FORBID_REPEAT_CLICK_KEY("禁止重复点击", "FORBID_REPEAT_CLICK:", 2L, TimeUnit.SECONDS),
    LIMIT_IP_REQUEST_TIMES("限制IP一段时间访问次数", "LIMIT_IP_REQUEST_TIMES:",60L, TimeUnit.MINUTES),
    ;


    RedisKeyEnums(String name, String key) {
        this.name = name;
        this.key = key;
    }

    RedisKeyEnums(String name, String key, Long expirationDuration, TimeUnit timeUnit) {
        this.name = name;
        this.key = key;
        this.expirationDuration = expirationDuration;
        this.timeUnit = timeUnit;
    }

    /**
     * 缓存 KEY 名称
     */
    private final String name;

    /**
     * 缓存 KEY 键名
     */
    private String key;

    /**
     * 指定过期时长
     */
    private Long expirationDuration;

    /**
     * 时间单位
     */
    private TimeUnit timeUnit;


    public String getName() {
        return name;
    }

    public String getKey() {
        return key;
    }

    public String getKey(String... suffixes) {
        String redisKey = this.key;
        for (String suffix : suffixes) {
            redisKey = StrUtil.concat(true, redisKey, suffix, ":");
        }
        return redisKey.substring(0, redisKey.lastIndexOf(":"));
    }

    public Long getExpirationDuration() {
        return expirationDuration;
    }

    public TimeUnit getTimeUnit() {
        return timeUnit;
    }
}
