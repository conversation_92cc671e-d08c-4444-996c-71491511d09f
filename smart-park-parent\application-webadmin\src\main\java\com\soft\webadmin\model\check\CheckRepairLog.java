package com.soft.webadmin.model.check;

import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.*;
import com.soft.webadmin.vo.check.CheckRepairLogVO;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * 报修记录对象 sp_check_repair_log
 *
 * <AUTHOR>
 * @date 2023-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_check_repair_log")
public class CheckRepairLog extends BaseModel {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 设备id */
    private Long equipmentId;

    /** 位置（空间点位） */
    private Long spaceId;

    /** 位置（完整路径id） */
    private String spacePath;

    /** 位置（完整路径） */
    private String spaceFullName;

    /** 优先级（1普通，2紧急，3特级） */
    private Integer priority;

    /** 问题描述 */
    private String content;

    /** 上报科室id */
    private Long reportDeptId;

    /** 上报人id */
    private Long reportUserId;

    /** 上报人电话 */
    private String reportUserPhone;

    /** 图片 */
    private String img;

    /** 删除标记(1: 正常 -1: 已删除) */
    @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;


    @Mapper
    public interface CheckRepairLogModelMapper extends BaseModelMapper<CheckRepairLogVO, CheckRepairLog> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        CheckRepairLog toModel(CheckRepairLogVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        CheckRepairLogVO fromModel(CheckRepairLog entity);
    }

    public static final CheckRepairLogModelMapper INSTANCE = Mappers.getMapper(CheckRepairLogModelMapper.class);
}
