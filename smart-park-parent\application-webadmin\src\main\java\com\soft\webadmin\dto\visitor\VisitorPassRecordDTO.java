package com.soft.webadmin.dto.visitor;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * VisitorPassRecordDTO对象
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@ApiModel("VisitorPassRecordDTO对象")
@Data
public class VisitorPassRecordDTO {

    @ApiModelProperty(value = "${column.columnComment}")
    @NotNull(message = "数据验证失败，${column.columnComment}不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "申请 id")
    private Long applyId;

    @ApiModelProperty(value = "访客姓名")
    private String visitorName;

    @ApiModelProperty(value = "访客手机号码")
    private String visitorPhone;

    @ApiModelProperty(value = "访客身份证号码")
    private String visitorIdCard;

    @ApiModelProperty(value = "车牌号码")
    private String licenceNumber;

    @ApiModelProperty(value = "预计到访时间")
    private Date planVisitTime;

    @ApiModelProperty(value = "实际到访时间")
    private Date realVisitTime;

    @ApiModelProperty(value = "来访事由")
    private String visitReason;

    @ApiModelProperty(value = "到访状态：1未到访；2已到访；")
    private Integer visitStatus;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private String enterType;

}
