<template>
  <dialog-common ref="dialog" @close="close" :title="state.title" @submit="submit" :formRef="ruleFormRef" :width="900"
    class="dialogTextarea">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="100px" label-suffix=":">
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">基本信息</div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="厂商编号" prop="equipmentNo">
              <el-input v-model="form.equipmentNo" :disabled="readOnly"
                placeholder="请输入厂商编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="equipmentName">
              <el-input v-model="form.equipmentName" :disabled="readOnly"
                placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备类别" prop="subType">
              <el-select v-model="form.subType" placeholder="请选择设备类别" clearable @change="onSubTypeChange">
                <el-option v-for="item in state.subTypeOptions" :key="item.name" :label="item.name"
                  :value="item.name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="equipmentTypeId">
              <el-select v-model="form.equipmentTypeId" placeholder="请选择设备类型" clearable>
                <el-option v-for="item in state.equipmentTypeOptions" :key="item.id" :label="item.name"
                  :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="生产厂商" prop="factory">
              <el-select v-model="form.factory" placeholder="请选择生产厂商" clearable>
                <el-option v-for="item in state.factoryOptions" :key="item.factoryCode" :label="item.factoryCode"
                  :value="item.factoryCode" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备型号" prop="equipmentModel">
              <el-input v-model="form.equipmentModel" placeholder="规格/型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="保修开始日期" prop="warrantyBeginDate">
              <el-date-picker :disabled-date="warrantyBeginDisabledDate" v-model="form.warrantyBeginDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择保修开始日期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保修结束日期" prop="warrantyEndDate">
              <el-date-picker :disabled-date="warrantyEndDisabledDate" v-model="form.warrantyEndDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择保修结束日期" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div>
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">管理信息</div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="安装位置" prop="spaceId">
              <el-cascader v-model="form.spaceId" :options="state.spaceOptions" :props="state.spaceProps" clearable
                placeholder="请选择安装位置" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用时间" prop="activationDate">
              <el-date-picker v-model="form.activationDate" type="date" placeholder="请选择启用日期" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="标签" prop="equipmentTagIds">
              <el-select v-model="form.equipmentTagIds" placeholder="请选择标签" multiple clearable collapse-tags
                collapse-tags-tooltip :max-collapse-tags="3">
                <el-option v-for="item in state.equipmentTagOptions" :key="item.id" :label="item.name"
                  :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联设备" prop="equipmentList">
              <el-tag v-for="equipment in form.equipmentList" :key="equipment.equipmentId" class="tag" closable
                @close="handleClose(equipment)">
                {{ equipment.equipmentName }}
              </el-tag>
              <el-button size="small" @click="openSelectPage"> + 选择设备 </el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="管理部门" prop="deptId">
              <el-cascader v-model="form.deptId" :clearable="true" placeholder="请选择管理部门"
                :loading="state.deptInfo.impl.loading" :props="{
                  value: 'deptId',
                  label: 'deptName',
                  checkStrictly: true,
                  expandTrigger: 'hover',
                }" @visible-change="onDeptIdVisibleChange" :options="state.deptOptions" @change="deptValueChange">
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="owner">
              <el-select v-model="form.owner" placeholder="请选择负责人" clearable>
                <el-option v-for="item in state.userOptions" :key="item.userId" :label="item.showName"
                  :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="ip地址" prop="ip">
              <el-input v-model="form.ip" placeholder="请输入ip地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="port端口" prop="port">
              <el-input-number v-model="form.port" :controls="false" class="input-number-left" placeholder="请输入端口号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否可控" prop="controller">
              <el-radio-group v-model="form.controller" class="ml-4">
                <el-radio :value="1" size="large">是</el-radio>
                <el-radio :value="2" size="large">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="三维坐标" prop="coordinate">
              <el-input-number v-model="form.coordinate.x" :controls="false" :precision="3" placeholder="请输入x坐标"
                clearable style="margin-bottom: 5px">
                <template #prepend>x:</template>
              </el-input-number>
              <el-input-number v-model="form.coordinate.y" :controls="false" :precision="3" placeholder="请输入y坐标"
                clearable style="margin-bottom: 5px">
                <template #prepend>y:</template>
              </el-input-number>
              <el-input-number v-model="form.coordinate.z" :controls="false" :precision="3" placeholder="请输入z坐标"
                clearable>
                <template #prepend>z:</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div v-if="form.config">
        <div class="divFlex">
          <div class="divLeft"></div>
          <div class="divRight">属性配置</div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="appKey" prop="appKey">
              <el-input v-model="form.config.appKey" placeholder="请输入appKey" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="appSecret" prop="appSecret">
              <el-input v-model="form.config.appSecret" placeholder="请输入appSecret" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </dialog-common>
  <select-iot ref="selectPageRef" @submit="selectedEquipment" />
</template>

<script setup>
import { treeAPI } from '@/api/iotManagement/space.js';
import { addEquipAPI, updateEquipmentAPI } from '@/api/iotManagement/equipManage.js'
import { getDeptListAPI, getPageAPI } from '@/api/settingSystem/user.js';
import { getSubSystemTypesAPI } from "@/api/iotManagement/equipManage.js"
import { DropdownWidget } from '@/utils/widget.js';
import { ElMessage,dayjs } from 'element-plus';

import { pageListAPI } from "@/api/settingSystem/tag.js";
import { equipemtListAPI } from "@/api/iotManagement/category.js";
import { FactoriesListAPI } from "@/api/iotManagement/subsystem.js";

// 父组件定义事件
const emit = defineEmits(['submit']);

const warrantyBeginDisabledDate = (time) => {
  if(form.warrantyEndDate){
    return time.getTime() > dayjs(form.warrantyEndDate).subtract(1, "day")
  }
}

const warrantyEndDisabledDate = (time) => {
  if(form.warrantyBeginDate){
    return time.getTime() < dayjs(form.warrantyBeginDate).add(1, "day");
  }
}

const selectPageRef = ref()

const readOnly = ref(false)
const dialog = ref();
const ruleFormRef = ref();
let form = reactive({
  subType: '',
  coordinate: {},
  equipmentList: []
});

const state = reactive({
  title: '',
  deptOptions: [],
  userOptions: [],
  spaceOptions: [],
  subTypeOptions: [],
  equipmentTypeOptions: [],
  factoryOptions: [],
  deptInfo: {
    impl: new DropdownWidget(loadDeptDropdownList, true, 'deptId'),
    value: [],
  },
  // 位置级联选择配置
  spaceProps: {
    emitPath: false,
    checkStrictly: true,
    label: 'name',
    value: 'id',
    expandTrigger: 'hover',
  },
  rules: {
    equipmentNo: [{ required: true, message: '厂商编号不能为空', trigger: 'change' }],
    equipmentName: [{ required: true, message: '设备名称不能为空', trigger: 'change' }],
    subType: [{ required: true, message: '设备类别不能为空', trigger: 'change' }],
    equipmentTypeId: [{ required: true, message: '设备类型不能为空', trigger: 'change' }],
    spaceId: [{ required: true, message: '安装位置不能为空', trigger: 'change' }],
  },
});

const close = () => {
  form.equipmentTags = []
  form.equipmentTagIds = []
}


const open = (val) => {
  dialog.value.open();


  nextTick(() => {
    if (val) {
      readOnly.value = true
      Object.assign(form, JSON.parse(JSON.stringify(val)))

      if (form.coordinate && Object.keys(form.coordinate).length > 0) {
        form.coordinate = JSON.parse(form.coordinate)
      } else {
        form.coordinate = {}
      }

      // 设备标签
      if (form.equipmentTags) {
        form.equipmentTagIds = form.equipmentTags.map(equipmentTag => equipmentTag.id)
      }

      // 关联设备
      if (form.equipmentRelationList) {
        form.equipmentList = form.equipmentRelationList.map(equipmentRelationListElement => {
          return {
            equipmentId: equipmentRelationListElement.relatedEquipmentId,
            equipmentName: equipmentRelationListElement.relatedEquipmentName
          }
        })
      }
    }else{
      readOnly.value = false
    }

    state.deptInfo.impl.onVisibleChange(true).then((res) => {
      state.deptOptions = res;
    });

    // 设备标签
    if (state.form && state.form.equipmentTags) {
      state.form.equipmentTagIds = state.form.equipmentTags.map(equipmentTag => equipmentTag.id);
    }

    // 加载设备类别（子系统类型）
    loadSubTypes()

    // 加载设备分类
    onQueryEquipmentTypes(form.subType, true)
    // 加载生产厂商
    onQueryFactories(form.subType, true);

    // 加载用户列表
    loadUserList();

    // 加载设备标签列表
    loadEquipmentTags()

    // 加载空间列表
    loadSpaceOptions();

  })
};

/** 查询部门list */
function loadDeptDropdownList() {
  return new Promise((resolve, reject) => {
    getDeptListAPI({})
      .then((res) => {
        resolve(res.data.dataList);
      })
      .catch((e) => {
        reject(e);
      });
  });
}

/** 部门下拉框显隐 */
const onDeptIdVisibleChange = (show) => {
  state.deptInfo.impl.onVisibleChange(show).catch((e) => { });
};

/** 部门下拉框改变事件 */
const deptValueChange = (value) => {
  form.deptId = Array.isArray(value) ? value[value.length - 1] : undefined;
  form.owner = undefined;
  loadUserList();
};

/** 查询用户 */
const loadUserList = () => {
  if (form.deptId) {
    const sysUserDtoFilter = {
      deptId: form.deptId,
    };
    getPageAPI({ sysUserDtoFilter }).then((res) => {
      if (res.success) {
        state.userOptions = res.data.dataList;
      }
    });
  }
};

// 获取设备类别
const loadSubTypes = async () => {
  const res = await getSubSystemTypesAPI()
  if (res.success) {
    state.subTypeOptions = res.data
  }
}


// 查询设备标签
const loadEquipmentTags = () => {
  pageListAPI({ type: 2 }).then(res => {
    if (res.success) {
      state.equipmentTagOptions = res.data.dataList
    }
  })
}


// 查询位置列表
const loadSpaceOptions = () => {
  /** 查询位置 */
  treeAPI({ deep: 4 }).then((res) => {
    if (res.success) {
      state.spaceOptions = res.data;
    }
  });
}


/**
 * 设备类别选择
 */
const onSubTypeChange = (val) => {
  onQueryEquipmentTypes(val)
  onQueryFactories(val);
}

// 加载设备分类
const onQueryEquipmentTypes = (subType, init) => {
  if (!init) {
    form.equipmentTypeId = null
  }
  if (subType) {
    equipemtListAPI({ subType: subType }).then(res => {
      if (res.success) {
        state.equipmentTypeOptions = res.data
      } else {
        state.equipmentTypeOptions = []
      }
    })
  } else {
    state.equipmentTypeOptions = []
  }
}


const onQueryFactories = (subType, init) => {
  if (!init) {
    form.factory = null
  }
  if (subType) {
    FactoriesListAPI({ subType: subType }).then(res => {
      if (res.success) {
        state.factoryOptions = res.data
      } else {
        state.factoryOptions = []
      }
    })
  } else {
    state.factoryOptions = []
  }
}


/** 打开选择关联设备的窗口 */
const openSelectPage = () => {
  selectPageRef.value.selectedList = JSON.parse(JSON.stringify(form.equipmentList));
  selectPageRef.value.open();
};

/** 删除所选的关联设备 */
const handleClose = (equipment) => {
  form.equipmentList.splice(
    form.equipmentList.findIndex((item) => item.equipmentId === equipment.equipmentId), 1
  );
};
// 回调，赋值选择的关联设备
const selectedEquipment = (list) => {
  form.equipmentList = list;
};

/** 保存 */
const submit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (form.equipmentList.length > 0) {
        form.equipmentRelationIdList = form.equipmentList.map((e) => {
          return e.equipmentId;
        });
      }else{
        form.equipmentRelationIdList = []
      }

      if (form.equipmentId) {
        updateEquipmentAPI(form).then((res) => {
          if (res.success) {
            ElMessage.success('编辑成功');
            dialog.value.close();
            emit('submit');
          } else {
            ElMessage.error(res.errorMessage);
          }
        });
      } else {
        addEquipAPI(form).then((res) => {
          if (res.success) {
            ElMessage.success('新增成功');
            dialog.value.close();
            emit('submit');
          } else {
            ElMessage.error(res.errorMessage);
          }
        });
      }
    }
  })
};

defineExpose({
  form,
  state,
  open
});
</script>

<style lang="less" scoped>

.tag {
  margin: 5px 5px 5px 0;
}

.input-number-left {
  :deep(.el-input__inner) {
    text-align: left;
  }
}
</style>
