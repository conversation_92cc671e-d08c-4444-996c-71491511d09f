<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef" :width="800"
    class="dialogTextarea">
    <el-form ref="ruleFormRef" :model="form" :rules="state.rules" label-width="130px" label-suffix=":"
      style="height: 57vh;overflow: auto;">
      <el-row>
        <el-col :span="12">
          <el-form-item label="车牌号" prop="licensePlateNumber">
            <el-input v-model="form.licensePlateNumber" clearable placeholder="请输入车牌号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车辆颜色" prop="color">
            <el-input v-model="form.color" clearable placeholder="请输入车辆颜色" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="车辆类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择车辆类型">
              <el-option v-for="item in state.options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人" prop="person">
            <el-input v-model="form.person" clearable placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" clearable placeholder="手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.panel == 2">
          <el-form-item label="通行权限" prop="equipmentList">
            <el-tree-select v-model="form.equipmentList" :data="state.data" :render-after-expand="false"
              placeholder="请选择通行权限" style="width: 240px" node-key="id" multiple />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="备注" prop="cause">
            <el-input v-model="form.cause" :rows="5" type="textarea" :maxlength="500" show-word-limit
              placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import { saveAPI, accessAuthority } from "@/api/iotManagement/parking.js";
import { ElMessage } from 'element-plus';

// 正则校验
const validateRegular = (value, callback, Reg, tip) => {
  if (value && !Reg.test(value)) {
    callback(new Error(tip))
  } else {
    callback()
  }
}

const emit = defineEmits(['submit'])
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});

const { title } = toRefs(props);
let ruleFormRef = ref()
let dialog = ref()

const form = reactive({
  equipmentList: "",
})
const state = reactive({
  data: [],
  options: [{
    value: 1,
    label: '小型车',
  }, {
    value: 2,
    label: '中型车',
  }, {
    value: 3,
    label: '大型车',
  }],
  rules: {
    licensePlateNumber: [{ required: true, message: '请输入车牌号', trigger: 'blur' },],
    color: [{ required: true, message: '请输入车辆颜色', trigger: 'blur' }],
    type: [{ required: true, message: '请选择车辆类型', trigger: 'blur' }],
    person: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    phone: [
      { required: true, message: '请输入手机号' },
      { validator: (rule, value, callback) => validateRegular(value, callback, /^1[3456789]\d{9}$/, '请输入正确的手机号'), trigger: 'blur' }
    ],
    equipmentList: [{ required: true, message: '请选择通行权限', trigger: 'blur' }]
  }
});

const open = () => {
  dialog.value.open()
  if (form.panel == 2) {
    accessAuthority({}).then(res => {
      state.data = res.data
    })
  }
}

// 提交
const submit = () => {
  let query = {
    licensePlateNumber:form.licensePlateNumber,
    color:form.color,
    type:form.type,
    person:form.person,
    phone:form.phone,
    cause:form.cause,
    panel:form.panel
  }

  if (form.equipmentList instanceof Array) {
    query.equipmentIds = form.equipmentList.join(",")
  }

  saveAPI(query).then((res) => {
    if (res.success) {
      ElMessage.success('名单已提交');
      dialog.value.close();
      emit('submit');
    } else {
      ElMessage.error(res.errorMessage);
    }
  });
}

defineExpose({
  form,
  open
})
</script>

<style lang='less' scoped></style>
