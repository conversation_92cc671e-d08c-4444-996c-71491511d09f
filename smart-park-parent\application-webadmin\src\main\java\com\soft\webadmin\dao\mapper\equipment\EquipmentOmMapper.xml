<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.webadmin.dao.equipment.EquipmentOmMapper">

    <sql id="selectEquipmentVo">
        t.equipment_id, t.equipment_code, t.equipment_name, t.equipment_model, t.equipment_type_id, t.factory, t.space_id, t.space_path, t.space_full_name, t.dept_id, t.owner, t.warranty_begin_date, t.warranty_end_date, t.activation_date, t.equipment_status, t.run_status, t.scrap_date, t.scrap_reason, t.remark, t.category, t.deleted_flag, t.create_user_id, t.create_time, t.update_user_id, t.update_time
    </sql>

    <select id="queryList" resultType="com.soft.webadmin.vo.equipment.EquipmentOmVO" parameterType="com.soft.webadmin.dto.equipment.EquipmentOmQueryDTO">
        select equipment_id, equipment_code, equipment_name, equipment_model, equipment_type_id, factory, space_id, space_path, space_full_name, dept_id,
        owner, warranty_begin_date, warranty_end_date, activation_date, equipment_status, run_status, scrap_date, scrap_reason, remark, category, deleted_flag,
        create_user_id, create_time, update_user_id, update_time, full_type_id, equipment_type_name, maintenance_count, repair_count, maintenance_status from
        (select equipment_id, equipment_code, equipment_name, equipment_model, equipment_type_id, factory, space_id, space_path, space_full_name,
        dept_id, owner, warranty_begin_date, warranty_end_date, activation_date, equipment_status, run_status, scrap_date, scrap_reason, remark, category,
        deleted_flag, create_user_id, create_time, update_user_id, update_time, full_type_id, equipment_type_name, maintenance_count, repair_count,
        (CASE WHEN repair_count > 0 THEN 12 WHEN maintenance_count > 0 THEN 11 ELSE equipment_status END) maintenance_status from
        (select <include refid="selectEquipmentVo"/>, t1.full_type_id, t1.name equipment_type_name,
        (select count(1) from sp_work_order w, sp_check_repair_log l where w.business_table = 'sp_check_repair_log'
        and w.business_id = l.id and w.state &lt; 4 and w.order_type = 'REPAIR' and l.equipment_id = t.equipment_id) repair_count,
        (select count(1) from sp_work_order w, sp_check_record_point p where w.business_table = 'sp_check_record'
        and w.business_id = p.record_id and p.point_type = 'EQUIPMENT' and w.state &lt; 4 and w.order_type = 'MAINTENANCE'
        and p.data_id = t.equipment_id) maintenance_count
        from sp_equipment_om t
        left join sp_equipment_type t1 on t1.id = t.equipment_type_id) t2) t3
        <where>
            deleted_flag = 1
            <if test="equipmentWord != null and equipmentWord != ''">
                and (equipment_name like concat('%', #{equipmentWord}, '%') or equipment_code like concat('%', #{equipmentWord}, '%'))
            </if>
            <if test="spaceId != null">
                and space_path like concat('%', #{spaceId}, '%')
            </if>
            <if test="equipmentTypeId != null">
                and full_type_id like concat('%', #{equipmentTypeId}, '%')
            </if>
            <if test="maintenanceStatus != null">
                and maintenance_status = #{maintenanceStatus}
            </if>
            <if test="category != null and category != ''">
                and category = #{category}
            </if>
            <if test="excludeStatus != null">
                and maintenance_status != #{excludeStatus}
            </if>
        </where>
        order by category desc, equipment_id desc
    </select>

    <select id="queryById" resultType="com.soft.webadmin.vo.equipment.EquipmentOmVO">
        select equipment_id, equipment_code, equipment_name, equipment_model, equipment_type_id, factory, space_id, space_path, space_full_name, dept_id,
        owner, warranty_begin_date, warranty_end_date, activation_date, equipment_status, run_status, scrap_date, scrap_reason, remark, category, deleted_flag,
        create_user_id, create_time, update_user_id, update_time, equipment_type_name, maintenance_count, repair_count, owner_name, dept_name,
        (CASE WHEN repair_count > 0 THEN 12 WHEN maintenance_count > 0 THEN 11 ELSE equipment_status END) maintenance_status from
        (select <include refid="selectEquipmentVo" />,
        (select name from sp_equipment_type where id = t.equipment_type_id) equipment_type_name,
        (select count(1) from sp_work_order w, sp_check_repair_log l where w.business_table = 'sp_check_repair_log'
        and w.business_id = l.id and w.state &lt; 4 and w.order_type = 'REPAIR' and l.equipment_id = t.equipment_id) repair_count,
        (select count(1) from sp_work_order w, sp_check_record_point p where w.business_table = 'sp_check_record'
        and w.business_id = p.record_id and p.point_type = 'EQUIPMENT' and w.state &lt; 4 and w.order_type = 'MAINTENANCE'
        and p.data_id = t.equipment_id) maintenance_count,
        (select show_name from common_sys_user where user_id = t.owner) owner_name,
        (select dept_name from common_sys_dept where dept_id = t.dept_id) dept_name
        from sp_equipment_om t) t1
        where equipment_id = #{equipmentId}
    </select>

    <select id="queryFaultList" resultType="com.soft.webadmin.vo.equipment.EquipmentOmVO">
        select equipment_id, equipment_code, equipment_name, repair_count, update_time from
        (select equipment_id, equipment_code, equipment_name,
        (select count(1) from sp_work_order w, sp_check_repair_log l where w.business_table = 'sp_check_repair_log'
        and w.business_id = l.id and w.state &lt; 4 and w.order_type = 'REPAIR' and l.equipment_id = t.equipment_id)
        repair_count,
        (select max(l.create_time) from sp_work_order w, sp_check_repair_log l where w.business_table =
        'sp_check_repair_log'
        and w.business_id = l.id and w.state &lt; 4 and w.order_type = 'REPAIR' and l.equipment_id = t.equipment_id)
        update_time
        from sp_equipment_om t
        <where>
            <foreach collection="equipmentIds" item="equipmentId" separator="or">
                equipment_id = #{equipmentId}
            </foreach>
        </where>
        ) t2 where repair_count > 0
    </select>

</mapper>