<template>
  <!-- 编辑机器人配置Dialog -->
  <dialog-common
      ref="dialog"
      :title="title"
      @submit="submit"
      :formRef="ruleFormRef"
      :width="900"
      class="dialogTextarea">
    <el-form
        ref="ruleFormRef"
        :model="form"
        :rules="state.rules"
        label-width="100px"
        label-suffix=":">
      <el-row>
        <el-col :span="12">
          <el-form-item label="平台" prop="platform">
              <el-select v-model="form.platform" placeholder="请选择平台">
                  <el-option
                    v-for="item in platforms"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  />
              </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
            <el-form-item label="类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择类型">
                    <el-option
                            v-for="item in robotTypes"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            :disabled="item.disabled"
                    />
                </el-select>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
            <el-form-item label="名称" prop="name">
                <el-input
                        v-model="form.name"
                        placeholder="请输入名称"
                        clearable/>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="WebHook" prop="webHook">
              <el-input
                      v-model="form.webHook"
                      type="textarea"
                      placeholder="请输入WebHook"
                      clearable
                      rows="2"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
          <el-col :span="24">
              <el-form-item label="密钥" prop="secret">
                  <el-input
                          v-model="form.secret"
                          type="textarea"
                          placeholder="请输入密钥"
                          clearable
                          rows="1"/>
              </el-form-item>
          </el-col>
      </el-row>
      <el-row>
          <el-col :span="24">
              <el-form-item label="描述" prop="remark">
              <el-input v-model="form.remark" type="textarea" maxlength="100" :show-word-limit="true" placeholder="请输入描述"
                        :rows="3"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {addThreePartyRobotAPI, updateThreePartyRobotAPI} from "@/api/settingSystem/threePartyRobot.js";
import {ElMessage} from "element-plus";
import {reactive, ref} from "vue";

const ruleFormRef = ref();
const dialog = ref();
const state = reactive({
  rules: {
    name: [{required: true, message: '名称不能为空', trigger: 'blur'}],
    platform: [{required: true, message: '平台不能为空', trigger: 'blur'}],
    webHook: [{required: true, message: 'WebHook不能为空', trigger: 'blur'}],
    type: [{required: true, message: '类型不能为空', trigger: 'blur'}],
    secret: [{required: true, message: '密钥不能为空', trigger: 'blur'}],
  },
});
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})
const { title } = toRefs(props)
const emit = defineEmits(['submit'])

/**
 * form表单数据
 */
const form = reactive({
  id: ''
})

const platforms = reactive({})
const robotTypes = reactive({})

/**
 * 打开弹窗
 */
const open = () => {
  dialog.value.open()
}

/**
 * 保存机器人配置
 */
const submit = () => {
  let data = form
  if (data.id) {
    subHandle(updateThreePartyRobotAPI, "编辑成功");
  } else {
    subHandle(addThreePartyRobotAPI, "添加成功");
  }

  function subHandle(req, title) {
    req(data).then((res) => {
      if (res.success) {
        ElMessage.success(title);
        dialog.value.close()
        emit('submit')
      } else {
        ElMessage.error(res.errorMessage);
      }
    });
  }
};

/**
 * 暴露属性，用来外部控制表单内容和弹窗状态
 */
defineExpose({
  form,
  open,
  platforms,
  robotTypes
})
</script>

