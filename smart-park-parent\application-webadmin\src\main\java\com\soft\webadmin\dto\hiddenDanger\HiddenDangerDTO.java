package com.soft.webadmin.dto.hiddenDanger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * HiddenDangerDTO对象
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@ApiModel("HiddenDangerDTO对象")
@Data
public class HiddenDangerDTO {

    @ApiModelProperty(value = "隐患类型(1消防隐患、2安全隐患)")
    @NotNull(message = "隐患类型不能为空！")
    private Integer type;

    @ApiModelProperty(value = "隐患等级(1一般隐患、2严重隐患、3重大隐患)")
    @NotNull(message = "隐患等级不能为空！")
    private Integer level;

    @ApiModelProperty(value = "隐患位置（空间id）")
    @NotNull(message = "隐患位置不能为空！")
    private Long spaceId;

    @ApiModelProperty(value = "具体情况")
    private String specificCircumstance;

    @ApiModelProperty(value = "上报图片")
    private String reportImgs;

    @ApiModelProperty(value = "是否现场整改(1是、0否)")
    private Integer whetherRectification = 0;

}
