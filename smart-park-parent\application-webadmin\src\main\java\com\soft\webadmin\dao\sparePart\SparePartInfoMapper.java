package com.soft.webadmin.dao.sparePart;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.webadmin.dto.sparePart.SparePartInfoQueryDTO;
import com.soft.webadmin.model.sparePart.SparePartInfo;
import com.soft.webadmin.vo.sparePart.SparePartInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备品备件信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface SparePartInfoMapper extends BaseMapper<SparePartInfo> {

    List<SparePartInfoVO> getList(SparePartInfoQueryDTO queryDTO);

    List<SparePartInfoVO> getListByIds(@Param("ids") List<Long> ids);

    List<SparePartInfoVO> getListByStorehouseIds(@Param("storehouseId") Long storehouseId);

    SparePartInfoVO detail(@Param("id") Long id);

}
