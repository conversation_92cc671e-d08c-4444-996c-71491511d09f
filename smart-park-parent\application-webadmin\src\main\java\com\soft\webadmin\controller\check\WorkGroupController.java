package com.soft.webadmin.controller.check;

import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.webadmin.dto.check.WorkGroupQueryDTO;
import com.soft.webadmin.dto.check.WorkGroupSaveOrUpdateDTO;
import com.soft.webadmin.service.check.WorkGroupService;
import com.soft.webadmin.vo.check.WorkGroupVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 工作班组控制器类
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@RestController
@RequestMapping("/check/work/group")
public class WorkGroupController {

    @Resource
    private WorkGroupService workGroupService;


    @ApiOperation("查询工作班组列表")
    @GetMapping("/list")
    public ResponseResult<MyPageData<WorkGroupVO>> list(WorkGroupQueryDTO workGroupQueryDTO) {
        MyPageData<WorkGroupVO> workGroupVOS = workGroupService.list(workGroupQueryDTO);
        return ResponseResult.success(workGroupVOS);
    }


    @ApiOperation("新增或删除工作组")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@RequestBody @Validated WorkGroupSaveOrUpdateDTO workGroupSaveOrUpdateDTO) {
        workGroupService.saveOrUpdate(workGroupSaveOrUpdateDTO);
        return ResponseResult.success();
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@RequestParam Long id) {
        workGroupService.deleteById(id);
        return ResponseResult.success();
    }
}
