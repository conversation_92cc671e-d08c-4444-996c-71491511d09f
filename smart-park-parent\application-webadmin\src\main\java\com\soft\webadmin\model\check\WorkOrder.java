package com.soft.webadmin.model.check;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import com.soft.webadmin.enums.WorkOrderTypeEnums;
import com.soft.webadmin.vo.check.WorkOrderVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * 工单对象 sp_work_order
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sp_work_order")
public class WorkOrder extends BaseModel {

    /** 主键id */
    @TableId(value = "id")
    private Long id;

    /** 工单编号 */
    private String orderNo;

    /** 工单类型（PATROL_INSPECTION巡检，MAINTENANCE维保，REPAIR维修） */
    private WorkOrderTypeEnums orderType;

    /** 优先级（1普通，2紧急，3特级） */
    private Integer priority;

    /** 业务id（检查记录id/报修记录id/设备告警id） */
    private Long businessId;

    /** 业务表 */
    private String businessTable;

    /** 状态（1待派单，2未响应，3处理中，4已关闭，5已完成） */
    private Integer state;

    /** 执行人 */
    private Long workUserId;

    /** 工单时间 */
    private Date workTime;

    /** 响应时间 */
    private Date responseTime;

    /** 实际完成时间 */
    private Date realityFinishTime;

    /** 工作时长，单位：分钟 */
    private Long workDuration;

    /** 处理时限，单位：分钟 */
    private Long handleLimitDuration;

    /** 处理预案 */
    private String treatmentPlan;

    /** 评分 */
    private Integer score;

    /** 删除标记(1: 正常 -1: 已删除) */
    @TableLogic(value = "1", delval = "-1")
    private Integer deletedFlag;

    @Mapper
    public interface WorkOrderModelMapper extends BaseModelMapper<WorkOrderVO, WorkOrder> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        WorkOrder toModel(WorkOrderVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        WorkOrderVO fromModel(WorkOrder entity);
    }

    public static final WorkOrderModelMapper INSTANCE = Mappers.getMapper(WorkOrderModelMapper.class);
}
