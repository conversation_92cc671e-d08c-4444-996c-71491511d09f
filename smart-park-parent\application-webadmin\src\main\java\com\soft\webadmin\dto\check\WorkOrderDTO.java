package com.soft.webadmin.dto.check;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * WorkOrderDTO对象
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@ApiModel("WorkOrderDTO对象")
@Data
public class WorkOrderDTO {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "数据验证失败，主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "工单编号")
    private String orderNo;

    @ApiModelProperty(value = "工单类型（PATROL_INSPECTION巡检，MAINTENANCE维保，REPAIR维修）")
    private String orderType;

    @ApiModelProperty(value = "优先级（1普通，2紧急，3特级）")
    private Integer priority;

    @ApiModelProperty(value = "业务id（检查记录id/报修记录id）")
    private Long businessId;

    @ApiModelProperty(value = "预计完成时间")
    private Date expectFinishTime;

    @ApiModelProperty(value = "处理时限")
    private Date timeLimit;

    @ApiModelProperty(value = "响应时间")
    private Date responseTime;

    @ApiModelProperty(value = "重新开始时间（取消挂单时间）")
    private Date restartTime;

    @ApiModelProperty(value = "实际完成时间")
    private Date realityFinishTime;

    @ApiModelProperty(value = "执行人")
    private Long workUserId;

    @ApiModelProperty(value = "状态（1未派单，2未响应，3已响应，4已退回，5已挂单，6已关闭，7已完成，8超时完成）")
    private Integer state;

    @ApiModelProperty(value = "工作时长，单位：分钟")
    private Long workTime;

    @ApiModelProperty(value = "评分")
    private Integer score;

    @ApiModelProperty(value = "删除标记(1: 正常 -1: 已删除)")
    private Integer deletedFlag;

    @ApiModelProperty(value = "创建者id")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者id")
    private Long updateUserId;

    @ApiModelProperty(value = "最后更新时间")
    private Date updateTime;

}
