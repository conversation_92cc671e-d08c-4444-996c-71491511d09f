<template>
  <dialog-common ref="dialog" :title="title" @submit="submit" :formRef="ruleFormRef">
    <el-form ref="ruleFormRef" label-width="100px" :model="form" :rules="state.rules" label-suffix=":">
      <el-form-item label="字典编码" prop="dictCode">
        <el-input v-model="form.dictCode" placeholder="请输入字典编码"/>
      </el-form-item>
      <el-form-item label="字典名称" prop="dictName">
        <el-input v-model="form.dictName" placeholder="请输入字典名称"/>
      </el-form-item>
    </el-form>
  </dialog-common>
</template>

<script setup>
import {dictionAddAPI, dictionEditAPI} from '@/api/settingSystem/dictionary.js'

import { ElMessage } from 'element-plus'

const props = defineProps({
  title:{
    type:String,
    default:''
  },
})

let {title} = toRefs(props)
const emit    = defineEmits(['submit'])

let ruleFormRef = ref()
let dialog      = ref()

const form  = reactive({})
const state = reactive({
  rules: {
    dictCode: [{ required: true, message: '请输入字典编码', trigger: 'blur' },],
    dictName: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
  }
})

const open = () => {
  dialog.value.open()
}

// 提交字典
const submit = () => {
  let data = {
    globalDictDto: form
  }

  if (data.globalDictDto.dictId) {
    subHandle(dictionEditAPI, '编辑成功')
  } else {
    subHandle(dictionAddAPI, '添加成功')
  }

  function subHandle(req, title) {
    req(data).then(res => {
      if (res.success) {
        ElMessage.success(title)
        dialog.value.close()
        emit('submit')
      } else {
        ElMessage.error(res.errorMessage)
      }
    })
  }
}

defineExpose({
  form,
  open
})
</script>

<style lang='less' scoped></style>
